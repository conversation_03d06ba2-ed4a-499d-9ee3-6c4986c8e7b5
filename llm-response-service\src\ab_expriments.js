const { supabase, wrappedSupabaseError } = require("./supabaseClient");

const getExperiments = async (profile_id) => {
  const group_id = profile_id % 10;
  const now = new Date();

  const { data, error } = await supabase
    .from("ab_experiments")
    .select("experiment_name")
    .eq("experiment_group", group_id)
    .lte("start_time", now.toISOString())
    .or(`end_time.is.null,end_time.gt.${now.toISOString()}`);

  if (error) {
    throw wrappedSupabaseError(error);
  }

  const experiments = [
    ...new Set(data.map((experiment) => experiment.experiment_name)),
  ];

  return experiments;
};

const isInTreatmentGroup = async (profile_id, experiment_name) => {
  const group_id = profile_id % 10;
  const now = new Date();

  const { count } = await supabase
    .from("ab_experiments")
    .select("id", { count: "exact", head: true })
    .eq("experiment_group", group_id)
    .eq("experiment_name", experiment_name)
    .lte("start_time", now.toISOString())
    .or(`end_time.is.null,end_time.gt.${now.toISOString()}`);

  return count && count > 0;
};

module.exports = {
  getExperiments,
  isInTreatmentGroup,
};
