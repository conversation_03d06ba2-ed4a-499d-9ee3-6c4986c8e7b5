{"last_node_id": 36, "last_link_id": 57, "nodes": [{"id": 26, "type": "UltralyticsDetectorProvider", "pos": [131, 895], "size": {"0": 315, "1": 78}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [7, 461], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cross-eyed,sketches, (worst quality), (low quality), (normal quality), lowres, normal quality, bad anatomy, DeepNegative, facing away, tilted head, {Multiple people}, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worstquality, low quality, normal quality, jpegartifacts, signature, watermark, username, blurry, bad feet, cropped, poorly drawn hands, poorly drawn face, mutation, deformed, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, extra fingers, fewer digits, extra limbs, extra arms,extra legs, malformed limbs, fused fingers, too many fingers, long neck, cross-eyed,mutated hands, polar lowres, bad body, bad proportions, gross proportions, text, error, missing fingers, missing arms, missing legs, extra digit, extra arms, extra leg, extra foot, (repeating hair)"]}, {"id": 30, "type": "LoraLoaderModelOnly", "pos": [-130, -153], "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 43}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [45], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["NSFWFilter.safetensors", -1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": {"0": 315, "1": 106}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 4]}, {"id": 35, "type": "CLIPSetLastLayer", "pos": [-230, 128], "size": {"0": 315, "1": 58}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 54}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [55], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 8, "type": "VAEDecode", "pos": [1110, -37], "size": {"0": 210, "1": 46}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 30}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [56], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 36, "type": "PreviewImage", "pos": [1470, -217], "size": {"0": 849, "1": 944}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 56}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [684, -99], "size": {"0": 315, "1": 262}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 46}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [606935613669668, "increment", 5, 1.6, "dpmpp_2s_ancestral", "karras", 1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-638, 192], "size": {"0": 315, "1": 98}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [43], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [5, 54], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [30], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["revanimated.safetensors"]}, {"id": 31, "type": "LoraLoaderModelOnly", "pos": [283, -159], "size": {"0": 315, "1": 82}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 45}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [46], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["lcm.safetensors", 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [186, 153], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 55}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a garbage can arms and legs"]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [30, 4, 2, 8, 1, "VAE"], [43, 4, 0, 30, 0, "MODEL"], [45, 30, 0, 31, 0, "MODEL"], [46, 31, 0, 3, 0, "MODEL"], [54, 4, 1, 35, 0, "CLIP"], [55, 35, 0, 6, 0, "CLIP"], [56, 8, 0, 36, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}