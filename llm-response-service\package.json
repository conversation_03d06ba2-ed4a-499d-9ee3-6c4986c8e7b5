{"name": "image-generation-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"lint": "npx eslint src", "lint:fix": "npm run lint -- --fix", "prettier": "npx prettier . --check", "prettier:fix": "npm run prettier -- --write", "check-format": "npm run prettier && npm run lint", "format": "npm run prettier:fix && npm run lint:fix", "test": "LOCAL=1 jest --passWithNoTests", "start": "LOCAL=1 LOG_DEBUG_ENABLED=1 GOOGLE_APPLICATION_CREDENTIALS=./google.json nodemon --require ./src/instrumentation/instrumentation.js src/index.js", "dev": "npm run start 2>&1 | npx pino-pretty", "profile": "LOCAL=1 GOOGLE_APPLICATION_CREDENTIALS=./google.json node --prof --require ./src/instrumentation/instrumentation.js src/index.js", "inspect": "LOCAL=1 GOOGLE_APPLICATION_CREDENTIALS=./google.json node --inspect --require ./src/instrumentation/instrumentation.js src/index.js", "deploy": "node deploy"}, "jest": {"testPathIgnorePatterns": ["evals/"]}, "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.33.1", "@fal-ai/client": "^1.2.3", "@google-cloud/bigtable": "^5.0.0", "@google-cloud/error-reporting": "^3.0.5", "@google-cloud/functions-framework": "^3.3.0", "@google-cloud/logging": "^11.0.0", "@google-cloud/monitoring": "^4.0.0", "@google-cloud/run": "^1.0.2", "@google-cloud/tasks": "^4.0.1", "@google-cloud/vision": "^4.0.2", "@gradio/client": "^1.12.0", "@grpc/grpc-js": "^1.10.8", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.50.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.53.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.53.0", "@opentelemetry/exporter-prometheus": "^0.53.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.53.0", "@opentelemetry/exporter-trace-otlp-proto": "^0.53.0", "@opentelemetry/instrumentation-runtime-node": "^0.7.0", "@opentelemetry/sdk-metrics": "^1.26.0", "@opentelemetry/sdk-node": "^0.53.0", "@opentelemetry/sdk-trace-node": "^1.26.0", "@parse/node-apn": "^6.0.1", "@runware/sdk-js": "^1.1.36", "@supabase/supabase-js": "^2.43.5", "@xenova/transformers": "^2.10.1", "ali-oss": "^6.22.0", "axios": "^1.5.1", "blurhash": "^2.0.5", "cors": "^2.8.5", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "expo-server-sdk": "^3.13.0", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "firebase-admin": "^11.11.0", "fluent-ffmpeg": "^2.1.3", "lamejs": "github:zhuker/lamejs", "loops": "^2.0.0", "mathjs": "^12.2.0", "mixpanel": "^0.18.0", "multer": "^1.4.5-lts.1", "nats": "^2.28.0", "node-fetch": "^2.6.12", "nunjucks": "^3.2.4", "openai": "^4.20.0", "p-queue": "^6.6.2", "pony-cause": "^2.1.11", "posthog-node": "^3.6.1", "redis": "^4.6.10", "replicate": "^0.22.0", "sharp": "^0.33.4", "sinon": "^16.1.0", "stream-buffers": "^3.0.2", "stripe": "^15.4.0", "text2png": "^2.3.0", "thumbhash": "^0.1.1", "twilio": "^5.4.2", "typescript": "^5.4.5", "typesense": "^1.7.2", "ua-parser-js": "^1.0.39", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^9.8.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.6.0", "globals": "^15.4.0", "jest": "^29.7.0", "node-gyp": "^10.1.0", "nodemon": "^3.0.1", "prettier": "3.3.3", "typescript-eslint": "^7.13.0"}}