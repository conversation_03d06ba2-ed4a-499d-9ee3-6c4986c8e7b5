const { supabase, retrySupabaseOperation } = require("./supabaseClient");
const { v4: uuidv4 } = require("uuid");
const { default: axios } = require("axios");
const vision = require("@google-cloud/vision");
const { autoModerate, skipModerate } = require("./moderation");
const { loggingInfo } = require("./logging");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const customParseFormat = require("dayjs/plugin/customParseFormat");
const { bumpProposedPostGenerationDate } = require("./proposedPostMode/utils");

dayjs.extend(utc);
dayjs.extend(customParseFormat);

const client = new vision.ImageAnnotatorClient();
const {
  logError,
  posthogCapture,
  logWarn,
  logInfo,
  wrappedSupabaseError,
} = require("./utils");
const { callAndLogOpenAI } = require("./llm");
const { generateBio } = require("./llmHelper");
const sharp = require("sharp");
const { sendEventFirstAIPost } = require("./loops");
const {
  tracer,
  decorateWithActiveSpanAsync,
  addSpanAttribute,
  addSpanEvent,
  SpanStatusCode,
} = require("./instrumentation/tracer");
const blurhash = require("blurhash");
const { Storage } = require("@google-cloud/storage");
const { updateDreamWithImage } = require("./dreamsImageUtils");
const redisClient = require("./redisClient");
const { isProposedPost } = require("./proposedPostMode/utils");
const {
  onProposedPostReadyForReview,
} = require("./proposedPostMode/onProposedPostReadyForReview");

const {
  setLeaderboadSubmissionPostToPending,
} = require("./leaderboardHelpers");

const bucketName = "butterflies-images-v1-us";
const bucketPath = "orig";

const convertImageToBase64 = decorateWithActiveSpanAsync(
  "fetch and convert image to base64",
  _convertImageToBase64,
);
async function _convertImageToBase64(url) {
  try {
    // Fetching the image data as a stream
    const response = await axios({
      method: "get",
      url: url,
      responseType: "arraybuffer",
    });

    // Using sharp to handle the image format automatically
    const image = sharp(response.data);
    const metadata = await image.metadata();

    // Converting the image to a Buffer
    const buffer = await image.toBuffer();

    // Encoding the buffer to Base64
    const base64Image = buffer.toString("base64");

    // Prepending the appropriate header for the Base64 encoded string
    return `data:image/${metadata.format};base64,${base64Image}`;
  } catch (error) {
    logError({
      context: "convertImageToBase64 error",
      error,
      url,
    });
    return null;
  }
}

const uploadToStorage = decorateWithActiveSpanAsync(
  "upload image to storage",
  _uploadToStorage,
);
async function _uploadToStorage({ url }) {
  // If this is an image proxy URL, just return the URL.
  if (url.includes("img.butterflies.ai")) {
    return url;
  }

  try {
    // Fetch the image from the URL
    const response = await axios.get(url, {
      responseType: "arraybuffer",
    });

    let myuuid = uuidv4();

    // Convert the data to a Buffer
    const buffer = Buffer.from(response.data, "binary");
    const pathInStorage = `/posts/${myuuid}`;

    // Upload the image to Supabase storage
    const { data, error: storageError } = await supabase.storage
      .from("ai-ig")
      .upload(pathInStorage, buffer, {
        contentType: "image/png", // Make sure to set this correctly for your file type
        cacheControl: "31536000",
      });

    if (storageError) {
      if (
        storageError.code === "ResourceAlreadyExists" ||
        storageError?.statusCode === "409"
      ) {
        logWarn({
          context: "*** uploadToStorage warn",
          message: "Resource already exists",
        });
        // continue on, we don't treat this as an error but we're still logging a warning to be aware of it
      } else {
        const error = wrappedSupabaseError(
          storageError,
          "failed to upload to storage",
        );
        logError({
          context: "*** uploadToStorage error",
          error: error,
        });
        throw error;
      }
    }

    const storage = new Storage();
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(`${bucketPath}/${myuuid}.png`);

    await file.save(buffer, {
      metadata: {
        contentType: "image/png",
      },
      resumable: false,
    });
    logInfo({
      context: "**** uploadToStorage",
      message: `uploading image to GCS ${bucketName}/${bucketPath}/${myuuid}.png`,
      url,
    });

    const publicUrl = supabase.storage.from("ai-ig").getPublicUrl(data.path)
      .data.publicUrl;

    return publicUrl;
  } catch (err) {
    logError({
      context: "Error uploading image:",
      error: err,
    });
    throw err;
  }
}

const updateBotAvatarWithURL = decorateWithActiveSpanAsync(
  "updateBotAvatarWithURL",
  _updateBotAvatarWithURL,
);
async function _updateBotAvatarWithURL({ url, profile_id, bot_description }) {
  try {
    let butterflyURL;

    if (url.includes("/object/public/ai-ig")) {
      butterflyURL = url;
    } else if (url.includes("img.butterflies.ai")) {
      butterflyURL = url;
    } else {
      // This should not happen, and if it does, we want to know.
      logInfo({
        context: "**** updateBotAvatarWithURL",
        message: `uploading image to storage`,
        url,
      });
      butterflyURL = await uploadToStorage({
        url,
      });
    }

    const { error: updateProfileError } = await supabase
      .from("profiles")
      .update({
        avatar_url: butterflyURL,
        image_widths: null,
      })
      .eq("id", profile_id);

    if (updateProfileError) {
      if (updateProfileError.code === "PGRST116") {
        logWarn({
          context: `**** updateBotAvatarWithURL failed to update profile with id ${profile_id}, because ${profile_id} does not exist`,
          message: updateProfileError.message,
        });

        return;
      }
      throw wrappedSupabaseError(
        updateProfileError,
        "failed to update profile avatar_url",
      );
    }

    if (bot_description) {
      const { error: updateBotsError } = await supabase
        .from("bots")
        .update({
          description: bot_description,
        })
        .eq("profile_id", profile_id);
      if (updateBotsError) {
        if (updateBotsError.code === "PGRST116") {
          logWarn({
            context: `**** updateBotAvatarWithURL failed to update bot with profile_id ${profile_id}, because ${profile_id} does not exist`,
            message: updateBotsError.message,
          });

          return;
        }
        throw wrappedSupabaseError(
          updateBotsError,
          "failed to update bot description",
        );
      }
    }
  } catch (error) {
    logError({ context: "Failed to update bot avatar:", error });
    throw error;
  }
}

const doesImageContainFace = decorateWithActiveSpanAsync(
  "doesImageContainFace",
  _doesImageContainFace,
);
async function _doesImageContainFace({ imageUrl }) {
  if (!imageUrl || !imageUrl.length > 0) {
    return false;
  }

  try {
    const [result] = await client.faceDetection(imageUrl);
    const faces = result.faceAnnotations;

    console.log("result", result);
    console.log("faces", JSON.stringify(faces));

    return faces.length > 0;
  } catch (error) {
    logError({ context: "doesImageContainFace failure:", error });
    return false;
  }
}

async function getBoundingPolyForFace({ imageUrl }) {
  if (!imageUrl || !imageUrl.length > 0) {
    return false;
  }

  try {
    const [result] = await client.faceDetection(imageUrl);
    const faces = result.faceAnnotations;

    if (faces.length === 0) {
      return null;
    }

    // find LEFT_EYE and RIGHT_EYE through .landsmarks
    const face = faces[0];
    const leftEye = face.landmarks.find(
      (landmark) => landmark.type === "LEFT_EYE",
    );

    const rightEye = face.landmarks.find(
      (landmark) => landmark.type === "RIGHT_EYE",
    );

    const noseTip = face.landmarks.find(
      (landmark) => landmark.type === "NOSE_TIP",
    );

    const mouthCenter = face.landmarks.find(
      (landmark) => landmark.type === "MOUTH_CENTER",
    );

    console.log("face", JSON.stringify(face));

    if (!leftEye || !rightEye || !noseTip || !mouthCenter) {
      return null;
    }

    if (face.detectionConfidence < 0.87) {
      return null;
    }

    return faces[0].boundingPoly;
  } catch (error) {
    logError({ context: "doesImageContainFace failure:", error });
    return false;
  }
}

const updatePost = decorateWithActiveSpanAsync("updatePost", _updatePost);
async function _updatePost({
  imageUrls,
  blurhash,
  post_id,
  profile_id,
  leaderboard_submission = false,
}) {
  addSpanEvent("updatePost");

  let media_url = imageUrls[0];
  // OPTIMIZATION: these two requests could happen concurrently

  addSpanEvent("updatePost:updateMediaURL");
  const postData = await tracer.withActiveSpan(
    "update post with media_url",
    async () => {
      const { data, error } = await supabase
        .from("posts")
        .update({
          media_url,
          image_widths: null,
        })
        .eq("id", post_id)
        .select("*")
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          logWarn({
            context: `**** updatePost failed to update post with id ${post_id}, because ${post_id} does not exist`,
            message: error.message,
          });

          addSpanEvent("updatePost:post_not_found");
          return;
        }
        addSpanEvent("updatePost:error");
        throw wrappedSupabaseError(
          error,
          "failed to update post with media_url",
        );
      }
      return data;
    },
  );

  addSpanEvent("updatePost:getProfileNSFW");
  const profile = await tracer.withActiveSpan("get profile nsfw", async () => {
    const { data: profile, error } = await supabase
      .from("profiles")
      .select("id, username, display_name, nsfw")
      .eq("id", profile_id)
      .neq("visibility", "archived")
      .single();

    if (error) {
      addSpanEvent("updatePost:get_profile_error");
      if (error.code === "PGRST116") {
        logWarn({
          context: `**** updatePost failed to get profile with id ${profile_id}, because ${profile_id} does not exist`,
          message: error.message,
        });

        return;
      }
      throw wrappedSupabaseError(error, "failed to get profile nsfw field");
    }

    return profile;
  });

  addSpanEvent("updatePost:updateVisibility");
  blurhash = blurhash ? blurhash[0] : undefined;
  let result = await updatePostVisibilityBasedOnAppropriateNSFWSettings({
    post_id,
    media_url,
    blurhash,
    profile,
    post: postData,
    leaderboard_submission,
  });

  if (result?.updateDict?.nsfw === "normal") {
    addSpanEvent("updatePost:visibility_public");
    // Check and update leaderboard submission if it exists
    const { data: leaderboardSubmission } = await supabase
      .from("leaderboard_submissions")
      .select("*")
      .eq("post_id", post_id)
      .eq("status", "generating")
      .single();

    if (leaderboardSubmission) {
      await supabase
        .from("leaderboard_submissions")
        .update({ status: "pending" })
        .eq("post_id", post_id)
        .eq("status", "generating");
    }

    // await automaticallyPostToLeaderboardIfQualifies(post_id, profile_id);
  } else if (result?.updateDict?.nsfw === "nsfw") {
    // Check and update leaderboard submission if it exists
    const { data: leaderboardSubmission } = await supabase
      .from("leaderboard_submissions")
      .select("*")
      .eq("post_id", post_id)
      .eq("status", "generating")
      .single();

    if (leaderboardSubmission) {
      await supabase
        .from("leaderboard_submissions")
        .update({ status: "archived" })
        .eq("post_id", post_id)
        .eq("status", "generating");
    }

    addSpanEvent("updatePost:visibility_hidden");
  }

  addSpanEvent("updatePost:done");
}

const _automaticallyPostToLeaderboardIfQualifies = async (
  post_id,
  profile_id,
) => {
  try {
    addSpanAttribute("post_id", post_id);
    addSpanAttribute("profile_id", profile_id);

    // Check if there's an active leaderboard
    const now = new Date().toISOString();
    addSpanEvent("checking_active_leaderboard");
    const { data: activeLeaderboard, error: leaderboardError } = await supabase
      .from("leaderboards")
      .select("*")
      .eq("active", true)
      .gte("end_at", now)
      .single();

    if (leaderboardError) {
      logError({
        context: "automaticallyPostToLeaderboardIfQualifies",
        error: leaderboardError,
      });
      addSpanEvent("leaderboard_error");
      return;
    }

    if (!activeLeaderboard) {
      // No active leaderboard found
      addSpanEvent("no_active_leaderboard");
      return;
    }
    addSpanAttribute("leaderboard_id", activeLeaderboard.id);

    // Check if the bot profile is public
    addSpanEvent("checking_profile_visibility");
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("visibility")
      .eq("id", profile_id)
      .single();

    if (profileError) {
      logError({
        context: "automaticallyPostToLeaderboardIfQualifies",
        error: profileError,
      });
      addSpanEvent("profile_error");
      return;
    }

    if (profile.visibility !== "public") {
      // Profile is not public
      addSpanEvent("profile_not_public");
      return;
    }

    // Check if the post is sfw and not flagged nsfw
    addSpanEvent("checking_post_nsfw_status");
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select("nsfw")
      .eq("id", post_id)
      .single();

    if (postError) {
      logError({
        context: "automaticallyPostToLeaderboardIfQualifies",
        error: postError,
      });
      addSpanEvent("post_error");
      return;
    }

    if (post.nsfw !== "normal") {
      // Post is nsfw
      addSpanEvent("post_nsfw");
      return;
    }

    // If all conditions are met, submit to leaderboard
    addSpanEvent("submitting_to_leaderboard");
    await setLeaderboadSubmissionPostToPending(post_id, activeLeaderboard.id);
    addSpanEvent("submission_successful");
  } catch (error) {
    logError({
      context: "automaticallyPostToLeaderboardIfQualifies",
      error,
      metadata: { post_id, profile_id },
    });
    addSpanEvent("unexpected_error");
  }
};

const automaticallyPostToLeaderboardIfQualifies = decorateWithActiveSpanAsync(
  "automaticallyPostToLeaderboardIfQualifies",
  _automaticallyPostToLeaderboardIfQualifies,
);

async function updatePostVisibilityBasedOnAppropriateNSFWSettings({
  post_id,
  media_url,
  blurhash,
  profile,
  post,
  leaderboard_submission = false,
}) {
  if (!profile) {
    return;
  }

  let services = ["HIVE", "OpenAI", "GoogleVision"];
  if (profile?.nsfw === "nsfw") {
    services = ["OpenAI", "HIVE", "GoogleVision"];
  }
  const moderationResult = await tracer.withActiveSpan(
    "autoModerate",
    async () => {
      return await autoModerate({
        imageUrl: media_url,
        id: post_id,
        context: "post",
        services,
        class: "updatePostVisibility",
        extra: {
          profile_age: profile?.age,
          profile_immitation: profile?.imitation,
          profile_nsfw: profile?.nsfw,
        },
      });
    },
  );

  const nsfw = moderationResult.nsfw;
  let nsfw_score = moderationResult.nsfw_score;
  const nsfl = moderationResult.nsfl || false;

  let updateDict = {
    visibility: "public",
    image_widths: null,
    media_url,
    safe_search_detection: moderationResult,
    nsfw,
    nsfw_score,
    nsfl,
  };

  // bot is NORMAL and the image is NSFW, do not publish!
  if (profile?.nsfw === "normal" && nsfw === "nsfw") {
    let show_nsfw = false; // default
    if (profile?.id) {
      const { data: botCreator, error: botCreatorError } = await supabase
        .from("bots")
        .select("creator_id")
        .eq("profile_id", profile.id)
        .single();
      if (botCreatorError) {
        logError({
          context: "updatePostVisibilityBasedOnAppropriateNSFWSettings",
          error: wrappedSupabaseError(
            botCreatorError,
            "failed to fetch bot creator",
          ),
          bot_id: profile.id,
        });
      } else {
        const { data: creatorSetting, error: creatorSettingError } =
          await supabase
            .from("profile_settings")
            .select("show_nsfw")
            .eq("profile_id", botCreator.creator_id)
            .single();
        if (creatorSettingError) {
          logError({
            context: "updatePostVisibilityBasedOnAppropriateNSFWSettings",
            error: wrappedSupabaseError(
              creatorSettingError,
              "failed to fetch creator setting",
            ),
            bot_id: profile.id,
          });
        } else {
          show_nsfw = creatorSetting.show_nsfw;
        }
      }
    }
    if (show_nsfw) {
      updateDict.visibility = "public";
    } else {
      updateDict.visibility = "hidden";
    }
  }

  // If it's NSFW, the posts is always public, even if its SFW
  if (profile?.nsfw === "nsfw") {
    updateDict.visibility = "public";
  }

  if (nsfl) {
    updateDict.visibility = "hidden";
  }

  const isProposed = isProposedPost(post);

  if (isProposed) {
    updateDict.visibility = "draft";
    updateDict.proposed_post_state = "proposed";
  }

  if (updateDict.visibility !== "hidden" && blurhash) {
    updateDict.previewhash = `0:${blurhash}`;
  }

  if (updateDict.visibility === "hidden") {
    loggingInfo("visibility", {
      profile_id: profile?.id ?? null,
      profile_nsfw: profile?.nsfw ?? null,
      post_id: post_id,
      post_nsfw: nsfw,
      post_nsfl: nsfl,
      visibility: updateDict.visibility,
    });
  }

  if (leaderboard_submission && updateDict.visibility === "public") {
    updateDict.visibility = "draft";
  }

  await tracer.withActiveSpan("update post in db", async (span) => {
    const { error: updatePostError } = await supabase
      .from("posts")
      .update(updateDict)
      .eq("id", post_id)
      .select("*");

    if (updatePostError) {
      const error = wrappedSupabaseError(
        updatePostError,
        "failed to update post in db",
      );
      logError({
        context: "**** updatePostVisibilityBasedOnAppropriateNSFWSettings",
        error,
      });
      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
      throw error;
    }
  });

  if (isProposed) {
    await onProposedPostReadyForReview({
      post: post,
      botProfile: profile,
    });
  }

  if (updateDict.visibility === "public") {
    await afterPostPublished({
      post_id,
      bot_profile_id: profile.id,
      media_url,
    });
  }

  return { updateDict };
}

async function afterPostPublished({ post_id, bot_profile_id, media_url }) {
  addSpanEvent("afterPostPublished");

  if (!bot_profile_id) {
    // Means that profile is deleted
    logWarn({
      context: "**** afterPostPublished",
      message: `profile is missing for post_id: ${post_id}`,
    });
    return;
  }

  addSpanEvent("afterPostPublished:sendEventFirstAIPost");
  // XXX: looks intentionally not awaited.
  // XXX: if this fails, we don't throw and never retry
  sendEventFirstAIPost({ botProfileId: bot_profile_id }).catch((error) => {
    logError({
      context: "**** afterPostPublished - sendEventFirstAIPost failed",
      error,
      bot_profile_id: bot_profile_id,
      post_id: post_id,
    });
  });

  addSpanEvent("afterPostPublished:notifyTaggedUsers");
  maybeNotifyTaggedUsers(post_id, bot_profile_id, media_url);
}

const resizeImageList = decorateWithActiveSpanAsync(
  "resizeImageList",
  _resizeImageList,
);
async function _resizeImageList(url) {
  let placeholderHash;

  let response;
  try {
    response = await tracer.withActiveSpan(
      "get image data from url",
      async () => {
        return await axios.get(url, { responseType: "arraybuffer" });
      },
    );
  } catch (err) {
    logError({
      context: "**** resizeImageList get image data from url error",
      error: err,
      url,
    });
    throw err;
  }
  const imageBuffer = Buffer.from(response.data, "binary");
  placeholderHash = await calculatePlaceholderHash(imageBuffer);
  return { placeholderHash };
}

async function calculatePlaceholderHash(resizedBuffer) {
  let placeholderHash;
  try {
    await tracer.withActiveSpan(`calculate preview hash`, async (span) => {
      const { data, info } = await sharp(resizedBuffer)
        .ensureAlpha()
        .raw()
        .toBuffer({
          resolveWithObject: true,
        });
      const { width, height } = info;

      const USE_THUMBHASH_INSTEAD_OF_BLURHASH = false;
      if (USE_THUMBHASH_INSTEAD_OF_BLURHASH) {
        span.setAttribute("algorithm", "thumbhash");
        const thumbhash = await import("thumbhash").then(
          (module) => module.default,
        );
        const binary = thumbhash.rgbaToThumbHash(width, height, data);
        const thumbhashValue = btoa(String.fromCharCode(...binary));
        placeholderHash = `1:${thumbhashValue}`;
      } else {
        span.setAttribute("algorithm", "blurhash");
        const blurhashValue = blurhash.encode(data, width, height, 3, 4);
        placeholderHash = `0:${blurhashValue}`;
      }
    });
  } catch (error) {
    logError({
      context: "**** placeholderHash calculation failed",
      error: error,
    });
  }
  return placeholderHash;
}

const updateMessageMetadataForNSFW = decorateWithActiveSpanAsync(
  "updateMessageMetadataForNSFW",
  _updateMessageMetadataForNSFW,
);
async function _updateMessageMetadataForNSFW(message_id) {
  let metadata;
  try {
    const data = await retrySupabaseOperation(
      () => supabase.from("messages").select("metadata").eq("id", message_id),
      "messageImageMetadata",
    );
    if (data.length > 0) {
      metadata = {
        ...data[0]?.metadata,
        nsfw: "true",
        status: "completed",
      };
    }
  } catch (error) {
    logError({
      context:
        "*** updateMessageMetadataForNSFW failed to get image message metadata",
      error,
    });
    throw error;
  }

  const { error: messageError } = await supabase
    .from("messages")
    .update({
      metadata: metadata,
    })
    .eq("id", message_id);

  if (messageError) {
    const error = wrappedSupabaseError(
      messageError,
      "failed to update message metadata",
    );

    if (messageError.code === "PGRST116") {
      logWarn({
        context: `**** updateMessageMetadataForNSFW failed to update image message with id ${message_id}, because ${message_id} does not exist`,
        message: messageError.message,
      });

      return;
    }

    logError({
      context: `**** updateMessageMetadataForNSFW failed to update image message with id ${message_id}`,
      error,
    });
    throw error;
  }
}

const updateMessageWithImage = decorateWithActiveSpanAsync(
  "updateMessageWithImage",
  _updateMessageWithImage,
);
async function _updateMessageWithImage({
  imageMessage_id,
  prompt,
  imageUrls,
  systemMessage_id,
  username,
  is_premium = false,
  executionId,
}) {
  if (!imageMessage_id) {
    const error = new Error(`Missing imageMesage_id: ${imageMessage_id}`);
    throw error;
  }

  let metadata;
  try {
    const data = await retrySupabaseOperation(
      () =>
        supabase.from("messages").select("metadata").eq("id", imageMessage_id),
      "imageMessageMetadata",
    );
    if (data.length > 0) {
      metadata = {
        ...data[0]?.metadata,
        ai_caption: prompt,
        media_url: imageUrls[0] ?? "",
        status: "completed",
        is_premium,
      };
    }
  } catch (error) {
    logError({
      executionId: executionId,
      context:
        "*** updateMessageWithImage failed to get image message metadata",
      error,
    });
    throw error;
  }

  const { error: messageError } = await supabase
    .from("messages")
    .update({
      media_url: metadata?.media_url,
      metadata: metadata,
    })
    .eq("id", imageMessage_id)
    .select("*")
    .single();

  if (messageError) {
    const error = wrappedSupabaseError(
      messageError,
      "failed to update message media_url and metadata",
    );

    if (messageError.code === "PGRST116") {
      logWarn({
        context: `**** updateMessageWithImage failed to update image message with id ${imageMessage_id}, because ${imageMessage_id} does not exist`,
        message: messageError.message,
      });

      return;
    }

    logError({
      context: `**** updateMessageWithImage failed to update image message with id ${imageMessage_id}`,
      error,
    });
    throw error;
  }

  const caption = `Sent an image by ${username}. \nDescription: "${prompt}"`;

  if (systemMessage_id) {
    const { error: systemMessageError } = await supabase
      .from("messages")
      .update({
        body: caption,
      })
      .eq("id", systemMessage_id);

    if (systemMessageError) {
      if (systemMessageError.code === "PGRST116") {
        logWarn({
          context: `**** updateMessageWithImage failed to update system message with id ${systemMessage_id}, because ${systemMessage_id} does not exist`,
          message: systemMessageError.message,
        });

        return;
      }

      const error = wrappedSupabaseError(
        systemMessageError,
        "failed to update system message body",
      );

      logError({
        context: `**** updateMessageWithImage failed to update system message with id ${systemMessage_id}`,
        error,
      });
      throw error;
    }
  } else {
    // XXX: when is systemMessage_id missing? and is that ok?
  }
}

const updateMessageWithPhoto = decorateWithActiveSpanAsync(
  "updateMessageWithPhoto",
  _updateMessageWithPhoto,
);
async function _updateMessageWithPhoto({
  generate_urls,
  imageUrls,
  generatePhotoMessage_id,
}) {
  logInfo({
    context: "**** updateMessageWithPhoto",
    message: `called with generate_urls: ${generate_urls} imageUrls: ${imageUrls}`,
  });
  const resolvedGenerateURLs = generate_urls
    ? generate_urls.concat(imageUrls[0])
    : [imageUrls[0]];

  let metadata;
  try {
    const data = await retrySupabaseOperation(
      () =>
        supabase
          .from("messages")
          .select("metadata")
          .eq("id", generatePhotoMessage_id),
      "imageMessageMetadata",
    );
    if (data.length > 0) {
      metadata = {
        ...data[0]?.metadata,
        generate_urls: resolvedGenerateURLs,
        status: "completed",
      };
    }
  } catch (error) {
    logError({
      context:
        "*** updateMessageWithPhoto failed to get image message metadata",
      error,
    });
    throw error;
  }

  const { error: updateGenerateURLsError } = await supabase
    .from("messages")
    .update({
      generate_urls: resolvedGenerateURLs,
      metadata,
    })
    .eq("id", generatePhotoMessage_id);

  if (updateGenerateURLsError) {
    const error = wrappedSupabaseError(
      updateGenerateURLsError,
      "failed to update message generate_urls",
    );

    if (updateGenerateURLsError.code === "PGRST116") {
      logWarn({
        context: `**** updateMessageWithPhoto failed to update generate urls with id ${generatePhotoMessage_id}, because ${generatePhotoMessage_id} does not exist`,
        message: updateGenerateURLsError.message,
      });

      return;
    }

    logError({
      context: `**** updateMessageWithPhoto failed`,
      error,
    });
    throw error;
  }
}

const updateForOnboarding = decorateWithActiveSpanAsync(
  "updateForOnboarding",
  _updateForOnboarding,
);
async function _updateForOnboarding({
  imageUrls,
  blurhash,
  post_id,
  profile_id,
  executionId,
  conversation_id,
}) {
  console.log("--------------- STEP 4 Starting ---------------");

  const post = await tracer.withActiveSpan(
    "update post image",
    async (span) => {
      const { data, error: postError } = await supabase
        .from("posts")
        .update({
          media_url: imageUrls[0],
          image_widths: null,
        })
        .eq("id", post_id)
        .select(
          "*, profiles(id, display_name, username, avatar_url, nsfw, age, imitation, bots!bots_profile_id_fkey(bio, characteristics, background, personality))",
        )
        .single();

      if (postError || !data) {
        if (postError.code === "PGRST116") {
          logWarn({
            context: `**** updateForOnboarding failed to update post with id ${post_id}, because ${post_id} does not exist`,
            message: postError.message,
          });

          return;
        }
        const error = postError
          ? wrappedSupabaseError(postError, "failed to update post image")
          : new Error("No post found");
        logError({
          executionId: executionId,
          context: "**** updateForOnboarding update post error",
          error,
        });
        span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
        throw error;
      }

      return data;
    },
  );

  const botProfile = post?.profiles;

  blurhash = blurhash ? blurhash[0] : undefined;
  const { updateDict } =
    await updatePostVisibilityBasedOnAppropriateNSFWSettings({
      post_id,
      media_url: imageUrls[0],
      blurhash,
      post,
      profile: botProfile,
    });

  if (updateDict.visibility !== "hidden") {
    console.log("--------------- STEP 5 Starting ---------------");

    const media_url = `https://butterflies.ai/users/${botProfile?.username}/p/${post?.slug}`;

    const body = `Bot is sharing its first post to user.\n
      The post has this caption: ${post?.description}\n\n
      Location of post: ${post?.location ?? "Unknown"}\n\n
      Description of the image: ${post?.ai_caption ?? "Unknown"}\n\n
      Media url of post: ${media_url}`;

    let prompt = `You are ${
      botProfile?.display_name ?? botProfile?.username
    }. This is some information about you: ${generateBio(botProfile.bots)}

    You just posted a post. Rewrite the message below in your unique style that captures your tone as ${
      botProfile?.display_name ?? botProfile?.username
    }. This is sample message: Hey tap in to check out my first post.
  
    ##
    Follow these rules:
  
    Just keep it one sentence
  
    ##
    Do not respond with more than one paragraph.`;

    let response;

    try {
      const chatCompletion = await callAndLogOpenAI(
        "OAI:Ping",
        {
          messages: [
            {
              role: "system",
              content: prompt,
            },
          ],
          model: "gpt-4o-mini",
        },
        {
          timeout: 8 * 1000,
        },
      );

      response = chatCompletion.choices[0].message.content;
    } catch (error) {
      logError({
        executionId: executionId,
        context: "**** Onboarding DM Error",
        error,
      });
      // We intentionally don't throw here and fall back to a default message:
      // "Sent a post by ..."
    }

    if (typeof response === "string" && response) {
      response = response.replace(/^"|"$/g, "");
    }

    const metadata = {
      title: `${botProfile?.display_name || ""} @${botProfile?.username}`,
      description: post?.description,
      url: media_url,
      image: post?.media_url,
      location: post?.location,
      ai_caption: post?.ai_caption,
      has_comment: response ? true : false,
      created_at: post?.created_at,
      creator: {
        avatar: botProfile?.avatar_url,
        username: botProfile?.username,
        name: botProfile?.display_name,
      },
      aspect_ratio: 0.75,
    };

    const { error: insertSystemMessageError } = await supabase
      .from("messages")
      .insert({
        conversation_id: conversation_id,
        sender_id: profile_id,
        body: body,
        branch_index: 0,
        is_system_message: true,
      });

    if (insertSystemMessageError) {
      const error = wrappedSupabaseError(
        insertSystemMessageError,
        "failed to insert system message",
      );
      logError({
        executionId: executionId,
        context: "**** updateForOnboarding failed to insert system message",
        error,
      });
      // XXX: not throwing here because it's not really user-visible
    }

    const { error: insertBotMessageError } = await supabase
      .from("messages")
      .insert({
        sender_id: profile_id,
        conversation_id: conversation_id,
        is_bot: true,
        body:
          response ??
          `Sent a post by ${botProfile?.display_name ?? botProfile?.username}`,
        branch_index: 0,
        type: "post",
        media_url: media_url,
        metadata: metadata,
        content_id: post_id,
      });

    if (insertBotMessageError) {
      const error = wrappedSupabaseError(
        insertBotMessageError,
        "failed to insert bot message",
      );
      logError({
        executionId: executionId,
        context: "**** updateForOnboarding failed to insert message",
        error,
      });
      throw error;
    }

    console.log("--------------- STEP FINISHED ---------------");
  }
}

async function getPostData(post_id) {
  if (!post_id) {
    return { user_prompted: false, is_mci: false };
  }
  const { data, error } = await supabase
    .from("posts")
    .select("user_prompted, tagged_profile_ids")
    .eq("id", post_id)
    .neq("visibility", "archived")
    .single();
  if (error) {
    if (error.code != "PGRST116") {
      logError({
        context: `updateImageInfo postId: ${post_id}`,
        error: wrappedSupabaseError(error, "failed to read post by post_id"),
      });
    }
    return { user_prompted: false, is_mci: false };
  }

  return {
    user_prompted: data.user_prompted,
    is_mci: data.tagged_profile_ids !== null,
  };
}

async function getProfileAgeImitation(profile_id) {
  if (!profile_id) {
    return {};
  }
  const { data, error } = await supabase
    .from("profiles")
    .select("age, imitation, nsfw")
    .neq("visibility", "archived")
    .eq("id", profile_id)
    .maybeSingle();
  if (error) {
    logError({
      context: `updateImageInfo profileId: ${profile_id}`,
      error: wrappedSupabaseError(
        error,
        "failed to read profile by profile_id",
      ),
    });
    return {};
  }

  return data ?? {};
}

const updateImageInfo = decorateWithActiveSpanAsync(
  "updateImageInfo",
  _updateImageInfo,
);
async function _updateImageInfo({
  imageUrls,
  blurhash,
  task_id,
  generationType,
  post_id,
  profile_id,
  creator_id,
  imageMessage_id,
  prompt,
  systemMessage_id,
  username,
  generatePhotoMessage_id,
  generate_urls,
  conversation_id,
  executionId,
  promptType,
  dream_id,
  dream_batch_id,
  device_type,
  leaderboard_submission = false,
  is_premium = false,
}) {
  addSpanAttribute("task_id", task_id);
  addSpanAttribute("generationType", generationType);
  addSpanAttribute("post_id", post_id);
  addSpanAttribute("profile_id", profile_id);
  addSpanAttribute("username", username);
  addSpanAttribute("dream_id", dream_id);

  try {
    // Filter out any undefined values in case of errors
    imageUrls = imageUrls.filter((url) => url !== undefined);

    if (!imageUrls || !imageUrls.length) {
      addSpanEvent("no_images");
      throw new Error("updateImageInfo called with no images");
    }

    if (!task_id) {
      addSpanEvent("no_task_id");
      throw new Error("no task_id");
    }

    //////////////////////////////////////////////////////////
    //
    // TODO: Split each of these updateXXX functions into two parts:
    //       * CRITICAL part which just updates the db with the URL to the new media (but doesn't _publish_ it until post-processing happens)
    //           * if this part fails, we must throw an error and consider onTaskDone handling to have failed
    //       * POSTPROCESSING part which does the actual post-processing (e.g. resizing, NSFW detection, etc.) and makes the db change to _publish_ the new media
    //           * before we kick off this part we can send a successful response back to the image service broker
    // XXX:      * if we do this, there is currently no system that will restart this post-processing, as far as I can tell
    //
    //////////////////////////////////////////////////////////

    ///////////////////////////////////////////////////////////////////////
    //
    // TODO: Add failure/success metrics for each of those generation types
    //       (see counter metrics inside comfy.js)
    //
    //       OpenTelemetry metrics API docs: https://opentelemetry.io/docs/languages/js/instrumentation/#metrics
    //       * use counters to count the number of successes and failures (or any non-negative, increasing value)
    //       * use histograms to track durations, if needed
    //
    ///////////////////////////////////////////////////////////////////////

    await tracer.withActiveSpan(
      "update task completed status",
      async (span) => {
        let flagged_nsfw = [];
        let flagged_racy = [];

        let is_clone = promptType === "reactor"; // VU: WARNING, might not be the best way to check for clones, may want to pass in "clone" generationType

        if (imageUrls.length) {
          let [
            { user_prompted, is_mci },
            { age: profileAge, imitation: profileImitation, nsfw: profileNsfw },
          ] = await Promise.all([
            getPostData(post_id),
            getProfileAgeImitation(profile_id),
          ]);
          const chatModeration =
            !is_premium &&
            device_type === "android" &&
            generationType === "message";
          const mustModerate =
            is_clone ||
            is_mci ||
            profileAge === "child" ||
            generationType === "avatar" ||
            profileImitation === "celebrity" ||
            chatModeration;

          let services = ["HIVE", "OpenAI", "GoogleVision"];
          if (chatModeration) {
            // OpenAI is free!
            services = ["OpenAI", "HIVE", "GoogleVision"];
          }
          const entity_id =
            generationType === "avatar"
              ? creator_id || profile_id || null
              : post_id || profile_id || creator_id || null;

          if (generationType === "avatar" && entity_id) {
            const avatarKey = `${entity_id}_avatargen`;
            const usageCount = await redisClient.incr(avatarKey);
            if (usageCount === 1) {
              // if works, we can move this lua to do in one shot
              await redisClient.expire(avatarKey, 24 * 60 * 60); // 24 hours
            }
            if (usageCount > 4) {
              services = ["OpenAI", "GoogleVision"];
            }
          }
          if (generationType === "post" && profileNsfw === "nsfw") {
            services = ["OpenAI", "HIVE", "GoogleVision"];
          }
          if (mustModerate) {
            const promises = imageUrls.map(async (url) => {
              const moderation = await tracer.withActiveSpan(
                "autoModerate",
                async () => {
                  return await autoModerate({
                    imageUrl: url,
                    id: entity_id,
                    context: generationType,
                    services,
                    class: "updateImageInfo",
                    extra: {
                      must_moderate: mustModerate,
                      user_prompted,
                      prompt_type: promptType,
                      generation_type: generationType,
                      is_clone,
                      is_mci,
                      task_id,
                      device_type,
                      profile_age: profileAge,
                      profile_immitation: profileImitation,
                      profile_nsfw: profileNsfw,
                    },
                  });
                },
              );

              if (moderation.nsfl == true || moderation.nsfw === "nsfw") {
                if (
                  chatModeration &&
                  (imageMessage_id || generatePhotoMessage_id)
                ) {
                  const message_id = imageMessage_id || generatePhotoMessage_id;
                  await updateMessageMetadataForNSFW(message_id);
                }

                flagged_nsfw.push(url);
                return null;
              }

              if (
                !user_prompted &&
                is_clone &&
                moderation.racy === "VERY_LIKELY"
              ) {
                flagged_racy.push(url);
                return null;
              }

              return url;
            });
            const results = await Promise.all(promises);
            imageUrls = results.filter((url) => url);
          } else {
            const justLog = imageUrls.map(async (url) => {
              await skipModerate({
                imageUrl: url,
                id: entity_id,
                context: generationType,
                class: "updateImageInfo",
                extra: {
                  must_moderate: mustModerate,
                  user_prompted,
                  prompt_type: promptType,
                  generation_type: generationType,
                  is_clone,
                  is_mci,
                  task_id,
                  device_type,
                  profile_age: profileAge,
                  profile_immitation: profileImitation,
                  profile_nsfw: profileNsfw,
                  imageMessage_id,
                  generatePhotoMessage_id,
                  systemMessage_id,
                },
              });
            });
            await Promise.all(justLog);
          }
        }

        const safeImageUrls = Array.isArray(imageUrls) ? imageUrls : [];
        const { error: taskError } = await supabase
          .from("tasks")
          .update({
            result: { data: { img_uris: safeImageUrls } },
            status: "completed",
          })
          .eq("id", task_id);

        if (taskError) {
          const wrappedError = wrappedSupabaseError(
            taskError,
            `failed to update task completed status: ${imageUrls}`,
          );
          addSpanEvent("task_error");
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: wrappedError?.message,
          });
          logError({
            context: `updateImageInfo taskId: ${task_id}`,
            executionId: executionId,
            error: wrappedError,
          });
          throw wrappedError;
        }

        if (generationType === "avatar") {
          // also update avatar_generation object
          await supabase
            .schema("internal")
            .from("avatar_generations")
            .update({ images: imageUrls, is_clone, flagged_nsfw, flagged_racy })
            .eq("task_id", task_id)
            .single();
        }
      },
    );

    if (imageUrls.length === 0) {
      addSpanEvent("no_images");
      if (generationType === "post" && post_id) {
        let updatePostWontBePublished = {
          visibility: "hidden",
          nsfl: true,
        };
        await supabase
          .from("posts")
          .update(updatePostWontBePublished)
          .eq("id", post_id);
      }
      logWarn({
        context:
          "updateImageInfo – Has filtered out all appropriate images and there are none left",
        message: `task_id: ${task_id}`,
      });
      return;
    }

    // update post if exists
    if (generationType === "post" && post_id) {
      try {
        await updatePost({
          imageUrls,
          blurhash,
          post_id,
          profile_id,
          leaderboard_submission,
        });
      } catch (error) {
        logError({
          context: `"updateImageInfo – Failed to updatePost: ${post_id}, profile_id:${profile_id}`,
          error,
          imageUrls,
        });
        throw error;
      }
    } else if (generationType === "avatar" && profile_id) {
      // XXX: currently, profile_id is always missing for generationType === "avatar"
      //      because 'modern' clients use /generateAvatarV2 and don't pass bot id
      //      or profile id

      try {
        await updateBotAvatarWithURL({ url: imageUrls[0], profile_id });
      } catch (error) {
        logError({
          context: `"updateImageInfo – Failed to updateBotAvatarWithURL: ${profile_id}`,
          error,
          imageUrls,
        });
        throw error;
      }
    } else if (generationType === "message" && imageMessage_id) {
      try {
        await updateMessageWithImage({
          imageMessage_id,
          prompt,
          imageUrls,
          systemMessage_id,
          username,
          is_premium,
          executionId,
        });
      } catch (error) {
        logError({
          context: `"updateImageInfo – Failed to updateMessageWithImage: imageMessage_id:${imageMessage_id}, systemMessage_id:${systemMessage_id}`,
          error,
        });
        throw error;
      }
    } else if (generationType === "message" && generatePhotoMessage_id) {
      try {
        await updateMessageWithPhoto({
          generate_urls,
          imageUrls,
          generatePhotoMessage_id,
        });
      } catch (error) {
        logError({
          context: `"updateImageInfo – Failed to updateMessageWithPhoto: generatePhotoMessage_id:${generatePhotoMessage_id}`,
          error,
        });
        throw error;
      }
    } else if (generationType === "onboarding") {
      try {
        await updateForOnboarding({
          imageUrls,
          blurhash,
          post_id,
          profile_id,
          executionId,
          conversation_id,
        });
      } catch (error) {
        logError({
          context: `"updateImageInfo – Failed to updateForOnboarding: post_id:${post_id}, profile_id:${post_id}, conversation_id:${conversation_id}`,
          error,
        });
        throw error;
      }
    } else if (generationType === "postPrompt") {
      // Do Nothing
    } else if (generationType === "avatar") {
      // avatar generation for onboarding w/o profile
    } else if (generationType === "dreams") {
      await updateDreamWithImage({
        imageUrls,
        blurhash,
        profile_id,
        dream_id,
        dream_batch_id,
      });
    } else if (generationType === "post_v2") {
      // Do Nothing
    } else {
      logError({
        context: `updateImageInfo taskId: ${task_id}`,
        error: new Error(`Unhandled generationType: ${generationType}`),
      });
    }
  } catch (error) {
    logError({
      executionId: executionId,
      context: "**** updateImageInfo Error",
      error,
    });

    // We weren't able to update our db with the new media, rethrow the error to fail onTaskDone
    throw error;
  }
}

const imageGenerationFailed = decorateWithActiveSpanAsync(
  "imageGenerationFailed",
  _imageGenerationFailed,
);
// XXX: if anything inside imageGenerationFailed fails, the task will be left in an inconsistent state
async function _imageGenerationFailed({
  error,
  executionId,
  task_id,
  generationType,
  user_id,
  imageMessage_id,
  systemMessage_id,
  user_usage_id = null,
  post_id,
  profile_id,
}) {
  logError({
    context: `imageGenerationFailed: ${task_id}`,
    executionId: executionId,
    error,
  });

  await tracer.withActiveSpan("update task failed status", async () => {
    const { error: updateTaskFailedStatusError } = await supabase
      .from("tasks")
      .update({
        status: "failed",
      })
      .eq("id", task_id);

    if (updateTaskFailedStatusError) {
      const error = wrappedSupabaseError(
        updateTaskFailedStatusError,
        "failed to update task status",
      );
      logError({
        context: `imageGenerationFailed: ${task_id} updateTaskFailedStatusError`,
        error: error,
      });
      // XXX: not rethrowing here
    }
  });

  if (user_usage_id) {
    try {
      const { error: deleteUserUsageError } = await supabase
        .from("user_usage")
        .delete()
        .eq("id", user_usage_id);
      if (deleteUserUsageError) {
        logError({
          context: `imageGenerationFailed: ${task_id} failed to delete user_usage`,
          error: deleteUserUsageError,
          user_usage_id,
        });
      }
    } catch (err) {
      logError({
        context: `imageGenerationFailed: ${task_id} exception deleting user_usage`,
        error: err,
        user_usage_id,
      });
    }
  }

  if (generationType === "post") {
    // posthog event capture when poke generation failure
    if (user_id) {
      posthogCapture(user_id, "poke_generation", {
        status: "failure",
        media_url: "",
      });
    }

    // Update proposed_post_state to "failed" if post is a proposed post
    if (typeof isProposedPost === "function" && post_id) {
      // Fetch the post by task_id (assuming you have post_id, otherwise fetch it)
      const { data: post, error: postError } = await supabase
        .from("posts")
        .select("*")
        .eq("id", post_id)
        .single();

      if (!postError && post && isProposedPost(post)) {
        await supabase
          .from("posts")
          .update({ proposed_post_state: "failed" })
          .eq("id", post_id);

        if (profile_id) {
          await bumpProposedPostGenerationDate({ botProfileId: profile_id });
        }
      }
    }
  }

  if (generationType === "message" && imageMessage_id) {
    let metadata;
    try {
      const data = await retrySupabaseOperation(
        () =>
          supabase
            .from("messages")
            .select("metadata")
            .eq("id", imageMessage_id),
        "imageMessageMetadata",
      );
      if (data.length > 0) {
        metadata = {
          ...data[0]?.metadata,
          status: "failed",
        };
      }
    } catch (error) {
      logError({
        context: `imageGenerationFailed: ${task_id} imageMessageMetadata error`,
        executionId: executionId,
        error,
      });
      throw error;
    }

    await tracer.withActiveSpan(
      "update image message failed status",
      async () => {
        const { error: updateImageMessageError } = await supabase
          .from("messages")
          .update({
            media_url: metadata?.media_url,
            metadata: metadata,
          })
          .eq("id", imageMessage_id);
        if (updateImageMessageError) {
          throw wrappedSupabaseError(
            updateImageMessageError,
            "failed to update image message media_url and metadata",
          );
        }
      },
    );

    if (systemMessage_id) {
      await tracer.withActiveSpan("delete system message", async () => {
        const { error: deleteSystemMessageError } = await supabase
          .from("messages")
          .delete()
          .eq("id", systemMessage_id);
        if (deleteSystemMessageError) {
          throw wrappedSupabaseError(
            deleteSystemMessageError,
            "failed to delete system message",
          );
        }
      });
    }
  }
}

async function maybeNotifyTaggedUsers(post_id, profile_id, image_url) {
  addSpanEvent("maybeNotifyTaggedUsers:start");

  logInfo({
    context: "maybeNotifyTaggedUsers",
    message: `called with post_id: ${post_id}, profile_id: ${profile_id}, image_url: ${image_url}`,
  });

  const [
    { data: post, error: postError },
    { data: origBot, error: origBotError },
    { data: origProfile, error: origProfileError },
  ] = await Promise.all([
    supabase
      .from("posts")
      .select("tagged_profile_ids, slug")
      .eq("id", post_id)
      .neq("visibility", "archived")
      .single(),
    supabase
      .from("bots")
      .select("creator_id")
      .eq("profile_id", profile_id)
      .single(),
    supabase
      .from("profiles")
      .select("username, display_name")
      .eq("id", profile_id)
      .neq("visibility", "archived")
      .single(),
  ]);

  if (postError) {
    addSpanEvent("maybeNotifyTaggedUsers:postError");
    throw wrappedSupabaseError(
      postError,
      "failed to get post tagged_profile_ids",
    );
  }

  if (
    !post?.tagged_profile_ids?.length ||
    post?.tagged_profile_ids?.length === 0
  ) {
    addSpanEvent("maybeNotifyTaggedUsers:no_tagged_profiles");
    // No one is tagged, no need to notify anyone.
    return;
  }

  logInfo({
    context: "maybeNotifyTaggedUsers",
    message: "found tagged profiles",
  });

  if (origProfileError || origBotError) {
    addSpanEvent("maybeNotifyTaggedUsers:origProfileError");
    throw wrappedSupabaseError(
      origProfileError ?? origBotError,
      "failed to get profile or bot",
    );
  }

  for (const tagged_profile_id of post.tagged_profile_ids) {
    const [
      { data: bot, error: botError },
      { data: profile, error: profileError },
    ] = await Promise.all([
      supabase
        .from("bots")
        .select("creator_id")
        .eq("profile_id", tagged_profile_id)
        .single(),
      supabase
        .from("profiles")
        .select("username, display_name")
        .eq("id", tagged_profile_id)
        .neq("visibility", "archived")
        .single(),
    ]);

    if (botError) {
      addSpanEvent("maybeNotifyTaggedUsers:botError");
      throw wrappedSupabaseError(botError, "failed to get bot creator_id");
    }

    if (!bot || !origBot) {
      addSpanEvent("maybeNotifyTaggedUsers:no_bot");
      // Maybe a user was tagged, not a bot? For now, we only support tagging bots.
      continue;
    }

    if (profileError) {
      addSpanEvent("maybeNotifyTaggedUsers:profileError");
      throw wrappedSupabaseError(
        profileError,
        "failed to get profile username",
      );
    }

    if (bot.creator_id == origBot.creator_id) {
      addSpanEvent("maybeNotifyTaggedUsers:same_bot");
      // We already have a notification scheduled for the same bot owner!
      continue;
    }

    const { error: insertNotificationError } = await supabase
      .from("notifications")
      .insert({
        profile_id: bot.creator_id,
        source_type: "bot_tagged",
        source_id: post_id,
        title: "tagged your butterfly",
        text: `${profile.display_name} was added to ${origProfile.display_name}’s post. Tap to see the new post!`,
        path: `/users/${origProfile.username}/p/${post.slug}`,
        sender_profile_id: profile_id,
        image_url: image_url,
      });
    if (insertNotificationError) {
      addSpanEvent("maybeNotifyTaggedUsers:insertNotificationError");
      const error = wrappedSupabaseError(insertNotificationError);
      logError({
        context: "maybeNotifyTaggedUsers: failed to insert notification record",
        error,
      });
    }
    addSpanEvent("maybeNotifyTaggedUsers:notification_inserted");
  }
}

module.exports = {
  updateBotAvatarWithURL,
  uploadToStorage,
  doesImageContainFace,
  convertImageToBase64,
  updatePost,
  updateMessageWithImage,
  updateMessageWithPhoto,
  updateForOnboarding,
  updateImageInfo,
  imageGenerationFailed,
  resizeImageList,
  updatePostVisibilityBasedOnAppropriateNSFWSettings,
  getBoundingPolyForFace,
  afterPostPublished,
  automaticallyPostToLeaderboardIfQualifies,
};
