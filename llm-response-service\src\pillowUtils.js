const { default: axios } = require("axios");
const { logInfo, logError } = require("./logUtils");
const { generateWorkflow, enqueueTask } = require("./image_service_broker");
const { tracer } = require("./instrumentation/tracer");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { callAndLogOpenAI } = require("./llm");
const { CHRYSALIS_BASE_URL } = require("./pillow/config");
const { PillowGenerationType } = require("./pillowImageUtils");

const schedulePillowImageGenerationTask = async ({
  snapRequest, // TODO: rename to "imageRequest" or something like that
  generationType,
  pillowClientId,
  pillowUserProfileId, // TODO: rename this here and downstream to "receiverProfileId"
  inResponseTo,
  senderProfileId,
  ...otherParams
}) => {
  if (!generationType) {
    throw new Error("Must provide a pillow image generation type");
  }

  if (!snapRequest) {
    throw new Error("Must provide a snapRequest");
  }

  const {
    text,
    location,
    image_description,
    upper_body_clothing,
    emote,
    selfie,
    // lighting,
    current_time,
    workflow_run_id,
  } = snapRequest;

  // mostly copied config from generatePostImageWithPrompts, which is what Vu's persona-based
  // image generation is currently using
  logInfo({
    context: "schedulePillowImageGenerationTask",
    message: "scheduling pillow image generation task",
    snapRequest,
  });

  // sofie
  // const face_image_url =
  //   "https://img.butterflies.ai/w/118f471d-cdab-4b9e-b804-6ab2be2374e1_1.webp";

  // tiffany
  const face_image_url =
    "https://db.butterflies.ai/storage/v1/object/public/ai-ig/tiffany.jpg";

  // const grainy = "grainy";
  // const blurry = "blurry";
  // const motionBlur = "motion blur";
  // const headPartiallyOutOfFrame = "head partially out of frame";
  // const darkPhoto = "dark photo";

  // let prompt = `1girl, solo, realistic, selfie, 24 year old woman, ${location}, asian woman, wearing ${upper_body_clothing}`;

  // remove selfie term
  // selfie introduces phones, which we don't want, "close up", is better
  let parsed_image_description = image_description.replace("selfie", "");

  let posePrompt = `a woman, ${location}, ${parsed_image_description}, close up, selfie`;
  let prompt = `a woman, long black hair, ${location}, ${parsed_image_description}, wearing ${upper_body_clothing}, selfie`;

  if (emote && emote.length > 0) {
    prompt += `, ${emote}`;
  }

  if (!JSON.parse(selfie)) {
    const prefix = parsed_image_description.startsWith("photo of")
      ? ""
      : "photo of a ";
    prompt = `${prefix}${parsed_image_description}, ${location}`;
  }

  // // 33% of the time, add dark photo
  // if (Math.random() < 0.33) {
  //   prompt += `, ${darkPhoto}`;
  // }

  // 20% of the time, add grainy
  if (Math.random() < 0.2) {
    // prompt += `, ${grainy}`;
  }

  // 20% of the time, add blurry
  if (Math.random() < 0.2) {
    // prompt += `, ${blurry}`;
  }

  // 20% of the time, add motion blur
  if (Math.random() < 0.2) {
    // prompt += `, ${motionBlur}`;
  }

  if (snapRequest.lighting === "bright_photo") {
    // prompt += ", bright photo";
  } else if (snapRequest.lighting === "dark_photo") {
    prompt += ", dark photo, low light";
  }

  prompt += `, ${current_time}`;

  const artStyle = "realistic";

  let task;

  const using_front_facing_camera = JSON.parse(selfie ?? "true");

  let workflowPayload = {
    posePrompt,
    prompt,
    artStyle,
    face_image_url,
    avatar_url: null,
    promptType: "selfie",
    width: 576,
    height: 1024,
    batch_size: 1,
    seed: null,
    nsfw: null,
    contains_character: true,
    generationType,
    pillowClientId,
    pillowUserProfileId,
    pillowCaption: text,
    pillowWorkflowRunId: workflow_run_id,
    pillowSenderProfileId: senderProfileId,
    pillowInResponseTo: inResponseTo,
    pillowOtherParams: otherParams,
    using_front_facing_camera,
    imageDescription: prompt,
  };

  if (!using_front_facing_camera) {
    workflowPayload.contains_character = false;
  }

  logInfo({
    context: "schedulePillowImageGenerationTask",
    message: "about to schedule pillow image generation task with payload",
    workflowPayload,
  });

  await tracer.withActiveSpan("insert tasks record", async (span) => {
    const response = await supabase
      .from("tasks")
      .insert({
        service: "comfy",
        status: "queued",
        payload: workflowPayload,
      })
      .select("id, created_at, status")
      .single();

    task = response.data;
    const taskError = response.error;

    if (taskError) {
      const error = wrappedSupabaseError(taskError);
      throw error;
    }
  });

  workflowPayload.task_id = task.id;

  const workflow = await generateWorkflow(workflowPayload);
  await enqueueTask(JSON.stringify(workflow), 10, "pillow", task.id.toString());

  return task;
};

async function rerunSnapGeneration({ workflowRunId }) {
  const payload = {
    run_id: workflowRunId,
  };
  const endpoint = `${CHRYSALIS_BASE_URL}/converse_replay`;
  let result;
  const response = await axios.post(endpoint, payload);
  result = response.data;
  if (typeof result === "string" || result instanceof String) {
    try {
      result = JSON.parse(result);
    } catch (error) {
      logError({
        context: "rerunSnapGeneration",
        message: "error parsing converse replay result",
        error,
      });
      throw error;
    }
  }
  logInfo({
    context: "rerunSnapGeneration",
    message: "snap generation replayed",
    result,
  });
  return result;
}

async function generatePillowSnapResponse({
  pillowClientId,
  userProfileId,
  targetProfileId, // TODO: rename this to botProfileId, since "target" is ambiguous
  inResponseTo,
  backendTweaks,
  testing = true,
  ...otherParams
}) {
  const conversePayload = {
    ...otherParams,
    message: inResponseTo?.userCaption,
    image_url: inResponseTo?.imageUrl,
    image_description: inResponseTo?.imageDescription,
    profile_id: userProfileId?.toString() ?? "",
  };
  logInfo({
    context: "generatePillowSnapResponse",
    message: "about to call /converse with conversePayload",
    conversePayload,
  });

  let snapRequest;

  // "http://localhost:8080/converse",
  const endpointURL = `${CHRYSALIS_BASE_URL}/converse_v2`;

  const result = await axios.post(
    endpointURL,

    conversePayload,
  );

  snapRequest = result.data;

  if (typeof snapRequest === "string" || snapRequest instanceof String) {
    try {
      snapRequest = JSON.parse(snapRequest);
    } catch (e) {
      logError({
        context: "generatePillowSnapResponse",
        message: "error parsing converse endpoint request result",
        error: e,
        result: snapRequest,
      });
      throw e;
    }
  }

  logInfo({
    context: "generatePillowSnapResponse",
    message: "converse result",
    snapRequest,
    userProfileId,
    pillowClientId,
  });

  const { image_description, text } = snapRequest;
  if (!image_description && !text) {
    const error = new Error(
      "no image description and no text in response from /converse_v2",
    );
    logError({
      context: "generatePillowSnapResponse",
      error,
      snapRequest,
      userProfileId,
      pillowClientId,
    });
    throw error;
  }

  logInfo({
    context: "generatePillowSnapResponse",
    message: "about to call schedulePillowImageGenerationTask",
    snapRequest,
    userProfileId,
    pillowClientId,
  });

  if (!testing) {
    const task = await schedulePillowImageGenerationTask({
      snapRequest,
      generationType: PillowGenerationType.SNAP,
      pillowClientId,
      pillowUserProfileId: userProfileId,
      inResponseTo,
      senderProfileId: targetProfileId,
      backendTweaks,
    });

    logInfo({
      context: "generatePillowSnapResponse",
      message: "scheduled pillow image generation task",
      task_id: task.id,
      snapRequest,
      userProfileId,
    });
  } else {
    return snapRequest;
  }
}

async function sendSnapToBotsUsingPillowConverseEndpoint({
  pillowClientId,
  userProfileId,
  userCaption,
  botTargetIds,
  imageDescription,
  imageUrl,
  testing = false,
  ...otherParams
}) {
  if (!imageDescription) {
    const payload = {
      model: "gpt-4o",
      max_tokens: 200,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "What’s in this image? Be specific, describe everything in terse language. Do not use new lines / line breaks or paragraphs. 3 sentences max.",
            },
            {
              type: "image_url",
              image_url: {
                url: imageUrl,
              },
            },
          ],
        },
      ],
    };

    const chatCompletion = await callAndLogOpenAI(
      "OAI:Pillow:DescribeImage",
      payload,
      {
        timeout: 8 * 1000,
      },
    );
    imageDescription = chatCompletion.choices[0].message.content;
  }

  for (const targetProfileId of botTargetIds) {
    console.log("prepare send to bot", targetProfileId);

    const { data: newSnap, error: insertError } = await supabase
      .from("snaps")
      .insert({
        image_url: imageUrl,
        caption: userCaption,
        blurhash: null,
        user_profile_id: targetProfileId,
        sender_profile_id: userProfileId,
        image_description: imageDescription,
      })
      .select("*")
      .single();

    if (insertError) {
      logError({
        context: "**** inserting a 'snaps' record for user -> bot snap",
        error: wrappedSupabaseError(insertError),
        imageUrl,
        userCaption,
        targetProfileId,
        userProfileId,
      });
      return null;
    }

    const responseData = await generatePillowSnapResponse({
      ...otherParams, // just forward all the crap the client sent us in the body
      pillowClientId,
      userProfileId,
      targetProfileId,
      testing,
      inResponseTo: {
        snapId: newSnap.id,
        imageUrl,
        userCaption,
        imageDescription,
      },
    });

    logInfo({
      context: "sendSnapImage response",
      responseData,
      pillowClientId,
      userProfileId,
      targetProfileId,
    });
    return responseData;
  }
}

module.exports = {
  generatePillowSnapResponse,
  schedulePillowImageGenerationTask,
  sendSnapToBotsUsingPillowConverseEndpoint,
  rerunSnapGeneration,
};
