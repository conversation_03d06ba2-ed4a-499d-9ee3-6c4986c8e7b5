### CURRENT CHAT FLOW

sendMessage() at src/chat/sendMessage.js

1. handles validations
2. reads data from database
3. inserts the message into database
4. updates memories / reengagement campaign / streaks
5. calls generateMessageResponse() at src/chat/sendMessage.js
   1. reads previous messages from database
   2. detects whether user is asking for an image
   3. calls selectMessageGenerator() at src/chat/sendMessage.js to select the generator
      1. decides whether request should be rejcted (e.g. clone / child / etc.)
      2. checks user's image quota
      3. selectes between realism vs roleplay
   4. calls the selected message generator
      1. text model is selected
         - realismGenerator at src/chat/message/realismGenerator.js
           1. textModel is set to RealismMessageModel at src/chat/model/RealismMessageModel.js
         - roleplayGenerator at src/chat/message/roleplayGenerator.js
           1. textModel is set to RoleplayMessageModel at src/chat/model/RoleplayMessageModel.js
      2. messageGenerator is called at src/chat/message/messageGenerator.js
      3. textModel (selected above) is ran to generate the text message
      4. if image is requested
         - if request is through "/imagine" ForcedImagePromptModel is run at src/chat/model/ForcedImagePromptModel.js
         - if request is detected MaybeImagePromptModel is run at src/chat/model/ForcedImagePromptModel.js
   5. writes the generated messages and images to database
6. handles likes
