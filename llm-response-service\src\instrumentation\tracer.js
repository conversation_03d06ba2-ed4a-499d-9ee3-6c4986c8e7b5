const { SugaredTracer } = require("@opentelemetry/api/experimental");
const {
  trace,
  context,
  TraceFlags,
  SpanStatusCode,
} = require("@opentelemetry/api");

const _tracer = trace.getTracer(
  process.env.K_SERVICE ?? "butterflies-api-unknown",
  "1.0.0",
);

const tracer = new SugaredTracer(_tracer);

const decorateWithActiveSpanAsync =
  (spanName, f) =>
  async (...args) => {
    return tracer.withActiveSpan(spanName, async (span) => {
      return await f(...args);
    });
  };

function collectActiveSpanMetadata() {
  try {
    let activeSpan = trace.getActiveSpan() ?? trace.getSpan(context.active());

    if (!activeSpan) {
      return;
    }

    const traceId = activeSpan.spanContext().traceId;
    const spanId = activeSpan.spanContext().spanId;
    const traceFlag = activeSpan.spanContext().traceFlags;

    // https://cloud.google.com/logging/docs/agent/logging/configuration#special-fields
    const activeSpanMetadata = {
      "logging.googleapis.com/trace": traceId,
      "logging.googleapis.com/spanId": spanId,
      "logging.googleapis.com/trace_sampled": traceFlag === TraceFlags.SAMPLED,
    };

    return activeSpanMetadata;
  } catch (error) {
    console.error(
      "Error collecting active span metadata to decorate logs",
      error,
    );
    return undefined;
  }
}

function addSpanAttribute(name, value) {
  const currentSpan = trace.getSpan(context.active());
  if (currentSpan) {
    currentSpan.setAttribute(name, value);
  }
}

function addSpanEvent(event) {
  const currentSpan = trace.getSpan(context.active());
  if (currentSpan) {
    currentSpan.addEvent(event);
  }
}

module.exports = {
  tracer,
  decorateWithActiveSpanAsync,
  collectActiveSpanMetadata,
  SpanStatusCode: SpanStatusCode,
  addSpanAttribute,
  addSpanEvent,
};
