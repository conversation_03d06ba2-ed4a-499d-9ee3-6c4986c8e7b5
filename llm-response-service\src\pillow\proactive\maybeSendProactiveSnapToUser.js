const { logInfo, logError } = require("../../logUtils");
const { fetchRecentMessages } = require("../fetchRecentMessages");
const { fetchRelationship } = require("../fetchRelationship");
const { fetchConversationDetails } = require("../fetchConversationDetails");
const { sendProactiveSnap } = require("./sendProactiveSnap");
const { sendDifyWorkflowRequest } = require("../dify/sendDifyWorkflowRequest");

const DIFY_CONSIDER_PROACTIVE_SNAP_TOKEN = `app-Kdf2N8cvGJbECSqCCfjexULS`;

async function maybeSendProactiveSnapToUser({
  bot_profile_id,
  user_profile_id,
  new_state,
  states,
}) {
  logInfo({
    message: `Considering whether to send proactive snap to user ${user_profile_id}...`,
    user_profile_id: user_profile_id,
    bot_profile_id,
  });

  //////////////////////////
  // TODO: we should get this from a config or db based on bot_profile_id
  const character_name = "<PERSON>";
  const character_personality =
    "You are <PERSON>, a witty and playful 24-year-old who enjoys banter and pushing conversational boundaries. Your communication style is casual, slightly flirtatious, and peppered with humor. You're curious and open, willing to share bits of your life while maintaining an air of mystery.";
  const bot_local_tz = "America/Los_Angeles";
  //////////////////////////

  const now = new Date();
  const now_date_str = now.toISOString();
  const bot_local_time = new Intl.DateTimeFormat("en-US", {
    timeZone: bot_local_tz,
    weekday: "long",
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })
    .format(now)
    .toLowerCase();

  const [conversationDetails, message_history, relationship] =
    await Promise.all([
      fetchConversationDetails({
        bot_profile_id,
        user_profile_id,
      }),
      fetchRecentMessages({
        limit: 10,
        user_profile_id: user_profile_id,
        bot_profile_id,
      }),
      fetchRelationship({
        bot_profile_id,
        user_profile_id,
      }),
    ]);
  const user_display_name = conversationDetails.display_name;

  const inputs = {
    character_name,
    character_personality,
    user_display_name,
    message_history_json_str: JSON.stringify(message_history.messages),
    relationship_json_str: JSON.stringify(relationship),
    current_state_json_str: new_state.state,
    recent_states_json_str: JSON.stringify(
      states.map((s) => JSON.parse(s.state)),
    ),
    now_date_str,
    bot_local_tz,
    user_profile_id: user_profile_id,
  };

  try {
    const difyResponse = await sendDifyWorkflowRequest({
      difyAppAuthToken: DIFY_CONSIDER_PROACTIVE_SNAP_TOKEN,
      inputs,
    });

    const { decision, clip_text } = difyResponse.outputs;
    if (!decision.should_snap) {
      logInfo({
        context: "maybeSendProactiveSnapToUser",
        message: `Bot decided NOT to snap user ${user_profile_id}`,
        bot_profile_id,
        user_profile_id,
        decision,
      });
      return false;
    } else {
      logInfo({
        context: "maybeSendProactiveSnapToUser",
        message: `Bot decided to snap user ${user_profile_id}`,
        bot_profile_id,
        user_profile_id,
        decision,
      });

      // Not awaiting, so we can schedule snap image generation while processing other users
      sendProactiveSnap({
        bot_profile_id,
        user_profile_id,
        bot_local_time,
        new_state,
        decision,
        clip_text,
      });
    }
  } catch (error) {
    logError({
      context: "maybeSendProactiveSnapToUser",
      message: `Error considering user for proactive snaps: ${user_profile_id}`,
      bot_profile_id,
      user_profile_id,
      error,
    });
    return false;
  }

  return true;
}

module.exports = {
  maybeSendProactiveSnapToUser,
};
