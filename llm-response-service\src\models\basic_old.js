function basicPromptWithUpscalerBatchable({
  prompt,
  seed,
  model = "photogasm",
  width = 864,
  height = 1024,
  batch_size = 1,
  nsfw = false,
  steps = 15,
  cfg = 10,
  faceSteps = 4,
  faceCfg = 4,
  sampler_name = "euler",
  scheduler = "karras",
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps,
        cfg,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["30", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, solid color background, white background, ${
          nsfw ? "" : ", (nudity, nsfw)"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    23: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 1024,
        seed: seed ?? Math.random() * 100000000000,
        steps: faceSteps,
        cfg: faceCfg,
        sampler_name,
        scheduler,
        denoise: 0.5,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        bbox_threshold: 0.5,
        bbox_dilation: 10,
        bbox_crop_factor: 3,
        sam_detection_hint: "center-1",
        sam_dilation: 0,
        sam_threshold: 0.93,
        sam_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        sam_mask_hint_use_negative: "False",
        drop_size: 50,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 10,
        image: ["8", 0],
        model: ["4", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
        bbox_detector: ["26", 0],
      },
      class_type: "FaceDetailer",
      _meta: {
        title: "FaceDetailer",
      },
    },
    24: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["23", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    26: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
  };
}

module.exports = { basicPromptWithUpscalerBatchable };
