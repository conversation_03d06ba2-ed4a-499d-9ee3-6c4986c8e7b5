const { CloudTasksClient } = require("@google-cloud/tasks");

const { botServerUrl } = require("../api");
const { logWarn, logInfo } = require("../logUtils");
const { getMD5 } = require("../utils");

async function scheduleProposedPostGenerationTask({
  botProfileId,
  scheduleDate,
}) {
  const queueName = "v1-proposed-posts-generation";
  const client = new CloudTasksClient();
  const parent = client.queuePath("butterflies-ai", "us-central1", queueName);

  const url = `${botServerUrl}/proposedPosts/generateProposedPostTask`;

  const payload = {
    bot_profile_id: botProfileId,
  };

  // "Using hashed strings for the task id or for the prefix of the task id is recommended"
  const taskIdWithoutHash = `${botProfileId}-${scheduleDate.getTime()}`;
  const hash = getMD5(taskIdWithoutHash);
  const taskId = `${hash}-${taskIdWithoutHash}`;

  const taskName = client.taskPath(
    "butterflies-ai",
    "us-central1",
    queueName,
    taskId,
  );

  const task = {
    name: taskName,
    httpRequest: {
      httpMethod: "POST",
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN}`,
      },
    },

    scheduleTime: {
      seconds: scheduleDate.getTime() / 1000,
    },
  };
  const request = { parent: parent, task: task };
  try {
    logInfo({
      context: "scheduleProposedPostGenerationTask",
      message: "Creating cloud task...",
      botProfileId,
      queueName,
      taskId,
      scheduleDate,
      payload,
    });
    const task = await client.createTask(request);
    return task;
  } catch (error) {
    // ALREADY_EXISTS
    if (error.code === 6) {
      logWarn({
        context: "scheduleProposedPostGenerationTask",
        message: "Post generation task already exists",
        error: error,
        botProfileId,
        queueName,
        taskId,
        scheduleDate,
        payload,
      });
    } else {
      throw error;
    }
  }
}

module.exports = {
  scheduleProposedPostGenerationTask,
};
