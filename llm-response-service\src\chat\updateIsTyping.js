const { addSpanEvent } = require("../instrumentation/tracer");
const {
  supabase,
  logSupabaseResultErrorWithDetails,
} = require("../supabaseClient");

function updateIsTyping(conversationId, botSenderId, isTyping) {
  addSpanEvent(isTyping ? "start typing" : "stop typing");
  supabase
    .from("conversation_participants")
    .update({
      typing: isTyping,
    })
    .eq("conversation_id", conversationId)
    .eq("profile_id", botSenderId)
    .then(
      logSupabaseResultErrorWithDetails(
        "failed to update 'conversation_participants'",
        {
          context: "sendMessage - bot update typing",
          conversation_id: conversationId,
          bot_sender_id: botSenderId,
          typing: isTyping,
        },
      ),
    );
}

module.exports = {
  updateIsTyping,
};
