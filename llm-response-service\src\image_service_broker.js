const { logError, logInfo, logWarn, generateTimeSeed } = require("./utils");
const { convertImageToBase64 } = require("./imageHelpers");
const { basicPromptWithUpscalerBatchable } = require("./models/basic_old");
const { sdxlFaceIdPrompt } = require("./models/sdxl_faceid");
const { sdxlIpadapterPrompt } = require("./models/sdxl_ipadapter");
const { sdxlAvatar } = require("./models/sdxl_avatar");
const { sdxlReactor } = require("./models/sdxl_reactor");
const { sdxlBasicPromptWithUpscaler } = require("./models/sdxl_basic");
const {
  tracer,
  decorateWithActiveSpanAsync,
  SpanStatusCode,
} = require("./instrumentation/tracer");
const opentelemetry = require("@opentelemetry/api");

const imageBrokerAddress = "***********:8080";

var PROTO_PATH = __dirname + "/proto/broker.proto";
var grpc = require("@grpc/grpc-js");
var protoLoader = require("@grpc/proto-loader");
const { sdxlTwoPeople } = require("./models/sdxl_two_people");
const { sdxlTwoPeopleAnime } = require("./models/sdxl_two_anime");
const { oneFriendV2Prompt } = require("./models/one_friend_v2");
const { oneFriendV3Prompt } = require("./models/one_friend_v3");
// const { sdxlFaceIdV2Prompt } = require("./models/sdxl_faceid_v2");
var packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
});
var protoDescriptor = grpc.loadPackageDefinition(packageDefinition);
var broker = protoDescriptor.broker;
var client = new broker.ImageServiceBroker(
  imageBrokerAddress,
  grpc.credentials.createInsecure(),
);

const meter = opentelemetry.metrics.getMeter("image_service_broker");
const enqueueTaskFailureCounter = meter.createCounter(
  "butterfliesapi_broker_enqueue_task_failure",
  {
    description: "Failed ImageServiceBroker.Enqueue calls",
  },
);
const enqueueTaskSuccessCounter = meter.createCounter(
  "butterfliesapi_broker_enqueue_task_success",
  {
    description: "Successful ImageServiceBroker.Enqueue calls",
  },
);

const getImageFailureCounter = meter.createCounter(
  "butterfliesapi_broker_get_image_failure",
  {
    description: "Failed ImageServiceBroker.GetImage calls",
  },
);
const getImageSuccessCounter = meter.createCounter(
  "butterfliesapi_broker_get_image_success",
  {
    description: "Successful ImageServiceBroker.GetImage calls",
  },
);

const getTaskStatusFailureCounter = meter.createCounter(
  "butterfliesapi_broker_get_task_status_failure",
  {
    description: "Failed ImageServiceBroker.Status calls",
  },
);
const getTaskStatusSuccessCounter = meter.createCounter(
  "butterfliesapi_broker_get_task_status_success",
  {
    description: "Successful ImageServiceBroker.Status calls",
  },
);

async function enqueueTask(workflow, priority, group, task_id) {
  let req = {
    task: {
      priority,
      group,
      workflow,
      completion_url: `${process.env.SERVER_ENDPOINT}/sd/taskDone`,
    },
    task_id: task_id,
  };

  return new Promise((resolve, reject) => {
    tracer.startActiveSpan("enqueueTask", async (span) => {
      // XXX: is there any point in this try/catch? I'd expect any failure to show up in the callback
      try {
        client.Enqueue(req, function (err, res) {
          if (err) {
            enqueueTaskFailureCounter.add(1);
            span.setStatus({ code: SpanStatusCode.ERROR });

            logWarn({
              context: "enqueue task",
              message: `${task_id} failing workflow: ${workflow}`,
              task_id,
              workflow,
            });

            logError({
              context: "enqueue task",
              error: err,
            });
            reject(err);
            span.end();
          } else {
            enqueueTaskSuccessCounter.add(1);
            logInfo({
              context: "enqueue task",
              message: `task enqueued, task id: ${res.task_id}`,
            });
            resolve(res.task_id);
            span.end();
          }
        });
      } catch (e) {
        enqueueTaskFailureCounter.add(1);
        span.setStatus({ code: SpanStatusCode.ERROR });

        logWarn({
          context: "enqueue task",
          message: `${task_id} failing workflow: ${workflow}`,
        });

        logError({
          context: "enqueue task",
          error: e,
        });
        reject(e);
        span.end();
      }
    });
  });
}

async function getImage(imageId) {
  const req = {
    image_id: [imageId],
  };

  return new Promise((resolve, reject) => {
    tracer.startActiveSpan("getImage", (span) => {
      // TODO: grpc-js client should be promisified, callbacks make for awful code
      client.GetImage(req, (err, res) => {
        if (err) {
          getImageFailureCounter.add(1);
          let errStr = JSON.stringify(err);
          span.setStatus({ code: SpanStatusCode.ERROR, message: errStr });

          logError({
            context: "get image",
            error: {
              ...err,
              message: `get image error: ${errStr}`,
            },
          });
          reject(err);
        } else {
          getImageSuccessCounter.add(1);
          resolve(res.image[imageId]);
        }
        span.end();
      });
    });
  });
}

async function generateWorkflow({
  prompt,
  posePrompt,
  artStyle,
  face_image_url,
  avatar_url,
  promptType,
  width,
  height,
  batch_size,
  seed,
  nsfw,
  contains_character,
  appearancePrompt,
  is_dm_message,
  using_front_facing_camera,
}) {
  const modelMap = {
    realistic: "realvisxlV40_v40LightningBakedvae",
    realistic_v2: "realvisxlV40_v40LightningBakedvae",
    realistic_v3: "realvisxlV40_v40LightningBakedvae",
    semi_realistic: "duchaitenAiartSDXL_v33515LightningTCD",
    drawing: "atomixAnimeXL_v10",
  };

  const faceDenoise = {
    realistic: 0.55,
    realistic_v2: 0.55,
    realistic_v3: 0.55,
    semi_realistic: 0.5,
    drawing: 0.5,
  };

  const model = modelMap[artStyle] || "realvisxlV40_v40LightningBakedvae";

  let commonParams = {
    prompt,
    width,
    height,
    batch_size,
    seed,
    nsfw,
  };

  let extraParams = {
    badquality: 0,
    blurxl: 0,
    envyzoomslider: 0,
  };

  // only if it's a realistic model, do we vary it
  if (model === "realvisxlV40_v40LightningBakedvae") {
    const zoomChance = Math.random();

    if (zoomChance < 0.1) {
      extraParams.envyzoomslider = -3;
    } else if (zoomChance < 0.22) {
      extraParams.envyzoomslider = -2;
    } else if (zoomChance < 0.35) {
      extraParams.envyzoomslider = -1;
    }

    const badQualityChance = Math.random();

    if (badQualityChance < 0.1 || commonParams.prompt.includes("selfie")) {
      commonParams.prompt = commonParams.prompt.replace(
        "high quality, award winning, highres, 8k,,",
        "bad quality, low quality,",
      );

      extraParams.blurxl = -2;
    }
  }

  if (
    model === "atomixAnimeXL_v10" ||
    model === "duchaitenAiartSDXL_v33515LightningTCD"
  ) {
    extraParams.sampler_name = "euler_ancestral";
    extraParams.scheduler = "normal";
    extraParams.steps = 12;
    extraParams.faceSteps = 8;
  }

  if (face_image_url) {
    // pick random pose from poses
    const poses = [
      "facing_left_1_kps.jpg",
      "facing_left_2_kps.jpg",
      "facing_left_3_kps.jpg",
      "facing_right_1_kps.jpg",
      "from_below_1_kps.jpg",
      // "from_below_2_kps.jpg",
      "straight_head_tilt_1_kps.jpg",
      "straight_head_tilt_2_kps.jpg",
      "straight_head_tilt_3_kps.jpg",
      "straight_head_tilt_4_kps.jpg",
      "straight_head_tilt_5_kps.jpg",
      "straight_head_tilt_6_kps.jpg",
      "straight_head_tilt_7_kps.jpg",
      "straight_head_tilt_8_kps.jpg",
    ];

    const random_pose = poses[Math.floor(Math.random() * poses.length)];

    // https://storage.googleapis.com/butterflies-ai-kps/facing_left_1_kps.jpg
    const pose_image_url = `https://storage.googleapis.com/butterflies-ai-kps/${random_pose}`;

    const imageData = await convertImageToBase64(face_image_url);
    const poseImageData = await convertImageToBase64(pose_image_url);

    if (promptType === "selfie") {
      // generate random number between 1.25 and 1.5
      extraParams = {
        badquality: 0,
        blurxl: 0,
        envyzoomslider: 0,
        posePrompt,
        tiffanyPrompt: prompt,
      };

      // use random weight for pinterest for variety
      extraParams.pinterest = 0.5;

      // make it zoomed, between 0.25 and 1.5
      extraParams.envyzoomslider = Math.random() * (0.5 - 0.25) + 0.25;
      extraParams.selfie = 0.5;

      // generate random number between -1 and 1
      // randomly blurry
      extraParams.blurxl = Math.random() * 2 - 1;

      extraParams.using_front_facing_camera = using_front_facing_camera;

      // fix seed to try to make things more consistent and less sporadic
      // seed is at 15 minute time intervals
      const seed = generateTimeSeed(15);
      extraParams.seed = seed;

      if (!using_front_facing_camera) {
        extraParams.faceInjectWeight = 0;
        extraParams.faceInjectWeightFaceIDV2 = 0;
        extraParams.blurxl = Math.random() * 0.4 - 0.6;
        extraParams.pinterest = 0.5;
        extraParams.pinterestFace = 0.0;
        extraParams.envyzoomslider = 0.0;
        extraParams.selfie = 0.0;
      }

      const offsetX = 192;

      extraParams.cropOffsetX = offsetX;

      if (prompt.includes("dark photo")) {
        // Set extraParams.dark to a random number between 0.1 and 0.2
        extraParams.dark = Math.random() * 0.1 + 0.1;
      } else {
        // Set extraParams.dark to a random number between 0 and 0.15
        extraParams.dark = Math.random() * 0.1;
      }

      if (using_front_facing_camera) {
        return oneFriendV3Prompt({
          ...commonParams,
          ...extraParams,
          model: modelMap[artStyle],
          faceImageData: imageData,
          poseImageData,
        });
      } else {
        return oneFriendV2Prompt({
          ...commonParams,
          ...extraParams,
          model: modelMap[artStyle],
          faceImageData: imageData,
          poseImageData,
        });
      }
    }

    if (!imageData) {
      return sdxlBasicPromptWithUpscaler({
        ...commonParams,
        ...extraParams,
        model: modelMap[artStyle],
        faceDenoise: faceDenoise[artStyle],
      });
    }

    if (promptType === "reactor") {
      return sdxlReactor({
        ...commonParams,
        ...extraParams,
        model: modelMap[artStyle],
        faceImageData: imageData,
      });
    } else {
      // if (is_dm_message) {
      return sdxlFaceIdPrompt({
        ...commonParams,
        ...extraParams,
        model: modelMap[artStyle],
        faceImageData: imageData,
        faceDenoise: faceDenoise[artStyle],
      });
      // } else {
      //   return sdxlFaceIdV2Prompt({
      //     ...commonParams,
      //     ...extraParams,
      //     model: modelMap[artStyle],
      //     faceImageData: imageData,
      //     faceDenoise: faceDenoise[artStyle],
      //     appearancePrompt,
      //   });
      // }
    }
  }

  if (batch_size > 1) {
    if (["realistic", "semi_realistic", "drawing"].includes(artStyle)) {
      return sdxlAvatar({
        ...commonParams,
        ...extraParams,
        model: modelMap[artStyle],
        width: 768,
        height: 768,
      });
    } else {
      return basicPromptWithUpscalerBatchable({
        ...commonParams,
        ...extraParams,
        model,
      });
    }
  }

  if (contains_character && avatar_url) {
    const imageData = await convertImageToBase64(avatar_url);

    if (!imageData) {
      return sdxlBasicPromptWithUpscaler({
        ...commonParams,
        ...extraParams,
        model: modelMap[artStyle],
        faceDenoise: faceDenoise[artStyle],
      });
    }

    return sdxlIpadapterPrompt({
      ...commonParams,
      ...extraParams,
      model: modelMap[artStyle],
      avatarImageData: imageData,
      faceDenoise: faceDenoise[artStyle],
    });
  }

  return sdxlBasicPromptWithUpscaler({
    ...commonParams,
    ...extraParams,
    model: modelMap[artStyle],
    faceDenoise: faceDenoise[artStyle],
  });
}

const getTaskStatus = decorateWithActiveSpanAsync(
  "getTaskStatus",
  _getTaskStatus,
);
async function _getTaskStatus(task_id) {
  const req = {
    task_id,
  };

  return new Promise((resolve, reject) => {
    client.Status(req, (err, res) => {
      if (err) {
        getTaskStatusFailureCounter.add(1);
        reject(err);
      } else {
        getTaskStatusSuccessCounter.add(1);
        resolve(res);
      }
    });
  });
}

async function generateWorkflowMultipleCharacters({
  image_prompt,
  artStyle,
  face_image_url_1,
  face_image_url_2,
  bot_description_1,
  bot_description_2,
  width,
  height,
  batch_size,
  seed,
  nsfw,
  background,
  bot_1_has_face,
  bot_2_has_face,
}) {
  const modelMap = {
    realistic: "realvisxlV40_v40LightningBakedvae",
    realistic_v2: "realvisxlV40_v40LightningBakedvae",
    realistic_v3: "realvisxlV40_v40LightningBakedvae",
    semi_realistic: "duchaitenAiartSDXL_v33515LightningTCD",
    drawing: "atomixAnimeXL_v10",
  };

  const faceDenoise = {
    realistic: 0.55,
    realistic_v2: 0.55,
    realistic_v3: 0.55,
    semi_realistic: 0.5,
    drawing: 0.5,
  };

  const model = modelMap[artStyle] || "realvisxlV40_v40LightningBakedvae";

  const face_image_data_1 = await convertImageToBase64(face_image_url_1);
  const face_image_data_2 = await convertImageToBase64(face_image_url_2);

  // dumb, but do it for now
  image_prompt = image_prompt.replace(/two people,/g, "");
  background = `${background}, high quality, 8k, dramatic lighting`;
  image_prompt = `two men, looking at camera, (same height:1.2), ${image_prompt}, high quality, 8k, dramatic lighting`;

  let extraParams = {
    badquality: 0,
    blurxl: 0,
    envyzoomslider: 0,
  };

  extraParams.sampler_name = "euler_ancestral";
  extraParams.scheduler = "normal";
  extraParams.steps = 12;
  extraParams.faceSteps = 8;

  if (bot_1_has_face && bot_2_has_face) {
    return sdxlTwoPeople({
      width,
      height,
      batch_size,
      seed,
      nsfw,
      model,
      faceDenoise: faceDenoise[artStyle],
      face_image_data_1,
      face_image_data_2,
      bot_description_1,
      bot_description_2,
      image_prompt,
      background,
      ...extraParams,
    });
  } else {
    return sdxlTwoPeopleAnime({
      width,
      height,
      batch_size,
      seed,
      nsfw,
      model,
      faceDenoise: faceDenoise[artStyle],
      face_image_data_1,
      face_image_data_2,
      bot_description_1,
      bot_description_2,
      image_prompt,
      background,
      ...extraParams,
    });
  }
}

module.exports = {
  enqueueTask,
  generateWorkflow,
  generateWorkflowMultipleCharacters,
  getImage,
  getTaskStatus,
};
