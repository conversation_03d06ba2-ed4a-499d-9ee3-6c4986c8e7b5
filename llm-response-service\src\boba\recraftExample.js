const RecraftClient = require("./recraft");

/**
 * Example usage of the Recraft client
 */
async function recraftExample() {
  // Replace with your actual auth token
  const authToken = "YOUR_AUTH_TOKEN";

  // Create a new Recraft client
  const recraftClient = new RecraftClient(authToken);

  try {
    // Example 1: Submit a rendering request and poll for the result separately
    console.log("Submitting rendering request...");
    const submitResult = await recraftClient.submitRenderingRequest({
      prompt: "a woman wearing a lacy dress with cleavage",
    });

    console.log("Rendering request submitted:", submitResult);

    // Poll for the result
    console.log("Polling for the result...");
    const pollResult = await recraftClient.pollRenderingResult(
      submitResult.operationId,
    );

    console.log("Poll result:", pollResult);

    // Get the image URL
    if (pollResult.images && pollResult.images.length > 0) {
      const imageUrl = recraftClient.getImageUrl(pollResult.images[0].image_id);
      console.log("Image URL:", imageUrl);
    }

    // Example 2: Generate an image with a single function call
    console.log("\nGenerating image with a single function call...");
    const generateResult = await recraftClient.generateImage({
      prompt: "a futuristic cityscape with flying cars",
      layerSize: { height: 768, width: 768 },
    });

    console.log("Generate result:", generateResult);
    console.log("Image URLs:", generateResult.imageUrls);
  } catch (error) {
    console.error("Error:", error.message);
  }
}

// Uncomment to run the example
// recraftExample();

module.exports = { recraftExample };
