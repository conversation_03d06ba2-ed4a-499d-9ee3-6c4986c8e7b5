function oneFriendPrompt({
  prompt,
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
  steps = 6,
  cfg = 1.5,
  faceSteps = 3,
  faceCfg = 1.5,
  faceDenoise = 0.4,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  badquality = 0,
  blurxl = 0,
  envyzoomslider = 0,
  faceInjectWeight = 1,
  faceInjectWeightFaceIDV2 = 3,
  dark = 0,
  pinterest = 1.5,
  pinterestFace = 1.0,
  selfie = 0,
  cropOffsetX = 224,
}) {
  seed = seed ?? Math.floor(Math.random() * 100000000000);

  return {
    3: {
      inputs: {
        seed,
        steps: 6,
        cfg,
        sampler_name: "dpmpp_sde",
        scheduler: "karras",
        denoise: 1,
        model: ["68", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width: 1024,
        height: 1024,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(phone), ${
          nsfw ? "" : ", (nudity, nsfw, naked)"
        }`,
        clip: ["4", 1],
    },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    11: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["64", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    33: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    35: {
      inputs: {
        weight: faceInjectWeight,
        weight_faceidv2: faceInjectWeightFaceIDV2,
        weight_type: "ease in",
        combine_embeds: "concat",
        start_at: 0.3,
        end_at: 1,
        embeds_scaling: "V only",
        model: ["74", 0],
        ipadapter: ["39", 0],
        image: ["11", 0],
        clip_vision: ["41", 0],
        insightface: ["40", 0],
      },
      class_type: "IPAdapterFaceID",
      _meta: {
        title: "IPAdapter FaceID",
      },
    },
    39: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sdxl.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    40: {
      inputs: {
        provider: "CPU",
        model_name: "buffalo_l",
      },
      class_type: "IPAdapterInsightFaceLoader",
      _meta: {
        title: "IPAdapter InsightFace Loader",
      },
    },
    41: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    57: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 1200,
        seed,
        steps: 4,
        cfg: 1.5,
        sampler_name: "dpmpp_sde",
        scheduler: "karras",
        denoise: 0.5,
        feather: 6,
        noise_mask: true,
        force_inpaint: true,
        wildcard: "",
        cycle: 1,
        inpaint_model: true,
        noise_mask_feather: 20,
        image: ["8", 0],
        segs: ["61", 0],
        model: ["82", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
      },
      class_type: "DetailerForEach",
      _meta: {
        title: "Detailer (SEGS)",
      },
    },
    60: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 5,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.77,
        post_dilation: 0,
        bbox_detector: ["33", 0],
        image: ["8", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    61: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["60", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    64: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    66: {
      inputs: {
        lora_name: "badquality.safetensors",
        strength_model: 0,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    67: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: -2,
        model: ["69", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    68: {
      inputs: {
        lora_name: "envyzoomslider.safetensors",
        strength_model: envyzoomslider,
        model: ["67", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    69: {
      inputs: {
        lora_name: "Instagram_Selfie_SDXL.safetensors",
        strength_model: selfie,
        model: ["79", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    71: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: -2,
        model: ["35", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    74: {
      inputs: {
        lora_name: "dark.safetensors",
        strength_model: dark,
        model: ["66", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    79: {
      inputs: {
        lora_name: "Pinterest_Snap_Selfie.safetensors",
        strength_model: 0.75,
        model: ["35", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    82: {
      inputs: {
        lora_name: "Pinterest_Snap_Selfie.safetensors",
        strength_model: 0.75,
        model: ["71", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    105: {
      inputs: {
        width: 576,
        height: 1024,
        x: cropOffsetX,
        y: 0,
        image: ["57", 0],
      },
      class_type: "ImageCrop",
      _meta: {
        title: "Image Crop",
      },
    },
    107: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["105", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { oneFriendPrompt };
