const express = require("express");
// const { authUser } = require("../middleware");
const { fetchBotsWithProposedPosts } = require("./fetchBotsWithProposedPosts");
const { fetchCurrentProposedPost } = require("./fetchCurrentProposedPost");
const {
  publishProposedPost,
  rejectProposedPost,
  validateProposedPostState,
} = require("./userActions");
const { logInfo } = require("../logUtils");
const {
  enableProposedPostModeForBot,
  enableProposedPostModeForAllBotsOfUser,
} = require("./enableProposedPostMode");
const {
  scheduleOrGenerateProposedPostForBotIfNeeded,
} = require("./scheduleOrGenerateProposedPostForBotIfNeeded");
const { generateProposedPostTask } = require("./generateProposedPostTask");
const { fetchBotAndBotProfile } = require("./fetchBotAndBotProfile");
const {
  sendProposedPostReminderNotifIfNeeded,
} = require("./sendProposedPostReminderNotifIfNeeded");
const {
  onProposedPostReadyForReview,
} = require("./onProposedPostReadyForReview");
const { countProposedPosts } = require("./countProposedPosts");
const { authAdminDebugOnly } = require("../middlewares/authAdminDebugOnly");
const { authUser } = require("../middleware");
const {
  checkProfileValid,
  validateBotProfileOwnedByUser,
} = require("../utils");

const router = express.Router();

router.post(
  "/generateProposedPostTask",
  // // FIXME: auth!
  async (req, res) => {
    logInfo({
      context: "generateProposedPostTask",
      message: "generating proposed post if needed...",
      body: req.body,
    });
    const { bot_profile_id: botProfileId } = req.body;

    const result = await generateProposedPostTask({
      botProfileId,
    });

    if (!result) {
      return res.status(404).json({ error: "bot not found" });
    }

    res.json(result);
  },
);

router.post(
  "/sendProposedPostReminderTask",
  // // FIXME: auth!
  async (req, res) => {
    logInfo({
      context: "sendProposedPostReminderTask",
      message: "will send proposed post reminder notif if needed...",
      body: req.body,
    });
    const { post, botProfile } = req.body;

    const nowDate = new Date();
    const result = await sendProposedPostReminderNotifIfNeeded({
      post,
      botProfile,
      nowDate,
    });

    res.json(result);
  },
);

router.post("/fetchBotsWithProposedPosts", authUser, async (req, res) => {
  const { userProfileId } = req.body;

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, userProfileId);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const bots = await fetchBotsWithProposedPosts({
    userProfileId,
  });

  res.json({
    data: bots,
  });
});

router.post("/fetchCurrentProposedPost", authUser, async (req, res) => {
  const { botProfileId, includeGenerating } = req.body;

  const user_id = req.user?.id;
  const isValid = await validateBotProfileOwnedByUser({
    userId: user_id,
    botProfileId,
  });
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const post = await fetchCurrentProposedPost({
    bot_profile_id: botProfileId,
    includeGenerating: includeGenerating,
  });

  res.json({
    data: post,
  });
});

router.post("/publishProposedPost", authUser, async (req, res) => {
  const { postId } = req.body;

  const user_id = req.user?.id;
  const isValid = await validateProposedPostState({ postId, userId: user_id });
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const post = await publishProposedPost({
    postId,
  });

  res.json({
    data: post,
  });
});

router.post("/rejectProposedPost", authUser, async (req, res) => {
  const { postId } = req.body;

  const user_id = req.user?.id;
  const isValid = await validateProposedPostState({ postId, userId: user_id });
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const post = await rejectProposedPost({
    postId,
  });

  res.json({
    data: post,
  });
});

router.post(
  "/DEBUG_enableProposedPostModeForBot",
  authAdminDebugOnly,
  async (req, res) => {
    const { botProfileId } = req.body;

    if (!botProfileId) {
      return res.status(400).json({ error: "no botProfileId provided" });
    }

    const nowDate = new Date();
    const proposedPostNextGenerationDate = nowDate;
    const botProfile = await enableProposedPostModeForBot({
      botProfileId,
      proposedPostNextGenerationDate,
    });

    res.json({
      data: botProfile,
    });
  },
);

router.post(
  "/DEBUG_enableProposedPostModeForAllBotsOfUser",
  authAdminDebugOnly,
  async (req, res) => {
    const { userProfileId } = req.body;

    if (!userProfileId) {
      return res.status(400).json({ error: "no userProfileId provided" });
    }

    await enableProposedPostModeForAllBotsOfUser({
      userProfileId,
      nowDate: new Date(),
    });

    res.sendStatus(200);
  },
);

router.post(
  "/DEBUG_scheduleOrGenerateProposedPostIfNeeded",
  authAdminDebugOnly,
  async (req, res) => {
    logInfo({
      body: req.body,
      context: "DEBUG_scheduleOrGenerateProposedPostIfNeeded",
      message: "scheduling or generating proposed post if needed",
    });
    const { botProfileId, forceNowTimestamp } = req.body;

    let nowDate;
    if (typeof forceNowTimestamp === "undefined") {
      nowDate = new Date();
    } else {
      nowDate = forceNowTimestamp;
    }

    const botData = await fetchBotAndBotProfile({ botProfileId });
    if (!botData) {
      return res.status(404).json({ error: "bot not found" });
    }
    const { bot, botProfile } = botData;

    const result = await scheduleOrGenerateProposedPostForBotIfNeeded({
      bot,
      botProfile,
      nowDate,
    });

    res.json(result);
  },
);

router.post(
  "/DEBUG_sendProposedPostReminderTask",
  authAdminDebugOnly,
  async (req, res) => {
    logInfo({
      context: "DEBUG_sendProposedPostReminderTask",
      message: "will send proposed post reminder notif if needed...",
      body: req.body,
    });
    const { botProfileId, forceNowDateMs } = req.body;

    const post = await fetchCurrentProposedPost({
      bot_profile_id: botProfileId,
    });
    const botData = await fetchBotAndBotProfile({ botProfileId });
    if (!botData) {
      return res.status(404).json({ error: "bot not found" });
    }
    const { botProfile } = botData;
    const nowDate = forceNowDateMs ? new Date(forceNowDateMs) : new Date();
    const result = await sendProposedPostReminderNotifIfNeeded({
      post,
      botProfile,
      nowDate,
    });

    res.json(result);
  },
);

router.post(
  "/DEBUG_onProposedPostReadyForReview",
  authAdminDebugOnly,
  async (req, res) => {
    logInfo({
      context: "DEBUG_onProposedPostReadyForReview",
      body: req.body,
    });
    const { botProfileId } = req.body;

    const post = await fetchCurrentProposedPost({
      bot_profile_id: botProfileId,
    });
    const botData = await fetchBotAndBotProfile({ botProfileId });
    if (!botData) {
      return res.status(404).json({ error: "bot not found" });
    }
    const { botProfile } = botData;
    const result = await onProposedPostReadyForReview({
      post,
      botProfile,
    });

    res.json(result);
  },
);

router.post(
  "/DEBUG_countProposedPosts",
  authAdminDebugOnly,
  async (req, res) => {
    logInfo({
      context: "DEBUG_countProposedPosts",
      body: req.body,
    });
    const { botProfileId } = req.body;

    const result = await countProposedPosts({
      botProfileId,
    });

    res.json(result);
  },
);

module.exports = {
  proposedPostsRouter: router,
};
