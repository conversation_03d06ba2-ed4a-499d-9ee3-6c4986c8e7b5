const { logInfo, logWarn, logError } = require("../../../logUtils");
const { wrappedSupabaseError, supabase } = require("../../../supabaseClient");
const { default: axios } = require("axios");

async function checkProcessingStoryPostVideos({ bot_profile_id }) {
  logInfo({
    context: "checkProcessingStoryPostVideos",
    message: `Checking for processing story post videos...`,
    bot_profile_id,
  });

  // TODO: acquire distributed lock
  const twentyFourHoursAgo = new Date(
    new Date().getTime() - 24 * 60 * 60 * 1000,
  );

  const { data: storyPosts, error: storyPostsError } = await supabase
    .from("pillow_story_posts")
    .select("*")
    .eq("processing_status", "video_generating")
    .eq("poster_profile_id", bot_profile_id)
    .gte("created_at", twentyFourHoursAgo.toISOString());

  if (storyPostsError) {
    const error = wrappedSupabaseError(storyPostsError);
    throw error;
  }

  if (!storyPosts || !storyPosts.length) {
    logInfo({
      context: "checkProcessingStoryPostVideos",
      message: `No story posts found in video_generating state`,
    });
    return false;
  }
  logInfo({
    context: "checkProcessingStoryPostVideos",
    message: `Found ${storyPosts.length} story posts in video_generating state`,
    story_post_ids: storyPosts.map((sp) => sp.id),
  });

  let stillProcessing = false;
  // TODO: either have a batch api or make this concurrent (with a limit)
  for (const storyPost of storyPosts) {
    try {
      stillProcessing |= await checkProcessingSingleStoryPostVideo({
        storyPost,
      });
    } catch {
      continue;
    }
  }
  // TODO: release distributed lock

  // poor man's background job, lol
  // TODO: replace this with some proper job mechanism
  if (stillProcessing) {
    setTimeout(
      () => checkProcessingStoryPostVideos({ bot_profile_id }),
      1000 * 60,
    );
  }

  return stillProcessing;
}

async function checkProcessingSingleStoryPostVideo({ storyPost }) {
  const expiryHours = 8;
  const expiryCutoff = new Date(
    new Date().getTime() - expiryHours * 60 * 60 * 1000,
  );

  let stillProcessing = false;

  const { id, video_generation_id, video_generation_start_date } = storyPost;

  const videoGenerationStartDate = new Date(video_generation_start_date);

  const checkVideoGenerationResult = await axios.post(
    `https://api.butterflies.ai/video/status`,
    { request_id: video_generation_id },
  );
  const { status } = checkVideoGenerationResult.data;
  if (status === "completed") {
    const { download_url } = checkVideoGenerationResult.data;
    // TODO: probably need to copy video to our storage, I bet hailuo don't keep it available forever

    logInfo({
      context: "checkProcessingStoryPostVideos",
      message: `Video generation completed for story post ${id}`,
      story_post_id: id,
      video_generation_id,
      status,
    });
    const { error: updateError } = await supabase
      .from("pillow_story_posts")
      .update({
        processing_status: "video_generated",
        video_url: download_url,
      })
      .eq("id", id);
    if (updateError) {
      const error = wrappedSupabaseError(updateError);
      throw error;
    }
  } else if (status === "failed") {
    logError({
      context: "checkProcessingStoryPostVideos",
      message: `Video generation failed for story post ${id}`,
      story_post_id: id,
      video_generation_id,
      status,
      reason: "failed",
    });

    const { error: updateError } = await supabase
      .from("pillow_story_posts")
      .update({
        processing_status: "video_failed",
      })
      .eq("id", id);
    if (updateError) {
      const error = wrappedSupabaseError(updateError);
      throw error;
    }
  } else if (status === "processing") {
    if (videoGenerationStartDate < expiryCutoff) {
      logError({
        context: "checkProcessingStoryPostVideos",
        message: `Video generation for story post ${id} has been processing for over ${expiryHours} hours, treating as failed`,
        story_post_id: id,
        video_generation_id,
        status,
        reason: "expired",
      });
    } else {
      logInfo({
        context: "checkProcessingStoryPostVideos",
        message: `Video generation still processing for story post ${id}`,
        story_post_id: id,
        video_generation_id,
        status,
      });
    }

    stillProcessing = true;
  } else {
    logWarn({
      context: "checkProcessingStoryPostVideos",
      message: `Unknown video generation status for story post ${id}`,
      story_post_id: id,
      video_generation_id,
      status,
    });
  }
  return stillProcessing;
}

module.exports = {
  checkProcessingStoryPostVideos,
};
