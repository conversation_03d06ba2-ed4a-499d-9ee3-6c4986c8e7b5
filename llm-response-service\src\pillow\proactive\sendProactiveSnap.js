const { logInfo } = require("../../logUtils");
const {
  notifyChrysalisAboutNewSnap,
} = require("../notifyChrysalisAboutNewSnap");
const { schedulePillowImageGenerationTask } = require("../../pillowUtils");
const { PillowGenerationType } = require("../../pillowImageUtils");

async function sendProactiveSnap({
  bot_profile_id,
  user_profile_id,
  bot_local_time,
  new_state,
  decision,
  clip_text,
}) {
  // Notifying chrysalis that the bot is sending a snap - this creates the pillow_messages record in the db.
  //
  // NOTE: intentionally not awaited
  notifyChrysalisAboutNewSnap({
    sender_profile_id: bot_profile_id.toString(),
    receiver_profile_id: user_profile_id.toString(),
    message: decision.text,
    // image_url, // we don't have an image URL at this point yet, but that's not actually currently necessary for memories
    image_description: decision.image_description,
    is_sender_bot: true,
  });

  // new_state.state is a string (that's how it's stored in the db)
  const parsed_new_state = JSON.parse(new_state.state);
  const { location, upper_body_clothing } = parsed_new_state;

  const snapRequest = {
    text: decision.text,
    location: location,
    current_time: bot_local_time,
    image_description: clip_text,
    upper_body_clothing: upper_body_clothing,
    selfie: decision.selfie ? "true" : "false",
    emote: decision.emote,
    lighting: decision.lighting,
  };
  logInfo({
    context: "maybeSendProactiveSnapsOnStateTransition",
    message: "will schedule image generation task",
    snapRequest,
    user_profile_id,
    bot_profile_id,
  });
  const task = await schedulePillowImageGenerationTask({
    snapRequest,
    generationType: PillowGenerationType.SNAP,
    pillowUserProfileId: user_profile_id,
    senderProfileId: bot_profile_id,
    decision,
  });
  logInfo({
    context: "maybeSendProactiveSnapsOnStateTransition",
    message: "scheduled pillow image generation task",
    task_id: task.id,
    snapRequest,
    user_profile_id,
    bot_profile_id,
  });
}

module.exports = {
  sendProactiveSnap,
};
