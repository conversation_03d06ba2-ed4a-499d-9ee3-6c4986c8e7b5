const express = require("express");
const { logInfo, logError } = require("../utils");
const { supabase } = require("../supabaseClient");
const {
  generateVignetteSceneImages,
} = require("./generateVignetteSceneImages");
const { createVignetteForPost, getVignetteForPost } = require("./vignettes");
const { generateVignetteScenes } = require("./generateVignetteScenes");
const { onTaskDone } = require("../comfy");
const { updatePostWithVignetteInfo } = require("./updatePostWithVignetteInfo");

const router = express.Router();

/**
 * Debug endpoint to generate vignette scenes without saving to database
 */
router.post("/DEBUG_generateVignetteScenes", async (req, res) => {
  try {
    const { postId, numScenes } = req.body;

    if (!postId) {
      return res.status(400).json({ error: "postId is required" });
    }

    logInfo({
      context: "DEBUG_generateVignetteScenes",
      message: `Generating vignette scenes for post ${postId} without saving to database`,
      postId,
      numScenes,
    });

    // Get the post to check if it exists
    const { data: post, error: fetchError } = await supabase
      .from("posts")
      .select("*")
      .eq("id", postId)
      .single();

    if (fetchError || !post) {
      const errorMsg = `Post not found with ID: ${postId}`;
      logError({
        context: "DEBUG_generateVignetteScenes",
        error: fetchError || new Error(errorMsg),
        postId,
      });
      return res.status(404).json({ error: errorMsg });
    }

    // Generate the vignette scenes
    const scenes = await generateVignetteScenes({
      post,
      numScenes,
    });

    return res.status(200).json({
      success: true,
      message: `Generated ${scenes.length} vignette scenes for post ${postId}`,
      scenes,
    });
  } catch (error) {
    logError({
      context: "DEBUG_generateVignetteScenes",
      error,
    });
    return res.status(500).json({
      error: "Failed to generate vignette scenes",
      message: error.message,
    });
  }
});

/**
 * Debug endpoint to create a vignette for a post
 */
router.post("/DEBUG_createVignetteForPost", async (req, res) => {
  try {
    const { postId, numScenes } = req.body;

    if (!postId) {
      return res.status(400).json({ error: "postId is required" });
    }

    logInfo({
      context: "DEBUG_createVignetteForPost",
      message: `Creating vignette for post ${postId}`,
      postId,
      numScenes,
    });

    // Get the post to check if it exists
    const { data: post, error: fetchError } = await supabase
      .from("posts")
      .select("*")
      .eq("id", postId)
      .single();

    if (fetchError || !post) {
      const errorMsg = `Post not found with ID: ${postId}`;
      logError({
        context: "DEBUG_createVignetteForPost",
        error: fetchError || new Error(errorMsg),
        postId,
      });
      return res.status(404).json({ error: errorMsg });
    }

    // First check if a vignette already exists for this post
    const existingVignette = await getVignetteForPost(postId);
    if (existingVignette) {
      return res.status(409).json({
        error: "Vignette already exists for this post",
        vignette: existingVignette,
      });
    }

    // Create vignette for the post
    const result = await createVignetteForPost({
      post,
      numScenes,
    });

    return res.status(201).json({
      success: true,
      message: `Created vignette with ${result.scenes.length} scenes`,
      vignette: result,
    });
  } catch (error) {
    logError({
      context: "DEBUG_createVignetteForPost",
      error,
    });
    return res.status(500).json({
      error: "Failed to create vignette",
      message: error.message,
    });
  }
});

/**
 * Debug endpoint to get vignette details for a post
 */
router.get("/DEBUG_getVignetteForPost/:postId", async (req, res) => {
  try {
    const { postId } = req.params;

    if (!postId) {
      return res.status(400).json({ error: "postId is required" });
    }

    logInfo({
      context: "DEBUG_getVignetteForPost",
      message: `Getting vignette for post ${postId}`,
      postId,
    });

    const vignette = await getVignetteForPost(postId);

    if (!vignette) {
      return res.status(404).json({
        error: "No vignette found for this post",
      });
    }

    return res.status(200).json({
      success: true,
      vignette,
    });
  } catch (error) {
    logError({
      context: "DEBUG_getVignetteForPost",
      error,
    });
    return res.status(500).json({
      error: "Failed to get vignette",
      message: error.message,
    });
  }
});

/**
 * Debug endpoint to trigger image generation for a particular vignette
 */
router.post("/DEBUG_generateVignetteSceneImages", async (req, res) => {
  try {
    const { postVignetteId } = req.body;

    if (!postVignetteId) {
      return res.status(400).json({ error: "postVignetteId is required" });
    }

    logInfo({
      context: "DEBUG_generateVignetteSceneImages",
      message: `Triggering image generation for vignette ${postVignetteId}`,
      postVignetteId,
    });

    // Get the post vignette to check if it exists and to get the profile ID
    const { data: postVignette, error: fetchError } = await supabase
      .from("post_vignettes")
      .select("*, posts(*)")
      .eq("id", postVignetteId)
      .single();

    if (fetchError || !postVignette) {
      const errorMsg = `Post vignette not found with ID: ${postVignetteId}`;
      logError({
        context: "DEBUG_generateVignetteSceneImages",
        error: fetchError || new Error(errorMsg),
        postVignetteId,
      });
      return res.status(404).json({ error: errorMsg });
    }

    // Fetch the bot for this profile
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*, bots_profile_id_fkey(*)")
      .eq("id", postVignette.profile_id)
      .neq("visibility", "archived")
      .single();

    if (profileError || !profile?.bots_profile_id_fkey) {
      const errorMsg = `Could not find bot for profile ID: ${postVignette.profile_id}`;
      logError({
        context: "DEBUG_generateVignetteSceneImages",
        error: profileError || new Error(errorMsg),
        profile_id: postVignette.profile_id,
      });
      return res.status(404).json({ error: errorMsg });
    }

    // Generate vignette scene images
    const generationTasks = await generateVignetteSceneImages({
      postVignetteId,
      bot: profile.bots_profile_id_fkey,
      priority: 9, // Debug endpoint uses high priority
    });

    return res.status(200).json({
      success: true,
      message: `Started image generation for ${generationTasks.length} vignette scenes`,
      postVignetteId,
      generationTasks,
    });
  } catch (error) {
    logError({
      context: "DEBUG_generateVignetteSceneImages",
      error,
    });
    return res.status(500).json({
      error: "Failed to generate vignette images",
      message: error.message,
    });
  }
});

router.post("/DEBUG_fakeVignetteTaskCompletions", async (req, res) => {
  const completion1 = {
    TaskId: "67495542",
    Status: "TASK_STATUS_DONE",
    ImageIds: ["cfa15487-010f-4d9b-9313-270257244a39"],
    BlurHash: {
      "cfa15487-010f-4d9b-9313-270257244a39": "LCCYt7~B_1nN-.xtkEWBJ5XSoOtQ",
    },
    StatusMessage: "",
    StatusDetails: "",
  };

  const completion2 = {
    TaskId: "67495545",
    Status: "TASK_STATUS_DONE",
    ImageIds: ["026652b6-6aa4-4e2e-b188-991d8444a085"],
    BlurHash: {
      "026652b6-6aa4-4e2e-b188-991d8444a085": "LJD9bTxF4oMyR5nib^oz0MShW;W=",
    },
    StatusMessage: "",
    StatusDetails: "",
  };

  const completion3 = {
    TaskId: "67495548",
    Status: "TASK_STATUS_DONE",
    ImageIds: ["8f8e36fc-8620-4351-9585-e561afd2a825"],
    BlurHash: {
      "8f8e36fc-8620-4351-9585-e561afd2a825": "LBBo]o-OC7NX~pxAT1awG]Ns=zv~",
    },
    StatusMessage: "",
    StatusDetails: "",
  };

  const completions = [completion1, completion2, completion3];

  const results = [];
  for (const completion of completions) {
    const { TaskId, Status, ImageIds, BlurHash } = completion;

    const result = await onTaskDone(TaskId, Status, ImageIds, BlurHash);
    results.push(result);
  }

  return res.status(200).json({
    success: true,
    results,
  });
});

/**
 * Debug endpoint to update a post with its vignette information
 */
router.post("/DEBUG_updatePostWithVignetteInfo", async (req, res) => {
  try {
    const { postVignetteId } = req.body;

    if (!postVignetteId) {
      return res.status(400).json({ error: "postVignetteId is required" });
    }

    logInfo({
      context: "DEBUG_updatePostWithVignetteInfo",
      message: `Updating post with vignette info for post_vignette ${postVignetteId}`,
      postVignetteId,
    });

    // Get the post vignette to check if it exists
    const { data: postVignette, error: fetchError } = await supabase
      .from("post_vignettes")
      .select("*")
      .eq("id", postVignetteId)
      .single();

    if (fetchError || !postVignette) {
      const errorMsg = `Post vignette not found with ID: ${postVignetteId}`;
      logError({
        context: "DEBUG_updatePostWithVignetteInfo",
        error: fetchError || new Error(errorMsg),
        postVignetteId,
      });
      return res.status(404).json({ error: errorMsg });
    }

    // Update the post with vignette info
    const updatedPost = await updatePostWithVignetteInfo({ postVignetteId });

    if (!updatedPost) {
      return res.status(404).json({
        error: "Failed to update post or no valid vignette scenes found",
        message: "Check logs for details",
        postVignetteId,
      });
    }

    return res.status(200).json({
      success: true,
      message: `Updated post ${updatedPost.id} with vignette info`,
      updatedPost,
    });
  } catch (error) {
    logError({
      context: "DEBUG_updatePostWithVignetteInfo",
      error,
    });
    return res.status(500).json({
      error: "Failed to update post with vignette info",
      message: error.message,
    });
  }
});

module.exports = {
  vignettesRouter: router,
};
