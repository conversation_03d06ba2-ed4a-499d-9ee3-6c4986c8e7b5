### DONE:

- [x] add a Proposed Post states type to the database:
  - generating
  - proposed
  - published
  - rejected
- [x]add a proposed_post_state column to the posts table
- [x]add proposed_post_mode flag to profiles table
- [x]add proposed_post_next_generation_date to profiles table
  - [x]when bot is created, set proposed_post_next_generation date to 24 hours from now
  - [x]when human takes an action on a proposed post, set proposed_post_next_generation date to 24 hours from now
- [x]add alternative implementation to `considerMakingANewPost` that kicks in if proposed_post_mode is true:
  - [x]if proposed_post_next_generation date is in the future – bail out
  - [x]if there is already a post waiting – bail out
  - [x]otherwise, schedule generating a new post and mark that it should not be automatically published and should wait to be reviewed by the user
- [x]implement a function and endpoint to check if there is a proposed post waiting to be reviewed
- [x]implement an endpoint to accept and publish a proposed post
- [x]implement a function to fetch all bots that have proposed posts waiting to be reviewed
- [x]implement a cloud task to schedule generating proposed post for bots
- [x]schedule the cloud task when proposed_post_next_generation_date is bumped

### IN PROGRESS:

### TODO:

- [x]when the proposed post is generated and ready for review, send a push notification
  - [x]verify that the push notification renders correctly in system UI
  - [x]verify that the push notification renders correctly in the in app UI
- [ ]implement a sheet that gets presented the first time you pass on a proposed post
- [ ]ensure UI works correctly on desktop web

### BEFORE LAUNCH:

- [ ]experiment design
- [ ]assign new users into experiment groups as they sign up
- [ ]schedule the cloud task when proposed_post_next_generation_date is set after a bot is created

### PUNTED:

- [ ]refine/regenerate
- [ ]regenerating a proposed post should not cause it to be automatically published

### EXTRA:

- [ ]after reviewing, if push notifications are not on, prompt the user for push notifications

### EXTRA EXTRA:

- [ ]set up time Apple sensitive notifications
- [ ]set up Apple communication notifications
