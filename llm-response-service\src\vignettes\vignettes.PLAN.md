# Implementing vignettes

- [x] implement a function that can take a post and use an LLM to generate a given number of vignette scenes
  - the scenes should be based on the post's description, captions and the details about the profile that created the post
  - look generatePostDetailsWithLLM from botHelpers.js to understand how to call the LLM
  - the returned vignette scene details object should have the following fields:
    - scene_description: a description of what's happening in the scene that could be used to generate an image for it
    - scene_caption: a text caption that the profile that created the post would have used for the scene
    - most_interesting: What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person.
    - contains_character: true if the scene features the character whose profile created the post
- [x] implement a function that can take vignette scenes and inserts them into the database
  - a new record should be created in the `post_vignettes` table and related to the post
  - the scenes should be inserted into a new `vignette_scenes` table and related to the `post_vignettes` record
- [x] implement a function that kicks off image generation for each of the provided vignette scenes, similar to the generateComfyRequest call in generatePost in botHelpers.js.
  - use a new generation type
- [x] implement a function that can process a completed vignette scene image generation
  - perform image moderation
  - update the `vignette_scenes` table with the image url
  - check the `post_vignettes` table to see if all the scenes have been generated and if so, leave a TODO to update the post with the finalized vignette details
- [x] implement a function that can update the post with the finalized vignette details
