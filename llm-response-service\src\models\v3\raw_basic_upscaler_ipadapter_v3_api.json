{"3": {"inputs": {"seed": 726813353597691, "steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "model": ["37", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "meinamix.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 864, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "(six wings, eyes all over the wings, halos everywhere, monster, giant eyeball), standing in a restaurant, monster", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(worst quality), (low quality), (normal quality), lowres, normal quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "24": {"inputs": {"filename_prefix": "ComfyUI", "images": ["35", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "26": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "30": {"inputs": {"lora_name": "NSFWFilter.safetensors", "strength_model": -1, "model": ["4", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "31": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 4, "segs": ["33", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "33": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 10, "crop_factor": 3, "drop_size": 50, "sub_threshold": 0.93, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["26", 0], "image": ["8", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "35": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 1024, "seed": 360114454801262, "steps": 6, "cfg": 8, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "image": ["8", 0], "segs": ["31", 0], "model": ["4", 0], "clip": ["4", 1], "vae": ["4", 2], "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "DetailerForEachDebug", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (SEGS)"}}, "37": {"inputs": {"weight": 1, "start_at": 0.5, "end_at": 1, "weight_type": "standard", "model": ["38", 0], "ipadapter": ["38", 1], "image": ["40", 0]}, "class_type": "IPAdapter", "_meta": {"title": "IPAdapter"}}, "38": {"inputs": {"preset": "PLUS (high strength)", "model": ["30", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "40": {"inputs": {"image": "4fkgc02tcs241.webp", "upload": "image"}, "class_type": "LoadImage //Inspire", "_meta": {"title": "Load Image (Inspire)"}}}