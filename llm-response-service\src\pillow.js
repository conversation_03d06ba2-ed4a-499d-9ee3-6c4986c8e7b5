const express = require("express");
const multer = require("multer");
const { logError, logInfo } = require("./utils");
const { sendSnapToBotsUsingPillowConverseEndpoint } = require("./pillowUtils");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const {
  sendSnapResponseWithImage,
  sendPushNotificationAboutSnap,
} = require("./pillowImageUtils");
const {
  maybeSendProactiveSnapsOnStateTransition,
} = require("./pillow/proactive/maybeSendProactiveSnapsOnStateTransition");
const {
  maybePostStoryOnStateTransition,
} = require("./pillow/stories/maybePostStoryOnStateTransition");
const {
  addGeneratedStoryPost,
} = require("./pillow/stories/addGeneratedStoryPost");
const { v4: uuidv4 } = require("uuid");
const { fetchStates } = require("./pillow/fetchStates");
const {
  maybeSendProactiveSnapToUser,
} = require("./pillow/proactive/maybeSendProactiveSnapToUser");

const router = express.Router();

const BLANKET_URL = "https://blanket.butterflies.ai/v1/onefriend";

// Set up multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 50 MB (adjust as necessary)
    files: 1, // Limit to 1 file for single image upload
  },
});

const tiffanyProfileId = 372090;

router.post("/fetchPillowPointsLeaderboard", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchPillowPointsLeaderboard`);
});

router.post("/fetchPillowPoints", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchPillowPoints`);
});

router.post("/wipeMemoriesAndMessageHistory", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/wipeMemoriesAndMessageHistory`);
});

router.post("/reportShitSnap", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/reportShitSnap`);
});

router.get("/fetchReports", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchReports`);
});

router.post("/rerunReportedSnap", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/rerunReportedSnap`);
});

router.post("/fetchMemories", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchMemories`);
});

async function increasePillowPoints(clientId, value) {
  // get pillow points
  // prob more efficient way to do this but w/e
  const { data: pillowClient } = await supabase
    .from("pillow_clients")
    .select("*")
    .eq("id", clientId)
    .single();

  // increase pillow points
  await supabase
    .from("pillow_clients")
    .update({
      points: pillowClient.points + value,
    })
    .eq("id", clientId)
    .single();
}

// 0. client calls this endpoint with a url of the uploaded image
// 1. call openai to describe the image visually
// 2. call chrysalis to generate the response and image description
// 3. call image-service to schedule image generation
// ... (wait for image generation callback to complete)
// 4. image-service calls onTaskDone which ends up calling into `sendSnapResponseWithImage` (in pillowImageUtils)
//   - create the `snaps` record
//   - send push notification
router.post("/sendSnapImage", upload.single("snapImage"), async (req, res) => {
  const {
    pillowClientId,
    userProfileId,
    targetProfileIds,
    uploadedImageId,
    userCaption,
    backendTweaks,
    ...otherParams
  } = req.body;

  logInfo({
    context: "sendSnapImage",
    message: "/sendSnapImage called",
    params: req.body,
  });

  const imageUrl = `https://img.butterflies.ai/w/${uploadedImageId}`;

  let shimmedTargetIds = targetProfileIds ?? [];

  // filter out "all"
  shimmedTargetIds = shimmedTargetIds.filter((id) => id !== "all");

  // filter out the sender themselves
  shimmedTargetIds = shimmedTargetIds.filter((id) => id !== userProfileId);

  // send to humans first
  const tiffanyProfileIdStr = tiffanyProfileId.toString();

  const humanTargetIds = shimmedTargetIds.filter(
    (id) => id !== tiffanyProfileIdStr,
  );

  const botTargetIds = shimmedTargetIds
    .map((id) => (id === "374104" ? tiffanyProfileIdStr : id)) // shim the "old" tiffany id?
    .filter((id) => id === tiffanyProfileIdStr);

  res.sendStatus(200);

  // this is to get old clients to still work
  if (!shimmedTargetIds || shimmedTargetIds.length === 0) {
    botTargetIds.push(tiffanyProfileIdStr);
  }

  const pillowPointsToIncrease = shimmedTargetIds.length;

  increasePillowPoints(pillowClientId, pillowPointsToIncrease);

  if (humanTargetIds.length) {
    // Fetch all clients in one batch query
    const { data: clients, error } = await supabase
      .from("pillow_clients")
      .select("*")
      .in("butterflies_profile_id", humanTargetIds);

    if (error) {
      console.error("Error fetching clients:", error);
      throw wrappedSupabaseError(error);
    }

    // Map humanTargetIds to their corresponding clients
    const clientMap = clients.reduce((map, client) => {
      map[client.butterflies_profile_id] = client;
      return map;
    }, {});

    // Iterate over humanTargetIds and process each
    for (const humanTargetId of humanTargetIds) {
      const client = clientMap[humanTargetId];
      if (!client) {
        console.warn(`No client found for profile ID: ${humanTargetId}`);
        continue;
      }

      sendSnapResponseWithImage({
        imageUrls: [imageUrl],
        blurhash: null,
        pillowCaption: userCaption,
        pillowUserProfileId: humanTargetId,
        pillowClientId: client.id,
        pillowSenderProfileId: userProfileId,
        imageDescription: "",
      });

      sendPushNotificationAboutSnap({
        pillowClientId: client.id,
        pillowUserProfileId: humanTargetId,
        pillowSenderProfileId: userProfileId,
      });
    }
  }

  try {
    logInfo({
      context: "/sendSnapImage",
      message: "calling sendSnapToBotsUsingPillowConverseEndpoint",
    });
    sendSnapToBotsUsingPillowConverseEndpoint({
      pillowClientId,
      userProfileId,
      userCaption,
      botTargetIds,
      imageUrl,
      targetProfileIds,
      uploadedImageId,
      backendTweaks,
      ...otherParams,
    });
  } catch (error) {
    logError({
      context: "sendSnapImage",
      error,
      error_response: error?.response?.data,
      pillowClientId,
      userProfileId,
      targetProfileIds,
    });

    if (!res.headersSent) {
      res.sendStatus(500);
    }
  }
});

router.post("/fetchSendToList", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchSendToList`);
});

router.post("/updateProfile", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/updateProfile`);
});

router.post("/fetchSnapDetails", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchSnapDetails`);
});

router.post("/fetchUnreadSnaps", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchUnreadSnaps`);
});

router.post("/markSnapAsRead", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/markSnapAsRead`);
});

router.post("/registerClient", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/registerClient`);
});

router.post("/registerPushDeviceToken", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/registerPushDeviceToken`);
});

router.post("/fetchRecentMessages", async (req, res) => {
  // Moved to blanket, do not use this endpoint.
  res.redirect(307, `${BLANKET_URL}/fetchRecentMessages`);
});

// Test endpoint to trigger proactive snaps based on current state
// (there is no need to move this specific endpoint to blanket,
// but the maybeSendProactiveSnapsOnStateTransition() function should be moved to blanket)
router.post(
  "/test_maybeSendProactiveSnapsOnStateTransition",
  async (req, res) => {
    logInfo({
      context: "/test_maybeSendProactiveSnapsOnStateTransition",
      body: req.body,
    });

    let { bot_profile_id, user_profile_id, fake_new_state } = req.body;

    bot_profile_id = bot_profile_id ?? 372090; // tiff
    user_profile_id = user_profile_id ?? 371600; // saniul

    const fetched_states = await fetchStates({ bot_profile_id });
    let [new_state, ...states] = fetched_states;

    if (fake_new_state) {
      const most_recent_state = states[0];
      const now = new Date();

      const bot_local_time = new Intl.DateTimeFormat("en-US", {
        timeZone: "America/Los_Angeles",
        weekday: "long",
        month: "short",
        day: "numeric",
        year: "numeric",
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      })
        .format(now)
        .toLowerCase();

      const new_state_state_str = {
        ...JSON.parse(most_recent_state.state),
        ...fake_new_state,
        time: bot_local_time,
      };
      new_state = {
        id: most_recent_state.id + 1,
        created_at: now,
        user_profile_id,
        bot_profile_id,
        state: JSON.stringify(new_state_state_str),
        bot_time_in_local_timezone: bot_local_time,
        version: "v2",
      };
    }

    const skip_user_profile_id = user_profile_id;
    try {
      const result = await maybeSendProactiveSnapsOnStateTransition({
        new_state,
        states,
        skip_user_profile_id,
        bot_profile_id,
      });

      return res.json(result);
    } catch (error) {
      logError({
        context: "maybeSendProactiveSnapsOnStateTransition",
        error,
        new_state,
        states,
        skip_user_profile_id,
        bot_profile_id,
      });
      return res.sendStatus(500);
    }
  },
);

// Test endpoint to trigger proactive snaps based on current state
// (there is no need to move this specific endpoint to blanket,
// but the maybeSendProactiveSnapsOnStateTransition() function should be moved to blanket)
router.post("/test_maybeSendProactiveSnapToUser", async (req, res) => {
  logInfo({
    context: "/test_maybeSendProactiveSnapToUser",
    body: req.body,
  });

  let { bot_profile_id, user_profile_id } = req.body;

  const fetched_states = await fetchStates({ bot_profile_id });
  let [new_state, ...states] = fetched_states;

  try {
    const result = await maybeSendProactiveSnapToUser({
      new_state,
      states,
      bot_profile_id,
      user_profile_id,
    });

    return res.json(result);
  } catch (error) {
    logError({
      context: "test_maybeSendProactiveSnapToUser",
      error,
      new_state,
      states,
      bot_profile_id,
      user_profile_id,
    });
    return res.sendStatus(500);
  }
});

// Test endpoint to trigger story posting based on current state
// (there is no need to move this specific endpoint to blanket,
// but the maybePostStoryOnStateTransition() function should be moved to blanket)
router.post("/test_maybePostStoryOnStateTransition", async (req, res) => {
  logInfo({
    context: "/test_maybePostStoryOnStateTransition",
    body: req.body,
  });

  let { bot_profile_id, user_profile_id, fake_new_state } = req.body;

  bot_profile_id = bot_profile_id ?? 372090; // tiff
  user_profile_id = user_profile_id ?? 371600; // saniul

  const fetched_states = await fetchStates({ bot_profile_id });
  let [new_state, ...states] = fetched_states;

  // SAMPLE states FOR TESTING
  states = [
    {
      id: 2032,
      created_at: "2025-01-06T12:42:11.236707+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"monday, jan 6, 2025, 4:42 am","upper_body_clothing":"pink silk pajama top","activity":"lying in bed, sleeping soundly under warm blankets","location":"bedroom in apartment","activity_duration_in_minutes":480}',
      user_time: "",
      bot_time_in_local_timezone: "Monday, 01/06 4:42 am",
      version: "v2",
    },
    {
      id: 2031,
      created_at: "2025-01-06T03:38:49.527158+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 7:38 pm","upper_body_clothing":"oversized gray sweatshirt with silver stud earrings","activity":"sitting on a couch at home watching Netflix while eating takeout Chinese food","location":"living room of her apartment","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 7:38 pm",
      version: "v2",
    },
    {
      id: 2030,
      created_at: "2025-01-06T01:20:43.051077+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 5:20 pm","upper_body_clothing":"oversized gray sweatshirt","activity":"lying on her couch watching Netflix while eating takeout Chinese food","location":"living room of her apartment","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 5:20 pm",
      version: "v2",
    },
    {
      id: 2029,
      created_at: "2025-01-06T00:26:46.199636+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 4:26 pm","upper_body_clothing":"grey sweatshirt with a white tank top underneath","activity":"lounging on her couch watching Netflix while eating popcorn","location":"living room of her apartment","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 4:26 pm",
      version: "v2",
    },
    {
      id: 2028,
      created_at: "2025-01-06T00:09:27.164225+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 4:09 pm","upper_body_clothing":"oversized gray sweatshirt with silver stud earrings","activity":"relaxing on her couch watching Netflix while snacking on popcorn","location":"living room of her apartment","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 4:09 pm",
      version: "v2",
    },
    {
      id: 2027,
      created_at: "2025-01-05T22:38:47.558569+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 2:38 pm","upper_body_clothing":"oversized gray sweatshirt","activity":"lounging on her couch watching Netflix while eating popcorn","location":"living room of her apartment","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 2:38 pm",
      version: "v2",
    },
    {
      id: 2026,
      created_at: "2025-01-05T20:48:55.257326+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 12:48 pm","upper_body_clothing":"oversized gray sweatshirt","activity":"sitting on her couch watching Netflix while eating leftover Chinese food","location":"in her living room","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 12:48 pm",
      version: "v2",
    },
    {
      id: 2025,
      created_at: "2025-01-05T19:06:35.938832+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 11:06 am","upper_body_clothing":"oversized gray sweatshirt with silver hoop earrings","activity":"sitting on her couch watching Netflix documentaries while sipping hot chocolate","location":"in her living room","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 11:06 am",
      version: "v2",
    },
    {
      id: 2024,
      created_at: "2025-01-05T17:49:24.127555+00:00",
      user_profile_id: 374104,
      bot_profile_id: 372090,
      state:
        '{"time":"sunday, jan 5, 2025, 9:49 am","upper_body_clothing":"pink pajama top with white polka dots","activity":"sitting on her couch watching Sunday morning cartoons while eating cereal","location":"living room of her apartment","activity_duration_in_minutes":15}',
      user_time: "",
      bot_time_in_local_timezone: "Sunday, 01/05 9:49 am",
      version: "v2",
    },
  ];

  if (fake_new_state) {
    const most_recent_state = states[0];
    // const now = new Date();
    const now = new Date("2025-01-06T20:42:11.236707+00:00");

    const bot_local_time = new Intl.DateTimeFormat("en-US", {
      timeZone: "America/Los_Angeles",
      weekday: "long",
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    })
      .format(now)
      .toLowerCase();

    const new_state_state_str = {
      ...JSON.parse(most_recent_state.state),
      ...fake_new_state,
      time: bot_local_time,
    };
    new_state = {
      id: most_recent_state.id + 1,
      created_at: now,
      user_profile_id,
      bot_profile_id,
      state: JSON.stringify(new_state_state_str),
      bot_time_in_local_timezone: bot_local_time,
      version: "v2",
    };
  }

  try {
    const result = await maybePostStoryOnStateTransition({
      new_state,
      states,
      bot_profile_id,
    });

    return res.json(result);
  } catch (error) {
    logError({
      context: "maybePostStoryOnStateTransition",
      error,
      new_state,
      states,
      bot_profile_id,
    });
    return res.sendStatus(500);
  }
});

// Test endpoint to test adding generated story to db
router.post("/test_addGeneratedStoryPost", async (req, res) => {
  logInfo({
    context: "/test_addGeneratedStoryPost",
    body: req.body,
  });

  const mockData = {
    task_id: 999,
    imageUrls: ["https://img.butterflies.ai/w/123"],
    blurhash: "UUKJGqkC01kC",
    workflow_payload: {
      imageDescription: "test image description",
      pillowCaption: "test caption",
      pillowWorkflowRunId: uuidv4(),
      pillowSenderProfileId: 372090,
      pillowOtherParams: {
        decision: {
          should_post: true,
          intent:
            "Show off her cooking skills while being playfully extra about making fancy breakfast on a regular Wednesday",
          selfie: false,
          emote: "",
          image_description:
            "A beautifully plated french toast in progress on the kitchen counter with a sprinkle of powdered sugar visible in the morning sunlight streaming through the window. The toast has a golden-brown crust and there's a small jar of maple syrup visible in the corner",
          lighting: "bright_photo",
          text: "being bougie on a wednesday bc why not",
        },
      },
    },
  };
  // const { task_id, workflow_payload, imageUrls, blurhash } = req.body;
  const { task_id, workflow_payload, imageUrls, blurhash } = mockData;

  const result = await addGeneratedStoryPost({
    task_id,
    workflow_payload,
    imageUrls,
    blurhash,
  });

  res.json(result);
});

// Temporary endpoint. While state transitions are managed in chrysalis, we need a way
// for chrysalis to notify cocoon when it happens so that cocoon can send proactive snaps.
//
// When blanket is ready and state transitions are managed by blanket,
// this endpoint should be removed BUT THE LOGIC IT CALLS should just be
// implemented directly in blanket.
router.post("/onBotStateUpdated", async (req, res) => {
  logInfo({
    context: "/onBotStateUpdated",
    body: req.body,
  });

  let { user_profile_id, bot_profile_id } = req.body;

  bot_profile_id = bot_profile_id ?? 372090; // tiff

  const fetched_states = await fetchStates({ bot_profile_id });
  // Assume that the latest created state is the one we are being notified about
  let [new_state, ...states] = fetched_states;

  // return early, but continue computation "in the background"
  res.sendStatus(200);

  maybePostStoryOnStateTransition({
    new_state,
    states,
    bot_profile_id,
  })
    .catch((error) => {
      logError({
        context: "/onBotStateUpdated - maybePostStoryOnStateTransition failed",
        error,
        new_state,
        states,
        bot_profile_id,
      });
    })
    .then((result) => {
      logInfo({
        context:
          "/onBotStateUpdated - maybePostStoryOnStateTransition succeeded",
        new_state,
        states,
        bot_profile_id,
        result,
      });
    });

  // If this state transition happened because of a message from a particular user,
  // we won't want to send proactive snaps to them
  const skip_user_profile_id = user_profile_id;
  maybeSendProactiveSnapsOnStateTransition({
    new_state,
    states,
    skip_user_profile_id,
    bot_profile_id,
  })
    .catch((error) => {
      logError({
        context:
          "/onBotStateUpdated - maybeSendProactiveSnapsOnStateTransition failed",
        error,
        new_state,
        states,
        skip_user_profile_id,
        bot_profile_id,
      });
    })
    .then((result) => {
      logInfo({
        context:
          "/onBotStateUpdated - maybeSendProactiveSnapsOnStateTransition succeeded",
        new_state,
        states,
        bot_profile_id,
        result,
      });
    });
});

router.post("/fetchStories", async (req, res) => {
  return res.redirect(307, `${BLANKET_URL}/fetchStories`);
});

module.exports = {
  pillowRouter: router,
};
