/**
 * Voice generation and management helpers
 *
 * This module provides functions for generating and managing voices using Hume.ai.
 * It includes functions for:
 * - Generating voices based on profile IDs or descriptions
 * - Converting text to speech using generated voices
 * - Uploading voice audio to Google Cloud Storage
 * - Managing voice IDs in the database
 */

const axios = require("axios");
const { supabase } = require("./supabaseClient");
const { generateBio } = require("./llmHelper");
const { callAndLogOpenAI } = require("./llm");
const { Storage } = require("@google-cloud/storage");

const bucketName = process.env.GCS_BUCKET_NAME || "butterflies-images-v1-us";
const bucketPath = "videos";
const HUME_API_KEY =
  process.env.HUME_API_KEY ??
  "MGOkvA9xC9GWnsWBKR2AZMuEcUtEjmSgNYLbVvdbi2hIxTpH";

/**
 * Generate a Hume.ai voice for a profile
 * @param {string} profileId - The profile ID to generate a voice for
 * @returns {Promise<string>} - The generated voice ID
 */
async function generateHumeVoiceForProfileId(profileId) {
  try {
    // Fetch bot profile with joined data
    const { data: bot, error: botError } = await supabase
      .from("bots")
      .select(
        `
      *
    `,
      )
      .eq("profile_id", profileId)
      .single();

    if (botError) {
      console.error("Error fetching profile:", botError);
      throw new Error("Error fetching profile");
    }

    if (!bot) {
      throw new Error("Error fetching profile");
    }

    let prompt = `Describe in one sentence what kind of voice this character would have. Be very detailed about the voice characteristics including age, gender, accent, tone, and speech patterns. Only return the voice description. (NOTE: If it is a child, describe them as a young adult).

${generateBio(bot)}
`;
    const payload = {
      messages: [
        {
          role: "system",
          content: prompt,
        },
      ],
      model: "gpt-4o",
    };
    let result = await callAndLogOpenAI("OAI:PostRegenerate", payload, {
      timeout: 8 * 1000,
    });

    const voice_description = result.choices[0].message.content;

    console.log("This is the voice_description", voice_description);

    console.log(`Generating Hume.ai voice for profile ID: ${profileId}`);

    // Generate voice audio with the description - this will create a new voice
    // and return the voice ID from the first API call
    const { voiceId } = await generateAndUploadVoiceAudio(
      null, // No existing voice ID, will use description instead
      "Hello, this is a test.", // Simple test text
      profileId,
      "initial_generation",
      voice_description,
    );

    // Save the voice ID to the database
    const { error: insertError } = await supabase
      .from("video_clip_voices")
      .insert({
        profile_id: profileId,
        voice_id: voiceId,
        created_at: new Date().toISOString(),
      });

    if (insertError) {
      console.error("Error saving voice ID to database:", insertError);
      // Continue anyway since we have the voice ID
    }

    return voiceId;
  } catch (error) {
    console.error("Error generating Hume.ai voice:", error);
    throw error;
  }
}

/**
 * Generate voice audio from Hume.ai and upload it to GCS
 * @param {string|null} voiceId - The Hume.ai voice ID to use, or null if using description
 * @param {string} text - The text to convert to speech
 * @param {string} profileId - The profile ID associated with this voice
 * @param {string} partId - A unique identifier for this part (for filename)
 * @param {string} [voiceDescription] - Voice description if voiceId is null
 * @returns {Promise<{voiceUrl: string, voiceId: string}>} - The GCS URL of the uploaded audio file and the voice ID used
 */
async function generateAndUploadVoiceAudio(
  voiceId,
  text,
  profileId,
  partId,
  voiceDescription = null,
) {
  try {
    console.log(
      `Generating voice audio for text: "${text}" ${
        voiceId
          ? `using voice ID: ${voiceId}`
          : `using description: ${voiceDescription}`
      }`,
    );

    // Prepare the utterance based on whether we have a voice ID or description
    const utterance = voiceId
      ? {
          text,
          voice: {
            id: voiceId,
          },
        }
      : {
          text,
          description: voiceDescription,
        };

    // Generate speech from text using Hume.ai
    const audioResponse = await axios.post(
      "https://api.hume.ai/v0/tts",
      {
        utterances: [utterance],
        format: {
          type: "mp3",
        },
        num_generations: 1,
      },
      {
        headers: {
          "X-Hume-Api-Key": HUME_API_KEY,
          "Content-Type": "application/json",
        },
      },
    );

    console.log("TTS API response received");

    if (!audioResponse.data || !audioResponse.data.generations) {
      throw new Error("Failed to generate audio from Hume.ai");
    }

    // If we don't have a voice ID yet, extract it from the response
    // The first generation will create a voice and return a generation_id
    if (!voiceId && audioResponse.data.generations[0].voice_id) {
      voiceId = audioResponse.data.generations[0].voice_id;
      console.log(`Extracted new voice ID from generation: ${voiceId}`);
    }

    // The response contains base64 encoded audio
    const audioBuffer = Buffer.from(
      audioResponse.data.generations[0].audio,
      "base64",
    );

    // Create a unique filename
    const timestamp = Date.now();
    const filename = `voice_${profileId}_${partId}_${timestamp}.mp3`;
    const gcsPath = `${bucketPath}/voices/${filename}`;

    // Initialize GCS storage
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(gcsPath);

    // Upload the audio file to GCS
    await file.save(audioBuffer, {
      metadata: {
        contentType: "audio/mp3",
      },
    });

    // Generate the public URL
    const gcsUrl = `https://storage.googleapis.com/${bucketName}/${gcsPath}`;
    console.log(`Voice audio uploaded to GCS: ${gcsUrl}`);

    // Return both the voice URL and the voice ID
    return { voiceUrl: gcsUrl, voiceId };
  } catch (error) {
    console.error("Error generating and uploading voice audio:", error);
    throw error;
  }
}

/**
 * Check if a voice exists for a profile and return it, or generate a new one
 * @param {string} profileId - The profile ID to check or generate a voice for
 * @returns {Promise<string>} - The voice ID
 */
async function getOrCreateVoiceForProfile(profileId) {
  try {
    console.log(`Checking for existing voice ID for profile ${profileId}`);

    // Check if a voice already exists for this profile
    const { data: existingVoice, error: voiceError } = await supabase
      .from("video_clip_voices")
      .select("voice_id")
      .eq("profile_id", profileId)
      .single();

    if (!voiceError && existingVoice && existingVoice.voice_id) {
      console.log(
        `Found existing voice ID for profile ${profileId}: ${existingVoice.voice_id}`,
      );
      return existingVoice.voice_id;
    } else {
      console.log(`Generating new voice for profile ${profileId}`);
      // Generate a new voice for this profile
      const voiceId = await generateHumeVoiceForProfileId(profileId);
      console.log(
        `Generated new voice ID for profile ${profileId}: ${voiceId}`,
      );
      return voiceId;
    }
  } catch (error) {
    console.error("Error in getOrCreateVoiceForProfile:", error);
    throw error;
  }
}

/**
 * Generate a voice using a description
 * @param {string} voiceDescription - Description of the voice to generate
 * @returns {Promise<string>} - The generated voice ID
 */
async function generateVoiceWithDescription(voiceDescription) {
  try {
    console.log(`Generating voice with description: "${voiceDescription}"`);

    // Call Hume.ai API to generate a voice
    const response = await axios.post(
      "https://api.hume.ai/v0/tts",
      {
        utterances: [
          {
            text: "This is a test of the voice generation system.",
            description: voiceDescription,
          },
        ],
        format: {
          type: "mp3",
        },
        num_generations: 1,
      },
      {
        headers: {
          "X-Hume-Api-Key": HUME_API_KEY,
          "Content-Type": "application/json",
        },
      },
    );

    if (
      !response.data ||
      !response.data.generations ||
      !response.data.generations[0].voice_id
    ) {
      throw new Error("Failed to generate voice from description");
    }

    const voiceId = response.data.generations[0].voice_id;
    console.log(`Successfully generated voice ID: ${voiceId}`);

    return voiceId;
  } catch (error) {
    console.error("Error generating voice with description:", error);
    throw error;
  }
}

module.exports = {
  generateHumeVoiceForProfileId,
  generateAndUploadVoiceAudio,
  getOrCreateVoiceForProfile,
  generateVoiceWithDescription,
};
