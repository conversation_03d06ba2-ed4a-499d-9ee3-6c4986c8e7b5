const { createClient } = require("@supabase/supabase-js");
const { logError, logWarn } = require("./logUtils");
const { messageWithCauses } = require("pony-cause");
const { tracer } = require("./instrumentation/tracer");
require("dotenv").config();

const supabaseUrl = process.env.SUPABASE_URL ?? "http://localhost:54321";
const supabaseSecretKey =
  process.env.SUPABASE_SECRET_KEY ??
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU";

const supabase = createClient(supabaseUrl, supabaseSecretKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
    detectSessionInUrl: false,
  },
});

const errorWithFilteredStackFrames = (error) => {
  const stack = (error.stack || "").split("\n");
  const filteredStack = stack.filter(
    (frame) => !frame.includes("wrappedSupabaseError"),
  );
  error.stack = filteredStack.join("\n");
  return error;
};

const logSupabaseResultErrorWithDetails =
  (forceMessage, { context, ...rest }) =>
  ({ data, error: supabaseError }) => {
    if (!supabaseError) {
      return;
    }

    const error = wrappedSupabaseError(supabaseError, forceMessage);
    logError({
      context,
      error,
      ...rest,
    });
  };

function wrappedSupabaseError(supabaseError, forceMessage) {
  if (!supabaseError) {
    const error = new Error("No underlying supabase error, check callsite");
    return errorWithFilteredStackFrames(error);
  }

  if (forceMessage) {
    const error = new Error(forceMessage, { cause: supabaseError });
    return errorWithFilteredStackFrames(error);
  }

  if (supabaseError instanceof String || typeof supabaseError === "string") {
    const error = new Error(supabaseError);
    return errorWithFilteredStackFrames(error);
  }

  if (supabaseError.stack || !Error.captureStackTrace) {
    const error = new Error(`${supabaseError.message}`, {
      cause: supabaseError,
    });
    return errorWithFilteredStackFrames(error);
  }

  // Use V8 API to capture the stack trace and inject it into the object without
  // needing to create a new Error object, which would make
  try {
    Error.captureStackTrace(supabaseError, wrappedSupabaseError);
    return supabaseError;
  } catch (e) {
    logWarn({
      context: "*** Failed to inject stack trace into supabase error",
      message: messageWithCauses(e) || e.message || e,
    });

    const message =
      supabaseError.message || "Unknown error, check logs and callsite";
    const error = new Error(message, {
      cause: supabaseError,
    });
    return error;
  }
}

const delay = (milliseconds) =>
  new Promise((resolve) => setTimeout(resolve, milliseconds));

const retrySupabaseOperation = async (
  operation,
  name,
  maximumRetries = 3,
  delayTime = 100,
) => {
  return await tracer.withActiveSpan(name, async () => {
    for (let i = 0; i < maximumRetries; i++) {
      const { data, error: supabaseError } = await operation();
      if (supabaseError) {
        const error = wrappedSupabaseError(supabaseError);
        logError({
          context: name,
          error,
        });
        if (i >= maximumRetries - 1) {
          // If it's the last run and it's still failing create a new error.
          throw new Error(
            `Operation ${name} failed after ${maximumRetries} attempts`,
            {
              cause: error,
            },
          );
        }
        await delay(delayTime);
      } else {
        return data;
      }
    }
  });
};

module.exports = {
  supabase,
  supabaseUrl,
  supabaseSecretKey,
  retrySupabaseOperation,
  wrappedSupabaseError,
  logSupabaseResultErrorWithDetails,
};
