jest.setTimeout(30 * 1000);

const {
  callAndLogVertexAIMaaS,
  callAndLogVertexAI,
} = require("../../src/llm.js");

const { jill, dr } = require("../common/personas.js");

test("it should say hello", async () => {
  const result = await callAndLogVertexAIMaaS(
    "FireworksAI:Instruct:GenerateMemories",
    {
      messages: [{ role: "user", content: "Only say hello" }],
      top_k: 10,
      top_p: 0.68,
      temperature: 1.1,
      max_tokens: 50,
    },
  );

  console.log(result);

  expect(result).toBeTruthy();
});
