async function* cloneImageRejector() {
  yield {
    type: "text",
    text: "As a clone, I am unable to send you any images at this time.",
  };
}

async function* cloneSensitiveRejector() {
  yield {
    type: "text",
    text: "As a clone, I can't talk about this topic. Let's talk about something else.",
  };
}

async function* childSensitiveRejector(_ctx, { imageRequested }) {
  yield {
    type: "text",
    text: `I am unable to send you any explicit ${imageRequested ? "images" : "message"}.`,
  };
}

async function* outOfImageQuotaRejector() {
  yield {
    type: "text",
    text: "I am unable to send you anymore images today. Let's talk about something.",
  };
}

module.exports = {
  cloneImageRejector,
  cloneSensitiveRejector,
  childSensitiveRejector,
  outOfImageQuotaRejector,
};
