{"last_node_id": 115, "last_link_id": 339, "nodes": [{"id": 102, "type": "LoadImage", "pos": {"0": 270, "1": 780}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [309, 312], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-2481472.3000000007.png [input]", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 103, "type": "LoadImage", "pos": {"0": 495, "1": 780}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [310, 311], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-2488434.200000001.png [input]", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "InstantIDModelLoader", "pos": {"0": 720, "1": 555}, "size": {"0": 325, "1": 58}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [197, 238], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InstantIDModelLoader"}, "widgets_values": ["ip-adapter.bin"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "InstantIDFaceAnalysis", "pos": {"0": 720, "1": 645}, "size": {"0": 325, "1": 58}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [198, 239], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis"}, "widgets_values": ["CPU"], "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "ControlNetLoader", "pos": {"0": 720, "1": 750}, "size": {"0": 325, "1": 58}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [199, 240], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["diffusion_pytorch_model.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 104, "type": "MaskComposite", "pos": {"0": 720, "1": 1005}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "destination", "type": "MASK", "link": 312}, {"name": "source", "type": "MASK", "link": 311}], "outputs": [{"name": "MASK", "type": "MASK", "links": [313], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskComposite"}, "widgets_values": [0, 0, "add"], "color": "#322", "bgcolor": "#533"}, {"id": 80, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 1080, "1": 450}, "size": {"0": 228.39999389648438, "1": 50}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 290}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 287}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [288], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "color": "#322", "bgcolor": "#533"}, {"id": 108, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 1080, "1": 555}, "size": {"0": 228.39999389648438, "1": 46}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 321}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 320}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [322], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "color": "#322", "bgcolor": "#533"}, {"id": 40, "type": "CLIPTextEncode", "pos": {"0": 270, "1": 630}, "size": {"0": 210, "1": 100}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 123}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [204, 278], "slot_index": 0, "shape": 3}], "title": "NEGATIVE PROMPT", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["hands"], "color": "#322", "bgcolor": "#533"}, {"id": 13, "type": "LoadImage", "pos": {"0": 45, "1": 105}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [214], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "title": "FACE 1", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["woman.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 78, "type": "LoadImage", "pos": {"0": 270, "1": 105}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [246], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "title": "FACE 2", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["man.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 105, "type": "InvertMask", "pos": {"0": 1080, "1": 645}, "size": {"0": 224.35960388183594, "1": 26}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 313}], "outputs": [{"name": "MASK", "type": "MASK", "links": [315], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InvertMask"}, "color": "#322", "bgcolor": "#533"}, {"id": 8, "type": "VAEDecode", "pos": {"0": 1080, "1": 720}, "size": {"0": 225, "1": 50}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 254}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [326], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "color": "#322", "bgcolor": "#533"}, {"id": 81, "type": "Reroute", "pos": {"0": 1080, "1": 795}, "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 253}], "outputs": [{"name": "VAE", "type": "VAE", "links": [254], "slot_index": 0}], "properties": {"showOutputText": true, "horizontal": false}, "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "CLIPTextEncode", "pos": {"0": 45, "1": 450}, "size": {"0": 210, "1": 125}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 122}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [203], "slot_index": 0, "shape": 3}], "title": "PROMPT 1", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["close-up, portrait photo, a woman, guardians of galaxy cinematic shot style, red hero suit"], "color": "#232", "bgcolor": "#353"}, {"id": 89, "type": "CLIPTextEncode", "pos": {"0": 270, "1": 450}, "size": {"0": 210, "1": 125}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 266}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [272], "slot_index": 0, "shape": 3}], "title": "PROMPT 2", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["close-up,portrait photo, a man, guardians of galaxy cinematic shot style, blue hero suit"], "color": "#232", "bgcolor": "#353"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": {"0": 720, "1": 855}, "size": {"0": 325, "1": 100}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [206], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [122, 123, 266, 314], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [253, 330], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["realvisxlV40_v40LightningBakedvae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 115, "type": "MaskBlur+", "pos": {"0": -345, "1": 480}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 338}], "outputs": [{"name": "MASK", "type": "MASK", "links": [339], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskBlur+"}, "widgets_values": [150, "auto"], "color": "#322", "bgcolor": "#533"}, {"id": 112, "type": "LoadImage", "pos": {"0": -345, "1": 105}, "size": {"0": 315, "1": 314.0000305175781}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [331], "shape": 3}, {"name": "MASK", "type": "MASK", "links": [338], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-6424735.099999994.png [input]", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 100, "type": "LoadImage", "pos": {"0": 45, "1": 780}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [306, 307], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["woman2.jpg", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 77, "type": "ApplyInstantID", "pos": {"0": 495, "1": 105}, "size": {"0": 210, "1": 300}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 238}, {"name": "insightface", "type": "FACEANALYSIS", "link": 239}, {"name": "control_net", "type": "CONTROL_NET", "link": 240}, {"name": "image", "type": "IMAGE", "link": 246}, {"name": "model", "type": "MODEL", "link": 255}, {"name": "positive", "type": "CONDITIONING", "link": 272}, {"name": "negative", "type": "CONDITIONING", "link": 278}, {"name": "image_kps", "type": "IMAGE", "link": 308}, {"name": "mask", "type": "MASK", "link": 310}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "slot_index": 0, "shape": 3}, {"name": "POSITIVE", "type": "CONDITIONING", "links": [247], "slot_index": 1, "shape": 3}, {"name": "NEGATIVE", "type": "CONDITIONING", "links": [290], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "ApplyInstantID"}, "widgets_values": [1, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 720, "1": 105}, "size": {"0": 325, "1": 262}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 325}, {"name": "positive", "type": "CONDITIONING", "link": 322}, {"name": "negative", "type": "CONDITIONING", "link": 288}, {"name": "latent_image", "type": "LATENT", "link": 332}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [22, "fixed", 10, 2, "ddpm", "exponential", 1], "color": "#322", "bgcolor": "#533"}, {"id": 113, "type": "VAEEncodeForInpaint", "pos": {"0": 720, "1": 405}, "size": {"0": 315, "1": 98}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 331}, {"name": "vae", "type": "VAE", "link": 330}, {"name": "mask", "type": "MASK", "link": 339}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [332], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEEncodeForInpaint"}, "widgets_values": [6], "color": "#322", "bgcolor": "#533"}, {"id": 101, "type": "ImageFlip+", "pos": {"0": 1080, "1": 105}, "size": {"0": 225, "1": 58}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 307}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [308], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageFlip+"}, "widgets_values": ["x"], "color": "#322", "bgcolor": "#533"}, {"id": 79, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 1080, "1": 345}, "size": {"0": 228.39999389648438, "1": 50}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 247}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 248}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [321], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "color": "#322", "bgcolor": "#533"}, {"id": 110, "type": "SaveImage", "pos": {"0": 1320, "1": 105}, "size": {"0": 575, "1": 675}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 326}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"], "color": "#322", "bgcolor": "#533"}, {"id": 107, "type": "ConditioningSetMask", "pos": {"0": 1080, "1": 210}, "size": {"0": 225, "1": 102}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 319}, {"name": "mask", "type": "MASK", "link": 315}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [320], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConditioningSetMask"}, "widgets_values": [1, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 60, "type": "ApplyInstantID", "pos": {"0": 495, "1": 450}, "size": {"0": 210, "1": 275}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 197}, {"name": "insightface", "type": "FACEANALYSIS", "link": 198}, {"name": "control_net", "type": "CONTROL_NET", "link": 199}, {"name": "image", "type": "IMAGE", "link": 214}, {"name": "model", "type": "MODEL", "link": 206}, {"name": "positive", "type": "CONDITIONING", "link": 203}, {"name": "negative", "type": "CONDITIONING", "link": 204}, {"name": "image_kps", "type": "IMAGE", "link": 306}, {"name": "mask", "type": "MASK", "link": 309}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [255, 325], "slot_index": 0, "shape": 3}, {"name": "POSITIVE", "type": "CONDITIONING", "links": [248], "slot_index": 1, "shape": 3}, {"name": "NEGATIVE", "type": "CONDITIONING", "links": [287], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "ApplyInstantID"}, "widgets_values": [1, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 106, "type": "CLIPTextEncode", "pos": {"0": 45, "1": 630}, "size": {"0": 210, "1": 100}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 314}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [319], "slot_index": 0, "shape": 3}], "title": "BACKGROUND PROMPT", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["guardians of galaxy cinematic background"], "color": "#232", "bgcolor": "#353"}], "links": [[7, 3, 0, 8, 0, "LATENT"], [122, 4, 1, 39, 0, "CLIP"], [123, 4, 1, 40, 0, "CLIP"], [197, 11, 0, 60, 0, "INSTANTID"], [198, 38, 0, 60, 1, "FACEANALYSIS"], [199, 16, 0, 60, 2, "CONTROL_NET"], [203, 39, 0, 60, 5, "CONDITIONING"], [204, 40, 0, 60, 6, "CONDITIONING"], [206, 4, 0, 60, 4, "MODEL"], [214, 13, 0, 60, 3, "IMAGE"], [238, 11, 0, 77, 0, "INSTANTID"], [239, 38, 0, 77, 1, "FACEANALYSIS"], [240, 16, 0, 77, 2, "CONTROL_NET"], [246, 78, 0, 77, 3, "IMAGE"], [247, 77, 1, 79, 0, "CONDITIONING"], [248, 60, 1, 79, 1, "CONDITIONING"], [253, 4, 2, 81, 0, "*"], [254, 81, 0, 8, 1, "VAE"], [255, 60, 0, 77, 4, "MODEL"], [266, 4, 1, 89, 0, "CLIP"], [272, 89, 0, 77, 5, "CONDITIONING"], [278, 40, 0, 77, 6, "CONDITIONING"], [287, 60, 2, 80, 1, "CONDITIONING"], [288, 80, 0, 3, 2, "CONDITIONING"], [290, 77, 2, 80, 0, "CONDITIONING"], [306, 100, 0, 60, 7, "IMAGE"], [307, 100, 0, 101, 0, "IMAGE"], [308, 101, 0, 77, 7, "IMAGE"], [309, 102, 1, 60, 8, "MASK"], [310, 103, 1, 77, 8, "MASK"], [311, 103, 1, 104, 1, "MASK"], [312, 102, 1, 104, 0, "MASK"], [313, 104, 0, 105, 0, "MASK"], [314, 4, 1, 106, 0, "CLIP"], [315, 105, 0, 107, 1, "MASK"], [319, 106, 0, 107, 0, "CONDITIONING"], [320, 107, 0, 108, 1, "CONDITIONING"], [321, 79, 0, 108, 0, "CONDITIONING"], [322, 108, 0, 3, 1, "CONDITIONING"], [325, 60, 0, 3, 0, "MODEL"], [326, 8, 0, 110, 0, "IMAGE"], [330, 4, 2, 113, 1, "VAE"], [331, 112, 0, 113, 0, "IMAGE"], [332, 113, 0, 3, 3, "LATENT"], [338, 112, 1, 115, 0, "MASK"], [339, 115, 0, 113, 2, "MASK"]], "groups": [{"title": "ID 2X", "bounding": [0, 0, 1925, 1150], "color": "#8AA", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5445000000000197, "offset": [547.9866864396455, 145.59919403220619]}, "info": {"name": "workflow", "author": "", "description": "", "version": "1", "created": "2024-09-14T18:56:21.849Z", "modified": "2024-09-14T18:56:21.849Z", "software": "ComfyUI"}}, "version": 0.4}