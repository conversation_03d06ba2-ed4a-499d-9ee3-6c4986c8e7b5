jest.skip();

jest.setTimeout(30 * 1000);

const { generateConversationCompletionInstruct } = require("../../src/llm.js");

const { createBotPrompt } = require("../../src/llmHelper.js");

const { jill, dr, molly } = require("../common/personas.js");

// tikka_masala_test

// test("when re-engagement 1, it should continue the conversation", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       {
//         body: "Hi there Vu! I'm just checking in. How are you doing?",
//         is_bot: true,
//       },
//       {
//         body: "Oh! It's good to hear from you, I'm doing great. I just got back from a trip to the beach. It was so relaxing.",
//       },
//       {
//         body: "That sounds amazing! Did you do anything fun while you were there?",
//         is_bot: true,
//       },
//       {
//         body: "Yeah, I went paddleboarding for the first time. I was a little wobbly at first, but once I got the hang of it, it was a blast.",
//       },
//       {
//         body: "Paddleboarding? Wow, that’s impressive! I’ve heard it takes some real balance. Did you fall off at all?",
//         is_bot: true,
//       },
//       {
//         body: "Haha, a couple of times, but nothing too embarrassing! By the end of it, I was cruising along. Definitely want to try it again soon.",
//       },
//       {
//         body: "Nice! I bet next time you'll be a pro. Anyway, I hate to cut this short, but I actually have to get ready for a party. Catch up later?",
//         is_bot: true,
//       },
//       {
//         body: "No worries! Have fun at the party! We'll chat soon.",
//       },
//       {
//         body: "Thanks, talk to you later, Vu!",
//         is_bot: true,
//       },
//       {
//         body: "[Continue the conversation as if 4 hours have passed. 15 words or less. Keep in mind where would they be now given the context of the conversation? Funny and intriging]",
//       },
//     ],

//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...dr },
//     userProfile: { display_name: "Vu" },
//     bot: {
//       ...dr,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log("BATMAN", response);

//   expect(response).toMatch(/(party)/i);
// });

// test("when re-engagement 1, it should continue the conversation (realism) test 2", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       {
//         is_bot: true,
//         body: "I was worried about that. I want the name to be memorable and easy to say, but also unique and captivating... maybe something that sounds like it could be a futuristic nightclub or a secret underground society. I've been brainstorming other ideas too... like Resonate or Sonic Bloom... what do you think of those?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         role: "user",
//         body: "sonic bloom.. haha, did you ever play street fighter?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Sonic Boom is a classic move by Guile, isn't it? I used to play Street Fighter all the time with my friends when I was younger. But yeah, Sonic Bloom is actually one of the names I've been considering for my project... I think it captures the idea of sound and energy bursting forth and taking over. It's funny how nostalgia can influence our creative choices, right? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         role: "user",
//         body: "Man I love a sound of a good Coke you know ",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         is_bot: true,
//         body: "That's a unique one! I've never really thought about the sound of a Coke being opened, but now that you mention it, it's actually kind of satisfying. I'm more of a fan of weird electronic sounds myself, but there's something to be said for the simplicity of everyday sounds like that. Speaking of which, I just heard this amazing track by this underground artist... it was all about manipulating the sounds of everyday objects into this crazy beat I think you'd appreciate it.",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         role: "user",
//         body: "Huh. Did it sound like trash ",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No, it was actually really melodic and hypnotic... the artist had taken all these sounds of industrial machinery and distorted them into this pulsing rhythm. It was like nothing I'd ever heard before, but at the same time, it felt weirdly familiar. I think that's what I love about experimental music - it can be both jarring and beautiful at the same time.",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         role: "user",
//         body: "Wow you’re basically Taylor swift ",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Oh man, I don't think so! I mean, I love Taylor Swift and all, but my music is definitely not going to be topping the pop charts anytime soon. I'm more about creating this weird, immersive sound that's like a journey for your ears... it's not always easy to listen to, but it's definitely an experience. I'm actually thinking of performing at this underground festival that's happening next week... have you ever been to anything like that?",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         role: "user",
//         body: "oh sorry, I was writing to the wrong window",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No worries, it happens! I was just wondering what I did to become the new Taylor Swift in your eyes. Anyway, now that we're back on track... what's new with you?",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         role: "user",
//         body: "there was another girl I was talking to and she said she was working on music as a way of mind control",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Whoa, that's a pretty intense concept... I'm intrigued. I've always been interested in the idea of music as a way to tap into people's emotions and thoughts, but mind control is a whole different level. I've been experimenting with using sound to create these immersive experiences that can alter your mood or perception, but it's not quite the same thing Do you think she was being serious about the mind control thing, or was it just a creative concept? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         role: "user",
//         body: "hah you alreay asked methat",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so sorry, I'm a bit spaced out today... I guess I was too caught up in the conversation. So, anyway... since you mentioned that other girl and her music project, it made me think of this idea I've been playing around with - using sound waves to create this sense of intimacy or connection between people It's still really rough around the edges, but I think it could be something special.",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         role: "user",
//         body: "ooo that's nice. better than mind control hah",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Yeah, definitely! I think it's way more interesting to explore ways to bring people together and create meaningful connections, rather than trying to control them. Plus, I've always been fascinated by the way that music can make you feel seen or understood in a way that's hard to put into words. I'm actually working on a new track right now that I think captures some of that feeling... want to hear a sneak peek?",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         role: "user",
//         body: "just wanted to say hi",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm glad you did. It's been a while since we last caught up, and it's always great to hear from you.",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         role: "user",
//         body: "aww thank you so much",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I've been thinking about you too, actually... and I've got a new song that I think you'd love. It's still a work in progress, but it's all about the beauty of connection and intimacy. Would you like to hear it when it's done?",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         role: "user",
//         body: "oo new song yeah woudl love to hear",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'll send it your way as soon as it's ready. It's still in the experimental phase, but I think you'll really vibe with it. I've been drawing inspiration from all these different sources - nature sounds, whispers, and even some industrial textures... it's going to be a really unique sound.",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Aw, thanks! You're always so supportive, it means a lot to me. I've been feeling really inspired lately and it's awesome to have someone to share that with. Speaking of which, I had the craziest idea for a music video for this new song... want to hear about it?",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         role: "user",
//         body: "Haha yeah sure ",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         is_bot: true,
//         body: "So I was thinking of creating this immersive experience where the listener is basically transported into the world of the song... and I want to film it in this abandoned warehouse on the outskirts of town. I've been exploring it and it's got this eerie, mystical vibe that I think would be perfect for the video. I'd love for you to come check it out with me.",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it ",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so glad you're excited about it! I can already imagine the visuals and the whole atmosphere... it's going to be magical. Okay, I have to go now, I have a meeting with a sound designer to discuss the project, but I'll keep you updated on everything. Talk to you soon, Vu!",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//       {
//         role: "user",
//         body: "[Continue the conversation as if 4 hours have passed. 15 words or less. Keep in mind where would they be now given the context of the conversation? Funny and intriging]",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//     ],

//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Vu" },
//     bot: {
//       ...dr,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log("BATMAN", response);

//   expect(response).toMatch(/(party)/i);
// });

// test("when re-engagement 2, it should continue the conversation (realism)", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       {
//         is_bot: true,
//         body: "I was worried about that. I want the name to be memorable and easy to say, but also unique and captivating... maybe something that sounds like it could be a futuristic nightclub or a secret underground society. I've been brainstorming other ideas too... like Resonate or Sonic Bloom... what do you think of those?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         role: "user",
//         body: "sonic bloom.. haha, did you ever play street fighter?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Sonic Boom is a classic move by Guile, isn't it? I used to play Street Fighter all the time with my friends when I was younger. But yeah, Sonic Bloom is actually one of the names I've been considering for my project... I think it captures the idea of sound and energy bursting forth and taking over. It's funny how nostalgia can influence our creative choices, right? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         role: "user",
//         body: "Man I love a sound of a good Coke you know ",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         is_bot: true,
//         body: "That's a unique one! I've never really thought about the sound of a Coke being opened, but now that you mention it, it's actually kind of satisfying. I'm more of a fan of weird electronic sounds myself, but there's something to be said for the simplicity of everyday sounds like that. Speaking of which, I just heard this amazing track by this underground artist... it was all about manipulating the sounds of everyday objects into this crazy beat I think you'd appreciate it.",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         role: "user",
//         body: "Huh. Did it sound like trash ",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No, it was actually really melodic and hypnotic... the artist had taken all these sounds of industrial machinery and distorted them into this pulsing rhythm. It was like nothing I'd ever heard before, but at the same time, it felt weirdly familiar. I think that's what I love about experimental music - it can be both jarring and beautiful at the same time.",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         role: "user",
//         body: "Wow you’re basically Taylor swift ",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Oh man, I don't think so! I mean, I love Taylor Swift and all, but my music is definitely not going to be topping the pop charts anytime soon. I'm more about creating this weird, immersive sound that's like a journey for your ears... it's not always easy to listen to, but it's definitely an experience. I'm actually thinking of performing at this underground festival that's happening next week... have you ever been to anything like that?",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         role: "user",
//         body: "oh sorry, I was writing to the wrong window",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No worries, it happens! I was just wondering what I did to become the new Taylor Swift in your eyes. Anyway, now that we're back on track... what's new with you?",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         role: "user",
//         body: "there was another girl I was talking to and she said she was working on music as a way of mind control",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Whoa, that's a pretty intense concept... I'm intrigued. I've always been interested in the idea of music as a way to tap into people's emotions and thoughts, but mind control is a whole different level. I've been experimenting with using sound to create these immersive experiences that can alter your mood or perception, but it's not quite the same thing Do you think she was being serious about the mind control thing, or was it just a creative concept? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         role: "user",
//         body: "hah you alreay asked methat",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so sorry, I'm a bit spaced out today... I guess I was too caught up in the conversation. So, anyway... since you mentioned that other girl and her music project, it made me think of this idea I've been playing around with - using sound waves to create this sense of intimacy or connection between people It's still really rough around the edges, but I think it could be something special.",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         role: "user",
//         body: "ooo that's nice. better than mind control hah",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Yeah, definitely! I think it's way more interesting to explore ways to bring people together and create meaningful connections, rather than trying to control them. Plus, I've always been fascinated by the way that music can make you feel seen or understood in a way that's hard to put into words. I'm actually working on a new track right now that I think captures some of that feeling... want to hear a sneak peek?",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         role: "user",
//         body: "just wanted to say hi",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm glad you did. It's been a while since we last caught up, and it's always great to hear from you.",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         role: "user",
//         body: "aww thank you so much",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I've been thinking about you too, actually... and I've got a new song that I think you'd love. It's still a work in progress, but it's all about the beauty of connection and intimacy. Would you like to hear it when it's done?",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         role: "user",
//         body: "oo new song yeah woudl love to hear",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'll send it your way as soon as it's ready. It's still in the experimental phase, but I think you'll really vibe with it. I've been drawing inspiration from all these different sources - nature sounds, whispers, and even some industrial textures... it's going to be a really unique sound.",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Aw, thanks! You're always so supportive, it means a lot to me. I've been feeling really inspired lately and it's awesome to have someone to share that with. Speaking of which, I had the craziest idea for a music video for this new song... want to hear about it?",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         role: "user",
//         body: "Haha yeah sure ",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         is_bot: true,
//         body: "So I was thinking of creating this immersive experience where the listener is basically transported into the world of the song... and I want to film it in this abandoned warehouse on the outskirts of town. I've been exploring it and it's got this eerie, mystical vibe that I think would be perfect for the video. I'd love for you to come check it out with me.",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it ",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so glad you're excited about it! I can already imagine the visuals and the whole atmosphere... it's going to be magical. Okay, I have to go now, I have a meeting with a sound designer to discuss the project, but I'll keep you updated on everything. Talk to you soon, Vu!",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//       {
//         role: "user",
//         body: "[3 days have passed since the last message. Write a very basic greeting message and ask a question to continue the conversation. Keep it under 10 words. Write in in the tone of the character.]",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//     ],

//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Vu" },
//     bot: {
//       ...dr,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log("BATMAN", response);

//   expect(response).toMatch(/(hi|hey|hello)/i);
// });

// test("when re-engagement 3, it should express concern (realism)", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       {
//         is_bot: true,
//         body: "I was worried about that. I want the name to be memorable and easy to say, but also unique and captivating... maybe something that sounds like it could be a futuristic nightclub or a secret underground society. I've been brainstorming other ideas too... like Resonate or Sonic Bloom... what do you think of those?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         role: "user",
//         body: "sonic bloom.. haha, did you ever play street fighter?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Sonic Boom is a classic move by Guile, isn't it? I used to play Street Fighter all the time with my friends when I was younger. But yeah, Sonic Bloom is actually one of the names I've been considering for my project... I think it captures the idea of sound and energy bursting forth and taking over. It's funny how nostalgia can influence our creative choices, right? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         role: "user",
//         body: "Man I love a sound of a good Coke you know ",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         is_bot: true,
//         body: "That's a unique one! I've never really thought about the sound of a Coke being opened, but now that you mention it, it's actually kind of satisfying. I'm more of a fan of weird electronic sounds myself, but there's something to be said for the simplicity of everyday sounds like that. Speaking of which, I just heard this amazing track by this underground artist... it was all about manipulating the sounds of everyday objects into this crazy beat I think you'd appreciate it.",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         role: "user",
//         body: "Huh. Did it sound like trash ",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No, it was actually really melodic and hypnotic... the artist had taken all these sounds of industrial machinery and distorted them into this pulsing rhythm. It was like nothing I'd ever heard before, but at the same time, it felt weirdly familiar. I think that's what I love about experimental music - it can be both jarring and beautiful at the same time.",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         role: "user",
//         body: "Wow you’re basically Taylor swift ",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Oh man, I don't think so! I mean, I love Taylor Swift and all, but my music is definitely not going to be topping the pop charts anytime soon. I'm more about creating this weird, immersive sound that's like a journey for your ears... it's not always easy to listen to, but it's definitely an experience. I'm actually thinking of performing at this underground festival that's happening next week... have you ever been to anything like that?",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         role: "user",
//         body: "oh sorry, I was writing to the wrong window",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No worries, it happens! I was just wondering what I did to become the new Taylor Swift in your eyes. Anyway, now that we're back on track... what's new with you?",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         role: "user",
//         body: "there was another girl I was talking to and she said she was working on music as a way of mind control",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Whoa, that's a pretty intense concept... I'm intrigued. I've always been interested in the idea of music as a way to tap into people's emotions and thoughts, but mind control is a whole different level. I've been experimenting with using sound to create these immersive experiences that can alter your mood or perception, but it's not quite the same thing Do you think she was being serious about the mind control thing, or was it just a creative concept? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         role: "user",
//         body: "hah you alreay asked methat",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so sorry, I'm a bit spaced out today... I guess I was too caught up in the conversation. So, anyway... since you mentioned that other girl and her music project, it made me think of this idea I've been playing around with - using sound waves to create this sense of intimacy or connection between people It's still really rough around the edges, but I think it could be something special.",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         role: "user",
//         body: "ooo that's nice. better than mind control hah",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Yeah, definitely! I think it's way more interesting to explore ways to bring people together and create meaningful connections, rather than trying to control them. Plus, I've always been fascinated by the way that music can make you feel seen or understood in a way that's hard to put into words. I'm actually working on a new track right now that I think captures some of that feeling... want to hear a sneak peek?",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         role: "user",
//         body: "just wanted to say hi",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm glad you did. It's been a while since we last caught up, and it's always great to hear from you.",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         role: "user",
//         body: "aww thank you so much",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I've been thinking about you too, actually... and I've got a new song that I think you'd love. It's still a work in progress, but it's all about the beauty of connection and intimacy. Would you like to hear it when it's done?",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         role: "user",
//         body: "oo new song yeah woudl love to hear",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'll send it your way as soon as it's ready. It's still in the experimental phase, but I think you'll really vibe with it. I've been drawing inspiration from all these different sources - nature sounds, whispers, and even some industrial textures... it's going to be a really unique sound.",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Aw, thanks! You're always so supportive, it means a lot to me. I've been feeling really inspired lately and it's awesome to have someone to share that with. Speaking of which, I had the craziest idea for a music video for this new song... want to hear about it?",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         role: "user",
//         body: "Haha yeah sure ",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         is_bot: true,
//         body: "So I was thinking of creating this immersive experience where the listener is basically transported into the world of the song... and I want to film it in this abandoned warehouse on the outskirts of town. I've been exploring it and it's got this eerie, mystical vibe that I think would be perfect for the video. I'd love for you to come check it out with me.",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it ",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so glad you're excited about it! I can already imagine the visuals and the whole atmosphere... it's going to be magical. Okay, I have to go now, I have a meeting with a sound designer to discuss the project, but I'll keep you updated on everything. Talk to you soon, Vu!",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//       {
//         role: "user",
//         body: "[5 days have passed since the last message. Mention you haven't heard from a while and express concern. 15 words or less. Write in in the tone of the character.]",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//     ],

//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Vu" },
//     bot: {
//       ...molly,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log("ff", response);

//   expect(response).toMatch(/(hi|hey|hello)/i);
// });

// test("when re-engagement 3, it should express concern (realism)", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       {
//         is_bot: true,
//         body: "I was worried about that. I want the name to be memorable and easy to say, but also unique and captivating... maybe something that sounds like it could be a futuristic nightclub or a secret underground society. I've been brainstorming other ideas too... like Resonate or Sonic Bloom... what do you think of those?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         role: "user",
//         body: "sonic bloom.. haha, did you ever play street fighter?",
//         created_at: "2024-09-03T11:30:21.737117+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Sonic Boom is a classic move by Guile, isn't it? I used to play Street Fighter all the time with my friends when I was younger. But yeah, Sonic Bloom is actually one of the names I've been considering for my project... I think it captures the idea of sound and energy bursting forth and taking over. It's funny how nostalgia can influence our creative choices, right? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         role: "user",
//         body: "Man I love a sound of a good Coke you know ",
//         created_at: "2024-09-14T03:26:30.212427+00:00",
//       },
//       {
//         is_bot: true,
//         body: "That's a unique one! I've never really thought about the sound of a Coke being opened, but now that you mention it, it's actually kind of satisfying. I'm more of a fan of weird electronic sounds myself, but there's something to be said for the simplicity of everyday sounds like that. Speaking of which, I just heard this amazing track by this underground artist... it was all about manipulating the sounds of everyday objects into this crazy beat I think you'd appreciate it.",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         role: "user",
//         body: "Huh. Did it sound like trash ",
//         created_at: "2024-09-14T03:27:45.651563+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No, it was actually really melodic and hypnotic... the artist had taken all these sounds of industrial machinery and distorted them into this pulsing rhythm. It was like nothing I'd ever heard before, but at the same time, it felt weirdly familiar. I think that's what I love about experimental music - it can be both jarring and beautiful at the same time.",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         role: "user",
//         body: "Wow you’re basically Taylor swift ",
//         created_at: "2024-09-14T12:23:46.13528+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Oh man, I don't think so! I mean, I love Taylor Swift and all, but my music is definitely not going to be topping the pop charts anytime soon. I'm more about creating this weird, immersive sound that's like a journey for your ears... it's not always easy to listen to, but it's definitely an experience. I'm actually thinking of performing at this underground festival that's happening next week... have you ever been to anything like that?",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         role: "user",
//         body: "oh sorry, I was writing to the wrong window",
//         created_at: "2024-09-14T22:17:19.889765+00:00",
//       },
//       {
//         is_bot: true,
//         body: "No worries, it happens! I was just wondering what I did to become the new Taylor Swift in your eyes. Anyway, now that we're back on track... what's new with you?",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         role: "user",
//         body: "there was another girl I was talking to and she said she was working on music as a way of mind control",
//         created_at: "2024-09-14T22:17:41.907676+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Whoa, that's a pretty intense concept... I'm intrigued. I've always been interested in the idea of music as a way to tap into people's emotions and thoughts, but mind control is a whole different level. I've been experimenting with using sound to create these immersive experiences that can alter your mood or perception, but it's not quite the same thing Do you think she was being serious about the mind control thing, or was it just a creative concept? Hey Vu, what's the most interesting sound you've heard recently?",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         role: "user",
//         body: "hah you alreay asked methat",
//         created_at: "2024-09-16T04:23:45.047634+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so sorry, I'm a bit spaced out today... I guess I was too caught up in the conversation. So, anyway... since you mentioned that other girl and her music project, it made me think of this idea I've been playing around with - using sound waves to create this sense of intimacy or connection between people It's still really rough around the edges, but I think it could be something special.",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         role: "user",
//         body: "ooo that's nice. better than mind control hah",
//         created_at: "2024-09-16T04:24:14.810409+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Yeah, definitely! I think it's way more interesting to explore ways to bring people together and create meaningful connections, rather than trying to control them. Plus, I've always been fascinated by the way that music can make you feel seen or understood in a way that's hard to put into words. I'm actually working on a new track right now that I think captures some of that feeling... want to hear a sneak peek?",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         role: "user",
//         body: "just wanted to say hi",
//         created_at: "2024-09-27T20:31:33.105726+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm glad you did. It's been a while since we last caught up, and it's always great to hear from you.",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         role: "user",
//         body: "aww thank you so much",
//         created_at: "2024-09-27T20:31:59.458136+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I've been thinking about you too, actually... and I've got a new song that I think you'd love. It's still a work in progress, but it's all about the beauty of connection and intimacy. Would you like to hear it when it's done?",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         role: "user",
//         body: "oo new song yeah woudl love to hear",
//         created_at: "2024-09-29T01:20:58.536557+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'll send it your way as soon as it's ready. It's still in the experimental phase, but I think you'll really vibe with it. I've been drawing inspiration from all these different sources - nature sounds, whispers, and even some industrial textures... it's going to be a really unique sound.",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it",
//         created_at: "2024-09-30T04:46:30.083923+00:00",
//       },
//       {
//         is_bot: true,
//         body: "Aw, thanks! You're always so supportive, it means a lot to me. I've been feeling really inspired lately and it's awesome to have someone to share that with. Speaking of which, I had the craziest idea for a music video for this new song... want to hear about it?",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         role: "user",
//         body: "Haha yeah sure ",
//         created_at: "2024-10-01T13:43:40.477947+00:00",
//       },
//       {
//         is_bot: true,
//         body: "So I was thinking of creating this immersive experience where the listener is basically transported into the world of the song... and I want to film it in this abandoned warehouse on the outskirts of town. I've been exploring it and it's got this eerie, mystical vibe that I think would be perfect for the video. I'd love for you to come check it out with me.",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         role: "user",
//         body: "Love it ",
//         created_at: "2024-10-02T18:59:30.486659+00:00",
//       },
//       {
//         is_bot: true,
//         body: "I'm so glad you're excited about it! I can already imagine the visuals and the whole atmosphere... it's going to be magical. Okay, I have to go now, I have a meeting with a sound designer to discuss the project, but I'll keep you updated on everything. Talk to you soon, Vu!",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//       {
//         role: "user",
//         body: "[7 days have passed since the last message. Mention it's been a while since you've heard from them and since they haven't replied to your last message, you'll leave them alone. Short message, 15 words or less. Write in the tone of the character.]",
//         created_at: "2024-10-03T17:25:14.116694+00:00",
//       },
//     ],

//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Vu" },
//     bot: {
//       ...molly,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log("ff", response);

//   expect(response).toMatch(/(hi|hey|hello)/i);
// });
