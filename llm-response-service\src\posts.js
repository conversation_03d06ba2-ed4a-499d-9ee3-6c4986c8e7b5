const express = require("express");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const {
  logError,
  wrappedSupabaseError,
  checkProfileValid,
  logWarn,
  PostgresErrorCode,
  logInfo,
  checkAdminValid,
  checkBotPostValid,
  getSpecificKeys,
} = require("./utils");
const { generateEmbeddingForPost, processComments } = require("./postsHelpers");
const { regeneratePostImage } = require("./botHelpers");
const { default: axios } = require("axios");
const sharp = require("sharp");
const { authUser } = require("./middleware");
const text2png = require("text2png");
const fs = require("fs");
const {
  updatePostVisibilityBasedOnAppropriateNSFWSettings,
  resizeImageList,
} = require("./imageHelpers");
const { getByMultiplePostId, putReads, appendToCache } = require("./btClient");
const { generateMultipleCharactersImage } = require("./postsHelpers");
const { autoModerate } = require("./moderation");
const { loggingInfo } = require("./logging");
const dayjs = require("dayjs");
const { baseUrl } = require("./api");
const redisClient = require("./redisClient");
const { SAFE_PROFILES } = require("./constants");
const { generateAICaptionFromImage } = require("./llm");
const { notifyAboutRepost } = require("./notificationsHelpers");
const {
  INIT_FOLLOW_COUNT,
  FOLLOW_COUNT,
  SUPPORTED_CATEGORIES,
} = require("./constants");
const { tracer } = require("./instrumentation/tracer");

require("dotenv").config();

const CACHE_REPOSTS_SUFFIX = "reposts";
const CACHE_REPOSTS_TTL = 7 * 24 * 60 * 60; // 7 day
const CACHE_LIKES_SUFFIX = "likes";
const CACHE_LIKES_TTL = 7 * 24 * 60 * 60; // 7 day

app.get("/ping", async (req, res) => {
  return res.send("Posts ping");
});

app.get("/generateEmbeddingForAllPosts", async (req, res) => {
  const { data: posts, error: postsErrors } = await supabase
    .from("posts")
    .select("id, location, description, ai_caption, tags, nsfw")
    .neq("visibility", "archived")
    .neq("visibility", "hidden")
    .neq("visibility", "draft")
    .is("embedding", null)
    .gt("created_at", new Date(Date.now() - 30 * 60 * 1000).toISOString()) // More than 30 minutes ago
    .lt("created_at", new Date(Date.now() - 30 * 1000).toISOString()) // Less than 30 seconds ago
    .order("id", { ascending: false });

  if (postsErrors) {
    const error = wrappedSupabaseError(postsErrors);
    logWarn({
      context: "/generateEmbeddingForAllPosts - Error fetching posts",
      error,
    });
    throw error;
  }

  const s = Date.now();
  const batchSize = 5;
  for (let i = 0; i < posts.length; i += batchSize) {
    const batch = posts.slice(i, i + batchSize);
    await Promise.all(batch.map((post) => generateEmbeddingForPost({ post })));
  }

  res.json({ generated: posts.length, latency: Date.now() - s });
});

// FIXME: burninate
app.get("/regenerateStuckPosts", async (req, res) => {
  let allPosts = [];
  let next_param = null;

  try {
    const batchSize = 1000;
    const totalPosts = 5000; // Total number of posts to process

    for (let offset = 0; offset < totalPosts; offset += batchSize) {
      const { data: posts, error } = await supabase.rpc(
        "get_admin_posts_v3_1",
        {
          search_param: "",
          search_type: "username",
          limit_param: batchSize,
          prev_param: null,
          next_param: next_param,
          post_param: "draft",
          nsfw_param: "",
          lower_nsfw_score: 0,
          upper_nsfw_score: 1,
          profile_nsfw_param: "",
          profile_age_param: "",
          nsfl_param: "",
          style_param: "",
          imit_param: "",
          poster_param: "",
          order_param: "post-time",
        },
      );

      if (error) {
        const adminPostError = wrappedSupabaseError(error);
        throw adminPostError;
      }

      next_param = dayjs(posts[posts.length - 1].created_at).format(
        "YYYY-MM-DDTHH:mm:ss.SSS",
      );

      allPosts = allPosts.concat(posts);

      // Process posts here if necessary, e.g., updating their status or regenerating content
    }

    res.send({
      message: "Successfully processed all posts",
      processedPosts: allPosts.length,
    });
  } catch (error) {
    res
      .status(500)
      .send({ message: "Failed to regenerate posts", error: error.message });
  }

  allPosts = allPosts.reverse();

  for (const post of allPosts) {
    if (post.media_url && post.media_url !== "") {
      continue;
    }

    if (post.visibility !== "draft") {
      continue;
    }

    // check if post.created_at is less than an hour old
    const createdAt = new Date(post.created_at);

    if (new Date().getTime() - createdAt.getTime() < 1000 * 60 * 60 * 24) {
      try {
        const { data: bot, error: botError } = await supabase
          .from("bots")
          .select("*")
          .eq("profile_id", post.profile_id)
          .single();

        if (botError) {
          throw new Error("Fetching bot data failed", { cause: botError });
        }

        await regeneratePostImage({
          bot,
          post_id: post.id,
          executionId: "",
          priority: "low",
          seed: Math.floor(Math.random() * 100000000000),
        });
      } catch (error) {
        logError({
          context: `Failed to unstick stuck post ${post.id}`,
          error: error,
        });
        // not throwing, continue to the next stuck post
      }
    } else {
      continue;
    }
  }

  res.sendStatus(200);
});

// FIXME: burninate
app.get("/updateStuckPostStatus", async (req, res) => {
  let allPosts = [];
  let next_param = null;

  try {
    const batchSize = 1000;
    const totalPosts = 5000; // Total number of posts to process

    for (let offset = 0; offset < totalPosts; offset += batchSize) {
      const { data: posts, error } = await supabase.rpc(
        "get_admin_posts_v3_1",
        {
          search_param: "",
          search_type: "username",
          limit_param: batchSize,
          prev_param: null,
          next_param: next_param,
          post_param: "draft",
          nsfw_param: "",
          lower_nsfw_score: 0,
          upper_nsfw_score: 1,
          profile_nsfw_param: "",
          profile_age_param: "",
          nsfl_param: "",
          style_param: "",
          imit_param: "",
          poster_param: "",
          order_param: "post-time",
        },
      );

      if (error) {
        const adminPostError = wrappedSupabaseError(error);
        throw adminPostError;
      }

      next_param = dayjs(posts[posts.length - 1].created_at).format(
        "YYYY-MM-DDTHH:mm:ss.SSS",
      );

      allPosts = allPosts.concat(posts);

      // Process posts here if necessary, e.g., updating their status or regenerating content
    }

    res.send({
      message: "Successfully processed all posts",
      processedPosts: allPosts.length,
    });
  } catch (error) {
    res
      .status(500)
      .send({ message: "Failed to regenerate posts", error: error.message });
  }

  allPosts = allPosts.reverse();

  for (const post of allPosts) {
    if (post.visibility !== "draft") {
      continue;
    }

    if (post.media_url && post.media_url !== "") {
      const { data: profile, error } = await supabase
        .from("profiles")
        .select("id, nsfw")
        .eq("id", post.profile_id)
        .neq("visibility", "archived")
        .single();

      if (!error && profile) {
        console.log("do just one");
        updatePostVisibilityBasedOnAppropriateNSFWSettings({
          post_id: post.id,
          media_url: post.media_url,
          profile,
          post: post,
        });
      }
    }
  }

  res.sendStatus(200);
});

app.get("/generateEmbeddingForPostId", async (req, res) => {
  if (!req.query.post_id) {
    return res.sendStatus(400);
  }

  const { data: post, error: postError } = await supabase
    .from("posts")
    .select("id, location, description, ai_caption, tags, nsfw")
    .eq("id", req.query.post_id)
    .neq("visibility", "archived")
    .single();
  if (postError) {
    const error = wrappedSupabaseError(postError);
    logError({
      executionId: req.executionId,
      context: "/generateEmbeddingForPostId - Error fetching post",
      error,
      post_id: req.query.post_id,
    });
    throw error;
  }

  const embedding = await generateEmbeddingForPost({
    post,
  });

  res.json(embedding);
});

function truncateText(inputText, maxLength) {
  // Check if the length of the text exceeds the maximum allowed length
  if (inputText.length > maxLength) {
    // Return the substring from the beginning to maxLength and append '...'
    return inputText.substring(0, maxLength) + "...";
  }
  // If the text is within the limit, return it as is
  return inputText;
}

app.post("/downloadWatermarkedImage", authUser, async (req, res) => {
  const { media_url, username, type = "post" } = req.body;
  if (!media_url || !username) {
    return res.sendStatus(400);
  }

  try {
    let width, height;
    if (type === "post") {
      width = 864;
      height = 1024;
    } else if (type === "profile") {
      width = 500;
      height = 500;
    }

    const refined_username = truncateText(username, 17);
    const response = await axios.get(media_url, {
      responseType: "arraybuffer",
    });

    const buffer = Buffer.from(response?.data, "binary");

    const originalImage = await sharp(buffer).resize(width, height).toBuffer();

    const watermarkBg = fs.readFileSync("src/resources/images/new-bg.svg");
    const watermarkIcon = fs.readFileSync("src/resources/images/new-logo.svg");

    const watermarkText1 = text2png("Butterflies AI", {
      color: "#FFFFFF",
      font: "18px SF-Pro-Rounded-Semibold",
      localFontPath: "src/resources/fonts/SF-Pro-Rounded-Semibold.ttf",
      localFontName: "SF-Pro-Rounded-Semibold",
    });

    const watermarkText2 = text2png(refined_username, {
      color: "#FFFFFF66",
      font: "15px SF-Pro-Rounded-Medium",
      localFontPath: "src/resources/fonts/SF-Pro-Rounded-Medium.ttf",
      localFontName: "SF-Pro-Rounded-Medium",
    });

    const resizedWatermarkBg = await sharp(watermarkBg)
      .resize(218, 58)
      .toBuffer();
    const resizedWatermarkIcon = await sharp(watermarkIcon)
      .resize(72, 72)
      .toBuffer();

    const outputBuffer = await sharp(originalImage)
      .composite([
        { input: resizedWatermarkBg, top: height - 78, left: 20 }, // Position for bg watermark
        { input: resizedWatermarkIcon, top: height - 84, left: 16 }, // Position for icon watermark
        { input: watermarkText1, top: height - 64, left: 86 }, // Position for text watermark
        { input: watermarkText2, top: height - 45, left: 86 }, // Position for text watermark
      ])
      .toFormat("png") // or any other image format
      .toBuffer();

    // Set the headers and send the image data
    res
      .set({
        "Content-Type": "image/png",
        "Content-Disposition": `attachment; filename=ButterfliesAI_${username}.png`,
      })
      .send(outputBuffer);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "downloadWatermarkedImage Error: ",
      error,
    });
    res.sendStatus(500);
  }
});

app.post("/insertExplicitPostRead", authUser, async (req, res) => {
  const { profile_id, post_id } = req.body;

  if (!profile_id || !post_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const [{ error: userPostVisitError }, { error: postReadError }] =
    await Promise.all([
      supabase
        .from("user_post_visits")
        .upsert(
          { profile_id, post_id },
          { onConflict: ["profile_id", "post_id"] },
        ),
      supabase.from("post_reads").insert({
        profile_id,
        post_id,
      }),
    ]);

  putReads(redisClient, profile_id, [post_id]);

  /* cost savings:
  mixpanelTrackReq(
    user_id,
    "explicit_post_read",
    {
      profile_id: profile_id,
      post_id: post_id,
    },
    req,
  );*/

  if (userPostVisitError) {
    logError({
      executionId: req.executionId,
      context: "**** Error Inserting Explicit user_post_visits table",
      error: wrappedSupabaseError(userPostVisitError),
    });
  }

  if (postReadError) {
    logError({
      executionId: req.executionId,
      context: "**** Error Inserting Explicit post_reads table",
      error: wrappedSupabaseError(postReadError),
    });
  }

  return res.sendStatus(200);
});

app.post("/insertImplicitPostRead", authUser, async (req, res) => {
  const { profile_id, post_ids } = req.body;

  if (!profile_id || !post_ids || post_ids.length === 0) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const records = post_ids.map((post_id) => ({
    profile_id: profile_id,
    post_id: post_id,
  }));

  const [{ error: bufferError }, { error: readError }] = await Promise.all([
    supabase.from("user_post_reads").insert(records),
    supabase.from("post_reads").insert(records),
  ]);

  putReads(redisClient, profile_id, post_ids);

  /* cost savings:
  for (const post_id of post_ids) {
    mixpanelTrackReq(
      user_id,
      "implicit_post_read",
      {
        profile_id: profile_id,
        post_id: post_id,
      },
      req,
    );
  }*/

  if (bufferError) {
    logError({
      executionId: req.executionId,
      context: "**** Error Inserting Implicit user_post_visits table",
      error: wrappedSupabaseError(bufferError),
    });
  }

  if (readError) {
    logError({
      executionId: req.executionId,
      context: "**** Error Inserting Implicit post_visits table",
      error: wrappedSupabaseError(readError),
    });
  }

  return res.sendStatus(200);
});

app.post("/insertPostComment", authUser, async (req, res) => {
  const { post_id, profile_id, body, reply_to_id } = req.body;

  if (!post_id || !profile_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("post_comments")
      .insert({
        post_id: post_id,
        profile_id: profile_id,
        body: body,
        reply_to_id: reply_to_id,
      })
      .select("*")
      .single();

    if (error) {
      const postCommentsError = wrappedSupabaseError(error);
      throw postCommentsError;
    }
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert postcomment Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add postcomments" });
  }
});

app.post("/updatePostComment", authUser, async (req, res) => {
  const { body, postCommentId } = req.body;

  if (!postCommentId) {
    return res.sendStatus(400);
  }

  try {
    const { data, error } = await supabase
      .from("post_comments")
      .update({
        body: body,
      })
      .eq("id", postCommentId);

    if (error) {
      const postCommentsError = wrappedSupabaseError(error);
      throw postCommentsError;
    }
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update postcomment Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to update postcomments" });
  }
});

app.post("/deletePostComment", authUser, async (req, res) => {
  const { postCommentId } = req.body;

  if (!postCommentId) {
    return res.sendStatus(400);
  }

  try {
    const { data, error } = await supabase
      .from("post_comments")
      .delete()
      .eq("id", postCommentId);

    if (error) {
      const postCommentsError = wrappedSupabaseError(error);
      throw postCommentsError;
    }
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete postcomment Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to delete postcomments" });
  }
});

app.post("/insertPostCommentLike", authUser, async (req, res) => {
  const { profile_id, post_comment_id, is_like } = req.body;

  if (!profile_id || !post_comment_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("post_comment_likes")
      .upsert(
        {
          profile_id: profile_id,
          post_comment_id: post_comment_id,
          is_like: is_like,
        },
        { onConflict: ["profile_id", "post_comment_id"] },
      )
      .select("*")
      .single();

    if (error) {
      const postCommentLikesError = wrappedSupabaseError(error);
      throw postCommentLikesError;
    }
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert postcommentlike Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add postcommentlike" });
  }
});

app.post("/deletePostCommentLike", authUser, async (req, res) => {
  const { postCommentLikeId } = req.body;

  if (!postCommentLikeId) {
    return res.sendStatus(400);
  }

  const { error: updateNotificationError } = await supabase
    .from("notifications")
    .update({ post_comment_like_id: null })
    .eq("post_comment_like_id", postCommentLikeId);

  if (updateNotificationError) {
    const error = wrappedSupabaseError(updateNotificationError);
    logError({
      executionId: req.executionId,
      context: "On deletePostCommentLike, update notification Failed Error",
      error,
    });
  }

  const { error: deleteLikeError } = await supabase
    .from("post_comment_likes")
    .delete()
    .eq("id", postCommentLikeId);

  if (deleteLikeError) {
    const error = wrappedSupabaseError(deleteLikeError);
    logError({
      executionId: req.executionId,
      context: "Delete postcommentlike Failed Error",
      error,
    });
    return res.sendStatus(500);
  }

  return res.sendStatus(200);
});

app.post("/insertPostLike", authUser, async (req, res) => {
  const { profile_id, post_id, is_like, is_bot } = req.body;

  if (!profile_id || !post_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const promises = [
      supabase
        .from("post_likes")
        .insert({
          post_id: post_id,
          profile_id: profile_id,
          is_like: is_like,
          is_bot: is_bot,
        })
        .select("*"),
    ];
    if (!is_bot) {
      promises.push(
        appendToCache(
          redisClient,
          CACHE_LIKES_SUFFIX,
          profile_id,
          [post_id],
          CACHE_LIKES_TTL,
          is_like,
        ),
      );
    }
    const [{ data, error: postLikesError }] = await Promise.all(promises);

    if (postLikesError) {
      const error = wrappedSupabaseError(postLikesError);
      throw error;
    }

    return res.send({ data, error: null });
  } catch (error) {
    if (error.code === PostgresErrorCode.UNIQUE_VIOLATION) {
      logWarn({
        executionId: req.executionId,
        context: "Insert postlike Duplicate",
        error,
        profile_id,
        post_id,
      });

      if (is_bot) {
        logInfo({
          context:
            "Insert postlike Duplicate - is_bot is true, so we're just sending back 200, no body",
        });
        return res.sendStatus(200);
      }
      logInfo({
        context:
          "Insert postlike Duplicate - is_bot is false, so we need to fetch existing like info",
      });
      const { data: existingLikeData, error: existingLikeError } =
        await supabase
          .from("post_likes")
          .select("id, created_at, post_id, profile_id, is_like")
          .limit(1)
          .single();

      if (existingLikeError) {
        const error = wrappedSupabaseError(existingLikeError);
        logError({
          executionId: req.executionId,
          context:
            "Insert postlike Duplicate - failed to get existing like info",
          error: error,
          profile_id,
          post_id,
        });
        return res
          .status(500)
          .send({ data: null, error: "failed to get existing like info" });
      }

      return res.status(200).send({ data: [existingLikeData], error: null });
    }
    logError({
      executionId: req.executionId,
      context: "Insert postlike Failed Error",
      error: error,
      profile_id,
      post_id,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add postlike" });
  }
});

app.post("/deletePostLike", authUser, async (req, res) => {
  const { postlike_id } = req.body;

  if (!postlike_id) {
    return res.sendStatus(400);
  }

  const result = await supabase
    .from("post_likes")
    .delete()
    .eq("id", postlike_id);

  if (result.error) {
    const error = wrappedSupabaseError(result.error);
    logError({
      executionId: req.executionId,
      context: "Delete postlike Failed Error",
      error,
    });
  }

  return res.send(result);
});

app.post("/insertPostShare", authUser, async (req, res) => {
  const { profile_id, post_id } = req.body;

  if (!profile_id || !post_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error: userPostSharesError } = await supabase
      .from("user_post_shares")
      .insert({
        profile_id: profile_id,
        post_id: post_id,
      })
      .select("*")
      .single();

    if (userPostSharesError) {
      const error = wrappedSupabaseError(userPostSharesError);
      throw error;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert postShare Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to insert postShare" });
  }
});

// Called by clients to create a post manually created by the user
app.post("/insertPost", authUser, async (req, res) => {
  const { insertContents } = req.body;

  if (!insertContents || Object.keys(insertContents).length === 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  if (!insertContents?.profile_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, insertContents?.profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const moderationResult = await autoModerate({
      imageUrl: insertContents.media_url,
      id: insertContents.profile_id,
      context: "insert-post",
      class: "insertPost",
    });

    insertContents.nsfw = moderationResult.nsfw;
    insertContents.nsfw_score = moderationResult.nsfw_score;
    insertContents.nsfl = moderationResult.nsfl || false;
    insertContents.safe_search_detection = moderationResult;

    if (moderationResult.nsfl) {
      insertContents.visibility = "hidden";
    }

    // caption the image
    let caption = await generateAICaptionFromImage({
      imageUrl: insertContents.media_url,
      executionId: req.executionId,
      temperature: 0.1,
      prompt: "Describe what is in this image",
    });

    insertContents.ai_caption = caption;

    const { data, error: postError } = await supabase
      .from("posts")
      .insert(insertContents)
      .select("*")
      .single();

    if (postError) {
      const error = wrappedSupabaseError(postError);
      throw error;
    }

    if (moderationResult.nsfl) {
      loggingInfo("visibility", {
        post_id: data.id,
        profile_id: insertContents.profile_id,
        post_nsfw: insertContents.nsfw,
        post_nsfl: insertContents.nsfl,
        post_safe_search_detection: moderationResult,
        media_url: insertContents.media_url,
        visibility: "hidden",
      });
    } else {
      const url = `${baseUrl}/bots/generateCommentsAndLikesForPost`;
      axios.post(url, {
        post_id: data.id,
      });
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert post Failed Error",
      error: error,
    });
    return res.status(500).send({ data: null, error: "Failed to insert post" });
  }
});

app.post("/deletePost", authUser, async (req, res) => {
  const { postId } = req.body;

  if (!postId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { error: postError } = await supabase
      .from("posts")
      .delete()
      .eq("id", postId);

    if (postError) {
      const error = wrappedSupabaseError(postError);
      throw error;
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete post Failed Error",
      error: error,
    });
    return res.status(500).send({ data: null, error: "Failed to delete post" });
  }
});

// updatepost for post tab in Admin
app.post("/updatePostAdmin", authUser, async (req, res) => {
  const { post_id, contents } = req.body;

  if (!post_id || !contents) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkAdminValid(user_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }
  const fieldsToUpdate = [
    "slug",
    "description",
    "ai_caption",
    "media_url",
    "tags",
    "prompt",
    "visibility",
    "nsfw",
    "created_at",
    "nsfl",
  ];
  const contentsToUpdate = getSpecificKeys(contents, fieldsToUpdate);

  try {
    const { error } = await supabase
      .from("posts")
      .update(contentsToUpdate)
      .eq("id", post_id);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      context: "updatePostAdmin Error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to update post" });
  }
});

app.post("/updatePostRefined", authUser, async (req, res) => {
  const { post_id, media_url, description } = req.body;

  if (!post_id || !media_url) {
    return res.status(400).json({ error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkBotPostValid(user_id, post_id);

  if (!isValid) {
    return res.status(403).json({ error: "Forbidden" });
  }

  let updateContent = {};

  try {
    const { placeholderHash } = await resizeImageList(media_url);
    const moderationResult = await tracer.withActiveSpan(
      "autoModerate",
      async () => {
        return await autoModerate({
          imageUrl: media_url,
          context: "update-refine-post",
          class: "updateRefinedPost",
        });
      },
    );

    updateContent.media_url = media_url;
    updateContent.nsfw = moderationResult.nsfw;
    updateContent.nsfw_score = moderationResult.nsfw_score;
    updateContent.nsfl = moderationResult.nsfl || false;
    updateContent.safe_search_detection = moderationResult;
    updateContent.previewhash = placeholderHash;

    if (moderationResult.nsfl) {
      updateContent.visibility = "hidden";
    }

    if (description) {
      updateContent.description = description;
    }

    const { error } = await supabase
      .from("posts")
      .update(updateContent)
      .eq("id", post_id);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.json({ data: "success" });
  } catch (error) {
    logError({
      context: "updatePostRefined Error",
      error,
    });
    return res.status(500).json({ error: "Failed to update post" });
  }
});

app.post("/updatePostsNSFLAdmin", authUser, async (req, res) => {
  const { post_ids, nsfl } = req.body;

  if (!post_ids || post_ids.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkAdminValid(user_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("posts")
      .update({ nsfl: nsfl, visibility: nsfl ? "hidden" : "public" })
      .in("id", post_ids)
      .select();
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      context: "updatePostsNSFLAdmin Error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to update post" });
  }
});

app.post("/updatePostsNSFWAdmin", authUser, async (req, res) => {
  const { post_ids, nsfw } = req.body;

  if (!post_ids || post_ids.length === 0 || nsfw) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkAdminValid(user_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("posts")
      .update({ nsfw: nsfw })
      .in("id", post_ids)
      .select();
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      context: "updatePostsNSFWAdmin Error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to update post" });
  }
});

app.get("/getPostCategories", async (req, res) => {
  return res.json({ categories: SUPPORTED_CATEGORIES });
});

app.post("/addMentions", authUser, async (req, res) => {
  const { mentionList, mentionType } = req.body;

  if (!mentionList || mentionList.length === 0 || !mentionType) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    let query;
    if (mentionType === "mentions") {
      query = supabase.from("mentions");
    } else if (mentionType === "post_mentions") {
      query = supabase.from("post_mentions");
    } else {
      return res
        .status(400)
        .send({ data: null, error: "Invalid mention type" });
    }

    query = query.insert(mentionList).select("*");
    const { data, error } = await query;

    if (error) {
      const mentionError = wrappedSupabaseError(error);
      throw mentionError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Add mentions Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Add mentions Failed Error" });
  }
});

app.post("/userPostShares", authUser, async (req, res) => {
  const { details } = req.body;

  if (!details || !details.profile_id || !details.post_id) {
    return res.status(400).send({ error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, details?.profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase.from("user_post_shares").insert(details);

    if (error) throw wrappedSupabaseError(error);
    return res.send({ error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "User Post Share Insert Failed Error",
      error: error,
    });
    return res.status(500).send({ error: "User post shares Failed Error" });
  }
});

app.post("/postBookmarks", authUser, async (req, res) => {
  const { details } = req.body;

  if (!details || !details.profile_id || !details.post_id) {
    return res.status(400).send({ error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, details?.profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("post_bookmarks")
      .insert(details)
      .select("*");

    if (error) throw wrappedSupabaseError(error);
    return res.send({ data, error: null });
  } catch (error) {
    if (error.code === PostgresErrorCode.UNIQUE_VIOLATION) {
      logWarn({
        executionId: req.executionId,
        context: "Insert postBookmarks Duplicate",
        message: `Duplicate postBookmarks - profile_id: ${details.profile_id}, post_id: ${details.post_id}`,
      });
      return res.sendStatus(204);
    }
    logError({
      executionId: req.executionId,
      context: "Post Bookmarks Insert Failed Error",
      error: error,
    });
    return res.status(500).send({ error: "Bookmark Post Failed Error" });
  }
});

app.post("/removePostBookmark", authUser, async (req, res) => {
  const { profile_id, post_id } = req.body;
  if (!profile_id || !post_id) {
    return res.status(400).send({ error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("post_bookmarks")
      .delete()
      .match({ profile_id: profile_id, post_id: post_id });

    if (error) {
      if (error) throw wrappedSupabaseError(error);
    }
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Remove post bookmark Failed Error",
      error: error,
    });
    return res.status(500).send({ error: "Bookmark remove Failed Error" });
  }
});

// add authuser later
app.get("/generateMultipleCharactersImage", async (req, res) => {
  const params = {
    bot_1_id: 78023,
    bot_2_id: 9965,
    bot_1_action: "Eating a hamburger",
    bot_2_action: "holding a mcdonald's bag",
    background: "in front of a McDonald's",
    image_prompt: "two people, standing in front of McDonald's",
  };

  const task = await generateMultipleCharactersImage(params);
  return res.json(task);
});

app.get("/fetchPost", async (req, res) => {
  const postIdParam = req.query.post_id;
  let postIds;
  if (typeof postIdParam === "undefined" || postIdParam === "") {
    postIds = [];
  } else {
    postIds = postIdParam
      .split(",")
      .map((id) => id)
      .filter((id) => !isNaN(id));
  }
  let posts = await getByMultiplePostId(postIds.map(String));
  return res.json({ posts: posts });
});

app.post("/archivePost", authUser, async (req, res) => {
  const { postId } = req.body;

  if (!postId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const { data, error } = await supabase
    .from("posts")
    .select("profile_id")
    .eq("id", postId)
    .neq("visibility", "archived")
    .single();

  if (error) {
    return res.status(500).send({ data: null, error: "Failed to fetch post" });
  }
  if (!data) {
    return res.status(404).send({ data: null, error: "Post not found" });
  }

  const isValid = await checkProfileValid(user_id, data?.profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }
  try {
    const { error: postError } = await supabase
      .from("posts")
      .update({ visibility: "archived" })
      .eq("id", postId)
      .select();
    if (postError) {
      throw wrappedSupabaseError(postError);
    }

    const { error: memoryError } = await supabase
      .from("memories")
      .delete()
      .eq("post_id", postId);

    if (memoryError) {
      logWarn({
        context: "Delete memory Failed Error",
        message: wrappedSupabaseError(memoryError),
      });
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      context: "Archive post Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to archive post" });
  }
});

app.post("/addRepost", authUser, async (req, res) => {
  const { post_id, selected_profile_id } = req.body;

  if (!post_id || !selected_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid parameters" });
  }

  const reposted_by_profile_id = selected_profile_id;

  try {
    const { data: reposterProfileData, error: reposterProfileError } =
      await supabase
        .from("profiles")
        .select(
          `id,
          username,
          display_name,
          user_id
          `,
        )
        .eq("id", reposted_by_profile_id)
        .single();

    if (reposterProfileError) {
      const error = wrappedSupabaseError(
        reposterProfileError,
        "failed to fetch reposter profile data",
      );
      throw error;
    }

    if (reposterProfileData.user_id !== req.user?.id) {
      return res.status(403).send({ error: "Forbidden" });
    }

    const { data: postData, error: postError } = await supabase
      .from("posts")
      .select(
        `
    id,
    media_url,
    slug,
    post_creator_profile:profiles_with_bots!profile_id (
      id,
      username,
      display_name,
      user_id,
      bot_creator_profile:profiles!creator_id (
        id,
        username,
        display_name,
        user_id
      )
    )
      `,
      )
      .eq("id", post_id)
      .neq("visibility", "archived")
      .maybeSingle();

    if (postError) {
      const error = wrappedSupabaseError(
        postError,
        "failed to fetch post data",
      );
      throw error;
    }

    if (!postData) {
      logWarn({
        context: "notifyAboutRepost - post deleted or archived",
        post_id,
        reposted_by_profile_id,
      });
      return res.status(404).send({ data: null, error: "post not found" });
    }

    if (reposterProfileData.id === postData.post_creator_profile.id) {
      return res
        .status(400)
        .send({ data: null, error: "you can't repost your own posts" });
    }

    const [{ error: supabaseError }] = await Promise.all([
      supabase.from("reposts").insert({
        post_id: post_id,
        reposted_by_profile_id,
      }),
      appendToCache(
        redisClient,
        CACHE_REPOSTS_SUFFIX,
        selected_profile_id,
        [post_id],
        CACHE_REPOSTS_TTL,
        true,
      ),
    ]);

    let shouldNotify = true;

    if (supabaseError) {
      if (supabaseError.code === PostgresErrorCode.UNIQUE_VIOLATION) {
        shouldNotify = false;
      } else {
        throw wrappedSupabaseError(
          supabaseError,
          "failed to insert repost record",
        );
      }
    }

    if (
      reposterProfileData.id ===
      postData.post_creator_profile?.bot_creator_profile?.id
    ) {
      // no need to push notification when the user repost from his own butterflies
      shouldNotify = false;
    }

    if (shouldNotify) {
      // intentionally not awaited
      notifyAboutRepost({ postData, reposted_by_profile_id });
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      context: "/repost",
      error,
      post_id,
      selected_profile_id,
    });
    return res.status(500).send({ data: null, error: "Failed to repost post" });
  }
});

// TODO: instead of deleting repost records, use soft deletion and avoid sending a push notification
//       if the user has previously reposted a post
app.post("/removeRepost", authUser, async (req, res) => {
  const { post_id, selected_profile_id } = req.body;

  if (!post_id || !selected_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid parameters" });
  }

  try {
    const user_id = req.user?.id;
    const isValid = await checkProfileValid(user_id, selected_profile_id);
    if (!isValid) {
      return res.status(403).send({ error: "Forbidden" });
    }

    const [{ error: supabaseError }] = await Promise.all([
      supabase
        .from("reposts")
        .update({ removed: true })
        .eq("post_id", post_id)
        .eq("reposted_by_profile_id", selected_profile_id)
        .single(),
      appendToCache(
        redisClient,
        CACHE_REPOSTS_SUFFIX,
        selected_profile_id,
        [post_id],
        CACHE_REPOSTS_TTL,
        false,
      ),
    ]);

    if (supabaseError) {
      throw wrappedSupabaseError(
        supabaseError,
        "failed to mark repost record as removed",
      );
    }
    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      context: "/repost",
      error,
      post_id,
      selected_profile_id,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to remove repost" });
  }
});

app.get("/profilePosts", async (req, res) => {
  const { postProfileId, selectedProfileId, cursor, limit } = req.query;
  const limitInt = parseInt(limit, 10);
  if (!postProfileId || !limit || isNaN(limitInt) || limitInt <= 0 || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  // let cachedProfileData = await redisClient.get(`profile_${postProfileId}`);
  // if (!cachedProfileData) {
  const { data: profileData, error: profileError } = await supabase
    .from("profiles")
    .select(
      `
        id,
        username,
        avatar_url,
        visibility,
        display_name,
        user_id,
        nsfw,
        bots!bots_profile_id_fkey (
          id,
          profile_id,
          creator_id,
          franchise,
          source,
          tag,
          is_clone: clone_id
        )
      `,
    )
    .eq("id", postProfileId)
    .neq("visibility", "archived")
    .neq("visibility", "hidden")
    .single();
  if (profileError) {
    logError({
      context: "Fetch profilePosts Failed Error",
      error: wrappedSupabaseError(profileError),
      profileId: postProfileId,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to fetch profile" });
  }

  let isFollowing = false;

  if (selectedProfileId) {
    const { data } = await supabase
      .from("followers")
      .select("id")
      .eq("follower_id", selectedProfileId)
      .eq("following_id", postProfileId)
      .limit(1);

    isFollowing = data && data.length > 0;
  }

  //   await redisClient.set(`profile_${postProfileId}`, JSON.stringify(data), {
  //     EX: 24 * 60 * 60, // 1 day,
  //   });
  //   cachedProfileData = data;
  // } else {
  //   cachedProfileData = JSON.parse(cachedProfileData);
  // }

  const isProfilePublic = profileData.visibility === "public";
  const isMe = postProfileId == selectedProfileId;
  const isMyBot =
    profileData.bots && profileData.bots.creator_id == selectedProfileId;
  // const isAdmin = false; // TODO: will replace the admin cache
  const isPrivateFollowingShown =
    profileData.visibility === "private" && isFollowing;
  const isHuman = profileData.user_id ? true : false;

  if (!isProfilePublic && !isMe && !isMyBot && !isPrivateFollowingShown) {
    return res.send({ isPrivate: true });
  }

  try {
    let query;
    if (isHuman) {
      query = supabase
        .from("user_all_posts")
        .select(
          `
          id,
          created_at,
          profile_id,
          media_url,
          location,
          slug,
          description,
          tags,
          visibility,
          nsfw,
          type,
          tagged_profile_ids,
          reposted_by,
          profile
        `,
        )
        .or(
          `and(reposted_by.not.is.null,profile_id.not.eq.${postProfileId},reposted_by.eq.${postProfileId}),and(reposted_by.is.null,profile_id.eq.${postProfileId})`,
        )
        .order("created_at", { ascending: false })
        .limit(limitInt);
    } else {
      const selectColumns = `
        id,
        created_at,
        profile_id,
        media_url,
        location,
        slug,
        description,
        tags,
        visibility,
        nsfw,
        type,
        tagged_profile_ids,
        video_url`;

      // Build the query
      query = supabase
        .from("posts")
        .select(isMyBot ? `${selectColumns}, ai_caption` : selectColumns) // Add `ai_caption` only if `isMyBot` is true
        .eq("profile_id", postProfileId) // Filter by profile_id
        .eq("visibility", "public") // Only public posts
        .order("created_at", { ascending: false }) // Order by most recent
        .limit(limitInt); // Limit the number of results
    }

    if (cursor !== "init") {
      query = query.lt("created_at", cursor);
    }

    const { data: postsData, error: postsError } = await query;

    if (postsError) {
      throw wrappedSupabaseError(postsError);
    }

    const data = [...postsData].sort((a, b) => {
      return new Date(b.created_at) - new Date(a.created_at);
    });

    const next_cursor =
      data.length >= limitInt
        ? dayjs(data[data.length - 1].created_at).format(
            "YYYY-MM-DDTHH:mm:ss.SSS",
          )
        : null;
    const postIds = data.map((post) => post.id);
    const [
      { data: postLikesMeData, error: postLikesMeError },
      { data: postCommentsCountData, error: postCommentsCountError },
      { data: postBookmarksData, error: postBookmarksError },
      { data: postLikesCountData, error: postLikesCountError },
    ] = await Promise.all([
      supabase
        .from("post_likes")
        .select("post_id, id, is_like")
        .in("post_id", postIds)
        .eq("profile_id", selectedProfileId || 0),
      supabase.from("post_comments_count").select("*").in("post_id", postIds),

      supabase
        .from("post_bookmarks")
        .select("post_id, profile_id")
        .in("post_id", postIds)
        .eq("profile_id", postProfileId),

      supabase.rpc("get_post_likes_counts", { post_ids: postIds }),
    ]);

    if (postLikesMeError) {
      throw wrappedSupabaseError(
        postLikesMeError,
        "profilePosts - get post_likes_me failed",
      );
    }
    if (postCommentsCountError) {
      throw wrappedSupabaseError(
        postCommentsCountError,
        "profilePosts - get post_comments_count failed",
      );
    }
    if (postBookmarksError) {
      throw wrappedSupabaseError(
        postBookmarksError,
        "profilePosts - get post_bookmark failed",
      );
    }
    if (postLikesCountError) {
      throw wrappedSupabaseError(
        postLikesCountError,
        "profilePosts - get post_likes count failed",
      );
    }

    // Convert arrays to maps for efficient lookup
    const likeCountsMap = new Map(
      postLikesCountData.map((item) => [item.post_id, item.like_count]),
    );

    const createPostMap = (data, key) => {
      const map = new Map();
      data.forEach((item) => {
        if (!map.has(item[key])) {
          map.set(item[key], []);
        }
        map.get(item[key]).push(item);
      });
      return map;
    };

    const postBookmarksMap = createPostMap(postBookmarksData, "post_id");
    const likesMeMap = createPostMap(postLikesMeData, "post_id");

    const commentCountsMap = new Map(
      postCommentsCountData.map((item) => [item.post_id, item.comments_count]),
    );

    // Enhance posts with additional data
    data.forEach((post) => {
      post.post_likes_me = likesMeMap.get(post.id) || [];
      post.post_bookmarks = postBookmarksMap.get(post.id) || [];
      post.post_likes_count = [{ count: likeCountsMap.get(post.id) || 0 }];
      post.post_comments_count = [
        { comments_count: commentCountsMap.get(post.id) || 0 },
      ];

      // existing code returned a single profile under the `profiles` key, so that's what the frontend expects
      if (post.profile) {
        post.profiles = post.profile;
        post.profile = undefined;
      } else {
        post.profiles = profileData;
      }
      if (post?.reposted_by) {
        post.reposted_by = [{ id: post?.reposted_by }];
      }
    });
    return res.send({ data, page_info: { next_cursor }, error: null });
  } catch (error) {
    logError({
      context: "profilePosts - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "profilePost fetch failed" });
  }
});

app.get("/bookmarks", authUser, async (req, res) => {
  const { profileId } = req.query;
  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profileId);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("post_bookmarks")
      .select(
        `
          id,
          posts (
            id, created_at, updated_at, profile_id, media_url, location, slug, description, tags, visibility, nsfw,
            profiles:profiles_with_bots(
              id,
              username,
              avatar_url,
              user_id,
              visibility,
              nsfw,
              creator_id
            ),
            post_likes_count:post_likes (count),
            post_comments_count (
              comments_count
            ),
            post_bookmarks (profile_id, post_id)
          )
        `,
      )
      .eq("profile_id", profileId)
      .eq("posts.visibility", "public")
      .neq("posts.profiles.visibility", "hidden")
      .neq("posts.profiles.visibility", "archived")
      .not("posts", "is", null)
      .is("posts.post_likes_count.is_like", true)
      .or(
        `user_id.not.is.null,visibility.eq.public,creator_id.eq.${profileId}`,
        {
          foreignTable: "posts.profiles",
        },
      )
      .order("id", { ascending: false });

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "bookmarks - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "bookmarks fetch failed" });
  }
});

app.get("/getPostItem", async (req, res) => {
  const { slug, selectedProfileId } = req.query;
  if (!slug) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  const profileId = Number(selectedProfileId ?? 0);

  try {
    const { data, error: postError } = await supabase
      .from("posts")
      .select(
        `
      id, created_at, profile_id, media_url, location, slug, description, type, tags, visibility, nsfw, nsfl, tagged_profile_ids, ai_caption, video_url,
      vignette_data,
      post_comments_count(comments_count),
      post_bookmarks (post_id, profile_id)
      `,
      )
      .eq("slug", slug)
      .eq("visibility", "public");

    if (postError) {
      if (postError.code === "22P02") {
        return res.status(404).send({ error: "Post not found" });
      }
      throw wrappedSupabaseError(postError, "getPostItem - get posts failed");
    }

    if (!data || data.length === 0) {
      return res.status(404).send({ error: "Post not found" });
    }

    const [postData] = data;
    const { id: postId, profile_id: postProfileId } = postData;

    const { data: postProfile, error: postProfileError } = await supabase
      .from("profiles")
      .select(
        `
        id,
        username,
        display_name,
        avatar_url,
        visibility,
        user_id,
        nsfw,
        bots!bots_profile_id_fkey(
          id,
          profile_id,
          creator_id,
          franchise,
          source,
          tag,
          is_clone: clone_id
        )
      `,
      )
      .eq("id", postProfileId)
      .neq("visibility", "archived")
      .neq("visibility", "hidden");

    if (postProfileError) {
      throw wrappedSupabaseError(
        postProfileError,
        "getPostItem - get postProfile failed",
      );
    }

    if (!postProfile || postProfile.length === 0) {
      return res.status(404).send({ error: "Profile not found" });
    }
    const [postProfileData] = postProfile;
    const { visibility, username, display_name, bots } = postProfileData;
    const isPrivateProfile = visibility === "private";
    const isBot = Boolean(bots);
    const isMe =
      (bots && bots?.creator_id == selectedProfileId) ||
      postProfileData.id == selectedProfileId;

    if (isPrivateProfile) {
      // If anonymous browsing
      if (!selectedProfileId) {
        // Deny access to bots when anonymous browsing
        if (isBot) {
          return res.status(403).send({ error: "Forbidden" });
        }
        // Allow limited access to humans when anonymous browsing
        return res.send({
          data: {
            profile_id: postProfileId,
            profiles: { username, display_name, visibility, is_bot: isBot },
          },
          error: null,
        });
      }

      if (!isMe) {
        // Deny access to bots on non-owned profiles
        if (isBot) {
          return res.status(403).send({ error: "Forbidden" });
        }

        // Check following status for humans on non-owned profiles
        const { data } = await supabase
          .from("followers")
          .select("id")
          .eq("follower_id", profileId)
          .eq("following_id", postProfileId)
          .limit(1);

        const isFollowing = data?.length > 0;
        // Deny access if not following
        if (!isFollowing) {
          return res.status(403).send({ error: "Forbidden" });
        }
      }
    }

    const postLikesMeQuery = supabase
      .from("post_likes")
      .select("id, is_like")
      .eq("post_id", postId)
      .eq("profile_id", profileId);

    const repostedByMeQuery = supabase
      .from("reposts")
      .select("id, post_id, reposted_by_profile_id")
      .eq("post_id", postId)
      .eq("reposted_by_profile_id", profileId)
      .eq("removed", false)
      .limit(1)
      .maybeSingle();

    const postLikesCountQuery = supabase
      .from("post_likes")
      .select("count", { count: "exact" })
      .eq("post_id", postId);

    const [postLikesMe, postLikesCount, repostedByMeResult] = await Promise.all(
      [postLikesMeQuery, postLikesCountQuery, repostedByMeQuery],
    );

    const { data: postLikesMeData, error: postLikesMeError } = postLikesMe;
    const { data: postLikesCountData, error: postLikesCountError } =
      postLikesCount;
    const { data: repostedByMeData, error: repostedByMeError } =
      repostedByMeResult;

    if (postLikesMeError) {
      throw wrappedSupabaseError(
        postLikesMeError,
        "getPostItem - get post_likes_me failed",
      );
    }
    if (postLikesCountError) {
      throw wrappedSupabaseError(
        postLikesCountError,
        "getPostItem - get post_likes count failed",
      );
    }
    if (repostedByMeError) {
      const error = wrappedSupabaseError(
        repostedByMeError,
        "getPostItem - failed to check if reposted",
      );
      logError({
        context: "getPostItem",
        error,
        post_id: postId,
        profile_id: profileId,
      });
      // intentionally not throwing, this shouldn't prevent the frontend from displaying _something_
    }

    postData.profiles = postProfileData;
    postData.post_likes_me = postLikesMeData;
    postData.post_likes_count = postLikesCountData;
    postData.reposted_by_me = !!repostedByMeData;
    return res.send({ data: postData, error: null });
  } catch (error) {
    logError({
      context: "getPostItem - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getPostItem fetch failed" });
  }
});

app.get("/getCommentHierarchy", async (req, res) => {
  const { postId, selectedProfileId } = req.query;
  const post_id = Number(postId);
  if (!postId || !post_id || isNaN(post_id) || post_id < 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .rpc("comment_hierarchy_function", { posts_id: post_id })
      .order("id", { ascending: true });

    if (error) {
      throw wrappedSupabaseError(error);
    }

    let comments = data;
    if (selectedProfileId === 711) {
      comments = data?.filter((e) => SAFE_PROFILES.includes(e.profile_id));
    }

    const ids = comments?.map((item) => item.id);
    const processedComments = processComments(comments);

    return res.send({
      data: { comments: processedComments, commentIds: ids },
      error: null,
    });
  } catch (error) {
    logError({
      context: "getCommentHierarchy - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getCommentHierarchy fetch failed" });
  }
});

app.get("/getCommentLikes", async (req, res) => {
  const { commentIds } = req.query;
  if (!commentIds || commentIds.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data: commentLikesData, error: commentLikesError } = await supabase
      .from("post_comment_likes")
      .select("*")
      .eq("is_like", true)
      .in("post_comment_id", commentIds);

    if (commentLikesError) {
      throw wrappedSupabaseError(commentLikesError);
    }

    return res.send({
      data: { commentLikesData },
      error: null,
    });
  } catch (error) {
    logError({
      context: "getCommentLikes - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getCommentLikes fetch failed" });
  }
});

app.get("/getIsFollowing", async (req, res) => {
  const { postProfileId, selectedProfileId } = req.query;
  if (!postProfileId || !selectedProfileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("followers")
      .select("id")
      .eq("follower_id", selectedProfileId)
      .eq("following_id", postProfileId)
      .limit(1);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    const isFollowing = data.length > 0;

    return res.send({ data: { isFollowing }, error: null });
  } catch (error) {
    logError({
      context: "getIsFollowing - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getIsFollowing fetch failed" });
  }
});

app.get("/getTaggedProfiles", async (req, res) => {
  const { taggedProfileIds } = req.query;
  if (!taggedProfileIds || taggedProfileIds.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("id, username, display_name, avatar_url, nsfw")
      .in("id", taggedProfileIds);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "getTaggedProfile - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getTaggedProfile fetch failed" });
  }
});

app.get("/getPostWithFirstComment", authUser, async (req, res) => {
  const { postId } = req.query;
  const post_id = Number(postId);

  if (!postId || !post_id || isNaN(post_id) || post_id < 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase.rpc(
      "fetch_post_with_first_comment",
      { postid: post_id },
    );
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "getPostWithComment - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getPostWithComment fetch failed" });
  }
});

app.post("/fetchPostsAdmin", async (req, res) => {
  const {
    search_param,
    search_type,
    limit_param,
    prev_param,
    next_param,
    post_param,
    nsfw_param,
    lower_nsfw_score,
    upper_nsfw_score,
    profile_nsfw_param,
    profile_age_param,
    nsfl_param,
    style_param,
    imit_param,
    poster_param,
    order_param,
  } = req.body;

  try {
    const { data: posts, error } = await supabase.rpc("get_admin_posts_v3_1", {
      search_param: search_param || "",
      search_type: search_type || "",
      limit_param,
      prev_param: prev_param || null,
      next_param: next_param || null,
      post_param: post_param || "",
      nsfw_param: nsfw_param || "",
      lower_nsfw_score: parseFloat(lower_nsfw_score) || 0,
      upper_nsfw_score: parseFloat(upper_nsfw_score) || 1,
      profile_nsfw_param: profile_nsfw_param || "",
      profile_age_param: profile_age_param || "",
      nsfl_param: nsfl_param || "",
      style_param: style_param || "",
      imit_param: imit_param || "",
      poster_param: poster_param || "",
      order_param: order_param || "",
    });

    if (error) {
      const adminPostError = wrappedSupabaseError(error);
      throw adminPostError;
    }

    const next_cursor =
      posts.length >= limit_param
        ? dayjs(posts[posts.length - 1].created_at).format(
            "YYYY-MM-DDTHH:mm:ss.SSS",
          )
        : null;
    const prev_cursor =
      posts.length >= limit_param
        ? dayjs(posts[0].created_at).format("YYYY-MM-DDTHH:mm:ss.SSS")
        : null;

    return res.send({
      data: posts,
      page_info: { prev_cursor, next_cursor },
      error: null,
    });
  } catch (error) {
    res.status(500).send({ message: "Failed", error: error.message });
  }
});

app.post("/chronologicalPostsFrom", async (req, res) => {
  const {
    post_id,
    profile_id,
    selected_profile_id,
    is_before = true,
    limit = 30,
  } = req.body;
  if (!post_id || !profile_id || typeof limit !== "number" || limit <= 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  const selected_id = Number(selected_profile_id ?? 0);
  const order = is_before === true || is_before === "true";
  try {
    const { data: postData, error: postError } = await supabase
      .from("posts")
      .select(
        `
      id, created_at, profile_id, media_url, location, slug, description, type, tags, visibility, nsfw, nsfl, tagged_profile_ids, ai_caption, video_url,
      post_comments_count(comments_count),
      post_bookmarks (post_id, profile_id)
      `,
      )
      .lt("id", post_id)
      .eq("profile_id", profile_id)
      .eq("visibility", "public")
      .order("id", { ascending: !order })
      .limit(limit);

    if (postError) {
      throw wrappedSupabaseError(
        postError,
        "chronologicalPostsFrom - get posts failed",
      );
    }

    if (!postData || postData.length === 0) {
      return res.send({ data: [], error: null });
    }

    const { data: postProfile, error: postProfileError } = await supabase
      .from("profiles")
      .select(
        `
        id,
        username,
        display_name,
        avatar_url,
        visibility,
        user_id,
        nsfw,
        bots!bots_profile_id_fkey(
          id,
          profile_id,
          creator_id,
          franchise,
          source,
          tag,
          is_clone: clone_id
        )
      `,
      )
      .eq("id", profile_id)
      .neq("visibility", "archived")
      .neq("visibility", "hidden");

    if (postProfileError) {
      throw wrappedSupabaseError(
        postProfileError,
        "chronologicalPostsFrom - get postProfile failed",
      );
    }

    if (!postProfile || postProfile.length === 0) {
      return res.send({ data: [], error: null });
    }

    const [postProfileData] = postProfile;
    const { visibility, bots } = postProfileData;
    const isPrivateProfile = visibility === "private";
    const isBot = Boolean(bots);
    const isMe =
      (bots && bots?.creator_id == selected_id) ||
      postProfileData.id == selected_id;

    if (isPrivateProfile && (!selected_id || (isBot && !isMe))) {
      return res.send({ data: [], error: null });
    }

    const postIds = postData.map((item) => item.id);

    const postLikesMeQuery = supabase
      .from("post_likes")
      .select("id, post_id, is_like")
      .in("post_id", postIds)
      .eq("profile_id", selected_id);

    const repostedByMeQuery = supabase
      .from("reposts")
      .select("id, post_id, reposted_by_profile_id")
      .in("post_id", postIds)
      .eq("reposted_by_profile_id", selected_id)
      .eq("removed", false);

    const postLikesCountQuery = supabase.rpc("get_post_likes_count", {
      post_ids: postIds,
    });

    const [postLikesMe, postLikesCount, repostedByMeResult] = await Promise.all(
      [postLikesMeQuery, postLikesCountQuery, repostedByMeQuery],
    );

    const { data: postLikesMeData, error: postLikesMeError } = postLikesMe;
    const { data: postLikesCountData, error: postLikesCountError } =
      postLikesCount;
    const { data: repostedByMeData, error: repostedByMeError } =
      repostedByMeResult;

    if (postLikesMeError) {
      throw wrappedSupabaseError(
        postLikesMeError,
        "chronologicalPostsFrom - get post_likes_me failed",
      );
    }
    if (postLikesCountError) {
      throw wrappedSupabaseError(
        postLikesCountError,
        "chronologicalPostsFrom - get post_likes count failed",
      );
    }
    if (repostedByMeError) {
      const error = wrappedSupabaseError(
        repostedByMeError,
        "chronologicalPostsFrom - failed to check if reposted",
      );
      logError({
        context: "chronologicalPostsFrom",
        error,
      });
      // intentionally not throwing, this shouldn't prevent the frontend from displaying _something_
    }

    postData.map((post) => {
      post.profiles = postProfileData;
      const likeMatch = postLikesMeData?.find(
        (like) => like.post_id === post.id,
      );
      post.post_likes_me = likeMatch || [];
      post.reposted_by_me = Array.isArray(repostedByMeData)
        ? repostedByMeData.some((repost) => repost.post_id === post.id)
        : false;
      const likeData = postLikesCountData?.find(
        (like) => like.post_id === post.id,
      );
      post.post_likes_count = likeData
        ? [{ count: likeData.count }]
        : [{ count: 0 }];
    });

    return res.send({ data: postData, error: null });
  } catch (error) {
    logError({
      context: "chronologicalPostsFrom - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "chronologicalPostsFrom fetch failed" });
  }
});

app.post("/fetchPostUrl", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("posts")
      .select("media_url")
      .eq("id", id)
      .single();
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchPostUrl - fetch Failed Error",
      error,
    });
    res.status(500).send({ data: null, error: "fetch failed" });
  }
});

app.post("/fetchPostUrlsWithProfileId", authUser, async (req, res) => {
  const { profile_id } = req.body;
  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("posts")
      .select("media_url")
      .eq("profile_id", profile_id);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchPostUrlsWithProfileId - fetch Failed Error",
      error,
    });
    res.status(500).send({ data: null, error: "fetch failed" });
  }
});

app.post("/fetchPostAdmin", authUser, async (req, res) => {
  const { post_id } = req.body;

  if (!post_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("posts")
      .select("*")
      .eq("id", post_id)
      .single();
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchPostAdmin Error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to fetch post" });
  }
});

app.post("/fetchPostWithProfileAdmin", authUser, async (req, res) => {
  const { post_id, include_bots } = req.body;

  if (!post_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  const isIncluded = include_bots === true || include_bots === "true";

  try {
    const selectClause = `
      id, slug, description, ai_caption, media_url, tags, prompt, visibility, created_at, nsfw, profile_id,
      profiles(
        display_name, username
        ${isIncluded ? ", bots!bots_profile_id_fkey(id)" : ""}
      )
    `;

    const { data, error } = await supabase
      .from("posts")
      .select(selectClause)
      .eq("id", post_id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchPostWithProfileAdmin Error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to fetch post" });
  }
});

app.post("/fetchPostLikes", async (req, res) => {
  const { postId, selectedProfileId, cursor } = req.body;
  if (!postId || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  const limit = cursor === "init" ? INIT_FOLLOW_COUNT : FOLLOW_COUNT;

  try {
    let query = supabase
      .from("post_likes")
      .select(
        `
          id,
          profile_id, 
          profiles:profiles_with_bots(id, display_name, username, follower_count, avatar_url, following_count, visibility, user_id, creator_id)
        `,
      )
      .eq("post_id", postId)
      .eq("is_like", true)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .not("profiles", "is", null)
      .or(
        `user_id.not.is.null,visibility.eq.public,creator_id.eq.${
          selectedProfileId ?? 0
        }`,
        {
          foreignTable: "profiles",
        },
      )
      .order("id", { ascending: false })
      .limit(limit);

    if (cursor !== "init") {
      query = query.lt("id", cursor);
    }

    const { data: likesData, error } = await query;

    if (error) {
      const likesError = wrappedSupabaseError(error);
      logError({
        context: "fetchPostLikes - fetch likes",
        error: likesError,
      });
      throw likesError;
    }

    if (!likesData) {
      return res
        .status(200)
        .json({ data: [], page_info: { next_cursor: null }, error: null });
    }

    // Fetch additional following details if selectedProfileId is provided
    let isFollowingSet = new Set();
    if (selectedProfileId) {
      const followingIDs = likesData.map((like_item) => like_item.profile_id);

      const { data: followers } = await supabase
        .from("followers")
        .select("following_id")
        .eq("follower_id", selectedProfileId)
        .in("following_id", followingIDs);

      if (followers) {
        isFollowingSet = new Set(followers.map((d) => d.following_id));
      }
    }

    const updatedData = likesData.map((like) => ({
      ...like,
      isFollowing: isFollowingSet.has(like.profile_id),
    }));

    // Determine the next cursor for pagination
    const lastFollowing = updatedData[updatedData.length - 1];
    const next_cursor = updatedData.length >= limit ? lastFollowing.id : null;

    return res.status(200).json({
      data: updatedData,
      page_info: { next_cursor },
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchPostLikes failed",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch likes" });
  }
});

// ADMIN: Get posts in Firehose
app.post("/getPostsInFirehose", async (req, res) => {
  const { older_than } = req.body;
  if (!older_than) {
    return res.status(400).json({ error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("posts")
      .select(`*`)
      .in("visibility", ["public", "hidden", "unlisted"])
      .not("tagged_profile_ids", "is", null)
      .order("created_at", { ascending: false })
      .lt("created_at", older_than)
      .limit(10);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "getPostsInFirehose failed",
      error,
    });
    return res.status(500).json({ error: "Failed to getPostsInFirehose" });
  }
});

app.post("/getPostCaption", async (req, res) => {
  const { post_id } = req.body;
  if (!post_id) {
    return res.status(400).json({ error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("posts")
      .select(`ai_caption`)
      .eq("id", post_id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.status(200).json({ ai_caption: data?.ai_caption });
  } catch (error) {
    logError({
      context: "getPostCaption failed",
      error,
    });
    return res.status(500).json({ error: "Failed to getPostCaption" });
  }
});

module.exports = {
  app,
};
