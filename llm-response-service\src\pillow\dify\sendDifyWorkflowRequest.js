const { logError, logInfo } = require("../../logUtils");
const axios = require("axios");

// const DIFY_BASE_URL = "http://dify.llm.svc.cluster.local/v1";
const DIFY_BASE_URL = "http://dify.butterflies.ai/v1";
const DIFY_WORKFLOW_URL = `${DIFY_BASE_URL}/workflows/run`;

const difyAPIClient = axios.create();

async function sendDifyWorkflowRequest({ inputs, difyAppAuthToken }) {
  const payload = {
    response_mode: "blocking",
    inputs: inputs,
    user: "backend",
  };

  let workflow_run_data;
  try {
    logInfo({
      message: `Sending dify workflow request...`,
      payload,
    });
    const response = await difyAPIClient.post(DIFY_WORKFLOW_URL, payload, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${difyAppAuthToken}`,
      },
    });
    logInfo({
      message: `Got dify workflow response`,
      response: response.data,
    });

    const {
      // workflow_run_id,
      // task_id,
      data,
    } = response.data;
    workflow_run_data = data;

    const {
      //   id,
      //   workflow_id,
      status, // "succeeded" or "failed" or ...?
      //   outputs, // { text, json, files }
      error,
      //   elapsed_time,
      //   total_tokens, // number
      //   total_steps, // number
      //   created_at, // timestamp number
      //   finished_at, // timestamp number
    } = workflow_run_data;

    if (status === "failed") {
      const error_message = error ?? "Unknown error";
      throw new Error(error_message);
    }

    return workflow_run_data;
  } catch (error) {
    logError({
      message: `Dify workflow request failed`,
      error,
      error_response: error.response?.data,
      workflow_run_data,
    });
    throw new Error(`Dify workflow request failed: ${error.message}`);
  }
}

module.exports = {
  sendDifyWorkflowRequest,
};
