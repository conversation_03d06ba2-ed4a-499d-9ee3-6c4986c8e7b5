# Deploying and Running Cocoon

Cocoon is our node.js backend running on GKE.

Cocoon is deployed on image-service cluster, in cocoon namespace.

If you haven't configured CLI access to this cluster yet, you can do so by running this command:

```bash
$ gcloud container clusters get-credentials "image-service" --zone "us-central1"
```

Then, use kubectl to run Kubernetes commands.

If you work with Kubernetes a lot, I recommend to also install [Oh My Zsh](https://ohmyz.sh/) with Kubernetes plugin - this will allow you to abbreviate many K8s command, for example, instead of "kubectl get pods" you could type "kgp" - after a while this becomes a second nature.

I strongly recommend to get comfortable with Kubernetes CLI, but you can also access the cluster in [web console](https://console.cloud.google.com/kubernetes/clusters/details/us-central1/image-service/details?hl=en&organizationId=69987700265&project=butterflies-ai). There are also many integrations, for example, for [Visual Studio Code](https://code.visualstudio.com/docs/azure/kubernetes).

## Deployments

There are three Cocoon deployments:

- Production (cocoon-deploy), available at cocoon.butterflies.ai

- Dev (cocoon-dev-deploy), available at cocoon-dev.butterflies.ai

- Staging (cocoon-stage-deploy), available at cocoon-stage.butterflies.ai

Production deploys automatically when there is a push to main branch of google-cloud-functions repo.

Dev deploys when there is a push to dev branch.

Staging is currently updated manually.

## Runbook

### Check the Current deployment

To see the currently running pods, do:

```bash
$ kubectl get pods

NAME                                   READY   STATUS    RESTARTS   AGE
cocoon-deploy-5bb9c45f55-84vhc         1/1     Running   0          10m
cocoon-deploy-5bb9c45f55-d6qtm         1/1     Running   0          9m33s
cocoon-deploy-5bb9c45f55-mz6gj         1/1     Running   0          12m
cocoon-deploy-5bb9c45f55-vnfp4         1/1     Running   0          12m
cocoon-deploy-5bb9c45f55-vxwwc         1/1     Running   0          9m54s
cocoon-dev-deploy-5f9f985fd6-jb26t     1/1     Running   0          12m
cocoon-stage-deploy-869445fff5-hwdkm   1/1     Running   0          21m
```

To see the Docker image currently deployed, do:

```bash
$ kubectl get deploy -o yaml cocoon-deploy | grep image:

image: us-central1-docker.pkg.dev/butterflies-ai/images/cocoon@sha256:c89d949b04352c0a07b85e7cbc638aafaae633c18f8c2eb3f9de9b3ebbb2489e
```

### Checking Logs

To quickly check the logs of a single pod, do:

```bash
$ kubectl logs <pod name>
```

or, add "-f" flag to stream logs.

Logs are ingested into GCP logging as well, so you can always check them in log explorer.

### Dashboard

See https://butterflies.grafana.net/d/adxlkzl1a5mo0a/cocoon?from=now-3h&to=now&timezone=browser&tab=queries&showCategory=Panel%20options&refresh=30s&var-query0=&editIndex=0&var-env=production

### Checking Deployment History

To check the deployment history, do:

```bash
$ kubectl rollout history deploy cocoon-deploy
deployment.apps/cocoon-deploy
REVISION  CHANGE-CAUSE
176       auto deployment f4591aa
177       auto deployment 2a7bb2e
178       auto deployment 7ea1687
179       auto deployment b1897f6
180       auto deployment b1897f6
181       auto deployment b1897f6
182       auto deployment 923d14c
183       auto deployment 6e4b53a
184       auto deployment 63a39ad
185       auto deployment f54acdc
186       auto deployment 6fb7671
```

Notice that the hashes correspond to the Git commit hashes.

### Bad Rollout

If you commit a change that breaks the deployment (to the extend it cannot start), don't worry, Kubernetes will roll it back after a few attempts to start.

If you have committed a change that does not break the deployment (it starts), but you want to rollback anyway, do:

```bash
kubectl rollout undo deployment/cocoon-deploy
```

This will roll the deployment back to the previous image version.
