const express = require("express");
const axios = require("axios");
const crypto = require("crypto");
const { v4: uuid } = require("uuid");
const OSS = require("ali-oss");
const redis = require("redis");
const { supabase } = require("./supabaseClient");
const { Storage } = require("@google-cloud/storage");
const storage = new Storage();
const { OpenAI } = require("openai");
const { fal } = require("@fal-ai/client");
const { Anthropic } = require("@anthropic-ai/sdk");
const { generateAndUploadVoiceAudio } = require("./voiceHelpers");

const bucketName = "butterflies-images-v1-us";
const bucketPath = "videos";

const app = express.Router();

// Initialize Anthropic client
const anthropic = new Anthropic({
  // apiKey: process.env.ANTHROPIC_API_KEY,
  apiKey:
    "************************************************************************************************************",
});

// Middleware to parse JSON bodies
app.use(express.json());

// Redis client setup (assumed to be configured elsewhere and injected or global)
// For this example, replace with your actual Redis client initialization
const redisClient = redis.createClient({
  url: process.env.REDIS_URL || "redis://localhost:6379",
});
redisClient
  .connect()
  .catch((err) => console.error("Redis connection error:", err));

// Utility Functions

/** Calculate MD5 hash
 * @param {string | Buffer} data - Data to hash
 * @returns {string} Hex-encoded MD5 hash
 */
function getMd5(data) {
  const hash = crypto.createHash("md5");
  hash.update(data);
  return hash.digest("hex");
}

/** Generate YY signature
 * @param {string} link - Full URL
 * @param {string} data - JSON string of request data
 * @param {number} ts - Timestamp in milliseconds
 * @returns {string} YY signature
 */
function yy(link, data, ts) {
  if (!data) data = "{}";
  const escapedLink = encodeURIComponent(
    link.slice("https://hailuoai.video".length),
  );
  return getMd5(escapedLink + "_" + data + getMd5(ts.toString()) + "ooui");
}

/** Generate URL with query parameters
 * @param {string} uri - API endpoint URI
 * @param {string} uid - User ID
 * @param {number} ts - Timestamp
 * @returns {string} Constructed URL
 */
function genUrl(uri, uid, ts) {
  const separator = uri.includes("?") ? "&" : "?";
  return `https://hailuoai.video${uri}${separator}device_platform=web&app_id=3001&version_code=22202&uuid=${uid}&lang=en&device_id=321985753385615365&os_name=Mac&browser_name=chrome&device_memory=8&cpu_core_num=10&browser_language=en-US&browser_platform=MacIntel&screen_width=1512&screen_height=982&unix=${ts}`;
}

/** Make HTTP request with custom headers
 * @param {string} method - HTTP method (GET, POST)
 * @param {string} uri - API endpoint URI
 * @param {Object} [data] - Request body data
 * @returns {Promise<Object>} Response data
 */
async function doReq(method, uri, data) {
  const uid = "56dd6eaf-4a7a-43ac-85a3-e60b6423c392";
  const ts = Math.floor(Date.now() / 1000) * 1000;
  const link = genUrl(uri, uid, ts);
  const yyValue = yy(link, data ? JSON.stringify(data) : "", ts);
  const headers = {
    Accept: "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9",
    Cookie: process.env.HAILUOAI_COOKIE,
    Priority: "u=1, i",
    Referer: "https://hailuoai.video/create",
    "Sec-Ch-Ua":
      '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"macOS"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    Token: process.env.HAILUOAI_AUTH_TOKEN,
    "User-Agent":
      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
    YY: yyValue,
  };
  if (method === "POST") {
    headers["Content-Type"] = "application/json";
    headers["Origin"] = "https://hailuoai.video";
  }
  try {
    const response = await axios({
      method,
      url: link,
      headers,
      data,
    });
    return response.data;
  } catch (error) {
    console.error("Request failed with details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      requestData: data,
      requestUrl: link,
    });
    throw error;
  }
}

/** Get upload policy from API
 * @returns {Promise<Object>} Policy data
 */
async function getPolicy() {
  const response = await doReq("GET", "/v1/api/files/request_policy", null);
  return response.data;
}

/** Upload image to Alibaba Cloud OSS
 * @param {Object} policy - Upload policy data
 * @param {Buffer} fileContent - Image content
 * @param {string} fileMimeType - MIME type (e.g., '.jpeg')
 * @returns {Promise<string>} Upload ID
 */
async function uploadImage(policy, fileContent, fileMimeType) {
  const client = new OSS({
    region: policy.endpoint.split(".")[0].slice(4),
    accessKeyId: policy.accessKeyId,
    accessKeySecret: policy.accessKeySecret,
    stsToken: policy.securityToken,
    bucket: policy.bucketName,
  });
  const uploadID = uuid();
  const objectKey = `${policy.dir}/${uploadID}${fileMimeType}`;
  await client.put(objectKey, fileContent);
  return uploadID;
}

/** Get file ID after upload
 * @param {Object} policy - Upload policy data
 * @param {string} uploadID - Upload ID
 * @param {Buffer} fileContent - Image content
 * @param {string} fileMimeType - MIME type
 * @returns {Promise<string>} File ID
 */
async function getFileID(policy, uploadID, fileContent, fileMimeType) {
  const callbackData = {
    fileName: uploadID + fileMimeType,
    originFileName: "picture" + fileMimeType,
    dir: policy.dir,
    endpoint: policy.endpoint,
    bucketName: policy.bucketName,
    size: fileContent.length.toString(),
    mimeType: fileMimeType.slice(1),
    fileMd5: getMd5(fileContent),
    fileScene: 10,
  };
  const response = await doReq(
    "POST",
    "/v1/api/files/policy_callback",
    callbackData,
  );
  if (!response.data.fileID) {
    throw new Error("FileID not present in response");
  }
  return response.data.fileID;
}

/** Make video generation request
 * @param {string} fileID - File ID from upload
 * @param {string} videoPrompt - Video prompt
 * @param {string} fileName - Original file name
 * @returns {Promise<string>} Video ID
 */
async function makeUploadVideoRequest(fileID, videoPrompt, fileName) {
  const mimeType = "." + fileName.split(".").pop();
  const videoReqData = {
    desc: videoPrompt,
    useOriginPrompt: false,
    fileList: [
      {
        id: fileID,
        name: fileName,
        type: mimeType.slice(1),
      },
    ],
    modelID: "23001",
    quantity: "1",
  };
  const response = await doReq(
    "POST",
    "/api/multimodal/generate",
    videoReqData,
  );
  if (!response.data.id) {
    throw new Error("Failed to get video ID");
  }
  return response.data.id;
}

// Routes

/** Handle video generation request
 * @param {string} image_url - URL of the image to process
 * @param {string} video_prompt - Prompt for video generation
 * @returns {Object} Response with request_id
 */
app.post("/generate", async (req, res) => {
  const { image_url, video_prompt } = req.body;

  console.log("image_url", image_url);
  console.log("video_prompt", video_prompt);

  if (!image_url) {
    return res.status(400).json({ error: "Missing image_url" });
  }

  try {
    // Download image
    const imageResponse = await axios.get(image_url, {
      responseType: "arraybuffer",
    });
    const fileContent = Buffer.from(imageResponse.data);
    const fileMimeType =
      "." + imageResponse.headers["content-type"].split("/")[1];

    // Get policy and upload
    const policy = await getPolicy();
    const uploadID = await uploadImage(policy, fileContent, fileMimeType);
    const fileID = await getFileID(policy, uploadID, fileContent, fileMimeType);

    // Store in Redis
    const redisRequestData = {
      status: "pending",
      request_id: uploadID,
      file_id: fileID,
      file_name: image_url.split("/").pop(),
      video_prompt,
    };
    const redisJson = JSON.stringify(redisRequestData);
    await redisClient.rPush("pending_video_requests", redisJson);
    await redisClient.hSet("video_request_statuses", uploadID, redisJson);

    res.set({
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, GET",
      "Access-Control-Allow-Headers": "*",
      "Content-Type": "application/json",
    });
    res.json({ request_id: uploadID });
  } catch (error) {
    console.error("Video generate error:", error);
    res.status(500).json({ error: error.message });
  }
});

/** Check video generation status
 * @param {string} request_id - ID of the video request
 * @returns {Object} Status details
 */
app.post("/status", async (req, res) => {
  const { request_id } = req.body;
  if (!request_id) {
    return res.status(400).json({ error: "Missing request_id" });
  }

  try {
    const response = await redisClient.hGet(
      "video_request_statuses",
      request_id,
    );
    if (!response) {
      return res.status(404).json({ error: "Request ID not found" });
    }
    res.set({
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, GET",
      "Access-Control-Allow-Headers": "*",
      "Content-Type": "application/json",
    });
    res.json(JSON.parse(response));
  } catch (error) {
    console.error("Status check error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Generate video for a specific post
app.post("/generateVideoForPost", async (req, res) => {
  try {
    const { postId, video_prompt } = req.body;

    // Fetch post data from Supabase
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select("media_url")
      .eq("id", postId)
      .single();

    if (postError) {
      throw postError;
    }

    if (!post || !post.media_url) {
      throw new Error("Post not found or missing media URL");
    }

    const mediaUrl = post.media_url;

    // Extract UUID from media URL
    const uuidMatch = mediaUrl.match(/\/([a-f0-9-]+)\.(webp|png|jpg|jpeg)/i);
    if (!uuidMatch) {
      throw new Error("Could not extract UUID from media URL");
    }
    const imageUuid = uuidMatch[1];

    // Construct original image URL
    const originalImageUrl = `https://storage.googleapis.com/butterflies-images-v1-us/orig/${imageUuid}.png`;

    // Generate video using the extracted function
    const { requestId } = await generateVideoFromURL({
      imageUrl: originalImageUrl,
      postId,
      video_prompt,
    });

    res.json({ requestId });
  } catch (error) {
    console.error("Error generating video for post:", error);
    res.status(500).json({ error: error.message });
  }
});

app.post("/generateVideoForImageURL", async (req, res) => {
  try {
    const { image_url, video_prompt } = req.body;

    if (!image_url) {
      res.status(400).json({ error: "image_url is required" });
      return;
    }

    // Generate video using null as post_id
    const { requestId } = await generateVideoFromURL({
      imageUrl: image_url,
      videoPrompt: video_prompt,
    });

    res.json({ requestId });
  } catch (error) {
    console.error("Error generating video from URL:", error);
    res.status(500).json({ error: error.message });
  }
});

// Using dynamic import for ES module
let Client;
(async () => {
  const gradioClient = await import("@gradio/client");
  Client = gradioClient.Client;
})();

/** Generate video from image URL
 * @param {string} imageUrl - URL of the image to process
 * @param {string|null} postId - ID of the post, optional
 * @param {string|null} videoPrompt - Custom prompt for video generation, optional
 * @returns {Promise<Object>} Object containing requestId
 */
async function generateVideoFromURL({
  imageUrl,
  postId,
  videoClipId,
  videoPrompt = null,
  soundEffects = null,
}) {
  // Request video generation
  const requestBody = {
    image_url: imageUrl,
  };

  // Add video_prompt if provided
  if (videoPrompt) {
    requestBody.video_prompt = videoPrompt;
  }

  const videoResponse = await axios.post(
    "https://api.butterflies.ai/video/generate",
    requestBody,
  );

  const requestId = videoResponse.data.request_id;

  // Create task object with required fields
  const taskData = {
    request_id: requestId,
    status: "processing",
    image_url: imageUrl,
    sound_effect: soundEffects,
    shot_action: videoPrompt,
  };

  // Add post_id only if it exists
  if (postId) {
    taskData.post_id = postId;
  }

  if (videoClipId) {
    taskData.video_clip_id = videoClipId;
  }

  // Add sound_effect if provided
  if (soundEffects) {
    taskData.sound_effect = soundEffects;
  }

  // Create video generation task
  const { error: insertError } = await supabase
    .from("video_generation_tasks")
    .insert(taskData);

  if (insertError) {
    throw insertError;
  }

  // Start background polling
  pollVideoStatus(requestId);

  return { requestId };
}

// Poll video status
async function pollVideoStatus(requestId) {
  let attempts = 0;
  const maxAttempts = 120; // 10 minutes (120 * 5 seconds)

  const pollInterval = setInterval(async () => {
    try {
      attempts++;

      const statusResponse = await axios.post(
        "https://api.butterflies.ai/video/status",
        {
          request_id: requestId,
        },
      );

      console.log(statusResponse.data);

      if (
        statusResponse.data.status === "completed" &&
        statusResponse.data.download_url
      ) {
        // Update task with download URL
        const { error: updateError } = await supabase
          .from("video_generation_tasks")
          .update({
            status: "completed",
            download_url: statusResponse.data.download_url,
          })
          .eq("request_id", requestId);

        if (updateError) {
          throw updateError;
        }
        clearInterval(pollInterval);
      }

      if (attempts >= maxAttempts) {
        const { error: timeoutError } = await supabase
          .from("video_generation_tasks")
          .update({ status: "timeout" })
          .eq("request_id", requestId);

        if (timeoutError) {
          throw timeoutError;
        }
        clearInterval(pollInterval);
      }
    } catch (error) {
      console.error(
        `Error polling video status for request ${requestId}:`,
        error,
      );
      clearInterval(pollInterval);
    }
  }, 5000);
}

// Background Processing

/** Process video requests in one pass
 * @returns {Promise<void>}
 */
async function processVideoRequestsOnePass() {
  const queueLimit = parseInt(process.env.HAILUOAI_QUEUE_SIZE) || 5;
  let processingRequests = {};

  // Load processing requests
  const previousProcessing = await redisClient.hGetAll(
    "processing_video_requests",
  );
  for (const [key, value] of Object.entries(previousProcessing)) {
    processingRequests[key] = JSON.parse(value);
  }

  // Process pending requests
  while (Object.keys(processingRequests).length < queueLimit) {
    const pendingRequestRaw = await redisClient.lPop("pending_video_requests");
    if (!pendingRequestRaw) break;

    const pendingRequest = JSON.parse(pendingRequestRaw);
    try {
      const videoID = await makeUploadVideoRequest(
        pendingRequest.file_id,
        pendingRequest.video_prompt,
        pendingRequest.file_name,
      );
      pendingRequest.video_id = videoID;
      pendingRequest.status = "processing";
      const redisData = JSON.stringify(pendingRequest);
      processingRequests[pendingRequest.request_id] = pendingRequest;
      await redisClient.hSet(
        "processing_video_requests",
        pendingRequest.request_id,
        redisData,
      );
      await redisClient.hSet(
        "video_request_statuses",
        pendingRequest.request_id,
        redisData,
      );
    } catch (error) {
      console.error("Video request error:", error);
      pendingRequest.status = "failed";
      const redisData = JSON.stringify(pendingRequest);
      await redisClient.hSet(
        "video_request_statuses",
        pendingRequest.request_id,
        redisData,
      );
    }
  }

  if (Object.keys(processingRequests).length === 0) return;

  // Check processing status
  const idList = Object.keys(processingRequests).join("%2C");
  try {
    const response = await doReq(
      "GET",
      `/api/multimodal/processing?idList=${idList}`,
      null,
    );
    const videos = response.data.videos || [];
    for (const video of videos) {
      if (video.downloadURL) {
        const processingRequest = processingRequests[video.id];
        if (processingRequest) {
          delete processingRequests[video.id];
          processingRequest.download_url = video.downloadURL;
          processingRequest.status = "completed";
          const finalRedisStatus = JSON.stringify(processingRequest);
          await redisClient.hDel(
            "processing_video_requests",
            processingRequest.request_id,
          );
          await redisClient.hSet(
            "video_request_statuses",
            processingRequest.request_id,
            finalRedisStatus,
          );
        }
      }
    }
  } catch (error) {
    console.error("Processing list error:", error);
  }
}

// Start background processing
setInterval(processVideoRequestsOnePass, 8000);

app.post("/downloadVideoToGCP", async (req, res) => {
  const { type, record, old_record } = req.body;
  if (type !== "UPDATE" || !record || !old_record) {
    console.log(
      "**** downloadVideoToGCP skipping non-update or missing records",
      { type, record, old_record },
    );
    res.sendStatus(400);
    return;
  }

  // Only process if download_url has changed
  if (record.download_url === old_record.download_url) {
    console.log("**** downloadVideoToGCP skipping - download_url unchanged", {
      record_id: record.id,
    });
    res.sendStatus(200);
    return;
  }

  try {
    // Fetch the video generation task
    const { data: task, error } = await supabase
      .from("video_generation_tasks")
      .select("*")
      .eq("id", record.id)
      .single();

    if (error) throw error;
    if (!task?.download_url) {
      console.warn(`No download_url found for task ${record.id}`);
      res.sendStatus(404);
      return;
    }

    let soundEffect;

    // Check if task.sound_effect already exists
    if (task.sound_effect) {
      console.log(`Using existing sound effect: ${task.sound_effect}`);
      soundEffect = task.sound_effect;
    } else {
      // Generate sound effect description using GPT-4
      console.log("Generating new sound effect with OpenAI");
      const openai = new OpenAI({
        timeout: 30 * 1000, // 30 seconds
      });

      const completion = await openai.chat.completions.create({
        model: "gpt-4o",
        max_tokens: 100,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Describe exactly what sound effects will go with this image. Only describe what you can see. Use terse, literal language. Short descriptions separated by commas. Only provide 1 sentence of the sound effect.",
              },
              {
                type: "image_url",
                image_url: {
                  url: record.image_url,
                  detail: "low",
                },
              },
            ],
          },
        ],
      });

      soundEffect = completion.choices[0].message.content;
    }

    console.log("soundEffect", soundEffect);

    // Generate audio with the sound effect
    const audioResult = await generateAudio({
      url: task.download_url,
      prompt: soundEffect,
    });
    const videoWithAudio = audioResult.video.url;

    // Download the video
    const response = await axios({
      method: "get",
      url: videoWithAudio,
      responseType: "arraybuffer",
    });

    // Generate a unique filename
    const filename = `${uuid()}.mp4`;
    const destinationPath = `${bucketPath}/${filename}`;

    // Upload to GCS
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(destinationPath);

    await file.save(response.data, {
      metadata: {
        contentType: "video/mp4",
      },
      resumable: false,
    });

    const publicUrl = `https://storage.googleapis.com/${bucketName}/${destinationPath}`;

    // Update the task with the new URL
    const { error: updateError } = await supabase
      .from("video_generation_tasks")
      .update({ gcs_url: publicUrl, sound_effect: soundEffect })
      .eq("id", record.id);

    if (updateError) throw updateError;

    // If task has post_id, update the post's video_url
    if (task.post_id) {
      const { error: postUpdateError } = await supabase
        .from("posts")
        .update({ video_url: publicUrl })
        .eq("id", task.post_id);

      if (postUpdateError) {
        console.error("Error updating post video_url:", postUpdateError);
        // Don't throw here as the video was still successfully processed
      }
    }

    res.json({ success: true, url: publicUrl });
  } catch (error) {
    console.error("Error in downloadVideoToGCP:", error);
    res.status(500).json({ error: error.message });
  }
});

app.post("/generateVoice", async (req, res) => {
  try {
    const { text, voice_id, voice_description } = req.body;

    if (!text) {
      return res.status(400).json({ error: "Text is required" });
    }

    // Either use an existing voice ID or generate a new one with the provided description
    let finalVoiceId = voice_id;
    if (!voice_id && voice_description) {
      // If no voice ID but we have a description, generate a new voice
      const { generateVoiceWithDescription } = require("./voiceHelpers");
      finalVoiceId = await generateVoiceWithDescription(voice_description);
    }

    if (!finalVoiceId) {
      return res
        .status(400)
        .json({ error: "Either voiceId or voice_description is required" });
    }

    // Generate the voice audio
    const voiceUrl = await generateAndUploadVoiceAudio(
      finalVoiceId,
      text,
      "api-request", // Use a generic identifier for the profile ID
      `test-${Date.now()}`, // Use timestamp for part ID
    );

    return res.status(200).json({
      success: true,
      voiceUrl,
      voiceId: finalVoiceId,
    });
  } catch (error) {
    console.error("Error in generateVoice endpoint:", error);
    return res.status(500).json({
      error: "Failed to generate voice",
      details: error.message,
    });
  }
});

app.get("/generateAudioTest", async (req, res) => {
  console.log("[generateAudioTest] Starting test endpoint");
  try {
    const url =
      "https://storage.googleapis.com/butterflies-images-v1-us/videos/b64a5f84-b3b7-4a32-8b94-a65641de7712.mp4";
    console.log("[generateAudioTest] Using test URL:", url);

    const result = await generateAudio({ url });
    console.log("[generateAudioTest] Success, returning result");
    res.json(result);
  } catch (error) {
    console.error("[generateAudioTest] Route error:", error);
    res.status(500).json({
      error: error.message || "Internal server error",
      stack: error.stack,
    });
  }
});

app.post("/generateVideoFromPrompt", async (req, res) => {
  console.log("[generateVideoFromPrompt] Starting text-to-video generation");
  try {
    const { prompt } = req.body;

    if (!prompt) {
      return res.status(400).json({
        error: "Missing required parameter: text",
      });
    }

    const messages = [
      {
        role: "user",
        content: `Take this user's prompt for a video.
<video_prompt>
${prompt}
</video_prompt>

Return in JSON format only the JSON object.

  {
      "first_frame": "a detailed visual description of the first image frame of the video. Use terse, simple language. Describe the lighting, and scenery and characters. When describing characters, vividly describe their clothing and pose and what they may be holding if anything.",
      "shot_action": "a description of the action happening in the 6 second video clip. Use terse, simple language. Also describe the camera movements and the speed of the shot (slow motion, regular motion, fast motion)",
      "sound_effects": "A description of the sound effects happening in the 6 second video clip. Describe only the most important sound. Never describe music.keep it under 8 words, terse literal language" // properly escape double quote or use single quotes
  }`,
      },
    ];

    // Call Claude API to generate video content
    const completion = await anthropic.messages.create({
      model: "claude-3-7-sonnet-********",
      max_tokens: 1024,
      messages,
      system:
        "When asked to output JSON, provide only the raw JSON with no explanations, markdown formatting, or code blocks.",
    });

    console.log("completion.content[0].text", completion.content[0].text);

    const response = JSON.parse(completion.content[0].text);

    console.log("response", response);

    // Generate image using Fireworks AI
    const fireworksUrl =
      "https://api.fireworks.ai/inference/v1/workflows/accounts/fireworks/models/flux-1-dev-fp8/text_to_image";
    const fireworksHeaders = {
      "Content-Type": "application/json",
      Accept: "image/png",
      Authorization: `Bearer twXcAfk7EAJrjdGqRiDz8fGs0ghPvcNmlE2gANDua4q1KCoq`,
    };
    const fireworksData = {
      prompt: response.first_frame,
      aspect_ratio: "9:16",
      guidance_scale: 3.5,
      num_inference_steps: 30,
    };

    console.log("[generateVideoFromPrompt] Generating image with Fireworks AI");
    const fireworksResponse = await axios.post(fireworksUrl, fireworksData, {
      headers: fireworksHeaders,
      responseType: "arraybuffer",
    });

    if (fireworksResponse.status !== 200) {
      throw new Error(`Fireworks API error: ${fireworksResponse.status}`);
    }

    // Upload image to GCS
    console.log("[generateVideoFromPrompt] Uploading image to GCS");
    const imageUuid = uuid();
    console.log(`[generateVideoFromPrompt] Generated image UUID: ${imageUuid}`);
    console.log(`[generateVideoFromPrompt] Bucket name: ${bucketName}`);
    console.log(`[generateVideoFromPrompt] Bucket path: ${bucketPath}`);
    console.log(
      `[generateVideoFromPrompt] Full path: ${bucketPath}/orig/${imageUuid}.png`,
    );

    const bucket = storage.bucket(bucketName);
    const file = bucket.file(`orig/${imageUuid}.png`);

    try {
      console.log(
        `[generateVideoFromPrompt] Image data length: ${fireworksResponse.data.length}`,
      );
      await file.save(Buffer.from(fireworksResponse.data), {
        metadata: {
          contentType: "image/png",
        },
        resumable: false,
      });
      console.log(
        `[generateVideoFromPrompt] Image successfully uploaded to: gs://${bucketName}/orig/${imageUuid}.png`,
      );

      // Verify file exists after upload
      const [exists] = await file.exists();
      console.log(
        `[generateVideoFromPrompt] File exists check after upload: ${exists}`,
      );
    } catch (error) {
      console.error(
        `[generateVideoFromPrompt] Error uploading image to GCS:`,
        error,
      );
      throw error;
    }

    const imageUrl = `https://storage.googleapis.com/${bucketName}/orig/${imageUuid}.png`;
    console.log(`[generateVideoFromPrompt] Image uploaded to: ${imageUrl}`);

    // Generate video from the image URL
    console.log("[generateVideoFromPrompt] Generating video from image URL");
    const { requestId } = await generateVideoFromURL({
      imageUrl: imageUrl,
      videoPrompt: response.shot_action,
      soundEffects: response.sound_effects,
      imagePrompt: response.first_frame,
    });

    // Return success response
    return res.status(200).json({
      success: true,
      requestId,
      imageUrl,
      imagePrompt: response.first_frame,
      shotAction: response.shot_action,
      soundEffects: response.sound_effects,
    });
  } catch (error) {
    console.error("[generateVideoFromPrompt] Error:", error);
    return res.status(500).json({
      error: error.message || "Internal server error",
      stack: error.stack,
    });
  }
});

app.get("/getVideoStatus", async (req, res) => {
  const { requestId } = req.query;
  const statusResponse = await axios.post(
    "https://api.butterflies.ai/video/status",
    {
      request_id: requestId,
    },
  );

  let ret = statusResponse.data;

  if (statusResponse.data.status === "completed") {
    const { data } = await supabase
      .from("video_generation_tasks")
      .select("*")
      .eq("request_id", requestId)
      .single();

    ret = { ...data, ...ret };
  }

  res.status(200).json(ret);
});

async function generateAudio({ url, prompt, useGradio = false }) {
  console.log("[generateAudio] Starting with url:", url);
  try {
    if (useGradio) {
      if (!Client) {
        console.log("[generateAudio] Importing Gradio client");
        const gradioClient = await import("@gradio/client");
        Client = gradioClient.Client;
      }

      console.log("[generateAudio] Fetching video from URL");
      const response = await fetch(url);
      console.log("[generateAudio] Fetch response status:", response.status);
      const videoBlob = await response.blob();
      console.log("[generateAudio] Video blob size:", videoBlob.size);

      console.log("[generateAudio] Connecting to Gradio client");
      const client = await Client.connect("hkchengrex/MMAudio");
      console.log("[generateAudio] Connected to Gradio client");

      console.log("[generateAudio] Starting prediction");

      const result = await client.predict("/predict", {
        video: {
          video: videoBlob,
          subtitles: null,
        },
        prompt,
        negative_prompt: "music",
        seed: -1,
        num_steps: 25,
        cfg_strength: 4.5,
        duration: 8,
      });

      console.log("[generateAudio] Prediction complete, data:", result.data);
      return result.data[0];
    } else {
      // Use FAL by default
      fal.config({
        credentials:
          "58baa7f5-b431-40b4-a15f-dd8d0c3e0ef4:aa7d590477d2fefaa83c0e4cc4fbc527",
      });

      console.log("[generateAudio] Using FAL client");
      const result = await fal.subscribe("fal-ai/mmaudio-v2", {
        input: {
          video_url: url,
          prompt: prompt ?? "",
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            update.logs.map((log) => log.message).forEach(console.log);
          }
        },
      });

      console.log("[generateAudio] FAL result:", result.data);
      console.log("[generateAudio] FAL request ID:", result.requestId);
      return result.data;
    }
  } catch (error) {
    console.error("[generateAudio] Error:", error);
    throw error;
  }
}

module.exports = {
  app,
  generateVideoFromURL,
};
