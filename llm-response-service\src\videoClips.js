const express = require("express");
const { supabase } = require("./supabaseClient");
const { Anthropic } = require("@anthropic-ai/sdk");
const { generateBio } = require("./llmHelper");
const { callAndLogOpenAI } = require("./llm");
const { generateComfyRequest } = require("./comfy");
const { generateVideoFromURL } = require("./video");
const { generateAndUploadVoiceAudio } = require("./voiceHelpers");
const app = express.Router();
const fs = require("fs");
const axios = require("axios");
const ffmpeg = require("fluent-ffmpeg");
const { Storage } = require("@google-cloud/storage");
const bucketName = process.env.GCS_BUCKET_NAME || "butterflies-images-v1-us"; // Use environment variable or default bucket
const bucketPath = "videos";
const os = require("os");
const path = require("path");

// Initialize Anthropic client
const anthropic = new Anthropic({
  // apiKey: process.env.ANTHROPIC_API_KEY,
  apiKey:
    "************************************************************************************************************",
});

// Middleware to parse JSON bodies
app.use(express.json());

/**
 * Generate video clip for a specific profile
 * Fetches bot profile data and generates video content using Claude
 */
app.post("/generateVideoClipForProfileId", async (req, res) => {
  try {
    const { profileId } = req.body;

    if (!profileId) {
      return res.status(400).json({ error: "profileId is required" });
    }

    // Fetch bot profile with joined data
    const { data: profileData, error: profileError } = await supabase
      .from("bots")
      .select(
        `
        *,
        profiles!bots_profile_id_fkey (*)
      `,
      )
      .eq("profile_id", profileId)
      .single();

    if (profileError) {
      console.error("Error fetching profile:", profileError);
      return res.status(500).json({ error: "Failed to fetch profile data" });
    }

    if (!profileData) {
      return res.status(404).json({ error: "Profile not found" });
    }

    console.log(profileData);

    // const response = {
    //   title: "The Heart of Wakanda",
    //   overarching_story:
    //     "T'Challa faces a devastating attack on Wakanda's vibranium reserves, forcing him to make difficult choices between tradition and innovation to protect his people.",
    //   parts: [
    //     {
    //       first_frame_description:
    //         "T'Challa sitting cross-legged on palace balcony, eyes closed in meditation, Wakandan city gleaming with advanced technology below, sunrise casting golden light",
    //       shot_action:
    //         "T'Challa's peaceful meditation is interrupted as his kimoyo beads light up with an urgent alert. He opens his eyes, face shifting from serenity to concern as he stands quickly.",
    //       dialog: {
    //         character: "T'Challa",
    //         dialog:
    //           "The ancestral plane will have to wait. Wakanda needs its king.",
    //         delay: 2,
    //       },
    //     },
    //     {
    //       first_frame_description:
    //         "T'Challa in Black Panther suit without mask, running through vibranium mine, explosion debris falling, miners evacuating, red emergency lights flashing",
    //       shot_action:
    //         "T'Challa leaps over fallen debris, helps injured miners to safety. His suit absorbs a kinetic blast as another explosion rocks the mine. He activates his mask, eyes glowing.",
    //       dialog: {
    //         character: "T'Challa",
    //         dialog: "Protect the workers! I will handle the intruders.",
    //         delay: 1,
    //       },
    //     },
    //     {
    //       first_frame_description:
    //         "Black Panther confronting masked mercenaries, vibranium extraction device glowing blue, cave walls cracking, warriors fighting in background",
    //       shot_action:
    //         "Black Panther dodges energy blasts, releases built-up kinetic energy in a powerful shockwave that knocks down multiple attackers. He disables the extraction device with precise strikes.",
    //       dialog: {
    //         character: "T'Challa",
    //         dialog:
    //           "Our ancestors protected this resource for generations. It will not fall today!",
    //         delay: 2,
    //       },
    //     },
    //     {
    //       first_frame_description:
    //         "T'Challa standing before Wakandan tribal council, holographic display showing new mine defense systems, elders with concerned expressions, traditional tapestries on walls",
    //       shot_action:
    //         "T'Challa gestures to the holographic plans, speaking passionately. The council members initially look skeptical but gradually nod in agreement as T'Challa concludes his proposal.",
    //       dialog: {
    //         character: "T'Challa",
    //         dialog:
    //           "We must honor our traditions by protecting them with innovation. Wakanda's future depends on this balance.",
    //         delay: 1,
    //       },
    //     },
    //   ],
    // };

    const messages = [
      {
        role: "user",
        content: `Generate a story for the character based on these characteristics:
    Name:
    ${generateBio(profileData)}

    The story should be told in 4 visual shots in JSON format. The story should be action packed and engaging, so think about a story you can tell in 30 seconds. The first video should be an engaging hook, the second part should be action, 3rd part should be climax, 4th part is some resolution. Return in JSON format, only the JSON object:

    Rules:
    1. Each "shot" can only last 6 seconds

    Return only the JSON object in the following format:

    {
      title: "The title of the story",
      overarching_story: "the story that will be told throughout the video",
      parts: [
        {
          first_frame_description: "Describe the most important visual details of the first image frame of the shot in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky'",
          shot_action: "Describe the movement of the 6 second scene",
          dialog: {
            text: "Dialog line", // any dialog lines should only be spoken by the main character
            delay: 0, // how many seconds in the scene to wait before the dialog is played, remember, the scene is only 6 seconds, so maximum delay is 3 seconds
          }
        },
      ]
    }
    `,
      },
    ];

    // Call Claude API to generate video content
    const completion = await anthropic.messages.create({
      model: "claude-3-7-sonnet-20250219",
      max_tokens: 1024,
      messages,
      system:
        "When asked to output JSON, provide only the raw JSON with no explanations, markdown formatting, or code blocks.",
    });

    const response = JSON.parse(completion.content[0].text);

    // Generate tasks for each part
    const tasks = await Promise.all(
      response.parts.map(async (part) => {
        const task = await generateComfyRequest({
          bot: profileData,
          descriptionOfImage: part.first_frame_description,
          generationType: "video_clip_frame",
          priority: "high",
          batch_size: 1,
        });
        return task;
      }),
    );

    // Create payload for video_clips table
    const script = {
      title: response.title,
      overarching_story: response.overarching_story,
      parts: response.parts.map((part, index) => ({
        ...part,
        task_id: tasks[index].id,
      })),
    };

    // Insert into Supabase video_clips table
    const { data: videoClip, error } = await supabase
      .from("video_clips")
      .insert([{ script, profile_id: profileId }])
      .select()
      .single();

    if (error) throw error;

    // Return the generated content
    return res.json({ tasks, videoClip });
  } catch (error) {
    console.error("Error in generateVideoClipForProfileId:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
    });
  }
});

/**
 * Turn a video clip into a video by generating videos for each part
 * Takes a video clip ID and processes all parts to generate videos
 */
app.post("/turnVideoClipToVideo", async (req, res) => {
  try {
    const { videoClipId } = req.body;

    if (!videoClipId) {
      return res.status(400).json({ error: "videoClipId is required" });
    }

    // Fetch the video clip
    const { data: videoClip, error: videoClipError } = await supabase
      .from("video_clips")
      .select("*")
      .eq("id", videoClipId)
      .single();

    if (videoClipError) {
      console.error("Error fetching video clip:", videoClipError);
      return res.status(500).json({ error: "Failed to fetch video clip" });
    }

    if (!videoClip) {
      return res.status(404).json({ error: "Video clip not found" });
    }

    // Update status to processing
    const { error: updateError } = await supabase
      .from("video_clips")
      .update({ status: "processing_videos" })
      .eq("id", videoClipId);

    if (updateError) {
      console.error("Error updating video clip status:", updateError);
      return res
        .status(500)
        .json({ error: "Failed to update video clip status" });
    }

    // Start processing in the background
    (async () => {
      try {
        console.log(
          `Starting background processing for video clip ${videoClipId}`,
        );
        await turnVideoClipToVideo({ videoClip });

        // Update status to completed when done
        const { error: finalUpdateError } = await supabase
          .from("video_clips")
          .update({ status: "videos_completed" })
          .eq("id", videoClipId);

        if (finalUpdateError) {
          console.error(
            "Error updating final video clip status:",
            finalUpdateError,
          );
        }
      } catch (processError) {
        console.error("Error in background processing:", processError);

        // Update status to error
        const { error: errorUpdateError } = await supabase
          .from("video_clips")
          .update({
            status: "videos_error",
            error_message: processError.message,
          })
          .eq("id", videoClipId);

        if (errorUpdateError) {
          console.error("Error updating error status:", errorUpdateError);
        }
      }
    })();

    // Return immediately with a success message
    return res.json({
      message: "Video generation started",
      videoClipId,
      status: "processing_videos",
    });
  } catch (error) {
    console.error("Error in turnVideoClipToVideo endpoint:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
    });
  }
});

async function turnVideoClipToVideo({ videoClip }) {
  try {
    console.log(`Processing video clip ${videoClip.id}`);

    if (!videoClip || !videoClip.script || !videoClip.script.parts) {
      throw new Error("Invalid video clip structure");
    }

    // Check if any parts have dialog and need voice processing
    const hasDialogParts = videoClip.script.parts.some(
      (part) => part.dialog && part.dialog.text,
    );

    // Check for an existing voice ID, but don't wait to generate if not found
    let profileVoiceId = null;
    if (hasDialogParts) {
      console.log(
        `Checking for existing voice ID for profile ${videoClip.profile_id}`,
      );

      // Check if a voice already exists for this profile
      const { data: existingVoice, error: voiceError } = await supabase
        .from("video_clip_voices")
        .select("voice_id")
        .eq("profile_id", videoClip.profile_id)
        .single();

      if (!voiceError && existingVoice && existingVoice.voice_id) {
        console.log(
          `Found existing voice ID for profile ${videoClip.profile_id}: ${existingVoice.voice_id}`,
        );
        profileVoiceId = existingVoice.voice_id;
      } else {
        console.log(
          `No existing voice found for profile ${videoClip.profile_id}, will generate during first dialog processing`,
        );
        // Voice ID will be null, and will be generated during the first dialog processing
      }
    }

    // Process each part
    const updatedParts = await Promise.all(
      videoClip.script.parts.map(async (part, index) => {
        try {
          console.log(`Processing part ${index}`);
          const partUpdates = {};
          let needsUpdate = false;

          // Process video if it doesn't exist yet
          if (!part.video_url) {
            console.log(`Part ${index} needs video processing`);

            // Get task_id from the part
            const taskId = part.task_id;
            if (!taskId) {
              console.error(`Part ${index} has no task_id`);
              return part;
            }

            // Get the image URL from the task
            const { data: task, error: taskError } = await supabase
              .from("tasks")
              .select("*")
              .eq("id", taskId)
              .single();

            if (taskError) {
              console.error(`Error fetching task ${taskId}:`, taskError);
              return part;
            }

            if (!task || !task.image_url) {
              console.error(`Task ${taskId} has no image_url or doesn't exist`);
              return part;
            }

            const imageUrl = task.image_url;
            console.log(`Using image URL from task: ${imageUrl}`);

            // Generate a video from the image URL
            let requestId;
            try {
              // Check if the image URL is from Supabase storage
              if (imageUrl.includes("storage.googleapis.com")) {
                // Direct GCS URL
                console.log("Using direct GCS URL for video generation");
                requestId = await generateVideoFromURL(imageUrl);
              } else if (imageUrl.includes("supabase")) {
                // Extract UUID from image URL
                const uuidMatch = imageUrl.match(/\/([0-9a-f-]+)\.webp$/i);
                if (!uuidMatch) {
                  console.error(
                    `Could not extract UUID from image URL: ${imageUrl}`,
                  );
                  return part;
                }

                const imageUuid = uuidMatch[1];
                console.log(`Extracted image UUID: ${imageUuid}`);

                // Fetch image data from Supabase
                const { data: imageData, error: imageError } = await supabase
                  .from("images")
                  .select("*")
                  .eq("id", imageUuid)
                  .single();

                if (imageError) {
                  console.error(
                    `Error fetching image data for UUID ${imageUuid}:`,
                    imageError,
                  );
                  return part;
                }

                if (!imageData || !imageData.gcs_url) {
                  console.error(
                    `Image data for UUID ${imageUuid} has no GCS URL`,
                  );
                  return part;
                }

                // Use the GCS URL for video generation
                console.log(
                  `Using GCS URL from image data: ${imageData.gcs_url}`,
                );
                requestId = await generateVideoFromURL(imageData.gcs_url);
              } else {
                // Use the image URL directly
                console.log("Using direct image URL for video generation");
                requestId = await generateVideoFromURL(imageUrl);
              }
            } catch (error) {
              console.error("Error generating video:", error);
              return part;
            }

            console.log(`Generated video with request ID: ${requestId}`);

            // Wait for the video to be generated
            let videoData = null;
            let attempts = 0;
            const maxAttempts = 30; // 5 minutes (10 seconds per attempt)
            while (!videoData && attempts < maxAttempts) {
              attempts++;
              console.log(
                `Checking video generation status (attempt ${attempts}/${maxAttempts})...`,
              );

              // Check if the video has been generated
              const { data, error } = await supabase
                .from("videos")
                .select("*")
                .eq("request_id", requestId)
                .single();

              if (error) {
                console.error(
                  `Error checking video status for request ID ${requestId}:`,
                  error,
                );
                // Continue trying
              } else if (data && data.gcs_url) {
                videoData = data;
                console.log(`Video generated: ${data.gcs_url}`);
                break;
              }

              // Wait 10 seconds before checking again
              await new Promise((resolve) => setTimeout(resolve, 10000));
            }

            if (!videoData) {
              console.error(
                `Video generation timed out after ${maxAttempts} attempts`,
              );
              return part;
            }

            // Update the part with the video URL
            if (videoData.gcs_url) {
              partUpdates.video_url = videoData.gcs_url;
              needsUpdate = true;
            }

            // Always store the request ID
            partUpdates.video_request_id = requestId;
            needsUpdate = true;
          } else {
            console.log(
              `Part ${index} already has video_url: ${part.video_url}`,
            );
          }

          // Process voice if dialog exists and voice_url doesn't exist yet
          if (part.dialog && part.dialog.text && !part.voice_url) {
            console.log(
              `Processing dialog for part ${index}: ${part.dialog.text}`,
            );

            // Use the profile voice ID we already retrieved
            if (profileVoiceId) {
              try {
                // Generate voice audio and upload to GCS
                const dialogText = part.dialog.text;
                const partId = `part_${index}`;
                const { voiceUrl, voiceId } = await generateAndUploadVoiceAudio(
                  profileVoiceId,
                  dialogText,
                  videoClip.profile_id,
                  partId,
                );

                // If a new voice ID was generated, update the profileVoiceId for future parts
                if (voiceId !== profileVoiceId) {
                  profileVoiceId = voiceId;
                }

                if (voiceUrl) {
                  partUpdates.voice_url = voiceUrl;
                  needsUpdate = true;
                }
              } catch (error) {
                console.error(
                  `Error generating voice audio for part ${index}:`,
                  error,
                );
                // Continue without voice
              }
            } else {
              // If no profileVoiceId, generate it directly in the first voice generation process
              try {
                // First, generate a voice description for this profile
                // Fetch bot profile with joined data
                const { data: bot, error: botError } = await supabase
                  .from("bots")
                  .select("*")
                  .eq("profile_id", videoClip.profile_id)
                  .single();

                if (botError || !bot) {
                  console.error("Error fetching profile:", botError);
                  throw new Error(
                    "Error fetching profile for voice generation",
                  );
                }

                let prompt = `Describe in one sentence what kind of voice this character would have. Be very detailed about the voice characteristics including age, gender, accent, tone, and speech patterns. Only return the voice description. (NOTE: If it is a child, describe them as a young adult).

${generateBio(bot)}
`;
                const payload = {
                  messages: [
                    {
                      role: "system",
                      content: prompt,
                    },
                  ],
                  model: "gpt-4o",
                };
                let result = await callAndLogOpenAI(
                  "OAI:PostRegenerate",
                  payload,
                  {
                    timeout: 8 * 1000,
                  },
                );

                const voice_description = result.choices[0].message.content;
                console.log("Generated voice description:", voice_description);

                // Generate voice audio and upload to GCS
                const dialogText = part.dialog.text;
                const partId = `part_${index}`;
                const { voiceUrl, voiceId } = await generateAndUploadVoiceAudio(
                  null,
                  dialogText,
                  videoClip.profile_id,
                  partId,
                  voice_description,
                );

                profileVoiceId = voiceId;

                // Save the voice ID to the database for future use
                const { error: insertError } = await supabase
                  .from("video_clip_voices")
                  .insert({
                    profile_id: videoClip.profile_id,
                    voice_id: voiceId,
                    created_at: new Date().toISOString(),
                  });

                if (insertError) {
                  console.error(
                    "Error saving voice ID to database:",
                    insertError,
                  );
                  // Continue anyway since we have the voice ID
                }

                if (voiceUrl) {
                  partUpdates.voice_url = voiceUrl;
                  needsUpdate = true;
                }
              } catch (error) {
                console.error(
                  `Error generating voice audio for part ${index}:`,
                  error,
                );
                // Continue without voice
              }
            }
          } else if (part.dialog && part.dialog.text) {
            console.log(
              `Part ${index} already has voice_url: ${part.voice_url}`,
            );
          }

          // Return updated part or original if no updates needed
          if (needsUpdate) {
            return {
              ...part,
              ...partUpdates,
            };
          } else {
            return part;
          }
        } catch (error) {
          console.error(`Error processing part ${index}:`, error);
          return part;
        }
      }),
    );

    // Update the script with the new parts
    const updatedScript = {
      ...videoClip.script,
      parts: updatedParts,
    };

    // Update the video clip in the database
    const { data: updatedVideoClip, error: updateError2 } = await supabase
      .from("video_clips")
      .update({
        script: updatedScript,
        status: "processing_videos",
      })
      .eq("id", videoClip.id)
      .select()
      .single();

    if (updateError2) {
      throw updateError2;
    }

    console.log(
      `Successfully updated video clip ${videoClip.id} with video URLs`,
    );
    return { success: true, videoClip: updatedVideoClip };
  } catch (error) {
    console.error("Error in turnVideoClipToVideo:", error);

    // Update status to error
    await supabase
      .from("video_clips")
      .update({
        status: "error",
        error_message: error.message,
      })
      .eq("id", videoClip.id);

    throw error;
  }
}

/**
 * Generate an ElevenLabs voice for a profile
 * @param {string} profileId - The profile ID to generate a voice for
 * @returns {Promise<string>} - The generated voice ID
 */
// Removed function generateElevenLabsVoiceForProfileId

/**
 * Generate voice audio from ElevenLabs and upload it to GCS
 * @param {string} voiceId - The ElevenLabs voice ID to use
 * @param {string} text - The text to convert to speech
 * @param {string} profileId - The profile ID associated with this voice
 * @param {string} partId - A unique identifier for this part (for filename)
 * @returns {Promise<string>} - The GCS URL of the uploaded audio file
 */
// Removed function generateAndUploadVoiceAudio

/**
 * Generate a final video by combining all video clips from a video_clip's script parts
 * Takes a video clip ID, processes all video URLs, and combines them into a single video
 */
app.post("/generateFinalVideo", async (req, res) => {
  try {
    const { videoClipId } = req.body;

    if (!videoClipId) {
      return res.status(400).json({
        error: "Missing videoClipId in request body",
      });
    }

    // Fetch the video clip from the database
    const { data: videoClip, error: fetchError } = await supabase
      .from("video_clips")
      .select("*")
      .eq("id", videoClipId)
      .single();

    if (fetchError) {
      return res.status(500).json({
        error: "Error fetching video clip",
        details: fetchError.message,
      });
    }

    if (!videoClip) {
      return res.status(404).json({
        error: "Video clip not found",
      });
    }

    // Call the function to generate the final video
    const result = await generateFinalVideo({ videoClip });

    return res.status(200).json({
      success: true,
      videoUrl: result.videoUrl,
    });
  } catch (error) {
    console.error("Error in generateFinalVideo endpoint:", error);
    return res.status(500).json({
      error: "Error generating final video",
      details: error.message,
    });
  }
});

/**
 * Combines all video parts from a video clip into a single video and uploads to GCS
 * @param {Object} options - The options object
 * @param {Object} options.videoClip - The video clip object containing script with parts
 * @returns {Promise<Object>} Object containing the final video URL
 */
async function generateFinalVideo({ videoClip }) {
  try {
    console.log(`Generating final video for clip with ID: ${videoClip.id}`);

    // Get the script from the video clip
    const script = videoClip.script;

    if (!script || !script.parts || !Array.isArray(script.parts)) {
      throw new Error("Invalid script structure in video clip");
    }

    // Filter out parts that don't have video_url
    const partsWithVideos = script.parts.filter((part) => part.video_url);

    if (partsWithVideos.length === 0) {
      throw new Error("No video parts found in the script");
    }

    console.log(`Found ${partsWithVideos.length} video parts to combine`);

    // Create a temporary directory to store downloaded videos
    const tempDir = path.join(os.tmpdir(), `video_${videoClip.id}`);
    await new Promise((resolve, reject) => {
      fs.mkdir(tempDir, { recursive: true }, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Download all videos to the temporary directory
    const videoFiles = await Promise.all(
      partsWithVideos.map(async (part, index) => {
        const videoUrl = part.video_url;
        const outputPath = path.join(tempDir, `part_${index}.mp4`);

        // Download the video
        const response = await axios({
          method: "get",
          url: videoUrl,
          responseType: "stream",
        });

        const writer = fs.createWriteStream(outputPath);
        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
          writer.on("finish", () => resolve(outputPath));
          writer.on("error", reject);
        });
      }),
    );

    // Path for the combined video
    const outputFilePath = path.join(tempDir, "final_video.mp4");

    // Use fluent-ffmpeg to combine all videos
    await new Promise((resolve, reject) => {
      let ffmpegCommand = ffmpeg();

      // Add each video file to the command
      videoFiles.forEach((file) => {
        ffmpegCommand = ffmpegCommand.input(file);
      });

      // Configure the output
      ffmpegCommand
        .on("start", (commandLine) => {
          console.log("FFmpeg process started:", commandLine);
        })
        .on("error", (err) => {
          console.error("Error during FFmpeg processing:", err);
          reject(err);
        })
        .on("end", () => {
          console.log("FFmpeg processing finished successfully");
          resolve();
        })
        .mergeToFile(outputFilePath, tempDir);
    });

    // Upload the final video to GCS
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);
    const filename = `${videoClip.id}_${Date.now()}.mp4`;
    const destinationPath = `${bucketPath}/${filename}`;
    const file = bucket.file(destinationPath);

    // Upload the file to GCS using streaming upload
    const readStream = fs.createReadStream(outputFilePath);

    // Replace file.save() with createWriteStream() and pipe
    await new Promise((resolve, reject) => {
      readStream
        .pipe(
          file.createWriteStream({
            metadata: {
              contentType: "video/mp4",
            },
            resumable: false,
          }),
        )
        .on("error", (err) => {
          console.error("Error uploading to GCS:", err);
          reject(err);
        })
        .on("finish", () => {
          console.log("Upload to GCS complete");
          resolve();
        });
    });

    // Make the file publicly accessible
    await file.makePublic();

    // Get the public URL
    const videoUrl = `https://storage.googleapis.com/${bucketName}/${destinationPath}`;

    console.log(`Final video uploaded to GCS: ${videoUrl}`);

    // Update the video_clip with the final video URL
    const { error: updateError } = await supabase
      .from("video_clips")
      .update({ video_url: videoUrl })
      .eq("id", videoClip.id);

    if (updateError) {
      console.error(
        "Error updating video_clip with final video URL:",
        updateError,
      );
      throw updateError;
    }

    // Clean up temporary files
    try {
      await new Promise((resolve, reject) => {
        fs.rmdir(tempDir, { recursive: true }, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    } catch (cleanupError) {
      console.warn(
        "Warning: Failed to clean up temporary files:",
        cleanupError,
      );
    }

    return { videoUrl };
  } catch (error) {
    console.error("Error in generateFinalVideo function:", error);
    throw error;
  }
}

module.exports = {
  app,
  turnVideoClipToVideo,
  generateFinalVideo,
};
