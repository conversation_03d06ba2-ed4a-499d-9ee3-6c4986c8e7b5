const express = require("express");
const { logError, logInfo } = require("./utils");
require("dotenv").config();
const bodyParser = require("body-parser");

const { authUser } = require("./middleware");
const {
  supabase,
  retrySupabaseOperation,
  wrappedSupabaseError,
} = require("./supabaseClient");
const {
  PLAN_TYPE,
  REDEMPTION_VALUE_PREMIUM_DAYS,
  REDEMPTION_TYPE,
} = require("./constants");
const app = express.Router();
app.use(express.static("public"));
app.use(express.urlencoded({ extended: true }));

const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);

const STRIPE_DEFAULT_TRIAL_DATE = 1;

async function getStripeCustomerId(authenticatedUser) {
  const userBilling = await retrySupabaseOperation(
    () =>
      supabase
        .from("user_billings")
        .select("*")
        .eq("user_id", authenticatedUser.id),
    "get customerId from user_billings",
  );

  let stripeCustomerId = "";
  let usedTrial;
  if (
    userBilling &&
    (userBilling.length === 0 || !userBilling[0].stripe_customer_id)
  ) {
    const option = { description: authenticatedUser.id };

    if (authenticatedUser.user_metadata?.full_name)
      option.name = authenticatedUser.user_metadata.full_name;

    if (authenticatedUser.email) option.email = authenticatedUser.email;

    if (authenticatedUser.phone) option.phone = authenticatedUser.phone;

    const stripeCustomer = await stripe.customers.create(option);

    stripeCustomerId = stripeCustomer.id;

    await retrySupabaseOperation(
      () =>
        supabase.from("user_billings").upsert({
          user_id: authenticatedUser.id,
          stripe_customer_id: stripeCustomerId,
        }),
      "user_billings upsert",
    );
  } else if (userBilling[0].stripe_customer_id) {
    stripeCustomerId = userBilling[0].stripe_customer_id;
    usedTrial = userBilling[0].used_trial;
  }

  return { stripeCustomerId, usedTrial };
}

app.get("/ping", async (req, res) => {
  const stripe_key = process.env.STRIPE_SECRET_KEY;
  const webhook = process.env.STRIPE_WEBHOOK_ENDPOINT_SECRET;
  logInfo({
    context: "*** billing secret",
    message: "the billing sct were logged",
    stripe_key,
    webhook,
  });
  res.send(process.env.LOCAL + " Billing router");
});

async function hasSubscription(customerId) {
  if (process.env.LOCAL) {
    return true;
  }
  let customer;
  try {
    customer = await stripe.customers.retrieve(customerId);
  } catch (err) {
    throw new Error(err);
  }

  const subscriptions = await stripe.subscriptions.list({
    customer: customer.id,
  });

  return subscriptions?.data[0] ?? null;
}

app.post(
  "/create-checkout-session",
  authUser,
  express.json(),
  bodyParser.json(),
  async (req, res) => {
    let customerId;
    let hasUsedTrial;
    console.log("**** create-checkout-session");
    try {
      const { stripeCustomerId, usedTrial } = await getStripeCustomerId(
        req.user,
      );
      customerId = stripeCustomerId;
      hasUsedTrial = usedTrial;
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "*** stripe getStripeCustomerId",
        error,
      });
      res.sendStatus(500);
      return;
    }

    if (!customerId) {
      logError({
        executionId: req.executionId,
        context: "*** stripe getStripeCustomerId no customer id",
        error: "get customer id error",
      });
      res.sendStatus(500);
      return;
    }

    // check subscription existence
    if (await hasSubscription(customerId)) {
      // throw new Error("User already has a subscription.");
      logError({
        executionId: req.executionId,
        context: "*** stripe hasSubscription exist",
        error: "User already has a subscription.",
      });
      res.sendStatus(409);
      return;
    }

    const prices = await stripe.prices.list({
      lookup_keys: [req.body.lookup_key],
      expand: ["data.product"],
    });

    const planId = await getPlanIdByName(req.body.lookup_key);
    const userPlan = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_plans")
          .insert({
            user_id: req.user.id,
            plan_id: planId,
          })
          .select("id")
          .single(),
      "insert plus plan",
    );

    let option = {
      billing_address_collection: "auto",
      line_items: [
        {
          price: prices.data[0].id,
          // For metered billing, do not pass quantity
          quantity: 1,
        },
      ],
      subscription_data: {
        metadata: { user_id: req.user.id, user_plan_id: userPlan.id },
      },
      customer: customerId,
      mode: "subscription",
      success_url: `${process.env.WEBSITE_URL}/?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.WEBSITE_URL}?canceled=true`,
    };

    if (!hasUsedTrial) {
      option = {
        ...option,
        subscription_data: {
          ...option.subscription_data,
          trial_settings: {
            end_behavior: {
              missing_payment_method: "cancel",
            },
          },
          trial_period_days: STRIPE_DEFAULT_TRIAL_DATE,
        },
      };
    }
    const session = await stripe.checkout.sessions.create(option);

    // res.redirect(303, session.url);
    res.json({ checkout_url: session.url });
  },
);

app.post(
  "/create-portal-session",
  authUser,
  express.json(),
  bodyParser.json(),
  async (req, res) => {
    // For demonstration purposes, we're using the Checkout session to retrieve the customer ID.
    // Typically this is stored alongside the authenticated user in your database.
    const { session_id } = req.body;
    let customerId;
    if (session_id) {
      const checkoutSession =
        await stripe.checkout.sessions.retrieve(session_id);
      customerId = checkoutSession.customer;
    } else {
      // get user id from user_billings by stripe_customer_id
      try {
        const { stripeCustomerId } = await getStripeCustomerId(req.user);
        customerId = stripeCustomerId;
      } catch (error) {
        logError({
          executionId: req.executionId,
          context: "*** stripe getStripeCustomerId create-portal-session",
          error,
        });
        return;
      }
    }
    // This is the url to which the customer will be redirected when they are done
    // managing their billing with the portal.
    const returnUrl = process.env.WEBSITE_URL;

    logInfo({
      context: "*** billing portal session",
      message: "billing portal session creating",
      returnUrl,
      customerId,
    });
    if (customerId) {
      const portalSession = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });

      // res.redirect(303, portalSession.url);
      res.json({ portal_session_url: portalSession.url });
      return;
    }

    res.sendStatus(500);
  },
);

function getDateString(timestamp) {
  const date = new Date(timestamp * 1000);
  return date.toISOString();
}

async function inactivateCurrentPlan(userId, subscriptionId) {
  const userPlan = await retrySupabaseOperation(
    () =>
      supabase
        .from("user_plans")
        .update({
          is_active: false,
        })
        .eq("user_id", userId)
        .eq("stripe_subscription_id", subscriptionId)
        .is("is_active", true)
        .select("id")
        .single(),
    "inactivateCurrentPlan",
  );

  return userPlan.id;
}

async function inactivateCurrentPlanTracking(userPlanId) {
  await retrySupabaseOperation(
    () =>
      supabase
        .from("user_plan_trackings")
        .update({
          // is_active: false,
          is_current: false,
        })
        .eq("user_plan_id", userPlanId)
        .is("is_current", true),
    "inactivateCurrentPlanTracking",
  );

  return true;
}

async function getPlanIdByName(planName) {
  const plan = await retrySupabaseOperation(
    () => supabase.from("plans").select("id").eq("name", planName).single(),
    "getPlanIdByName",
  );

  return plan.id;
}

async function getActivePlanPackageVersion() {
  const planPackageVersion = await retrySupabaseOperation(
    () =>
      supabase
        .from("plan_package_versions")
        .select("id")
        .is("is_active", true)
        .single(),
    "get active planPackageVersion",
  );

  return planPackageVersion.id;
}

async function activateFreePlan(userId, subscriptionId) {
  const currentUserPlanId = await inactivateCurrentPlan(userId, subscriptionId);
  await inactivateCurrentPlanTracking(currentUserPlanId);

  // get free plan id
  const planId = await getPlanIdByName(PLAN_TYPE.FREE);

  const planPackageVersionId = await getActivePlanPackageVersion();

  // insert user_plan. other user_plans of the particular user will be deactivated by trigger on insert action.
  const userPlan = await retrySupabaseOperation(
    () =>
      supabase
        .from("user_plans")
        .insert({
          user_id: userId,
          plan_id: planId,
          is_active: true,
        })
        .select("id")
        .single(),
    "insert free plan",
  );

  await retrySupabaseOperation(
    () =>
      supabase.from("user_plan_trackings").insert({
        user_plan_id: userPlan.id,
        billing_status: "free",
        is_active: true,
        is_current: true,
        plan_package_version_id: planPackageVersionId,
        start_date: new Date().toISOString(),
      }),
    "insert free plan trackings ",
  );

  return true;
}

async function handleRedemption(data) {
  let redemption_id;
  let redemptions = JSON.parse(data);
  let redemption;
  const redemptionId = redemptions?.new;
  if (redemptionId) {
    redemption = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_redemptions")
          .select("*")
          .eq("id", redemptionId)
          .single(),
      "get active planPackageVersion",
    );

    if (!redemption?.is_applied) {
      redemption_id = redemptionId;

      await retrySupabaseOperation(
        () =>
          supabase
            .from("user_redemptions")
            .update({
              is_applied: true,
            })
            .eq("id", redemption_id),
        "user_redemptions set is_apply true",
      );
    }
  }
  return redemption;
}

async function handleRegisterNotification(notifyData) {
  await retrySupabaseOperation(
    () => supabase.from("notifications").insert(notifyData),
    "add notification data",
  );
}

async function handleRedemNotification(redemption, user_id) {
  // get profiles from user_id
  const { data: receiverProfiles, error: receiverProfilesError } =
    await supabase
      .from("profiles")
      .select("id, username, user_id, avatar_url")
      .eq("user_id", user_id)
      .neq("visibility", "archived")
      .order("id", { ascending: true });

  if (receiverProfilesError) {
    const error = wrappedSupabaseError(receiverProfilesError);
    logError({
      context: "handleRedeemNotification - failed to get profile",
      error,
    });
    return;
  }

  // insert redem notifications
  const redemptionNotifyData = {
    profile_id: receiverProfiles[0]?.id,
    source_type: `redemption`,
    source_id: redemption?.id,
    title: redemption?.description,
    text: "",
    path: "",
    image_url: "",
    sender_profile_id: null,
    additional_info: { redem_type: redemption?.type },
  };

  switch (redemption?.type) {
    case "inviter":
      redemptionNotifyData.image_url =
        redemption?.metadata?.invitee?.avatar_url;
      redemptionNotifyData.path = `/users/${redemption?.metadata?.invitee?.username}`;
      break;
    case "invitee":
      redemptionNotifyData.image_url =
        redemption?.metadata?.inviter?.avatar_url;
      redemptionNotifyData.path = `/users/${redemption?.metadata?.inviter?.username}`;
      break;
    case "post_sharer":
      redemptionNotifyData.image_url = redemption?.metadata?.post?.media_url;
      redemptionNotifyData.path = `/users/${redemption?.metadata?.post?.profiles?.username}/p/${redemption?.metadata?.post?.slug}`;
      break;
  }

  await handleRegisterNotification(redemptionNotifyData);
}

async function handleSubscriptionNotification(customer, title) {
  try {
    const userData = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_billings")
          .select("user_id")
          .eq("stripe_customer_id", customer)
          .single(),
      "get user_id from user_billings by stripe_customer_id",
    );

    const receiverProfiles = await retrySupabaseOperation(
      () =>
        supabase
          .from("profiles")
          .select("id, username, user_id, avatar_url")
          .eq("user_id", userData?.user_id)
          .neq("visibility", "archived")
          .order("id", { ascending: true }),
      "get profiles from user_id",
    );

    const subscriptionPaidNotifyData = {
      profile_id: receiverProfiles[0]?.id,
      source_type: "subscription",
      source_id: receiverProfiles[0]?.id,
      title: title,
      text: "",
      path: "/me/settings/premium",
      image_url: "",
      sender_profile_id: null,
    };

    await handleRegisterNotification(subscriptionPaidNotifyData);
  } catch (error) {
    logError({ context: "handleSubscriptionNotification", error });
  }
}

async function handleInvoicePaid(invoice) {
  if (invoice.amount_paid != 0) {
    await handleSubscriptionNotification(
      invoice?.customer,
      "Your monthly subscription payment has been successfully processed!",
    );
  }
}

async function handleInvoiceFailed(invoice) {
  await handleSubscriptionNotification(
    invoice?.customer,
    "We’ve encountered an issue with your subscription payment—please check your payment details to continue enjoying our service without interruption.",
  );
}

async function addUserPlanTracking({
  user_plan_id,
  is_active,
  redemption_id,
  subscription,
}) {
  // get active plan package version id
  const planPackageVersion = await retrySupabaseOperation(
    () =>
      supabase
        .from("plan_package_versions")
        .select("id")
        .is("is_active", true)
        .single(),
    "get active planPackageVersion",
  );

  // insert new billing cycle
  let option = {
    user_plan_id: user_plan_id,
    billing_status: subscription.status,
    is_active: is_active,
    is_current: true,
    plan_package_version_id: planPackageVersion.id,
    start_date: getDateString(
      subscription?.items?.data[0]?.current_period_start,
    ),
    end_date: getDateString(subscription?.items?.data[0]?.current_period_end),
  };

  if (redemption_id) {
    option.redemption_id = redemption_id;
  }

  await retrySupabaseOperation(
    () => supabase.from("user_plan_trackings").insert(option),
    "insert user plan trackings",
  );
}

async function getUserPlanIdFromSubscriptionId(subscription, user_id) {
  let user_plan_id;
  // get current plan
  const currentUserPlan = await retrySupabaseOperation(
    () =>
      supabase
        .from("user_plans")
        .select("id, plan_id, stripe_subscription_id")
        .eq("stripe_subscription_id", subscription.id),
    "get current plan by stripe_customer_id",
  );

  if (currentUserPlan && currentUserPlan.length > 0) {
    user_plan_id = currentUserPlan[0].id;
  } else {
    // get plan id from plans by stripe_plan_id
    const plan = await retrySupabaseOperation(
      () =>
        supabase
          .from("plans")
          .select("id")
          .eq("stripe_plan_id", subscription.plan.id)
          .single(),
      "get plan_id from plans by stripe plan id",
    );

    // inactivate current plan
    await retrySupabaseOperation(
      () =>
        supabase
          .from("user_plans")
          .update({
            is_active: false,
          })
          .eq("user_id", user_id)
          .is("is_active", true),
      "inactivateCurrentPlan",
    );

    // insert user_plan. other user_plans of the particular user will be deactivated by trigger on insert action.
    const userPlan = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_plans")
          .insert({
            user_id: user_id,
            plan_id: plan.id,
            stripe_subscription_id: subscription.id,
            is_active: true,
          })
          .select("id")
          .single(),
      "insert user plan",
    );
    user_plan_id = userPlan.id;
  }
  return user_plan_id;
}

async function handleSubscriptionCreated(subscription) {
  console.log("******* handleSubscriptionCreated", subscription.metadata);
  let user_id = subscription.metadata?.user_id;
  let user_plan_id = subscription.metadata?.user_plan_id;
  logInfo({
    context: "*** billing subscription",
    message: "customer subscription creating",
    subscription,
  });
  if (!user_id) {
    // get user id from user_billings by stripe_customer_id
    const user = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_billings")
          .select("user_id")
          .eq("stripe_customer_id", subscription.customer)
          .single(),
      "get user_id from user_billings by stripe_customer_id",
    );
    user_id = user.user_id;
  }

  if (!user_plan_id) {
    console.error("**** user_plan_id doesn't exist");
    return false;
  }

  // set subscription id to the user_plan
  await retrySupabaseOperation(
    () =>
      supabase
        .from("user_plans")
        .update({
          is_active: true,
          stripe_subscription_id: subscription.id,
        })
        .eq("id", user_plan_id),
    "set subscription id to user plan",
  );

  // inactivate current plan
  await retrySupabaseOperation(
    () =>
      supabase
        .from("user_plans")
        .update({
          is_active: false,
        })
        .eq("user_id", user_id)
        .neq("id", user_plan_id)
        .is("is_active", true),
    "inactivateCurrentPlan",
  );

  let is_active = false;
  let redemption_id;
  if (subscription.status === "trialing") {
    // Update the user's status in DB to reflect they are on a trial.
    await retrySupabaseOperation(
      () =>
        supabase
          .from("user_billings")
          .update({
            used_trial: true,
          })
          .eq("user_id", user_id),
      "set flag as used trial",
    );

    if (subscription.metadata?.redemptions) {
      const redemption = await handleRedemption(
        subscription.metadata?.redemptions,
      );

      if (redemption) {
        redemption_id = redemption.id;

        await handleRedemNotification(redemption, user_id);
      }
    }
    is_active = true;
    await retrySupabaseOperation(
      () =>
        supabase
          .from("user_plan_trackings")
          .update({
            // is_active: false,
            is_current: false,
          })
          .eq("user_plan_id", user_plan_id)
          .is("is_current", true),
      "inactivateCurrentPlanTracking",
    );
    await addUserPlanTracking({
      user_plan_id,
      user_id,
      is_active,
      redemption_id,
      subscription,
    });
  } else if (subscription.status === "active") {
    // The user's subscription is incomplete. Update the user's status in DB.
    is_active = true;
  } else if (subscription.status === "incomplete") {
    // The user's subscription is incomplete. Update the user's status in DB.
    is_active = false;
  }

  return true;
}

async function handleSubscriptionUpdated(subscription) {
  console.log("******* handleSubscriptionUpdated", subscription.metadata);
  let user_id = subscription.metadata?.user_id;
  let user_plan_id = subscription.metadata?.user_plan_id;
  if (!user_id) {
    // get user id from user_billings by stripe_customer_id
    const user = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_billings")
          .select("user_id")
          .eq("stripe_customer_id", subscription.customer)
          .single(),
      "get user_id from user_billings by stripe_customer_id",
    );
    user_id = user.user_id;
  }

  if (!user_plan_id) {
    user_plan_id = await getUserPlanIdFromSubscriptionId(subscription, user_id);
  }

  let redemption_id;
  let is_active = false;
  if (subscription.status === "active") {
    // If the plan changes to active, debit has succeeded thus update the account as premium.
    is_active = true;
  } else if (subscription.status === "trialing") {
    // If the plan changes to trial, debit has succeeded thus update the account as premium.
    is_active = true;
    if (subscription.metadata?.redemptions) {
      const redemption = await handleRedemption(
        subscription.metadata?.redemptions,
      );
      if (redemption) {
        redemption_id = redemption?.id;

        await handleRedemNotification(redemption, user_id);
      }
    }
  } else if (subscription.status === "incomplete") {
    // If the plan becomes incomplete after the trial, the user hasn't entered payment method.
    is_active = false;
  } else if (subscription.status === "past_due") {
    // Payment failed for the subscription, update the account status and notify the user.
    is_active = false;
  } else if (subscription.status === "canceled") {
    // The plan is canceled, change the account back to freemium.
    is_active = false;
  } else {
    is_active = false;
  }

  await inactivateCurrentPlanTracking(user_plan_id);
  await addUserPlanTracking({
    user_plan_id,
    user_id,
    is_active,
    redemption_id,
    subscription,
  });

  return true;
}

async function handleSubscriptionDeleted(subscription) {
  let user_id = subscription.metadata?.user_id;
  if (!user_id) {
    // get user id from user_billings by stripe_customer_id
    const user = await retrySupabaseOperation(
      () =>
        supabase
          .from("user_billings")
          .select("user_id")
          .eq("stripe_customer_id", subscription.customer)
          .single(),
      "get user_id from user_billings by stripe_customer_id",
    );
    user_id = user.user_id;
  }

  if (subscription.status === "canceled") {
    // The plan is canceled, change the account back to freemium.
    await activateFreePlan(user_id, subscription.id);
  }

  return true;
}

app.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  async (request, response) => {
    let event = request.body;
    // Replace this endpoint secret with your endpoint's unique secret
    // If you are testing with the CLI, find the secret by running 'stripe listen'
    // If you are using an endpoint defined with the API or dashboard, look in your webhook settings
    // at https://dashboard.stripe.com/webhooks
    const endpointSecret = process.env.STRIPE_WEBHOOK_ENDPOINT_SECRET;
    // Only verify the event if you have an endpoint secret defined.
    // Otherwise use the basic event deserialized with JSON.parse
    if (endpointSecret) {
      // Get the signature sent by Stripe
      const signature = request.headers["stripe-signature"];
      try {
        event = stripe.webhooks.constructEvent(
          request.body,
          signature,
          endpointSecret,
        );
      } catch (err) {
        console.log(`⚠️  Webhook signature verification failed.`, err.message);
        return response.sendStatus(400);
      }
    }
    let subscription;
    let invoice;
    let status;
    // Handle the event
    console.info("**** subscription event", event.id, event.type);
    try {
      switch (event.type) {
        case "customer.subscription.trial_will_end":
          subscription = event.data.object;
          status = subscription.status;
          console.info(
            `**** Subscription status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Subscription trial_will_end.`, subscription);
          // Then define and call a method to handle the subscription trial ending.
          // handleSubscriptionTrialEnding(subscription);
          break;
        case "customer.subscription.deleted":
          subscription = event.data.object;
          status = subscription.status;
          console.info(
            `**** Subscription status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Subscription deleted.`, subscription);
          // Then define and call a method to handle the subscription deleted.
          await handleSubscriptionDeleted(subscription);
          break;
        case "customer.subscription.created":
          subscription = event.data.object;
          status = subscription.status;
          console.info(
            `**** Subscription status is ${status}.`,
            event.id,
            event.type,
          );
          // Then define and call a method to handle the subscription created.
          await handleSubscriptionCreated(subscription);
          break;
        case "customer.subscription.updated":
          subscription = event.data.object;
          status = subscription.status;
          console.info(
            `**** Subscription status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Subscription updated.`, subscription);
          // Then define and call a method to handle the subscription update.
          await handleSubscriptionUpdated(subscription);
          break;
        case "customer.subscription.paused":
          subscription = event.data.object;
          status = subscription.status;
          console.info(
            `**** Subscription status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Subscription paused.`, subscription);
          // Then define and call a method to handle the subscription pause.
          // handleSubscriptionPaused(subscription);
          break;
        case "customer.subscription.resumed":
          subscription = event.data.object;
          status = subscription.status;
          console.info(
            `**** Subscription status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Subscription resumed.`, subscription);
          // Then define and call a method to handle the subscription resume.
          // handleSubscriptionResumed(subscription);
          break;
        case "invoice.paid":
          invoice = event.data.object;
          status = invoice.status;

          console.info(
            `**** Invoice status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Invoice paid.`, invoice);
          // Then define and call a method to handle the subscription resume.
          await handleInvoicePaid(invoice);
          break;
        case "invoice.payment_failed":
          invoice = event.data.object;
          status = invoice.status;

          console.info(
            `**** Invoice status is ${status}.`,
            event.id,
            event.type,
          );
          // console.log(`**** Invoice payment_failed.`, invoice);
          // Then define and call a method to handle the subscription resume.
          await handleInvoiceFailed(invoice);
          break;
        default:
          // Unexpected event type
          console.info(
            `Unhandled event type ${event.type}.`,
            event.id,
            event.type,
          );
      }
      console.info("*** handled event", event.id, event.type);
    } catch (error) {
      logError({
        executionId: event.id,
        context: "*** stripe webhook",
        error,
      });
      return response.sendStatus(500);
    }
    // Return a 200 response to acknowledge receipt of the event
    response.send();
  },
);

async function providePremiumTrial(user_id, type, redemption_id) {
  // get inviter info
  const { data, error } = await supabase.auth.admin.getUserById(user_id);

  if (error || !data?.user) {
    return false;
  }
  let customerId;

  try {
    const { stripeCustomerId } = await getStripeCustomerId(data.user);
    customerId = stripeCustomerId;
  } catch (error) {
    logError({
      context: "*** stripe getStripeCustomerId providePremiumTrial",
      error,
    });
    return false;
  }

  if (!customerId) {
    logError({
      context: "*** stripe getStripeCustomerId no customer id",
      error: "get customer id error",
    });
    return;
  }

  try {
    let subscription = await hasSubscription(customerId);

    if (subscription) {
      let metadata;
      if (subscription.metadata?.redemptions) {
        let redemptions = JSON.parse(subscription.metadata?.redemptions);

        console.log("metadata", metadata);
        let redemption_ids = redemptions?.redemption_ids ?? [];
        console.log("redemption_ids", redemption_ids);
        if (!redemption_ids.includes(redemption_id)) {
          redemption_ids.push(redemption_id);
          metadata = {
            redemptions: JSON.stringify({ redemption_ids, new: redemption_id }),
          };
        }
      } else {
        metadata = {
          redemptions: JSON.stringify({
            redemption_ids: [redemption_id],
            new: redemption_id,
          }),
        };
      }

      metadata.user_id = user_id;

      let user_plan_id = subscription.metadata?.user_plan_id;
      if (!user_plan_id) {
        user_plan_id = await getUserPlanIdFromSubscriptionId(
          subscription,
          user_id,
        );
      }
      metadata.user_plan_id = user_plan_id;

      console.log("updated metadata", metadata);
      await stripe.subscriptions.update(subscription.id, {
        trial_end:
          subscription.current_period_end +
          REDEMPTION_VALUE_PREMIUM_DAYS[type] * 24 * 60 * 60,
        metadata: metadata,
        proration_behavior: "none",
      });
    } else {
      const prices = await stripe.prices.list({
        lookup_keys: [PLAN_TYPE.PLUS],
        expand: ["data.product"],
      });

      const planId = await getPlanIdByName(PLAN_TYPE.PLUS);
      const userPlan = await retrySupabaseOperation(
        () =>
          supabase
            .from("user_plans")
            .insert({
              user_id: user_id,
              plan_id: planId,
            })
            .select("id")
            .single(),
        "insert plus plan",
      );

      subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [
          {
            price: prices.data[0].id,
          },
        ],
        trial_period_days: REDEMPTION_VALUE_PREMIUM_DAYS[type],
        payment_settings: {
          save_default_payment_method: "on_subscription",
        },
        trial_settings: {
          end_behavior: {
            missing_payment_method: "cancel",
          },
        },
        metadata: {
          redemptions: JSON.stringify({
            redemption_ids: [redemption_id],
            new: redemption_id,
          }),
          user_id: user_id,
          user_plan_id: userPlan.id,
        },
      });
      console.log("*** new", subscription);
    }
  } catch (error) {
    logError({
      context: "*** stripe handleInviteRedemption subscription update error",
      error,
    });
    return false;
  }

  return true;
}

async function handleInviteRedemption({
  inviter_id,
  invitee_id,
  type,
  redemptionData,
}) {
  const inviterRedemptionData = redemptionData.find(
    (item) => item.user_id == inviter_id,
  );
  await providePremiumTrial(inviter_id, type, inviterRedemptionData.id);

  if (type === REDEMPTION_TYPE.INVITE) {
    const inviteeRedemptionData = redemptionData.find(
      (item) => item.user_id == invitee_id,
    );
    await providePremiumTrial(invitee_id, type, inviteeRedemptionData.id);
  }
  return;
}

module.exports = {
  app,
  handleInviteRedemption,
};
