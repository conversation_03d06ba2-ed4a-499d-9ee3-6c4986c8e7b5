steps:
  - name: gcr.io/cloud-builders/docker
    dir: "llm-response-service"
    args:
      - build
      # - '--no-cache'
      - "--platform"
      - "linux/amd64"
      - "-t"
      - "gcr.io/$PROJECT_ID/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA"
      - "-t"
      - "gcr.io/$PROJECT_ID/$REPO_NAME/$_SERVICE_NAME:latest"
      - .
      # - '-f'
      # - llm-response-service/Dockerfile

  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA"]

  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/$REPO_NAME/$_SERVICE_NAME:latest"]

  # Deploy container image to Cloud Run
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    entrypoint: gcloud
    args: [
        "beta",
        "run",
        "deploy",
        "$_SERVICE_NAME",
        "--region",
        "$_CLOUD_RUN_REGION",
        "--tag",
        "$BRANCH_NAME",
        "--allow-unauthenticated",
        "--execution-environment",
        "gen2",
        "--min-instances",
        "$_MIN_INSTANCES",
        "--max-instances",
        "$_MAX_INSTANCES",
        "--concurrency",
        "$_CONCURRENCY",
        "--timeout",
        "$_TIMEOUT",

        "--container",
        "$_SERVICE_NAME-1",
        "--image",
        "gcr.io/$PROJECT_ID/$REPO_NAME/$_SERVICE_NAME:$COMMIT_SHA",
        "--port",
        "8080",
        "--memory",
        "$_MEMORY",
        "--set-env-vars",
        "SERVER_ENDPOINT=$_SERVER_ENDPOINT,BOT_SERVER_ENDPOINT=$_BOT_SERVER_ENDPOINT,SUPABASE_URL=$_SUPABASE_URL,SUPABASE_SECRET_KEY=$_SUPABASE_SECRET_KEY,SUPABASE_ANON_KEY=$_SUPABASE_ANON_KEY,GPT_TEXT_MODEL=$_GPT_TEXT_MODEL,OPENAI_API_KEY=$_OPENAI_API_KEY,CLOUD_TASK_V1=$_CLOUD_TASK_V1,CLOUD_TASK_V1_IMAGE_GENERATION=$_CLOUD_TASK_V1_IMAGE_GENERATION,CLOUD_TASK_V1_RUN_BOTS=$_CLOUD_TASK_V1_RUN_BOTS,CLOUD_TASK_V1_IMAGE_GENERATION_UPDATE_STATUS=$_CLOUD_TASK_V1_IMAGE_GENERATION_UPDATE_STATUS,CLOUD_TASK_V1_HANDLE_IMAGE=$_CLOUD_TASK_V1_HANDLE_IMAGE,TYPESENSE_HOST=$_TYPESENSE_HOST,TYPESENSE_API_KEY=$_TYPESENSE_API_KEY,NODE_ENV=$_NODE_ENV,LOG_DEBUG_ENABLED=$_LOG_DEBUG_ENABLED,STRIPE_SECRET_KEY=$_STRIPE_SECRET_KEY,STRIPE_WEBHOOK_ENDPOINT_SECRET=$_STRIPE_WEBHOOK_ENDPOINT_SECRET,WEBSITE_URL=$_WEBSITE_URL,BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN=$_BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN",
        "--set-env-vars",
        "OTEL_EXPORTER_OTLP_ENDPOINT=localhost:4317", # grpc
        # "--set-env-vars",
        # "OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318", # http
        "--set-env-vars",
        "OTEL_TRACES_EXPORTER=otlp",
        "--set-env-vars",
        "OTEL_METRICS_EXPORTER=otlp",
        "--set-env-vars",
        "OTEL_EXPORTER_OTLP_INSECURE=true",
        "--set-env-vars",
        "OTEL_EXPORTER_OTLP_COMPRESSION=gzip",
        "--set-env-vars",
        "OTEL_SERVICE_NAME=$_SERVICE_NAME",
        # FIXME: temporarily uncommented
        # '--set-env-vars', 'OTEL_LOG_LEVEL=debug', # uncomment to enable more OpenTelemetry diagnostic logs

        # OpenTelemetry Collector container
        ##################################################################
        # NOTE: the otel-collector image DOES NOT GET BUILT on every push.
        #       If you want to change the otel-collector configuration:
        #        * cd google-cloud-functions
        #        * modify ./otel-collector/collector-config.yaml
        #        * run ./otel-collector/build_and_push_image.sh
        #        * redeploy butterflies-api (llm-response-service)
        ##################################################################
        # TODO: no way to specify startup probe for otel-collector.
        "--container",
        "otel-collector",
        "--image",
        "$_CLOUD_RUN_REGION-docker.pkg.dev/$PROJECT_ID/run-otel/otel-collector-metrics",
        "--cpu",
        "$_CPU",
        "--memory",
        "$_MEMORY",
        "--set-env-vars",
        "GOOGLE_CLOUD_PROJECT=$PROJECT_ID",
        # '--set-env-vars', 'OTEL_LOG_LEVEL=debug', # uncomment to enable more OpenTelemetry diagnostic logs
      ]
# timeout: "1500s"
