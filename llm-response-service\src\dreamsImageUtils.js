const { logError, logWarn } = require("./logUtils");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
// const dayjs = require("dayjs");

async function updateDreamWithImage({
  dream_batch_id,
  dream_id,
  profile_id,
  imageUrls,
  blurhash,
}) {
  if (!imageUrls.length || !imageUrls[0].length) {
    throw new Error("No image URLs provided");
  }

  if (!dream_id) {
    throw new Error("No dream_id provided");
  }

  if (blurhash && Array.isArray(blurhash) && blurhash[0]) {
    blurhash = blurhash[0];
  }

  const nowTimestamptz = new Date().toISOString();

  const { data: updatedDream, error: updateError } = await supabase
    .from("dreams")
    .update({
      updated_at: nowTimestamptz,
      media_url: imageUrls[0],
      status: "image_generated",
      blurhash,
    })
    .eq("id", dream_id)
    .single();

  if (updateError) {
    logError({
      context: "**** updateDreamWithImage updateError",
      error: wrappedSupabaseError(updateError),
      dream_id,
      dream_batch_id,
      profile_id,
    });
    return null;
  }

  // Delay to try to let image-service generate the rest of the dreams from the batch
  setTimeout(() => {
    maybeNotifyAboutNewDreams({
      dream_batch_id,
      dream_id,
    });
  }, 60000);

  return updatedDream;
}

async function maybeNotifyAboutNewDreams({ dream_batch_id, dream_id }) {
  // poor-man's distributed locking...
  const { data: batchData, error: batchError } = await supabase
    .from("dreams_batches")
    .update({
      notification_inserted: true,
    })
    .eq("id", dream_batch_id)
    .eq("notification_inserted", false)
    .select("*")
    .maybeSingle();

  if (batchError) {
    const error = wrappedSupabaseError(batchError);
    logWarn({
      context: "maybeNotifyAboutNewDreams - batchError",
      error,
    });
    return;
  }

  if (!batchData) {
    // no batch data means we couldn't find dream batch data or
    // notification_inserted was already true so we don't need to notify
    return;
  }

  const owner_profile_id = batchData.owner_profile_id;

  notifyAboutNewDreams({
    dream_batch_id,
    dream_id,
    owner_profile_id,
  });
}

async function notifyAboutNewDreams({
  dream_batch_id,
  dream_id,
  owner_profile_id,
}) {
  const { error: insertNotificationError } = await supabase
    .from("notifications")
    .insert({
      profile_id: owner_profile_id,
      source_type: "new_dreams",
      source_id: dream_batch_id,
      title: "New Dreams Available",
      text: `Your personal AI camera roll has been updated with new Dreams, tap to reveal them!`,
      path: `/dreams`,
      sender_profile_id: null,
      // image_url: image_url,
    });
  if (insertNotificationError) {
    const error = wrappedSupabaseError(insertNotificationError);
    logError({
      context:
        "maybeNotifyAboutNewDreams: failed to insert notification record",
      error,
    });
    throw error;
  }
}

module.exports = {
  updateDreamWithImage,
  maybeNotifyAboutNewDreams,
  notifyAboutNewDreams,
};
