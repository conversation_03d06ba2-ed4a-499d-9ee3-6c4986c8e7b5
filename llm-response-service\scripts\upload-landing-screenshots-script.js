const { supabase } = require("./src/supabaseClient");
const glob = require("glob");
const fs = require("fs");
const { promisify } = require("util");
const { basename } = require("path");

const files = glob.sync(
  "../../ai-ig/packages/common/components/LandingPage/assets/screenshots/*.png",
);
// Make sure to set this correctly for your file type
const contentType = "image/png";

const sortedFiles = files.sort((a, b) => {
  // sort by name
  return a.localeCompare(b);
});

console.log(sortedFiles);

const readFile = promisify(fs.readFile);

const main = async () => {
  const publicPaths = [];

  // process the images
  for (const filePath of sortedFiles) {
    const fileData = await readFile(filePath);
    const buffer = Buffer.from(fileData, "binary");
    const fileBasename = basename(filePath);
    const pathInStorage = `/landing_screenshots/${fileBasename}`;

    console.log(`Uploading ${filePath} to ${pathInStorage}...`);
    const { data, error } = await supabase.storage
      .from("ai-ig")
      .upload(pathInStorage, buffer, {
        contentType: contentType,
        cacheControl: "31536000",
      });

    if (error) {
      throw error;
    }

    const publicUrl = supabase.storage.from("ai-ig").getPublicUrl(data.path)
      .data.publicUrl;

    publicPaths.push(publicUrl);
  }

  const sortedPublicPaths = publicPaths.sort((a, b) => {
    return a.localeCompare(b);
  });

  console.log(sortedPublicPaths);
};

main();
