const { default: OpenAI } = require("openai");
const { loggingInfo } = require("../../logging");

class LLMServiceBase {
  async run(ctx) {
    const client = new OpenAI({
      baseURL: "http://10.128.0.69/v1",
      timeout: 30 * 1000, // 30 seconds (default is 10 minutes)
      response_format: { type: "json_object" },
    });

    const payload = {
      messages: await this.getMessages(),
      model: this.getModel(),
      temperature: this.getTemperature(),
      stream: this.isStreaming(),
    };
    const max_tokens = this.getMaxTokens();
    if (max_tokens) payload.max_tokens = max_tokens;
    const frequency_penalty = this.getFrequencyPenalty();
    if (frequency_penalty) payload.frequency_penalty = frequency_penalty;
    const top_p = this.getTopP();
    if (top_p) payload.top_p = top_p;
    const options = {
      headers: {},
    };
    const timeout = this.getTimeout();
    if (timeout) options.timeout = timeout;
    if (ctx.profile_id) options.headers["X-Profile-Id"] = ctx.profile_id;
    if (ctx.experiments.length > 0) {
      options.headers["X-Experiment-Names"] = ctx.experiments.join(",");
    }

    if (ctx.logging.verbose) {
      if (!ctx.logging.verbose.models) ctx.logging.verbose.models = {};
      ctx.logging.verbose.models[this.constructor.name] = {
        payload,
      };
    }

    const startTime = Date.now();
    const response = await client.chat.completions.create(payload, options);

    loggingInfo("api_calls", {
      payload,
      options,
      result: response,
      status: "completed",
      service: "llmService",
      duration: Date.now() - startTime,
      context: "newConversation:llmService",
    });

    if (this.isStreaming()) {
      return this.parseStreamingResponse(ctx, response);
    } else {
      if (ctx.logging.verbose) {
        ctx.logging.verbose.models[this.constructor.name].response = response;
      }

      return this.parseResponse(response);
    }
  }

  isStreaming() {
    throw new Error("Unimplemented method: isStreaming");
  }

  parseResponse(/* response */) {
    throw new Error("Unimplemented method: parseResponse");
  }

  parseStreamingResponse(/* ctx, response */) {
    throw new Error("Unimplemented method: parseStreamingResponse");
  }

  async getMessages() {
    throw new Error("Unimplemented method: getMessages");
  }

  getModel() {
    throw new Error("Unimplemented method: getModel");
  }

  getTemperature() {
    throw new Error("Unimplemented method: getTemperature");
  }

  getMaxTokens() {
    return null;
  }

  getFrequencyPenalty() {
    return null;
  }

  getTopP() {
    return null;
  }

  getTimeout() {
    return null;
  }
}

module.exports = {
  LLMServiceBase,
};
