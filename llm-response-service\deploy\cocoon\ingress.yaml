apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cocoon-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: cocoon-ip
spec:
  tls:
    - secretName: cocoon-tls-secret-bundle
  rules:
    - host: "cocoon.butterflies.ai"
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: cocoon-service
                port:
                  number: 80
    - host: "cocoon-dev.butterflies.ai"
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: cocoon-dev-service
                port:
                  number: 80
    - host: "cocoon-stage.butterflies.ai"
      http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: cocoon-stage-service
                port:
                  number: 80
