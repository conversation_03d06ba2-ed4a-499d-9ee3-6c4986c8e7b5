const crypto = require("crypto");
const math = require("mathjs");
const GTESmall = require("./transformer");
const { client } = require("./posthog");
const sharp = require("sharp");
const { default: axios } = require("axios");
const Replicate = require("replicate");
const { v4: uuidv4 } = require("uuid");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const {
  logFatal,
  logError,
  logErrorV2,
  logWarn,
  logInfo,
  logDebug,
  __console_error_do_not_use,
  __console_info_do_not_use,
  __console_warn_do_not_use,
} = require("./logUtils");

const url = require("url");
const querystring = require("querystring");
const { Storage } = require("@google-cloud/storage");

const PostgresErrorCode = {
  UNIQUE_VIOLATION: "23505",
};

const SupabaseAuthErrorCode = {
  USER_NOT_FOUND: "user_not_found",
};

const validHosts = [
  "db.butterflies.ai",
  "img.butterflies.ai",
  "www.butterflies.ai",
  "ciqehpcxkkhdjdxolvho.supabase.co",
  "api.butterflies.ai",
  "share.butterflies.ai",
  "butterflies.ai",
];

const { SpanStatusCode } = require("@opentelemetry/api");
const {
  tracer,
  decorateWithActiveSpanAsync,
  collectActiveSpanMetadata,
} = require("./instrumentation/tracer");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const customParseFormat = require("dayjs/plugin/customParseFormat");

dayjs.extend(utc);
dayjs.extend(customParseFormat);

const replicate = new Replicate({
  auth: "****************************************",
});

function normalizeExponential(value) {
  // Adjust these parameters to change the curve's shape
  const a = 80; // Scale factor
  const b = 8; // Rate of growth

  // Normalizing the value to start from 0, based on 0.85
  const normalizedValue = value - 0.87;

  // Exponential function
  let result = a * Math.exp(b * normalizedValue) - a; // Subtracting 'a' to start from 0

  // If the input value is less than 0.85, adjust the result to be negative
  if (value < 0.87) {
    result = -Math.abs(result);
  }

  return result;
}

function getMD5(input) {
  return crypto.createHash("md5").update(input).digest("hex");
}

function generateSpanID() {
  const buffer = crypto.randomBytes(8); // Generates an 8-byte buffer

  let spanID = "";
  for (let i = 0; i < buffer.length; i++) {
    spanID += buffer[i].toString(16).padStart(2, "0"); // Convert each byte to a 2-character hexadecimal string
  }

  // Ensure the generated span ID is not zero
  if (spanID === "0000000000000000") {
    return generateSpanID(); // Recursively call the function until a non-zero span ID is generated
  }

  return spanID;
}

function cosineSimilarity(vecA, vecB) {
  const dotProduct = math.dot(vecA, vecB);
  const normA = math.norm(vecA);
  const normB = math.norm(vecB);
  return dotProduct / (normA * normB); // This is the cosine similarity
}

function euclideanDistance(vecA, vecB) {
  if (vecA.length !== vecB.length) {
    throw "Vectors are not of the same dimension";
  }

  let sum = 0;
  for (let i = 0; i < vecA.length; i++) {
    sum += (vecA[i] - vecB[i]) ** 2;
  }
  return Math.sqrt(sum);
}

async function generateEmbedding(text) {
  const classifier = await GTESmall.getInstance();
  const response = await classifier(text, {
    pooling: "mean",
    normalize: true,
  });

  // Extract the embedding output
  const embedding = Array.from(response?.data);

  return embedding;
}

function posthogCapture(user_id, eventName, properties) {
  client.capture({
    distinctId: user_id,
    event: eventName,
    properties: properties,
  });
}

const detectNSFW = decorateWithActiveSpanAsync("detectNSFW", _detectNSFW);
async function _detectNSFW(url) {
  if (!url) {
    throw new Error("detectNSFW: missing url");
  }

  let nsfw = 0;
  try {
    nsfw = await runReplicate(url);
    logInfo({ context: "*** nsfw", message: `nsfw: ${nsfw}` });
  } catch (error) {
    logError({ context: "*** detect nsfw failed on full sized image", error });

    const { publicUrl, pathInStorage } = await processImageSharp(url);

    try {
      nsfw = await runReplicate(publicUrl);
      logInfo({ context: "*** nsfw", message: `nsfw: ${nsfw}` });
    } catch (error) {
      logError({ context: "*** detect nsfw failed on resized image", error });
      throw error;
    }

    // Intentionally not awaiting here, since this is a non-critical cleanup operation
    supabase.storage
      .from("ai-ig")
      .remove([pathInStorage])
      .then((deleteError) => {
        logError({
          context: "*** error deleting temporary uploaded image",
          error: deleteError,
        });
      });
  }

  return nsfw;
}

async function runReplicate(url) {
  let nsfw = await replicate.run(
    "butterflies-ai/nsfw-detector:67091b0cf77c5ceb0116f6442f5e480085c50beab15aa0cb5ab72f5fdacbf376",
    {
      input: {
        image: url,
      },
    },
  );

  return JSON.parse(nsfw).nsfw;
}

const processImageSharp = decorateWithActiveSpanAsync(
  "process image and upload to storage",
  _processImageSharp,
);
async function _processImageSharp(url) {
  let imageArray;
  let publicUrl;
  let pathInStorage;

  try {
    const response = await tracer.withActiveSpan(
      "fetch image data",
      async () => {
        return await axios.get(url, { responseType: "arraybuffer" });
      },
    );

    imageArray = await tracer.withActiveSpan(
      "resize image and convert to jpeg",
      async () => {
        // Convert the image data to a Buffer and then to sharp
        const imageBuffer = Buffer.from(response.data, "binary");
        const image = sharp(imageBuffer).withMetadata().jpeg();
        return await image.resize(600).toBuffer();
      },
    );
  } catch (err) {
    logError({
      context: "**** resizeImageSharp error",
      error: err,
    });
    throw err;
  }

  let myuuid = uuidv4();

  // Upload the image to Supabase storage
  pathInStorage = `/temp/${myuuid}`;

  return await tracer.withActiveSpan(
    "upload image to storage",
    async (span) => {
      const { data, error: storageError } = await supabase.storage
        .from("ai-ig")
        .upload(pathInStorage, imageArray, {
          contentType: "image/jpeg", // Make sure to set this correctly for your file type
          cacheControl: "31536000",
        });

      if (storageError) {
        if (storageError?.statusCode === "409") {
          logWarn({
            context: "*** upload image duplicate error",
            error: storageError,
          });
          // 409: duplicate
          publicUrl = supabase.storage.from("ai-ig").getPublicUrl(pathInStorage)
            .data.publicUrl;
          return { publicUrl, pathInStorage };
        } else {
          const error = wrappedSupabaseError(storageError);
          logError({
            context: "*** upload image error",
            error: error,
          });
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error.message,
          });
          throw error;
        }
      } else {
        publicUrl = supabase.storage.from("ai-ig").getPublicUrl(data.path)
          .data.publicUrl;
      }

      return { publicUrl, pathInStorage };
    },
  );
}

const resizeImageFromBuffer = decorateWithActiveSpanAsync(
  "resizeImageFromBuffer",
  _resizeImageFromBuffer,
);
async function _resizeImageFromBuffer(imageBuffer, contentType) {
  try {
    imageBuffer = Buffer.from(imageBuffer, "binary");
    let image = sharp(imageBuffer, {
      animated: true,
      pages: -1,
    }).withMetadata();

    const metadata = await image.metadata();
    const actualWidth =
      metadata.orientation >= 5 ? metadata.height : metadata.width;

    switch (contentType) {
      case "image/jpeg":
        image.jpeg();
        break;
      case "image/webp":
        image.webp();
        break;
      case "image/png":
        image.png();
        break;
      case "image/gif":
        image.gif();
        break;
      default:
        image.jpeg();
    }

    const imageArray = await image.resize(actualWidth).toBuffer();
    return imageArray;
  } catch (err) {
    logError({
      context: "**** resizeImageFromBuffer error",
      error: err,
    });
    throw err;
  }
}

// function to check the profile owner
async function checkProfileValid(user_id, profile_id) {
  if (!user_id || !profile_id) return false;
  return tracer.withActiveSpan("validate profile id", async (span) => {
    try {
      const { data, error } = await supabase.rpc("check_user_id_match", {
        input_user_id: user_id,
        input_profile_id: profile_id,
      });

      if (error) {
        throw wrappedSupabaseError(error);
      }

      console.log("----------isProfile", data);
      return data;
    } catch (error) {
      logError({
        context: "checkProfileValid",
        error: error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }
  });
}

async function checkBotPostValid(user_id, post_id) {
  if (!user_id || !post_id) return false;
  return tracer.withActiveSpan("validate post id from bots", async (span) => {
    try {
      const { data, error } = await supabase.rpc("check_bot_post_valid", {
        post_id_input: post_id,
        user_id_input: user_id,
      });

      if (error) {
        throw wrappedSupabaseError(error);
      }

      return data;
    } catch (error) {
      logError({
        context: "checkBotPostValid",
        error: error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }
  });
}

// function to check the bot owner
async function checkBotValid(user_id, bot_id) {
  if (!user_id || !bot_id) return false;
  return tracer.withActiveSpan("validate bot id", async (span) => {
    const { data: bot, error: botError } = await supabase
      .from("bots")
      .select("creator:profiles!bots_creator_id_fkey(id, user_id)")
      .eq("id", bot_id)
      .single();

    if (botError) {
      const error = wrappedSupabaseError(botError);
      logError({
        context: "checkBotValid: fetch bot error",
        error,
        bot_id,
        user_id,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }

    if (!bot || !bot.creator) {
      const error = new Error("validBotOwnerOrAdmin: bot not found");
      logError({
        context: "checkBotValid: bot not found",
        error,
        bot_id,
        user_id,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }

    console.log("----------isBot", bot.creator.user_id === user_id);
    return bot.creator.user_id === user_id;
  });
}

async function validateBotProfileOwnedByUser({ userId, botProfileId }) {
  if (!userId || !botProfileId) return false;

  return tracer.withActiveSpan(
    "validateBotProfileOwnedByUser",
    async (span) => {
      const { data: bot, error: botError } = await supabase
        .from("bots")
        .select(
          "profile_id, creator:profiles!bots_creator_id_fkey(id, user_id)",
        )
        .eq("profile_id", botProfileId)
        .single();

      if (botError) {
        const error = wrappedSupabaseError(botError);
        logError({
          context: "validateBotProfileOwnedByUser: fetch bot error",
          error,
          botProfileId,
          userId,
        });
        return false;
      }

      if (!bot || !bot.creator) {
        const error = new Error("validBotOwnerOrAdmin: bot not found");
        logError({
          context: "validateBotProfileOwnedByUser: bot not found",
          error,
          botProfileId,
          userId,
        });
        return false;
      }

      return bot.creator.user_id === userId;
    },
  );
}

// function to check the admin or moderator
async function checkAdminValid(user_id) {
  if (!user_id) return false;
  return tracer.withActiveSpan("validate admin", async (span) => {
    const { data: userRole, error: userRoleError } = await supabase
      .from("users")
      .select("role")
      .eq("id", user_id)
      .single();

    if (userRoleError) {
      const error = wrappedSupabaseError(userRoleError);
      logError({
        context: "checkAdminValid: fetch user role error",
        error,
        user_id,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }

    console.log(
      "----------isAdmin",
      userRole.role === "admin" || userRole.role === "moderator",
    );

    return userRole.role === "admin" || userRole.role === "moderator";
  });
}

// function to check if the clone_id is owned by the user
async function checkCloneValid(user_id, clone_id) {
  if (!user_id || !clone_id) return false;
  return tracer.withActiveSpan("validate bot's clone_id", async (span) => {
    const { data: bot, error: botError } = await supabase
      .from("bots")
      .select("creator:profiles!bots_creator_id_fkey(id, user_id)")
      .eq("clone_id", clone_id)
      .maybeSingle();

    if (botError) {
      const error = wrappedSupabaseError(botError);
      logError({
        context: "checkCloneValid: fetch bot error",
        error,
        clone_id,
        user_id,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }

    if (!bot) {
      logInfo({
        context: "checkCloneValid: bot not found",
        clone_id,
        user_id,
      });
      // new bot being created, so – valid?
      return true;
    }

    if (!bot.creator) {
      const error = new Error("checkCloneValid: bot creator not found");
      logError({
        context: "checkCloneValid: bot creator not found",
        error,
        clone_id,
        user_id,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      return false;
    }

    console.log("----------isClone", bot.creator.user_id === user_id);
    return bot.creator.user_id === user_id;
  });
}

function getSpecificKeys(obj, keys) {
  return Object.keys(obj)
    .filter((key) => keys.includes(key))
    .reduce((acc, key) => {
      acc[key] = obj[key];
      return acc;
    }, {});
}

async function validateOwnConversation(userId, conversationId) {
  try {
    const { data, error } = await supabase
      .from("conversation_participants")
      .select(
        `
      profiles (
        id, 
        user_id
      )
    `,
      )
      .neq("profiles.visibility", "archived")
      .neq("profiles.visibility", "hidden")
      .not("profiles", "is", null)
      .eq("conversation_id", conversationId);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    if (data.length === 0) {
      throw new Error("This conversation does not exist");
    }

    // Check if any profile matches the userId
    const isUserParticipant = data.some(
      (participant) =>
        participant.profiles && participant.profiles.user_id === userId,
    );

    if (!isUserParticipant) {
      throw new Error("You have no permission to join this conversation");
    }
    return true;
  } catch (error) {
    logError({
      context: "validateOwnConversation",
      error,
      conversationId,
      userId,
    });
    return false;
  }
}

async function getOrGenerateSignedUrl(publicUrl) {
  const storage = new Storage();

  // Parse the URL to get the query parameters
  const parsedUrl = url.parse(publicUrl);
  const queryParams = querystring.parse(parsedUrl.query);

  // Extract the expiration time (in seconds since epoch)
  const expiresInSeconds = parseInt(queryParams["X-Goog-Expires"], 10);

  const dateHeader = queryParams["X-Goog-Date"];

  // Parse the date using Day.js with UTC plugin
  const signedDate = dayjs.utc(dateHeader, "YYYYMMDDTHHmmss[Z]");

  // Calculate the expiration date by adding the expiration time
  const expirationDate = signedDate.add(expiresInSeconds, "second").valueOf();

  // Calculate the time 3 days after the signed date
  const threeDaysAfterSignedDate = signedDate.add(3, "day").valueOf();

  // Get the current time
  const currentTime = Date.now();

  // If the URL is more than 3 days old or has expired, generate a new one
  if (currentTime > threeDaysAfterSignedDate || currentTime > expirationDate) {
    console.log(
      "Signed URL is expired or older than 3 days, generating a new one...",
    );

    // Parse the bucket name and file path from the URL
    const bucketName = parsedUrl.pathname.split("/")[1];
    const filePath = parsedUrl.pathname.split(`${bucketName}/`)[1];

    const options = {
      version: "v4",
      action: "read",
      expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
    };

    // Get a reference to the file
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(filePath);

    // Generate the new signed URL
    const [newSignedUrl] = await file.getSignedUrl(options);
    return newSignedUrl;
  }

  // Return the original URL if it hasn't expired and is less than 3 days old
  console.log("Signed URL is still valid and less than 3 days old.");
  return publicUrl;
}

const lazyInit = (fn) => {
  let value = undefined;
  return () => {
    if (value) {
      return value;
    } else {
      value = fn();
      return value;
    }
  };
};

// Remove junk that is sometimes returned by Vertex AI.
function fixVertexAIResponse(s) {
  if (!s) {
    return "";
  }

  s = s.replace("[{'type': 'text', 'text': ", "");
  s = s.replace(
    "[{'type': 'image', 'content': 'https://example.com/image ",
    "",
  );
  s = s.replace("'}]", "");

  return s;
}

// More robust hash function for time-based seeds
function hashCode(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Generate a deterministic seed based on time intervals
function generateTimeSeed(intervalMinutes = 15, timestamp = Date.now()) {
  const intervalMs = intervalMinutes * 60 * 1000;
  const baseTime = Math.floor(timestamp / intervalMs) * intervalMs;

  // Create a more unique string to hash by including date components
  const date = new Date(baseTime);
  const hashInput = [
    date.getFullYear(),
    date.getMonth() + 1,
    date.getDate(),
    date.getHours(),
    Math.floor(date.getMinutes() / intervalMinutes) * intervalMinutes,
  ].join("-");

  return hashCode(hashInput);
}

function checkMediaUrlValid(mediaUrl) {
  try {
    const url = new URL(mediaUrl);
    return validHosts.includes(url.host);
  } catch (error) {
    logError({ context: "*** invalid media_url", error });
    return false;
  }
}

function getDeviceType(userAgent) {
  const lowerUserAgent = userAgent.toLowerCase();
  if (lowerUserAgent.includes("android")) {
    return "android";
  } else if (lowerUserAgent.includes("iphone") || userAgent.includes("ipad")) {
    return "ios";
  } else if (!lowerUserAgent.startsWith("mozilla")) {
    return "script";
  } else {
    return "desktop";
  }
}

module.exports = {
  generateSpanID,
  logFatal,
  logError,
  logErrorV2,
  logWarn,
  logInfo,
  logDebug,
  __console_error_do_not_use,
  __console_info_do_not_use,
  __console_warn_do_not_use,
  wrappedSupabaseError,
  cosineSimilarity,
  euclideanDistance,
  generateEmbedding,
  normalizeExponential,
  posthogCapture,
  detectNSFW,
  resizeImageFromBuffer,
  checkProfileValid,
  checkBotValid,
  validateBotProfileOwnedByUser,
  checkAdminValid,
  checkCloneValid,
  checkBotPostValid,
  getSpecificKeys,
  collectActiveSpanMetadata,
  getMD5,
  PostgresErrorCode,
  SupabaseAuthErrorCode,
  validateOwnConversation,
  getOrGenerateSignedUrl,
  fixVertexAIResponse,
  lazyInit,
  generateTimeSeed,
  checkMediaUrlValid,
  getDeviceType,
};
