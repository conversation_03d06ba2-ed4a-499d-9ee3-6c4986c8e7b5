{"3": {"inputs": {"seed": 3731173248, "steps": 5, "cfg": 1.6, "sampler_name": "dpmpp_2s_ancestral", "scheduler": "karras", "denoise": 1, "model": ["31", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "meinamix.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 512, "height": 512, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "purple hair, woman, holding deck of cards, magician, wearing top hat, magic, dramatic, close", "clip": ["35", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "cross-eyed,sketches, (worst quality), (low quality), (normal quality), lowres, normal quality, bad anatomy, DeepNegative, facing away, tilted head, Multiple people, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worstquality, low quality, normal quality, jpegartifacts, signature, watermark, username, blurry, bad feet, cropped, poorly drawn hands, poorly drawn face, mutation, deformed, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, extra fingers, fewer digits, extra limbs, extra arms,extra legs, malformed limbs, fused fingers, too many fingers, long neck, cross-eyed,mutated hands, polar lowres, bad body, bad proportions, gross proportions, text, error, missing fingers, missing arms, missing legs, extra digit, extra arms, extra leg, extra foot, (repeating hair)", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "30": {"inputs": {"lora_name": "NSFWFilter.safetensors", "strength_model": -1, "model": ["4", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "31": {"inputs": {"lora_name": "lcm.safetensors", "strength_model": 0.6, "model": ["30", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "35": {"inputs": {"stop_at_clip_layer": -2, "clip": ["4", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "CLIP Set Last Layer"}}, "37": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 1024, "seed": 1111880837726787, "steps": 4, "cfg": 1.6, "sampler_name": "dpmpp_2s_ancestral", "scheduler": "karras", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "image": ["8", 0], "model": ["31", 0], "clip": ["4", 1], "vae": ["4", 2], "positive": ["6", 0], "negative": ["7", 0], "bbox_detector": ["42", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "42": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "43": {"inputs": {"filename_prefix": "ComfyUI", "images": ["37", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}