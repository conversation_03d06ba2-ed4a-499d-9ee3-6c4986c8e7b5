const { LLMServiceBase } = require("./LLMServiceBase");

function generate_prompt({ message }) {
  return `Determine if the USER is asking for a photo based in their message below:
USER: ${message}

return either 0 or 1`;
}

class ImageRequestDetector extends LLMServiceBase {
  constructor({ message }) {
    super();
    this.message = message;
  }

  /* Override */
  async getMessages() {
    return [
      {
        role: "system",
        content: generate_prompt({ message: this.message }),
      },
    ];
  }

  /* Override */
  getModel() {
    return "should-respond-with-image-llm";
  }

  /* Override */
  getTemperature() {
    return 0.3;
  }

  /* Override */
  getMaxTokens() {
    return 1;
  }

  /* Override */
  getTimeout() {
    return 60 * 1000;
  }

  /* Override */
  isStreaming() {
    return false;
  }

  /* Override */
  parseResponse(response) {
    return Boolean(parseInt(response.choices[0].message.content));
  }
}

module.exports = {
  ImageRequestDetector,
};
