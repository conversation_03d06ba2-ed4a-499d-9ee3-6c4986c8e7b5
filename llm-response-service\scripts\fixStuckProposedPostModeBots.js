const { supabase } = require("../src/supabaseClient");
const { default: axios } = require("axios");

async function fetchProfilesWithoutProposedPosts(batchSize = 4000) {
  let allProfiles = [];
  let from = 0;
  let to = batchSize - 1;
  const now = new Date();
  let hasMore = true;

  while (hasMore) {
    // First, get all profiles that match the basic criteria
    const { data: profilesData, error: profilesError } = await supabase
      .from("profiles")
      .select("id, username, description, display_name, proposed_post_mode, proposed_post_next_generation_date")
      .eq("proposed_post_mode", true)
      .lte("proposed_post_next_generation_date", now.toISOString())
      .not("visibility", "in", "(archived,hidden)")
      .order("created_at", { ascending: false })
      .range(from, to);

    if (profilesError) {
      console.error(profilesError);
      break;
    }

    if (!profilesData || profilesData.length === 0) {
      hasMore = false;
      break;
    }

    // For each profile, check if it has any posts with the specified states
    const profilesWithoutProposedPosts = [];
    for (const profile of profilesData) {
      const { data: postsData, error: postsError } = await supabase
        .from("posts")
        .select("id")
        .eq("profile_id", profile.id)
        .not("visibility", "in", "(hidden,archived)")
        .in("proposed_post_state", ["generating", "proposed"])
        .limit(1); // We only need to know if any exist

      if (postsError) {
        console.error(`Error checking posts for profile ${profile.id}:`, postsError);
        continue;
      }

      // If no posts found with the specified states, add to our result
      if (!postsData || postsData.length === 0) {
        profilesWithoutProposedPosts.push(profile);
      }
    }

    allProfiles = allProfiles.concat(profilesWithoutProposedPosts);
    from += batchSize;
    to += batchSize;
    if (profilesData.length < batchSize) hasMore = false;
  }

  return allProfiles;
}

async function main() {
  console.log("fetching profiles without proposed posts...");
  const profilesWithoutProposedPosts = await fetchProfilesWithoutProposedPosts(4000);
  console.log(`fetched ${profilesWithoutProposedPosts.length} profiles without proposed posts`);

  let idx = 0;
  for (const botProfile of profilesWithoutProposedPosts) {
    console.log(
      `#${idx}/${profilesWithoutProposedPosts.length}: Fixing bot profile ${botProfile.id}`,
    );

    try {
      const result = await triggerGenerateProposedPostTask({
        bot_profile_id: botProfile.id,
      });
      console.log("result", result.data);
    } catch (err) {
      console.error(
        `Error for bot profile ${botProfile.id}:`,
        err.message || err,
      );
    }
    idx += 1;
  }
}

const triggerGenerateProposedPostTask = async ({ bot_profile_id }) => {
  const result = await axios.post(
    "http://cocoon.butterflies.ai/v1/proposedPosts/generateProposedPostTask",
    {
      bot_profile_id,
    },
  );

  return result;
};

main().then(() => {
  console.log("done");
  process.exit(0);
});
