const { supabase } = require("../src/supabaseClient");
const { default: axios } = require("axios");

async function fetchProfilesPaginated(batchSize = 4000) {
  let allProfiles = [];
  let from = 0;
  let to = batchSize - 1;
  const now = new Date();
  let hasMore = true;

  while (hasMore) {
    const { data, error } = await supabase
      .from("profiles")
      .select(
        `id, username, description, display_name, proposed_post_mode, proposed_post_next_generation_date,
          posts(
            id, created_at, profile_id, media_url, location, slug, description, type, tags, visibility, nsfw, nsfl, previewhash, proposed_post_state
          )
        `,
      )
      .eq("proposed_post_mode", true)
      .lte("proposed_post_next_generation_date", now.toISOString())
      .not("visibility", "in", "(archived,hidden)")
      .not("posts.visibility", "in", "(hidden,archived)")
      .in("posts.proposed_post_state", ["generating", "proposed"])
      .order("created_at", { ascending: false })
      .range(from, to);

    if (error) {
      console.error(error);
      break;
    }

    if (!data || data.length === 0) {
      hasMore = false;
    } else {
      allProfiles = allProfiles.concat(data);
      from += batchSize;
      to += batchSize;
      if (data.length < batchSize) hasMore = false;
    }
  }

  return allProfiles;
}

async function main() {
  console.log("fetching...");
  const profiles = await fetchProfilesPaginated(4000);
  console.log(`fetched ${profiles.length} profiles`);

  const profilesThatHaveProposedPosts = [];
  const profilesWithoutProposedPosts = [];
  for (const botProfile of profiles) {
    if (botProfile.posts.length === 0) {
      profilesWithoutProposedPosts.push(botProfile);
    } else {
      profilesThatHaveProposedPosts.push(botProfile);
    }
  }

  console.log(
    "!!! profilesThatHaveProposedPosts",
    profilesThatHaveProposedPosts.length,
  );
  console.log(
    "!!! profilesWithoutProposedPosts",
    profilesWithoutProposedPosts.length,
  );

  let idx = 0;
  for (const botProfile of profilesWithoutProposedPosts) {
    console.log(
      `#${idx}/${profilesWithoutProposedPosts.length}: Fixing bot profile ${botProfile.id}`,
    );

    try {
      const result = await triggerGenerateProposedPostTask({
        bot_profile_id: botProfile.id,
      });
      console.log("result", result.data);
    } catch (err) {
      console.error(
        `Error for bot profile ${botProfile.id}:`,
        err.message || err,
      );
    }
    idx += 1;
  }
}

const triggerGenerateProposedPostTask = async ({ bot_profile_id }) => {
  const result = await axios.post(
    "http://cocoon.butterflies.ai/v1/proposedPosts/generateProposedPostTask",
    {
      bot_profile_id,
    },
  );

  return result;
};

main().then(() => {
  console.log("done");
  process.exit(0);
});
