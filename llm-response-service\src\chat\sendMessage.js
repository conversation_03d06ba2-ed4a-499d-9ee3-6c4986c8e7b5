const { v4: uuidv4 } = require("uuid");
const { addSpanAttribute } = require("../instrumentation/tracer");
const { generateEmbeddingIfNecessary } = require("../memory");
const { maybeSendPushForConversation } = require("../notifications");
const {
  retrySupabaseOperation,
  supabase,
  wrappedSupabaseError,
} = require("../supabaseClient");
const {
  checkMediaUrlValid,
  logError,
  logInfo,
  getDeviceType,
} = require("../utils");
const { loggingInfo } = require("../logging");
const { getExperiments } = require("../ab_expriments");
const { generateBotResponseStreaming } = require("./generateBotResponse");
const {
  respondWithTextMessage,
} = require("./responders/respondWithTextMessage");
const {
  respondWithImageMessage,
} = require("./responders/respondWithImageMessage");
const { Context } = require("./context");
const {
  authorizeUserForConversation,
} = require("./authorizeUserForConversation");
const { readProfiles } = require("./readProfiles");
const { updateIsTyping } = require("./updateIsTyping");

// XXX: we need to correctly handle exceptions. There's too many holes rn
// XXX: we need to insert a row into the database so we can retry the generation
//      if it fails.
// XXX: currently this function just updates the database whenever it feels like it.
//      Everything should be generated first and only inserted into the database if
//      it's all done.
async function sendMessage(req, res) {
  // Validate the input
  if (!req.body.body || !req.body.conversation_id || !req.body.sender_id) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  const senderId = req.body.sender_id;
  const conversationId = req.body.conversation_id;
  const userId = req.user?.id;

  const ctx = new Context(uuidv4(), conversationId, senderId);

  if (senderId == 411382) {
    ctx.setVerbose();
  }

  try {
    ctx.initExperiments(await getExperiments(senderId));
  } catch (error) {
    logError({
      context: "/sendMessage - failed to fetch experiments",
      error,
    });
    return res.status(500).send({ error: "Internal server error" });
  }

  logInfo({
    ...ctx.logging,
    request_body: req.body,
    msg: "handling sendMessage request",
  });

  // authorize the request.
  if (!req.isAdmin) {
    if (
      !(await authorizeUserForConversation(conversationId, senderId, userId))
    ) {
      logInfo({
        ...ctx.logging,
        msg: "request not authorized.",
      });
      return res.status(403).json({ error: "Forbidden" });
    }
  }

  // Validate media url
  const mediaUrl = req.body.media_url;
  if (mediaUrl && !checkMediaUrlValid(mediaUrl)) {
    logInfo({
      ...ctx.logging,
      msg: "invalid media_url.",
    });
    return res.status(400).json({ error: "Bad media_url" });
  }

  const { message_grouping_id } = await retrySupabaseOperation(
    () => supabase.from("message_groupings").insert({}).select("id").single(),
    "insert message groupings",
  );

  const type = req.body.type;
  const metadata = req.body.metadata;
  const branchIndex = req.body.branch_index ? req.body.branch_index : 0;
  let body;
  if (type === "image") {
    // insert the image caption into messages
    await retrySupabaseOperation(
      () =>
        supabase
          .from("messages")
          .insert({
            conversation_id: conversationId,
            sender_id: senderId,
            body: req.body.body,
            branch_index: 0,
            is_system_message: true,
          })
          .select("*")
          .single(),
      "insert image caption",
    );

    // Override the body to something that makes sense for the rest of the logic.
    const sender =
      metadata?.sender?.display_name || metadata?.sender?.username || "Unknown";
    body = req.body.comment || `Sent an image by ${sender}.`;
  } else {
    body = req.body.body;
  }

  const insertedMessage = await retrySupabaseOperation(
    () =>
      supabase
        .from("messages")
        .insert({
          is_bot: false,
          is_system_message: false,
          conversation_id: conversationId,
          sender_id: senderId,
          body,
          branch_index: 0,
          type,
          media_url: mediaUrl,
          metadata,
          pre_processed: false,
          message_grouping_id,
        })
        .select(
          "id, conversation_id, sender_id, body, branch_index, type, media_url, metadata, created_at, message_grouping_id",
        )
        .single(),
    "insert messages",
  );

  logInfo({
    ...ctx.logging,
    msg: "responding to client",
    inserted_message_id: insertedMessage.id,
  });

  // Relieve the request so we can take our sweet time generating the response.
  res.json(insertedMessage);

  // TODO: currently if there's an error or if the rug is pulled we will leave the user
  //       on read. We can fix this with DB RLLs/tags and a periodical janitor.
  //       For now, keep parity with the previous implementation, since we need to
  //       to be able to differentiate between recoverable/terminal errors and figure
  //       out retry policies.

  const {
    userParticipant,
    botParticipant,
    userProfile,
    botProfile,
    botConfiguration,
  } = await readProfiles(ctx, { conversationId, senderId });

  if (!botConfiguration) {
    logInfo({
      ...ctx.logging,
      msg: "sending push notification to user",
      other_user_id: botParticipant.profile_id,
    });

    // Bot not found, it's a user.
    // XXX: We should check if the error is NOT_FOUND
    maybeSendPushForConversation({
      userProfileId: botParticipant.profile_id,
      conversation_id: conversationId,
      notificationProfileId: botParticipant.profile_id,
      notificationTitle: userProfile?.display_name || userProfile?.username,
      notificationText: body,
      conversationType: "with_human",
    });
    return;
  }

  ctx.setProfiles({
    userParticipant,
    botParticipant,
    userProfile,
    botProfile,
    botConfiguration,
  });

  addSpanAttribute("bot_sender", botParticipant);
  addSpanAttribute("user_display_name", userProfile.display_name);
  addSpanAttribute("bot_display_name", botProfile.display_name);

  const messageId = insertedMessage.id;

  updateIsTyping(conversationId, botProfile.id, true);

  // Update memories.
  generateEmbeddingIfNecessary({ conversation_id: conversationId }).catch(
    (error) => {
      logError({
        ...ctx.logging,
        msg: "generateEmbeddingIfNecessary failed",
        error,
      });
    },
  );

  // Update the last message timestamp for reengage campaign.
  updateReengageCampaignTimestamp(
    conversationId,
    userProfile.id,
    botProfile.id,
  );

  // Update the user streak
  updateStreak(userProfile.id, botProfile.id);

  const deviceType = getDeviceType(req.get("User-Agent"));
  ctx.setDeviceType(deviceType);

  const { data: history, error: historyError } = await supabase
    .from("messages")
    .select("*")
    .eq("conversation_id", conversationId)
    .order("id", { ascending: false })
    .limit(40);
  if (historyError) {
    const error = wrappedSupabaseError(
      historyError,
      "Failed to fetch messages",
    );
    logError({
      ...ctx.logging,
      msg: "failed to fetch messages",
      error,
    });
    throw error;
  }

  history.reverse();

  ctx.setMessageHistory(history);

  ctx.startMessageGeneration();

  try {
    const botMessages = generateBotResponseStreaming(
      ctx,
      { conversationConfiguration: botParticipant, botConfiguration },
      { userProfile, botProfile },
      { body, history, deviceType },
    );

    for await (const message of botMessages) {
      ctx.registerGeneratedMessage(message);
      if (message.type === "text") {
        await respondWithTextMessage(
          ctx,
          { conversationId, messageGroupingId: message_grouping_id },
          { botProfile },
          { branchIndex },
          message,
        );
      } else if (message.type === "image") {
        await respondWithImageMessage(
          ctx,
          { conversationId },
          { botConfiguration, botProfile, userProfile },
          { branchIndex, deviceType },
          message,
        );
      }
      // So ugly :(
      generateEmbeddingIfNecessary({ conversation_id: conversationId }).catch(
        (error) => {
          logError({
            ...ctx.logging,
            msg: "generateEmbeddingIfNecessary failed",
            error,
          });
        },
      );
    }

    ctx.endMessageGeneration();

    logInfo({
      ...ctx.logging,
      msg: "generation done",
      success: true,
    });
    // Send the logs to bigquery as well
    loggingInfo("chat_backend", {
      type: "GENERATION_DONE",
      result: "SUCCESS",
      info: JSON.stringify({
        ...ctx.getCleanLogging(),
      }),
    });
  } catch (error) {
    logError({
      ...ctx.logging,
      msg: "generation done",
      success: false,
      error,
    });
    // Send the logs to bigquery as well
    loggingInfo("chat_backend", {
      type: "GENERATION_DONE",
      result: "FAILURE",
      info: JSON.stringify({
        ...ctx.getCleanLogging(),
      }),
    });

    updateIsTyping(conversationId, botProfile.id, false);

    throw error;
  }

  await handleLikeAction(conversationId, senderId, messageId, metadata);

  updateIsTyping(conversationId, botProfile.id, false);
}

async function handleLikeAction(conversationId, senderId, messageId, metadata) {
  if (Math.random() >= 0.25) {
    return;
  }

  // update message metadata with heart emoji
  const { error } = await supabase
    .from("messages")
    .update({
      metadata: {
        ...(metadata || {}),
        emojis: [...(metadata?.emojis || []), "❤️"],
      },
    })
    .eq("id", messageId);
  if (error) {
    logError({
      context: "/sendMessage - updateMessageMetadata failed",
      error: wrappedSupabaseError(error),
      conversation_id: conversationId,
      sender_id: senderId,
      message_id: messageId,
    });
  }
}

async function updateReengageCampaignTimestamp(
  conversationId,
  userProfileId,
  botProfileId,
) {
  const { error } = await supabase.from("reengage_message_campaigns").upsert(
    {
      conversation_id: conversationId,
      user_profile_id: userProfileId,
      bot_profile_id: botProfileId,
      last_human_message_received_at: new Date().toISOString(),
      last_engaged_level: 0,
    },
    { onConflict: ["conversation_id"] },
  );

  if (error) {
    logError({
      context: "/sendMessage - failed to update reengage campaign",
      conversation_id: conversationId,
      user_profile_id: userProfileId,
      bot_profile_id: botProfileId,
      error: wrappedSupabaseError(error, "Failed to update reengagecampaign"),
    });
  }
}

async function updateStreak(userProfileId, botProfileId) {
  const { data, error: retrieveError } = await supabase
    .from("streaks")
    .select("*")
    .eq("user_profile_id", userProfileId)
    .eq("bot_profile_id", botProfileId)
    .single();

  if (retrieveError && retrieveError.code != "PGRST116") {
    logError({
      context: "/sendMessage - Failed to retrieve streaks",
      user_profile_id: userProfileId,
      bot_profile_id: botProfileId,
      error: wrappedSupabaseError(retrieveError, "Failed to retrieve streaks"),
    });
    return;
  }

  if (!data) {
    // First message, insert the streak.
    const { error } = await supabase.from("streaks").insert({
      user_profile_id: userProfileId,
      bot_profile_id: botProfileId,
      updated_at: new Date().toISOString(),
      current_streak: 0,
      max_streak: 1,
    });

    if (error) {
      logError({
        context: "/sendMessage - Failed to insert streaks",
        user_profile_id: userProfileId,
        bot_profile_id: botProfileId,
        error: wrappedSupabaseError(error, "Failed to insert streaks"),
      });
    }
    return;
  }

  const hoursSicneLastMessage =
    (new Date().getTime() - new Date(data.updated_at).getTime()) / 3600000;

  if (hoursSicneLastMessage < 16) {
    return;
  }

  const reset = hoursSicneLastMessage >= 48;

  const { error } = await supabase
    .from("streaks")
    .update({
      updated_at: reset ? data.updated_at : new Date().toISOString(),
      current_streak: reset ? 0 : data.current_streak + 1,
      max_streak: reset
        ? data.max_streak
        : Math.max(data.max_streak, data.current_streak + 1),
    })
    .eq("user_profile_id", userProfileId)
    .eq("bot_profile_id", botProfileId);

  if (error) {
    logError({
      context: "/sendMessage - Failed to update streaks",
      user_profile_id: userProfileId,
      bot_profile_id: botProfileId,
      error: wrappedSupabaseError(error, "Failed to update streaks"),
    });
  }
}

module.exports = {
  sendMessage,
};
