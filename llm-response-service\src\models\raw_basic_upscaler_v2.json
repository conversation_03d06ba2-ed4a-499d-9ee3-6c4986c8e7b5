{
  "last_node_id": 36,
  "last_link_id": 30,
  "nodes": [
    {
      "id": 5,
      "type": "EmptyLatentImage",
      "pos": [
        100,
        358
      ],
      "size": {
        "0": 315,
        "1": 106
      },
      "flags": {},
      "order": 0,
      "mode": 0,
      "outputs": [
        {
          "name": "LATENT",
          "type": "LATENT",
          "links": [
            4
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "EmptyLatentImage"
      },
      "widgets_values": [
        768,
        1024,
        1
      ]
    },
    {
      "id": 30,
      "type": "LoraLoaderModelOnly",
      "pos": [
        515,
        130
      ],
      "size": {
        "0": 315,
        "1": 82
      },
      "flags": {},
      "order": 3,
      "mode": 0,
      "inputs": [
        {
          "name": "model",
          "type": "MODEL",
          "link": 17
        }
      ],
      "outputs": [
        {
          "name": "MODEL",
          "type": "MODEL",
          "links": [
            1
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "LoraLoaderModelOnly"
      },
      "widgets_values": [
        "NSFWFilter.safetensors",
        -1
      ]
    },
    {
      "id": 4,
      "type": "CheckpointLoaderSimple",
      "pos": [
        100,
        130
      ],
      "size": {
        "0": 315,
        "1": 98
      },
      "flags": {},
      "order": 1,
      "mode": 0,
      "outputs": [
        {
          "name": "MODEL",
          "type": "MODEL",
          "links": [
            17,
            22
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "CLIP",
          "type": "CLIP",
          "links": [
            5,
            6,
            24
          ],
          "shape": 3,
          "slot_index": 1
        },
        {
          "name": "VAE",
          "type": "VAE",
          "links": [
            8,
            25
          ],
          "shape": 3,
          "slot_index": 2
        }
      ],
      "properties": {
        "Node name for S&R": "CheckpointLoaderSimple"
      },
      "widgets_values": [
        "meinamix.safetensors"
      ]
    },
    {
      "id": 7,
      "type": "CLIPTextEncode",
      "pos": [
        515,
        672
      ],
      "size": {
        "0": 400,
        "1": 200
      },
      "flags": {},
      "order": 5,
      "mode": 0,
      "inputs": [
        {
          "name": "clip",
          "type": "CLIP",
          "link": 6
        }
      ],
      "outputs": [
        {
          "name": "CONDITIONING",
          "type": "CONDITIONING",
          "links": [
            3,
            27
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "CLIPTextEncode"
      },
      "widgets_values": [
        `(worst quality), (low quality), (normal quality), lowres, normal quality, ${nsfw ? "" : "nsfw"}`
      ]
    },
    {
      "id": 26,
      "type": "UltralyticsDetectorProvider",
      "pos": [
        586,
        988
      ],
      "size": {
        "0": 315,
        "1": 78
      },
      "flags": {},
      "order": 2,
      "mode": 0,
      "outputs": [
        {
          "name": "BBOX_DETECTOR",
          "type": "BBOX_DETECTOR",
          "links": [
            18
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "SEGM_DETECTOR",
          "type": "SEGM_DETECTOR",
          "links": null,
          "shape": 3,
          "slot_index": 1
        }
      ],
      "properties": {
        "Node name for S&R": "UltralyticsDetectorProvider"
      },
      "widgets_values": [
        "bbox/face_yolov8m.pt"
      ]
    },
    {
      "id": 6,
      "type": "CLIPTextEncode",
      "pos": [
        398,
        392
      ],
      "size": {
        "0": 400,
        "1": 200
      },
      "flags": {},
      "order": 4,
      "mode": 0,
      "inputs": [
        {
          "name": "clip",
          "type": "CLIP",
          "link": 5
        }
      ],
      "outputs": [
        {
          "name": "CONDITIONING",
          "type": "CONDITIONING",
          "links": [
            2,
            26
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "CLIPTextEncode"
      },
      "widgets_values": [
        "two girls, sitting at a bar"
      ]
    },
    {
      "id": 33,
      "type": "ImpactSimpleDetectorSEGS",
      "pos": [
        1039,
        793
      ],
      "size": {
        "0": 315,
        "1": 310
      },
      "flags": {},
      "order": 8,
      "mode": 0,
      "inputs": [
        {
          "name": "bbox_detector",
          "type": "BBOX_DETECTOR",
          "link": 18
        },
        {
          "name": "image",
          "type": "IMAGE",
          "link": 28,
          "slot_index": 1
        },
        {
          "name": "sam_model_opt",
          "type": "SAM_MODEL",
          "link": null
        },
        {
          "name": "segm_detector_opt",
          "type": "SEGM_DETECTOR",
          "link": null
        }
      ],
      "outputs": [
        {
          "name": "SEGS",
          "type": "SEGS",
          "links": [
            19
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "ImpactSimpleDetectorSEGS"
      },
      "widgets_values": [
        0.5,
        10,
        3,
        50,
        0.93,
        0,
        0,
        0.7,
        0
      ]
    },
    {
      "id": 31,
      "type": "ImpactSEGSOrderedFilter",
      "pos": [
        1424,
        792
      ],
      "size": {
        "0": 315,
        "1": 150
      },
      "flags": {},
      "order": 9,
      "mode": 0,
      "inputs": [
        {
          "name": "segs",
          "type": "SEGS",
          "link": 19
        }
      ],
      "outputs": [
        {
          "name": "filtered_SEGS",
          "type": "SEGS",
          "links": [
            21
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "remained_SEGS",
          "type": "SEGS",
          "links": null,
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "ImpactSEGSOrderedFilter"
      },
      "widgets_values": [
        "area(=w*h)",
        true,
        0,
        4
      ]
    },
    {
      "id": 8,
      "type": "VAEDecode",
      "pos": [
        1527,
        112
      ],
      "size": {
        "0": 210,
        "1": 46
      },
      "flags": {},
      "order": 7,
      "mode": 0,
      "inputs": [
        {
          "name": "samples",
          "type": "LATENT",
          "link": 7
        },
        {
          "name": "vae",
          "type": "VAE",
          "link": 8
        }
      ],
      "outputs": [
        {
          "name": "IMAGE",
          "type": "IMAGE",
          "links": [
            23,
            28
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "VAEDecode"
      }
    },
    {
      "id": 3,
      "type": "KSampler",
      "pos": [
        1016,
        124
      ],
      "size": {
        "0": 315,
        "1": 262
      },
      "flags": {},
      "order": 6,
      "mode": 0,
      "inputs": [
        {
          "name": "model",
          "type": "MODEL",
          "link": 1
        },
        {
          "name": "positive",
          "type": "CONDITIONING",
          "link": 2
        },
        {
          "name": "negative",
          "type": "CONDITIONING",
          "link": 3
        },
        {
          "name": "latent_image",
          "type": "LATENT",
          "link": 4
        }
      ],
      "outputs": [
        {
          "name": "LATENT",
          "type": "LATENT",
          "links": [
            7
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "KSampler"
      },
      "widgets_values": [
        726813353597651,
        "fixed",
        20,
        7,
        "euler",
        "karras",
        1
      ]
    },
    {
      "id": 24,
      "type": "SaveImage",
      "pos": [
        2306,
        285
      ],
      "size": [
        678.4091064453128,
        930.4446105957031
      ],
      "flags": {},
      "order": 11,
      "mode": 0,
      "inputs": [
        {
          "name": "images",
          "type": "IMAGE",
          "link": 30
        }
      ],
      "properties": {},
      "widgets_values": [
        "ComfyUI"
      ]
    },
    {
      "id": 35,
      "type": "DetailerForEachDebug",
      "pos": [
        1825,
        313
      ],
      "size": {
        "0": 443.4000244140625,
        "1": 600
      },
      "flags": {},
      "order": 10,
      "mode": 0,
      "inputs": [
        {
          "name": "image",
          "type": "IMAGE",
          "link": 23
        },
        {
          "name": "segs",
          "type": "SEGS",
          "link": 21
        },
        {
          "name": "model",
          "type": "MODEL",
          "link": 22
        },
        {
          "name": "clip",
          "type": "CLIP",
          "link": 24
        },
        {
          "name": "vae",
          "type": "VAE",
          "link": 25
        },
        {
          "name": "positive",
          "type": "CONDITIONING",
          "link": 26
        },
        {
          "name": "negative",
          "type": "CONDITIONING",
          "link": 27
        },
        {
          "name": "detailer_hook",
          "type": "DETAILER_HOOK",
          "link": null
        }
      ],
      "outputs": [
        {
          "name": "image",
          "type": "IMAGE",
          "links": [
            30
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "cropped",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        },
        {
          "name": "cropped_refined",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        },
        {
          "name": "cropped_refined_alpha",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        },
        {
          "name": "cnet_images",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        }
      ],
      "properties": {
        "Node name for S&R": "DetailerForEachDebug"
      },
      "widgets_values": [
        384,
        true,
        1024,
        367860861367949,
        "randomize",
        6,
        8,
        "euler",
        "normal",
        0.5,
        5,
        true,
        true,
        "",
        1,
        false,
        20
      ]
    }
  ],
  "links": [
    [
      1,
      30,
      0,
      3,
      0,
      "MODEL"
    ],
    [
      2,
      6,
      0,
      3,
      1,
      "CONDITIONING"
    ],
    [
      3,
      7,
      0,
      3,
      2,
      "CONDITIONING"
    ],
    [
      4,
      5,
      0,
      3,
      3,
      "LATENT"
    ],
    [
      5,
      4,
      1,
      6,
      0,
      "CLIP"
    ],
    [
      6,
      4,
      1,
      7,
      0,
      "CLIP"
    ],
    [
      7,
      3,
      0,
      8,
      0,
      "LATENT"
    ],
    [
      8,
      4,
      2,
      8,
      1,
      "VAE"
    ],
    [
      17,
      4,
      0,
      30,
      0,
      "MODEL"
    ],
    [
      18,
      26,
      0,
      33,
      0,
      "BBOX_DETECTOR"
    ],
    [
      19,
      33,
      0,
      31,
      0,
      "SEGS"
    ],
    [
      21,
      31,
      0,
      35,
      1,
      "SEGS"
    ],
    [
      22,
      4,
      0,
      35,
      2,
      "MODEL"
    ],
    [
      23,
      8,
      0,
      35,
      0,
      "IMAGE"
    ],
    [
      24,
      4,
      1,
      35,
      3,
      "CLIP"
    ],
    [
      25,
      4,
      2,
      35,
      4,
      "VAE"
    ],
    [
      26,
      6,
      0,
      35,
      5,
      "CONDITIONING"
    ],
    [
      27,
      7,
      0,
      35,
      6,
      "CONDITIONING"
    ],
    [
      28,
      8,
      0,
      33,
      1,
      "IMAGE"
    ],
    [
      30,
      35,
      0,
      24,
      0,
      "IMAGE"
    ]
  ],
  "groups": [],
  "config": {},
  "extra": {},
  "version": 0.4
}