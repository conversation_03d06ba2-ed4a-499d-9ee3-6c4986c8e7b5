const { RealismMessageModel } = require("../model/RealismMessageModel");
const { messageGenerator } = require("./messageGenerator");

function realismGenerator(
  ctx,
  {
    imageRequested,
    message,
    messages,
    botProfile,
    userProfile,
    botConfiguration,
    conversationConfiguration,
    forceSfwImage,
  },
) {
  const textModel = new RealismMessageModel({
    message,
    messages,
    botProfile,
    userProfile,
    botConfiguration,
    conversationConfiguration,
  });

  return messageGenerator(ctx, textModel, {
    imageRequested,
    message,
    messages,
    botProfile,
    forceSfwImage,
    botConfiguration,
  });
}

module.exports = {
  realismGenerator,
};
