const seed_bots = [
  {
    franchise: "Zelda",
    source: "The Legend of Zelda",
    character: "Princess Zelda",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Final Fantasy",
    source: "Final Fantasy VII",
    character: "<PERSON><PERSON><PERSON>",
    type: "game",
    gender: "female",
  },
  {
    franchise: "<PERSON><PERSON><PERSON>",
    source: "<PERSON><PERSON><PERSON>",
    character: "Sakura Haruno",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Harry Potter",
    source: "Harry Potter Series",
    character: "Hermione Granger",
    type: "book",
    gender: "female",
  },
  {
    franchise: "The Hunger Games",
    source: "The Hunger Games",
    character: "Katniss Everdeen",
    type: "book",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Frozen",
    character: "<PERSON>",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "The Little Mermaid",
    character: "Ariel",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "One Piece",
    source: "One Piece",
    character: "<PERSON><PERSON>",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Black Widow",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Wonder Woman",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Star Wars",
    source: "Star Wars Series",
    character: "Leia Organa",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Attack on Titan",
    source: "Attack on Titan",
    character: "Mikasa Ackerman",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Game of Thrones",
    source: "A Song of Ice and Fire",
    character: "Daenerys Targaryen",
    type: "book",
    gender: "female",
  },
  {
    franchise: "Resident Evil",
    source: "Resident Evil Series",
    character: "Jill Valentine",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Naruto",
    source: "Naruto",
    character: "Hinata Hyuga",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Street Fighter",
    source: "Street Fighter",
    character: "Chun-Li",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Final Fantasy",
    source: "Final Fantasy X",
    character: "Yuna",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Kingdom Hearts",
    source: "Kingdom Hearts",
    character: "Kairi",
    type: "game",
    gender: "female",
  },
  {
    franchise: "The Legend of Korra",
    source: "The Legend of Korra",
    character: "Korra",
    type: "animation",
    gender: "female",
  },
  {
    franchise: "Sailor Moon",
    source: "Sailor Moon",
    character: "Usagi Tsukino",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Final Fantasy",
    source: "Final Fantasy XIII",
    character: "Lightning",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Beauty and the Beast",
    character: "Belle",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Final Fantasy",
    source: "Final Fantasy VI",
    character: "Terra Branford",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Super Mario",
    source: "Super Mario Series",
    character: "Princess Peach",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Tekken",
    source: "Tekken Series",
    character: "Ling Xiaoyu",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Mulan",
    character: "Mulan",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Overwatch",
    source: "Overwatch",
    character: "Tracer",
    type: "game",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Batwoman",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Mass Effect",
    source: "Mass Effect",
    character: "Commander Shepard (Female)",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Dragon Ball",
    source: "Dragon Ball",
    character: "Bulma",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Harry Potter",
    source: "Harry Potter Series",
    character: "Harry Potter",
    type: "book",
    gender: "male",
  },
  {
    franchise: "The Lord of the Rings",
    source: "The Lord of the Rings",
    character: "Frodo Baggins",
    type: "book",
    gender: "male",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Spider-Man",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Star Wars",
    source: "Star Wars Series",
    character: "Luke Skywalker",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Superman",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "One Piece",
    source: "One Piece",
    character: "Monkey D. Luffy",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Attack on Titan",
    source: "Attack on Titan",
    character: "Eren Yeager",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Super Mario",
    source: "Super Mario Series",
    character: "Mario",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Resident Evil",
    source: "Resident Evil Series",
    character: "Leon S. Kennedy",
    type: "game",
    gender: "male",
  },
  {
    franchise: "The Witcher",
    source: "The Witcher",
    character: "Geralt of Rivia",
    type: "game",
    gender: "male",
  },
  {
    franchise: "God of War",
    source: "God of War",
    character: "Kratos",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Overwatch",
    source: "Overwatch",
    character: "Reaper",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Halo",
    source: "Halo Series",
    character: "Master Chief",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Metal Gear",
    source: "Metal Gear Solid",
    character: "Solid Snake",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Tekken",
    source: "Tekken Series",
    character: "Jin Kazama",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Disney",
    source: "Pocahontas",
    character: "Pocahontas",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Catwoman",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Fullmetal Alchemist",
    source: "Fullmetal Alchemist",
    character: "Winry Rockbell",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Snow White and the Seven Dwarfs",
    character: "Snow White",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Neon Genesis Evangelion",
    source: "Neon Genesis Evangelion",
    character: "Rei Ayanami",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Dragon Age",
    source: "Dragon Age",
    character: "Morrigan",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Tangled",
    character: "Rapunzel",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Aladdin",
    character: "Jasmine",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Scarlet Witch",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Genshin Impact",
    source: "Genshin Impact",
    character: "Mona",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Steven Universe",
    source: "Steven Universe",
    character: "Garnet",
    type: "animation",
    gender: "female",
  },
  {
    franchise: "Fairy Tail",
    source: "Fairy Tail",
    character: "Erza Scarlet",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Cinderella",
    character: "Cinderella",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Danganronpa",
    source: "Danganronpa",
    character: "Kyoko Kirigiri",
    type: "game",
    gender: "female",
  },
  {
    franchise: "X-Men",
    source: "X-Men Series",
    character: "Jean Grey",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Captain Marvel",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Buffy the Vampire Slayer",
    source: "Buffy the Vampire Slayer",
    character: "Buffy Summers",
    type: "tv-show",
    gender: "female",
  },
  {
    franchise: "My Hero Academia",
    source: "My Hero Academia",
    character: "Ochako Uraraka",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Cardcaptor Sakura",
    source: "Cardcaptor Sakura",
    character: "Sakura Kinomoto",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Pokemon",
    source: "Pokemon Series",
    character: "Misty",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Metroid",
    source: "Metroid Series",
    character: "Samus Aran",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Mortal Kombat",
    source: "Mortal Kombat",
    character: "Sonya Blade",
    type: "game",
    gender: "female",
  },
  {
    franchise: "RWBY",
    source: "RWBY",
    character: "Ruby Rose",
    type: "animation",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Storm",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Cowboy Bebop",
    source: "Cowboy Bebop",
    character: "Faye Valentine",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Fire Emblem",
    source: "Fire Emblem: Awakening",
    character: "Lucina",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Star Wars",
    source: "Star Wars Series",
    character: "Darth Vader",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Iron Man",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Thor",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Dragon Ball",
    source: "Dragon Ball",
    character: "Vegeta",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Pirates of the Caribbean",
    source: "Pirates of the Caribbean",
    character: "Jack Sparrow",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Wolverine",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Final Fantasy",
    source: "Final Fantasy XV",
    character: "Noctis",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Mortal Kombat",
    source: "Mortal Kombat",
    character: "Scorpion",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Berserk",
    source: "Berserk",
    character: "Guts",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Star Trek",
    source: "Star Trek",
    character: "Lieutenant Uhura",
    type: "tv-show",
    gender: "female",
  },
  {
    franchise: "Resident Evil",
    source: "Resident Evil Series",
    character: "Jill Valentine",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Harry Potter",
    source: "Harry Potter",
    character: "Hermione Granger",
    type: "book",
    gender: "female",
  },
  {
    franchise: "Disney",
    source: "Mulan",
    character: "Mulan",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Shin Megami Tensei",
    source: "Persona 5",
    character: "Ann Takamaki",
    type: "game",
    gender: "female",
  },
  {
    franchise: "K-On!",
    source: "K-On!",
    character: "Yui Hirasawa",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Frozen",
    source: "Frozen",
    character: "Elsa",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Clannad",
    source: "Clannad",
    character: "Nagisa Furukawa",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Tomb Raider",
    source: "Tomb Raider Series",
    character: "Lara Croft",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Black Widow",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Sailor Moon",
    source: "Sailor Moon",
    character: "Sailor Mercury",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Assassin's Creed",
    source: "Assassin's Creed Odyssey",
    character: "Kassandra",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "She-Hulk",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Wonder Woman",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "The Simpsons",
    source: "The Simpsons",
    character: "Lisa Simpson",
    type: "animation",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Rogue",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Ms. Marvel",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "Star Wars",
    source: "Star Wars Series",
    character: "Rey",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Kingdom Hearts",
    source: "Kingdom Hearts",
    character: "Aqua",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Life is Strange",
    source: "Life is Strange",
    character: "Max Caulfield",
    type: "game",
    gender: "female",
  },
  {
    franchise: "The Last of Us",
    source: "The Last of Us Part II",
    character: "Ellie",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "Captain America",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Star Wars",
    source: "Star Wars Series",
    character: "Han Solo",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Indiana Jones",
    source: "Indiana Jones Series",
    character: "Indiana Jones",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Marvel",
    source: "Marvel Comics",
    character: "The Hulk",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Naruto",
    source: "Naruto",
    character: "Kakashi Hatake",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Bleach",
    source: "Bleach",
    character: "Ichigo Kurosaki",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Sonic the Hedgehog",
    source: "Sonic the Hedgehog Series",
    character: "Sonic",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Assassin's Creed",
    source: "Assassin's Creed II",
    character: "Ezio Auditore da Firenze",
    type: "game",
    gender: "male",
  },
  {
    franchise: "James Bond",
    source: "James Bond Series",
    character: "James Bond",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Street Fighter",
    source: "Street Fighter Series",
    character: "Ryu",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Fist of the North Star",
    source: "Fist of the North Star",
    character: "Kenshiro",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Dragon Age",
    source: "Dragon Age Series",
    character: "Morrigan",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Resident Evil",
    source: "Resident Evil Series",
    character: "Claire Redfield",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Pirates of the Caribbean",
    source: "Pirates of the Caribbean Series",
    character: "Elizabeth Swann",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Buffy the Vampire Slayer",
    source: "Buffy the Vampire Slayer",
    character: "Buffy Summers",
    type: "tv-show",
    gender: "female",
  },
  {
    franchise: "Neon Genesis Evangelion",
    source: "Neon Genesis Evangelion",
    character: "Rei Ayanami",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Ghost in the Shell",
    source: "Ghost in the Shell",
    character: "Motoko Kusanagi",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Fairy Tail",
    source: "Fairy Tail",
    character: "Lucy Heartfilia",
    type: "anime",
    gender: "female",
  },
  {
    franchise: "Bayonetta",
    source: "Bayonetta Series",
    character: "Bayonetta",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Halo",
    source: "Halo Series",
    character: "Cortana",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Star Wars",
    source: "Star Wars Series",
    character: "Princess Leia",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Xena: Warrior Princess",
    source: "Xena: Warrior Princess",
    character: "Xena",
    type: "tv-show",
    gender: "female",
  },
  {
    franchise: "Final Fantasy",
    source: "Final Fantasy VI",
    character: "Terra Branford",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Mass Effect",
    source: "Mass Effect Series",
    character: "Liara T'Soni",
    type: "game",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Batgirl",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Supergirl",
    type: "comic",
    gender: "female",
  },
  {
    franchise: "The Matrix",
    source: "The Matrix Series",
    character: "Trinity",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "The Terminator",
    source: "The Terminator Series",
    character: "Sarah Connor",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Underworld",
    source: "Underworld Series",
    character: "Selene",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "Metal Gear Solid",
    source: "Metal Gear Solid Series",
    character: "Meryl Silverburgh",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Gears of War",
    source: "Gears of War Series",
    character: "Anya Stroud",
    type: "game",
    gender: "female",
  },
  {
    franchise: "Mad Max",
    source: "Mad Max: Fury Road",
    character: "Imperator Furiosa",
    type: "movie",
    gender: "female",
  },
  {
    franchise: "DC",
    source: "DC Comics",
    character: "Green Lantern",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "JoJo's Bizarre Adventure",
    source: "JoJo's Bizarre Adventure",
    character: "Jotaro Kujo",
    type: "anime",
    gender: "male",
  },
  {
    franchise: "Die Hard",
    source: "Die Hard Series",
    character: "John McClane",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Super Mario",
    source: "Super Mario Series",
    character: "Luigi",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Deadpool",
    source: "Marvel Comics",
    character: "Deadpool",
    type: "comic",
    gender: "male",
  },
  {
    franchise: "Rocky",
    source: "Rocky Series",
    character: "Rocky Balboa",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Mission Impossible",
    source: "Mission Impossible Series",
    character: "Ethan Hunt",
    type: "movie",
    gender: "male",
  },
  {
    franchise: "Metal Gear Solid",
    source: "Metal Gear Solid Series",
    character: "Revolver Ocelot",
    type: "game",
    gender: "male",
  },
  {
    franchise: "Jurassic Park",
    source: "Jurassic Park Series",
    character: "Dr. Ian Malcolm",
    type: "movie",
    gender: "male",
  },
];

module.exports = { seed_bots };
