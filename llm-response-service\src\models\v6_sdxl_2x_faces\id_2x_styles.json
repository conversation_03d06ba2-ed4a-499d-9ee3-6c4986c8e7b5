{"last_node_id": 123, "last_link_id": 375, "nodes": [{"id": 11, "type": "InstantIDModelLoader", "pos": {"0": 780, "1": 570}, "size": {"0": 325, "1": 58}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [197, 238], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InstantIDModelLoader"}, "widgets_values": ["ip-adapter.bin"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "InstantIDFaceAnalysis", "pos": {"0": 780, "1": 660}, "size": {"0": 325, "1": 58}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [198, 239], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis"}, "widgets_values": ["CPU"], "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "ControlNetLoader", "pos": {"0": 780, "1": 750}, "size": {"0": 325, "1": 58}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [199, 240], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["diffusion_pytorch_model.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 104, "type": "MaskComposite", "pos": {"0": 780, "1": 1020}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "destination", "type": "MASK", "link": 312}, {"name": "source", "type": "MASK", "link": 311}], "outputs": [{"name": "MASK", "type": "MASK", "links": [313], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskComposite"}, "widgets_values": [0, 0, "add"], "color": "#322", "bgcolor": "#533"}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": {"0": 780, "1": 870}, "size": {"0": 325, "1": 100}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [206], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [122, 123, 266, 359, 361, 363, 365], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [371], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["realvisxlV40_v40LightningBakedvae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 102, "type": "LoadImage", "pos": {"0": 30, "1": 780}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [309, 312], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-449405.png [input]", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "CLIPTextEncode", "pos": {"0": 30, "1": 450}, "size": {"0": 210, "1": 125}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 122}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [367], "slot_index": 0, "shape": 3}], "title": "PROMPT 1", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["close-up, portrait photo, a woman"], "color": "#232", "bgcolor": "#353"}, {"id": 78, "type": "LoadImage", "pos": {"0": 270, "1": 90}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [246], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "title": "FACE 2", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["man.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 89, "type": "CLIPTextEncode", "pos": {"0": 270, "1": 450}, "size": {"0": 210, "1": 125}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 266}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [368], "slot_index": 0, "shape": 3}], "title": "PROMPT 2", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["close-up, portrait photo, a man"], "color": "#232", "bgcolor": "#353"}, {"id": 103, "type": "LoadImage", "pos": {"0": 270, "1": 780}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [310, 311], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["clipspace/clipspace-mask-436054.4000000004.png [input]", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 108, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 510, "1": 1140}, "size": {"0": 240, "1": 60}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 321}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 320}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [322], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "color": "#322", "bgcolor": "#533"}, {"id": 105, "type": "InvertMask", "pos": {"0": 510, "1": 1230}, "size": {"0": 240, "1": 30}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 313}], "outputs": [{"name": "MASK", "type": "MASK", "links": [315], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "InvertMask"}, "color": "#322", "bgcolor": "#533"}, {"id": 8, "type": "VAEDecode", "pos": {"0": 510, "1": 1290}, "size": {"0": 240, "1": 60}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 371}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [326], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "color": "#322", "bgcolor": "#533"}, {"id": 77, "type": "ApplyInstantID", "pos": {"0": 510, "1": 90}, "size": {"0": 240, "1": 270}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 238}, {"name": "insightface", "type": "FACEANALYSIS", "link": 239}, {"name": "control_net", "type": "CONTROL_NET", "link": 240}, {"name": "image", "type": "IMAGE", "link": 246}, {"name": "model", "type": "MODEL", "link": 255}, {"name": "positive", "type": "CONDITIONING", "link": 356}, {"name": "negative", "type": "CONDITIONING", "link": 358}, {"name": "image_kps", "type": "IMAGE", "link": 373}, {"name": "mask", "type": "MASK", "link": 310}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "slot_index": 0, "shape": 3}, {"name": "POSITIVE", "type": "CONDITIONING", "links": [247], "slot_index": 1, "shape": 3}, {"name": "NEGATIVE", "type": "CONDITIONING", "links": [290], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "ApplyInstantID"}, "widgets_values": [1, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 60, "type": "ApplyInstantID", "pos": {"0": 510, "1": 420}, "size": {"0": 240, "1": 270}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 197}, {"name": "insightface", "type": "FACEANALYSIS", "link": 198}, {"name": "control_net", "type": "CONTROL_NET", "link": 199}, {"name": "image", "type": "IMAGE", "link": 214}, {"name": "model", "type": "MODEL", "link": 206}, {"name": "positive", "type": "CONDITIONING", "link": 355}, {"name": "negative", "type": "CONDITIONING", "link": 357}, {"name": "image_kps", "type": "IMAGE", "link": 306}, {"name": "mask", "type": "MASK", "link": 309}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [255, 325], "slot_index": 0, "shape": 3}, {"name": "POSITIVE", "type": "CONDITIONING", "links": [248], "slot_index": 1, "shape": 3}, {"name": "NEGATIVE", "type": "CONDITIONING", "links": [287], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "ApplyInstantID"}, "widgets_values": [1, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 107, "type": "ConditioningSetMask", "pos": {"0": 510, "1": 750}, "size": {"0": 240, "1": 102}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 354}, {"name": "mask", "type": "MASK", "link": 315}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [320], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConditioningSetMask"}, "widgets_values": [1, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 79, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 510, "1": 900}, "size": {"0": 240, "1": 60}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 247}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 248}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [321], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "color": "#322", "bgcolor": "#533"}, {"id": 80, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": {"0": 510, "1": 1020}, "size": {"0": 240, "1": 60}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 290}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 287}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [288], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}, "color": "#322", "bgcolor": "#533"}, {"id": 120, "type": "workflow/group", "pos": {"0": 1140, "1": 810}, "size": {"0": 600, "1": 270}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 359}, {"name": "Positive Prompt from <PERSON> clip", "type": "CLIP", "link": 361}, {"name": "Negative Prompt from <PERSON> clip", "type": "CLIP", "link": 363}, {"name": "Negative Prompt from Styles 2 clip", "type": "CLIP", "link": 365}, {"name": "conditioning_to", "type": "CONDITIONING", "link": 367}, {"name": "ConditioningConcat conditioning_to", "type": "CONDITIONING", "link": 368}, {"name": "ConditioningConcat 2 conditioning_to", "type": "CONDITIONING", "link": 369}, {"name": "ConditioningConcat 3 conditioning_to", "type": "CONDITIONING", "link": 370}, {"name": "text", "type": "STRING", "link": 360, "widget": {"name": "text"}}, {"name": "Positive Prompt from Styles text", "type": "STRING", "link": 362, "widget": {"name": "Positive Prompt from Styles text"}}, {"name": "Negative Prompt from Styles text", "type": "STRING", "link": 364, "widget": {"name": "Negative Prompt from Styles text"}}, {"name": "Negative Prompt from Styles 2 text", "type": "STRING", "link": 366, "widget": {"name": "Negative Prompt from Styles 2 text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [354], "shape": 3}, {"name": "ConditioningConcat CONDITIONING", "type": "CONDITIONING", "links": [355], "shape": 3}, {"name": "ConditioningConcat 5 CONDITIONING", "type": "CONDITIONING", "links": [356], "shape": 3}, {"name": "ConditioningConcat 6 CONDITIONING", "type": "CONDITIONING", "links": [357], "shape": 3}, {"name": "ConditioningConcat 7 CONDITIONING", "type": "CONDITIONING", "links": [358], "shape": 3}], "properties": {"Node name for S&R": "workflow/group"}, "widgets_values": ["rusty metal, mech, cinematic, red eyes", "rusty metal, mech, cinematic, red eyes", "ugly, watermark", "ugly, watermark"], "color": "#322", "bgcolor": "#533"}, {"id": 110, "type": "SaveImage", "pos": {"0": 1140, "1": 90}, "size": {"0": 600, "1": 660}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 326}], "outputs": [], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"], "color": "#322", "bgcolor": "#533"}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 780, "1": 90}, "size": {"0": 325, "1": 262}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 325}, {"name": "positive", "type": "CONDITIONING", "link": 322}, {"name": "negative", "type": "CONDITIONING", "link": 288}, {"name": "latent_image", "type": "LATENT", "link": 300}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [29, "increment", 10, 2, "ddpm", "exponential", 1], "color": "#322", "bgcolor": "#533"}, {"id": 100, "type": "LoadImage", "pos": {"0": 30, "1": 1140}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [306], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["bride3.jpg", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 121, "type": "LoadImage", "pos": {"0": 270, "1": 1140}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [373], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["groom2.jpg", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 13, "type": "LoadImage", "pos": {"0": 30, "1": 90}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [214], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "title": "FACE 1", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["woman.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 95, "type": "EmptyLatentImage", "pos": {"0": 780, "1": 420}, "size": {"0": 325, "1": 106}, "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [300], "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1200, 1200, 1], "color": "#322", "bgcolor": "#533"}, {"id": 40, "type": "CLIPTextEncode", "pos": {"0": 270, "1": 630}, "size": {"0": 210, "1": 100}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 123}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [369, 370], "slot_index": 0, "shape": 3}], "title": "NEGATIVE PROMPT", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 122, "type": "ShowText|pysssss", "pos": {"0": 1851.9420166015625, "1": 93.64659118652344}, "size": [315, 276], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 374, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "title": "POSITIVE", "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["underwater photography, otherworldly landscape, unique perspective, ethereal lighting, dynamic composition, captivating underwater scene, immersive, serene, expert lighting technique, breathtaking moment captured"], "underwater photography, otherworldly landscape, unique perspective, ethereal lighting, dynamic composition, captivating underwater scene, immersive, serene, expert lighting technique, breathtaking moment captured"]}, {"id": 123, "type": "ShowText|pysssss", "pos": {"0": 2196.94189453125, "1": 93.64659118652344}, "size": [315, 270], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 375, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": null, "shape": 6}], "title": "NEGATIVE", "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["bad anatomy, comics, cropped, cross-eyed, worst quality, low quality, painting, 3D render, drawing, crayon, sketch, graphite, impressionist, cartoon, anime, noisy, blurry, soft, deformed, ugly, lowres, low details, JPEG artifacts, airbrushed, semi-realistic, CGI, render, Blender, digital art, manga, amateur, mutilated, distorted"], "bad anatomy, comics, cropped, cross-eyed, worst quality, low quality, painting, 3D render, drawing, crayon, sketch, graphite, impressionist, cartoon, anime, noisy, blurry, soft, deformed, ugly, lowres, low details, JPEG artifacts, airbrushed, semi-realistic, CGI, render, Blender, digital art, manga, amateur, mutilated, distorted"]}, {"id": 113, "type": "Load Styles CSV", "pos": {"0": 30, "1": 630}, "size": {"0": 210, "1": 100}, "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "positive prompt", "type": "STRING", "links": [360, 362, 374], "slot_index": 0, "shape": 3}, {"name": "negative prompt", "type": "STRING", "links": [364, 366, 375], "slot_index": 1, "shape": 3}], "title": "CHOSE STYLE", "properties": {"Node name for S&R": "Load Styles CSV"}, "widgets_values": ["Painting | Watercolor"], "color": "#232", "bgcolor": "#353"}], "links": [[7, 3, 0, 8, 0, "LATENT"], [122, 4, 1, 39, 0, "CLIP"], [123, 4, 1, 40, 0, "CLIP"], [197, 11, 0, 60, 0, "INSTANTID"], [198, 38, 0, 60, 1, "FACEANALYSIS"], [199, 16, 0, 60, 2, "CONTROL_NET"], [206, 4, 0, 60, 4, "MODEL"], [214, 13, 0, 60, 3, "IMAGE"], [238, 11, 0, 77, 0, "INSTANTID"], [239, 38, 0, 77, 1, "FACEANALYSIS"], [240, 16, 0, 77, 2, "CONTROL_NET"], [246, 78, 0, 77, 3, "IMAGE"], [247, 77, 1, 79, 0, "CONDITIONING"], [248, 60, 1, 79, 1, "CONDITIONING"], [255, 60, 0, 77, 4, "MODEL"], [266, 4, 1, 89, 0, "CLIP"], [287, 60, 2, 80, 1, "CONDITIONING"], [288, 80, 0, 3, 2, "CONDITIONING"], [290, 77, 2, 80, 0, "CONDITIONING"], [300, 95, 0, 3, 3, "LATENT"], [306, 100, 0, 60, 7, "IMAGE"], [309, 102, 1, 60, 8, "MASK"], [310, 103, 1, 77, 8, "MASK"], [311, 103, 1, 104, 1, "MASK"], [312, 102, 1, 104, 0, "MASK"], [313, 104, 0, 105, 0, "MASK"], [315, 105, 0, 107, 1, "MASK"], [320, 107, 0, 108, 1, "CONDITIONING"], [321, 79, 0, 108, 0, "CONDITIONING"], [322, 108, 0, 3, 1, "CONDITIONING"], [325, 60, 0, 3, 0, "MODEL"], [326, 8, 0, 110, 0, "IMAGE"], [354, 120, 0, 107, 0, "CONDITIONING"], [355, 120, 1, 60, 5, "CONDITIONING"], [356, 120, 2, 77, 5, "CONDITIONING"], [357, 120, 3, 60, 6, "CONDITIONING"], [358, 120, 4, 77, 6, "CONDITIONING"], [359, 4, 1, 120, 0, "CLIP"], [360, 113, 0, 120, 8, "STRING"], [361, 4, 1, 120, 1, "CLIP"], [362, 113, 0, 120, 9, "STRING"], [363, 4, 1, 120, 2, "CLIP"], [364, 113, 1, 120, 10, "STRING"], [365, 4, 1, 120, 3, "CLIP"], [366, 113, 1, 120, 11, "STRING"], [367, 39, 0, 120, 4, "CONDITIONING"], [368, 89, 0, 120, 5, "CONDITIONING"], [369, 40, 0, 120, 6, "CONDITIONING"], [370, 40, 0, 120, 7, "CONDITIONING"], [371, 4, 2, 8, 1, "VAE"], [373, 121, 0, 77, 7, "IMAGE"], [374, 113, 0, 122, 0, "STRING"], [375, 113, 1, 123, 0, "STRING"]], "groups": [{"title": "ID 2X + STYLES", "bounding": [0, 0, 1800, 1500], "color": "#ffffff", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6588450000000242, "offset": [403.5202337838665, 80.90128874340746]}, "groupNodes": {"group": {"nodes": [{"type": "CLIPTextEncode", "pos": [-450, 675], "size": {"0": 425, "1": 54}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null, "slot_index": 0}, {"name": "text", "type": "STRING", "link": null, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0}], "title": "Positive Prompt from Styles", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["rusty metal, mech, cinematic, red eyes"], "color": "#322", "bgcolor": "#533", "index": 0}, {"type": "CLIPTextEncode", "pos": [-450, 225], "size": {"0": 425, "1": 54}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null, "slot_index": 0}, {"name": "text", "type": "STRING", "link": null, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0}], "title": "Positive Prompt from Styles", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["rusty metal, mech, cinematic, red eyes"], "color": "#322", "bgcolor": "#533", "index": 1}, {"type": "CLIPTextEncode", "pos": [-450, 775], "size": {"0": 425.08154296875, "1": 54}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null}, {"name": "text", "type": "STRING", "link": null, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0}], "title": "Negative Prompt from Styles", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["ugly, watermark"], "color": "#322", "bgcolor": "#533", "index": 2}, {"type": "CLIPTextEncode", "pos": [-450, 325], "size": {"0": 425.08154296875, "1": 54}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": null}, {"name": "text", "type": "STRING", "link": null, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0}], "title": "Negative Prompt from Styles", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["ugly, watermark"], "color": "#322", "bgcolor": "#533", "index": 3}, {"type": "ConditioningConcat", "pos": [-450, 975], "size": {"0": 425, "1": 50}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "conditioning_to", "type": "CONDITIONING", "link": null}, {"name": "conditioning_from", "type": "CONDITIONING", "link": null}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConditioningConcat"}, "color": "#322", "bgcolor": "#533", "index": 4}, {"type": "ConditioningConcat", "pos": [-450, 525], "size": {"0": 425, "1": 50}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "conditioning_to", "type": "CONDITIONING", "link": null}, {"name": "conditioning_from", "type": "CONDITIONING", "link": null}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConditioningConcat"}, "color": "#322", "bgcolor": "#533", "index": 5}, {"type": "ConditioningConcat", "pos": [-450, 875], "size": {"0": 425, "1": 50}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "conditioning_to", "type": "CONDITIONING", "link": null}, {"name": "conditioning_from", "type": "CONDITIONING", "link": null}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConditioningConcat"}, "color": "#322", "bgcolor": "#533", "index": 6}, {"type": "ConditioningConcat", "pos": [-450, 425], "size": {"0": 425, "1": 50}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "conditioning_to", "type": "CONDITIONING", "link": null}, {"name": "conditioning_from", "type": "CONDITIONING", "link": null}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConditioningConcat"}, "color": "#322", "bgcolor": "#533", "index": 7}], "links": [[null, 1, 0, 0, 4, "CLIP"], [null, 0, 0, 1, 113, "STRING"], [null, 1, 1, 0, 4, "CLIP"], [null, 0, 1, 1, 113, "STRING"], [null, 1, 2, 0, 4, "CLIP"], [null, 1, 2, 1, 113, "STRING"], [null, 1, 3, 0, 4, "CLIP"], [null, 1, 3, 1, 113, "STRING"], [null, 0, 4, 0, 39, "CONDITIONING"], [0, 0, 4, 1, 111, "CONDITIONING"], [null, 0, 5, 0, 89, "CONDITIONING"], [1, 0, 5, 1, 116, "CONDITIONING"], [null, 0, 6, 0, 40, "CONDITIONING"], [2, 0, 6, 1, 112, "CONDITIONING"], [null, 0, 7, 0, 40, "CONDITIONING"], [3, 0, 7, 1, 117, "CONDITIONING"]], "external": [[1, 0, "CONDITIONING"], [4, 0, "CONDITIONING"], [5, 0, "CONDITIONING"], [6, 0, "CONDITIONING"], [7, 0, "CONDITIONING"]]}}}, "version": 0.4}