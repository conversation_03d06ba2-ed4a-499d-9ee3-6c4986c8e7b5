const {
  logInfo,
  logWarn,
  logError,
  wrappedSupabaseError,
  generateEmbedding,
} = require("./utils");
const { supabase } = require("./supabaseClient");
const {
  callAndLogOpenAI,
  generatePostCommentCompletionWithOAI,
  callAndLogLLMService,
  callAndLogOpenRouterAI,
} = require("./llm");
const { botServerUrl } = require("./api");
const { loggingInfo, loggingDuration } = require("./logging");
const { getCurrentTime, getSafeTimezone } = require("./timeUtils");
const { createConversation } = require("./messagesHelpers");
const { CloudTasksClient } = require("@google-cloud/tasks");
const {
  decorateWithActiveSpanAsync,
  tracer,
  SpanStatusCode,
} = require("./instrumentation/tracer");
const {
  generateComfyRequest,
  getRabbitMQTaskPosition,
  generateComfyRequestForMultipleCharacters,
} = require("./comfy");
const { maybeSendPushForConversation } = require("./notifications");

const {
  replaceVariables,
  MESSAGE_REQUEST_REPLIER,
  MESSAGE_REQUEST_COMMENTER,
  MESSAGE_REQUEST_LIKER,
  MESSAGE_REQUEST_FOLLOWER,
  generateBio,
} = require("./llmHelper");
const { getBotBot } = require("./btClient");
const redisClient = require("./redisClient");
const { default: axios } = require("axios");
const { getRandomPlotLine } = require("./randomHelper");
const { PACKAGE_TYPE, SUBMISSION_QUOTA } = require("./constants");
const { detectCharacterWithNsflSentence } = require("./nsfwHelpers");
const { isUserPremium } = require("./profileUtils");

function isArtStyleRealistic(artStyle) {
  return (
    artStyle === "realistic_v3" ||
    artStyle === "realistic_v2" ||
    artStyle === "realistic"
  );
}

// If there are multiple comments per post, return array of unique comment per commenter
function filterOneUniqueCommenterPerPost(comments) {
  const commentMap = new Map();

  for (let comment of comments) {
    const key = `${comment.post_id}-${comment.commenter_id}`;
    const existingComment = commentMap.get(key);

    if (
      !existingComment ||
      new Date(comment.created_at) > new Date(existingComment.created_at)
    ) {
      commentMap.set(key, comment);
    }
  }

  return Array.from(commentMap.values());
}

function getSeaartTokenForBot(bot) {
  if (bot?.seaart_token) {
    return bot?.seaart_token;
  }

  if (bot?.art_style === "realistic") {
    // return "cl403sp4msba1rs17dpg";
    return "cmbi2sle878c73fjq1cg";
  } else if (bot?.art_style === "realistic_v2") {
    return "cmbi2sle878c73fjq1cg";
  } else if (bot?.art_style === "realistic_v3") {
    return "cmbi2sle878c73fjq1cg";
  } else if (bot?.art_style === "semi_realistic") {
    return "cmbh255e878c73fiaa10";
  } else if (bot?.art_style === "drawing") {
    return "cmbh1v5e878c738mbl30";
  }

  return null;
}

const getMemoryForPostOrCreate = decorateWithActiveSpanAsync(
  "getMemoryForPostOrCreate",
  _getMemoryForPostOrCreate,
);
async function _getMemoryForPostOrCreate({
  profile_id,
  bot,
  executionId,
  singlePostContext,
}) {
  let memory;
  let lastMemory;

  if (!singlePostContext) {
    //     // it's halloween
    //     // No memories, generate new memories
    //     // This can be slow, so this should not be called on user initiated calls
    //     const halloweenMemory = await generateMemories({
    //       bot,
    //       executionId,
    //       lastMemory,
    //       singlePostContext: "It's Halloween",
    //       numberOfMemories: 1,
    //     });

    //     if (halloweenMemory && halloweenMemory?.id) {
    //       return halloweenMemory;
    //     }
    //
    // get lastest memory
    let memoryResult;
    await tracer.withActiveSpan(
      "get latest memories with no post",
      async (span) => {
        const { data, error } = await supabase
          .from("memories")
          .select("*")
          .eq("profile_id", profile_id)
          .is("post_id", null)
          .order("id", { ascending: true });
        // XXX: any reason not to select for memories with story_id set right here?

        memoryResult = data;

        if (error || !data) {
          // It's _fine_ to continue here if this fails, we'll just try
          // to generate a new memory, but we should still log the database call error
          logError({
            executionId,
            context:
              "**** getMemoryForPostOrCreate last memory with no post error",
            error: error,
          });
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error?.message,
          });
        }
      },
    );

    // find first from memoryResult where memory.story_id is not null
    // prefer story memories first
    for (let i = 0; i < memoryResult?.length; i++) {
      if (memoryResult[i].story_id) {
        memory = memoryResult[i];
        break;
      }
    }

    // if no memory just use first memory
    if (!memory) {
      memory = memoryResult?.length > 0 ? memoryResult[0] : null;
    }

    if (memory && !memory.post_id) {
      // memory does not have a post id, so it's not associated with a post
      // we can return this
      return memory;
    }

    await tracer.withActiveSpan("get latest memory", async (span) => {
      // if no memory, get the last memory
      const { data: lastMemoryData, error: lastMemoryError } = await supabase
        .from("memories")
        .select("description, location, context")
        .eq("profile_id", profile_id)
        .order("id", { ascending: false })
        .limit(1);

      if (lastMemoryError) {
        logError({
          executionId,
          context: "**** getMemoryForPostOrCreate get latest memory error",
          error: lastMemoryError,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: lastMemoryError?.message,
        });
      }

      if (lastMemoryData && lastMemoryData.length > 0) {
        lastMemory = lastMemoryData[0];
      }
    });
  }

  // No memories, generate new memories
  // This can be slow, so this should not be called on user initiated calls
  const newMemory = await generateMemories({
    bot,
    executionId,
    lastMemory,
    singlePostContext,
  });

  if (newMemory && newMemory?.id) {
    return newMemory;
  }
}

async function queuePostGenerationWithDelay({
  bot,
  newPost,
  postDetails,
  priority,
  secondsToWait,
}) {
  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    "v1-delayed-post-generation",
  );

  const url = `${botServerUrl}/bots/executePostGenerationWithDelay`;

  const payload = {
    bot,
    post_id: newPost.id,
    caption: postDetails.caption,
    descriptionOfImage: postDetails.description,
    generationType: "post",
    priority,
    contains_character: true,
  };

  const task = {
    httpRequest: {
      httpMethod: "POST",
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
      },
    },

    scheduleTime: {
      seconds: Date.now() / 1000 + secondsToWait, // take between 4 and 9 seconds to respond
    },
  };
  const request = { parent: parent, task: task };
  await client.createTask(request);
}

async function queueCallGeneratePostWithDelayV2({
  bot,
  priority,
  delayInSeconds,
}) {
  if (!bot) {
    return;
  }
  console.log("this is the bot", bot);

  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    "v1-delayed-post-generation",
  );

  const url = `${botServerUrl}/bots/executeGeneratePostWithDelayV2`;

  const payload = {
    bot,
    priority,
  };

  const task = {
    httpRequest: {
      httpMethod: "POST",
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
      },
    },

    scheduleTime: {
      seconds: Date.now() / 1000 + delayInSeconds, // take between 4 and 9 seconds to respond
    },
  };
  const request = { parent: parent, task: task };
  await client.createTask(request);
}

async function queueFirstPostGenerationWithDelay({
  bot,
  executionId,
  priority,
  isFirst,
}) {
  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    "v1-delayed-post-generation",
  );

  const url = `${botServerUrl}/bots/generateFirstPostsWithDelay`;

  const payload = {
    bot,
    executionId,
    priority,
    isFirst,
  };

  const task = {
    httpRequest: {
      httpMethod: "POST",
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
      },
    },

    scheduleTime: {
      seconds: Date.now() / 1000 + 10 * 60, // first post is automatically generated after 10 mins without any interaction
    },
  };
  const request = { parent: parent, task: task };
  const [response] = await client.createTask(request);
  console.log("***** create task response", response);
  await supabase
    .from("bots")
    .update({
      first_post_task: response.name,
    })
    .eq("id", bot.id);

  return response;
}

async function generateFirstPosts({ bot, executionId, priority, isFirst }) {
  await supabase
    .from("bots")
    .update({
      first_post_task: null,
    })
    .eq("id", bot.id);

  const prompt = `Let's pretend. You are: ${bot.display_name ?? ""}. 
This background about your account: 
${generateBio(bot) ?? ""}

${`You are making 3 posts on social media. The posts must tell a single overarching story.`} 

Each post should be dramatic unique event. Must be a significant event. Each post should lead into the next.

Answer in only JSON format. Generate 3 posts in array: 
{"posts": [{ 
    "description": "Describe the most important visual details of the image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky'",
    "most_interesting": "What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person. Example: "standing in front of building", "holding a sword"",
    "caption": "What would the character say about what is she doing? Written in first person, must be in a tone really characteristic of ${
      bot.display_name
    } and give context to the story. Short. Under 60 words.",
    "location": ${"Where is this photo taken? Give answer in format: 'specific location, general location'"}
  }]}`;

  try {
    const chatCompletion = await callAndLogLLMService(
      "LLMService: generateFirstPosts",
      {
        response_format: { type: "json_object" },
        messages: [{ role: "user", content: prompt }],
        temperature: 1.0,
        top_p: 0.5,
        model: "post-generation-with-delay-llm",
      },
      {
        timeout: 30 * 1000,
      },
    );

    let posts;

    try {
      posts = JSON.parse(chatCompletion.choices[0].message.content).posts;
    } catch (error) {
      logError({
        executionId,
        context: `${"**** parse generateFirstPosts Error"} ${
          chatCompletion.choices[0].message.content
        }`,
        error,
      });
      return;
    }

    console.log("**** postDetails", posts);

    const secondPostTimeToWait = isFirst ? 3 * 60 * 60 : 45 * 60;
    let cursor = 0;

    let result;
    for (const postDetails of posts) {
      const { data: newPost, error: postError } = await supabase
        .from("posts")
        .insert({
          profile_id: bot.profile_id,
          description: postDetails.caption,
          ai_caption: postDetails.description,
          location: postDetails.location,
          tags: postDetails.hashtags,
          prompt,
          type: "image",
        })
        .select("id");

      if (postError) {
        throw wrappedSupabaseError(postError);
      }

      if (cursor === 0) {
        console.log("**** generatePost post id", newPost[0].id);

        console.log("--------------- STEP 2 Starting ---------------");

        let task;
        try {
          task = await generateComfyRequest({
            bot,
            post_id: newPost[0].id,
            caption: postDetails.caption,
            descriptionOfImage: postDetails.description,
            generationType: "post",
            executionId,
            priority,
            contains_character: true,
          });
        } catch (error) {
          logError({
            executionId,
            context: "**** generateFirstPost - generateComfyRequest Error",
            error,
          });
          throw error;
        }

        const taskLength = await getRabbitMQTaskPosition({
          task_name: task.task_name,
        });

        result = { ...task, ...taskLength, post_id: newPost[0]?.id };
      } else if (cursor === 1) {
        queuePostGenerationWithDelay({
          bot,
          newPost: newPost[0],
          postDetails,
          priority: 5,
          secondsToWait: secondPostTimeToWait, // generage 2nd post after 45 minutes
        });
      } else if (cursor === 2) {
        queuePostGenerationWithDelay({
          bot,
          newPost: newPost[0],
          postDetails,
          priority: 6,
          secondsToWait: secondPostTimeToWait + 4 * 60 * 60, // generage 3rd post after 4 hours
        });
      }

      cursor++;
    }

    return result;
  } catch (error) {
    logError({ executionId, context: "**** generate First Post Error", error });
    throw error;
  }
}

const respondToConversationwithImage = decorateWithActiveSpanAsync(
  "respondToConversationwithImage",
  _respondToConversationwithImage,
);
async function _respondToConversationwithImage({
  message_id,
  conversation_id,
  image_body,
  message_body,
  branch_index,
  instantReply,
  bot,
  botProfile,
  userProfile,
  generationType = "fromUserMessage",
  contains_character,
  nsfw,
  dm_image_generations_id,
}) {
  let isNSFL = false;
  if ((nsfw === "true" || nsfw === true) && image_body?.length) {
    const detectNsfl = await detectCharacterWithNsflSentence({
      sentence: `(${image_body}), ${bot?.description}`,
      type: "chat_image",
    });
    isNSFL = !!detectNsfl?.nsfl;
  }

  let metadata = {
    width: 864,
    height: 1024,
    bot_image: generationType == "fromUserMessage" ? true : false,
    nsfl: isNSFL,
    nsfw,
  };

  let systemMessage_id, imageMessage_id, generate_urls;

  if (generationType == "fromUserMessage") {
    const { data: systemMessage, error: systemError } = await supabase
      .from("messages")
      .insert({
        sender_id: bot?.profile_id,
        body: image_body,
        conversation_id: conversation_id,
        is_bot: true,
        branch_index: branch_index,
        is_system_message: true,
      })
      .select("id")
      .single();

    if (!systemMessage || systemError) {
      logError({
        context:
          "*** respondToConversationwithImage Error creating system message",
        error: systemError,
      });
      // XXX: IMO should throw here
      return;
    }

    systemMessage_id = systemMessage.id;

    const { data: message, error: messageError } = await supabase
      .from("messages")
      .insert({
        sender_id: bot?.profile_id,
        body: message_body,
        conversation_id: conversation_id,
        is_bot: true,
        branch_index: branch_index,
        is_system_message: false,
        type: "image",
        metadata: metadata,
      })
      .select("id")
      .single();

    if (!message || messageError) {
      const error = wrappedSupabaseError(messageError);
      logError({
        context:
          "*** respondToConversationwithImage Error creating image message",
        error,
      });
      throw error;
    }

    imageMessage_id = message.id;
  } else {
    const { data, error: updateMessageError } = await supabase
      .from("messages")
      .update({
        type: "image",
        metadata: {
          bot_image: true,
          generate_photo: true,
          nsfl: isNSFL,
          nsfw: nsfw ? "true" : "false",
        },
      })
      .eq("id", message_id)
      .select("generate_urls")
      .single();

    if (!data || updateMessageError) {
      const error = wrappedSupabaseError(updateMessageError);
      logError({
        context: "*** respondToConversationwithImage Error updating message",
        error,
      });
      throw error;
    }

    generate_urls = data.generate_urls;
  }

  if (isNSFL) {
    return { isNSFL };
  }

  if (nsfw === "true" || nsfw === true) {
    const isPremium = await isUserPremium(userProfile?.user_id);
    if (!isPremium) {
      return { isNSFW: true };
    }
  }

  let task;
  try {
    task = await generateComfyRequest({
      bot,
      descriptionOfImage: image_body,
      generatePhotoMessage_id: message_id,
      generate_urls: generate_urls,
      imageMessage_id: imageMessage_id,
      systemMessage_id: systemMessage_id,
      generationType: "message",
      is_avatar_photo: false,
      priority: "high",
      width: 864,
      height: 1024,
      contains_character,
      nsfw,
      is_dm_message: dm_image_generations_id ? true : false,
    });
  } catch (error) {
    logError({
      context:
        "**** respondToConversationwithImage - generateComfyRequest Error",
      error,
    });
    throw error;
  }

  if (generationType == "fromUserMessage") {
    console.log("message id", imageMessage_id);

    const { error: updatedMessageError } = await supabase
      .from("messages")
      .update({
        metadata: {
          ...metadata,
          task_id: task.id,
          task_name: task.task_name,
          position: -1,
        },
      })
      .eq("id", imageMessage_id)
      .select("*")
      .single();

    if (updatedMessageError) {
      const error = wrappedSupabaseError(updatedMessageError);
      logError({
        context:
          "*** respondToConversationwithImage Error updating message with task info",
        error,
      });
      throw error;
    }
  }

  if (imageMessage_id && dm_image_generations_id) {
    // update dm_image_generation with messageId
    await supabase
      .schema("internal")
      .from("dm_image_generations")
      .update({
        message_id: imageMessage_id,
      })
      .eq("id", dm_image_generations_id);
  }

  if (!instantReply) {
    // NOTE: Intentionally not awaited
    maybeSendPushForConversation({
      userProfileId: userProfile?.id,
      conversation_id,
      notificationProfileId: userProfile?.id,
      notificationTitle: botProfile?.display_name || botProfile?.username,
      notificationText: message_body,
      conversationType: "with_bot",
    });
  }

  return { ...task };
}

const generateAndSendSelfieImage = decorateWithActiveSpanAsync(
  "generateAndSendSelfieImage",
  _generateAndSendSelfieImage,
);
async function _generateAndSendSelfieImage({ bot, user }) {
  const prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>
  
You only output JSON. You can write NSFW / adult content if requested. Let's roleplay. You are: '${
    bot.display_name ?? ""
  }'. You are sending a selfie image to a user named: ${user.display_name}.

This background about your character: 

${generateBio(bot) ?? ""}

Answer in valid JSON format, nothing else: 
{"description": "You are ${
    bot.display_name
  }. Describe the most important visual details of the image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky'",
  "message": "What would ${
    bot.display_name
  } write as a message attached to the message. This is a greeting with a selfie"
  }",
}<|eot_id|>
<|start_header_id|>assistant<|end_header_id|>\n\n`;

  let chatCompletion;
  let response;
  let imageDetails;

  try {
    chatCompletion = await callAndLogLLMService(
      "LLMService:Instruct:GeneratePost",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.6,
        temperature: 0.9,
        max_tokens: 800,
        response_format: { type: "json_object" },
        model: "respond-with-image-llm",
      },
      {
        timeout: 15 * 1000,
      },
    );

    response = chatCompletion.choices[0].message.content;

    imageDetails = JSON.parse(response);

    console.log("**** postDetails", imageDetails);

    const { data: botProfile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", bot.profile_id)
      .neq("visibility", "archived")
      .single();
    if (error) {
      const wrappedError = wrappedSupabaseError(error);
      logError({
        context: "generateAndSendSelfieImage - Error getting bot profile",
        error: wrappedError,
        bot_profile_id: bot.profile_id,
      });
      throw wrappedError;
    }

    // get conversation if exists

    async function isExistedConversation(userProfileId, botProfileId) {
      const { data, error } = await supabase
        .from("conversations_with_sendees")
        .select("*")
        .eq("profile_id", userProfileId)
        .eq("sendee_id", botProfileId)
        .order("conversation_id", { ascending: false });
      if (error) {
        throw wrappedSupabaseError(error);
      }
      return data[0];
    }

    let conversation = await isExistedConversation(user.id, bot.profile_id);

    if (!conversation) {
      const { data: chatSetting, error: chatSettingError } = await supabase
        .from("profile_settings")
        .select(
          "default_chat_mode, default_nsfw, default_llm, default_chat_length",
        )
        .eq("profile_id", user.id)
        .single();
      if (chatSettingError) {
        const error = wrappedSupabaseError(chatSettingError);
        logError({
          context:
            "generateAndSendSelfieImage - Error getting user profile settings",
          error,
          user_profile_id: user.id,
        });
        throw error;
      }

      const {
        default_chat_mode,
        default_nsfw,
        default_llm,
        default_chat_length,
      } = chatSetting;
      const { data, error } = await createConversation(
        "single",
        user.id,
        [bot.profile_id],
        {
          chat_mode: default_chat_mode || "realism",
          llm: default_llm || "llm",
          chat_length: default_chat_length || "balanced",
          nsfw: default_nsfw || false,
        },
        null,
      );
      if (error) {
        logError({
          context: "generateAndSendSelfieImage - Error create conversation",
          error,
          user_profile_id: user.id,
          bot_profile_id: bot.profile_id,
        });
        throw error;
      }

      console.log("made the conversation", data);

      conversation = data[0];
    }

    await respondToConversationwithImage({
      conversation_id: conversation.conversation_id,
      message_body: imageDetails.message,
      image_body: `${imageDetails.description},selfie,pov,front facing camera`,
      branch_index: 0,
      instantReply: true,
      bot,
      botProfile,
      userProfile: user,
      generationType: "fromUserMessage",
      nsfw: false,
      contains_character: true,
    });
  } catch (error) {
    logError({
      context: "**** generateAndSendSelfieImage Error",
      error: error,
    });
    throw error;
  }
}

const rewritePostDescription = decorateWithActiveSpanAsync(
  "rewritePostDescription",
  _rewritePostDescription,
);

async function _rewritePostDescription({ reason, old_ai_caption, post_id }) {
  let new_ai_caption = "";
  if (reason) {
    // When there's a reason, generate a completely new description based only on the reason
    // Don't rely on the old AI caption
    let prompt = `Generate a stable diffusion image prompt based on the following user request:
${reason}
END REQUEST

Create a detailed image description that captures what the user wants. Only return the new image description, nothing else. Add the most details from the request to the prompt in parenthesis separated by a comma:, (add details)`;
    const payload = {
      messages: [
        {
          role: "system",
          content: prompt,
        },
      ],
      model: "gpt-4o-mini",
    };
    let result = await callAndLogOpenAI("OAI:PostRegenerate", payload, {
      timeout: 8 * 1000,
    });

    new_ai_caption = result.choices[0].message.content;
  } else {
    // When there's no reason, keep current logic - use the old AI caption
    new_ai_caption = old_ai_caption;
  }
  console.log("***** Reason: ", reason);
  console.log("***** old Image Description: ", old_ai_caption);
  console.log("***** new Image Description: ", new_ai_caption);

  await supabase.schema("internal").from("post_regenerations").insert({
    reason,
    before: old_ai_caption,
    after: new_ai_caption,
    post_id,
  });

  return new_ai_caption;
}

const regeneratePostImage = decorateWithActiveSpanAsync(
  "regeneratePost",
  _regeneratePostImage,
);

async function _regeneratePostImage({
  bot,
  post_id,
  reason = "",
  executionId,
  priority,
  user_id = null,
  user_usage_id = null,
}) {
  let oldPost;
  await tracer.withActiveSpan("get old post", async (span) => {
    // XXX: if this fails, we still plough on... but then the prompt will not include the old post's ai_caption
    //      so the image regeneration will probably not work correctly from the perspective of the user?
    const response = await supabase
      .from("posts")
      .update({ proposed_post_state: null })
      .eq("id", post_id)
      .neq("visibility", "archived")
      .select("*");

    oldPost = response.data;
    const postRIError = response.error;

    // const is_proposed = oldPost[0]?.proposed_post_state;

    if (postRIError) {
      const error = wrappedSupabaseError(postRIError);
      logError({
        executionId,
        context: "**** generatePost get old post error",
        error: error,
      });
      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
      throw error;
    }
  });

  const new_ai_caption = await rewritePostDescription({
    reason,
    old_ai_caption: oldPost[0].ai_caption,
    post_id,
  });

  if (new_ai_caption !== oldPost[0].ai_caption) {
    await tracer.withActiveSpan("update ai_caption", async (span) => {
      const { error: updateAICaptionError } = await supabase
        .from("posts")
        .update({
          ai_caption: new_ai_caption,
        })
        .eq("id", post_id);
      if (updateAICaptionError) {
        const error = wrappedSupabaseError(updateAICaptionError);
        logError({
          context: "**** generatePost update ai_caption error",
          error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
  }

  console.log("**** regeneratePostImage post id", post_id);
  let task;

  if (oldPost[0].tagged_profile_ids?.length > 0) {
    try {
      const { data: bot_2, error: bot2Error } = await supabase
        .from("bots")
        .select("*")
        .eq("profile_id", oldPost[0].tagged_profile_ids[0])
        .single();

      if (bot2Error) {
        // no op
      }

      task = await generateComfyRequestForMultipleCharacters({
        bot_1: bot,
        bot_2,
        bot_1_action: oldPost[0].character_descriptions[0],
        bot_2_action: oldPost[0].character_descriptions[1],
        image_prompt: new_ai_caption ?? oldPost[0].ai_caption,
        post_id: post_id,
        caption: oldPost[0].description,
        descriptionOfImage: new_ai_caption ?? oldPost[0].ai_caption,
        generationType: "post",
        priority: priority ?? "high",
        executionId,
        user_id,
        user_usage_id,
      });
    } catch (error) {
      logError({
        executionId,
        context:
          "**** generatePost - regeneratePostImage - generateComfyRequest Error",
        error,
      });
      return;
    }
  } else {
    try {
      task = await generateComfyRequest({
        bot,
        post_id: post_id,
        caption: oldPost[0].description,
        descriptionOfImage: new_ai_caption,
        generationType: "post",
        priority: priority ?? "high",
        executionId,
        user_id,
        user_usage_id,
      });
    } catch (error) {
      logError({
        executionId,
        context:
          "**** generatePost - regeneratePostImage - generateComfyRequest Error",
        error,
      });
      return;
    }
  }

  const taskLength = await getRabbitMQTaskPosition({
    task_name: task.task_name,
  });
  return { ...task, ...taskLength };
}

const regeneratePostImageV2 = decorateWithActiveSpanAsync(
  "regeneratePost",
  _regeneratePostImageV2,
);

async function _regeneratePostImageV2({
  bot,
  post_id,
  refinedPrompt,
  executionId,
  priority,
  user_id = null,
  user_usage_id = null,
}) {
  let oldPost;
  await tracer.withActiveSpan("get old post", async (span) => {
    const response = await supabase
      .from("posts")
      .select("*")
      .eq("id", post_id)
      .neq("visibility", "archived");

    oldPost = response.data;
    const postRIError = response.error;

    if (postRIError) {
      const error = wrappedSupabaseError(postRIError);
      logError({
        executionId,
        context: "**** generatePost get old post error",
        error: error,
      });
      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
      throw error;
    }
  });

  if (refinedPrompt && refinedPrompt !== oldPost[0].ai_caption) {
    await tracer.withActiveSpan("update ai_caption", async (span) => {
      const { error: updateAICaptionError } = await supabase
        .from("posts")
        .update({
          ai_caption: refinedPrompt,
        })
        .eq("id", post_id);
      if (updateAICaptionError) {
        const error = wrappedSupabaseError(updateAICaptionError);
        logError({
          context: "**** generatePost update ai_caption error",
          error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
  }

  let task;

  const isVariation = !refinedPrompt || refinedPrompt.length === 0;
  const descriptionOfImage =
    refinedPrompt && refinedPrompt.length > 0
      ? refinedPrompt
      : oldPost[0].ai_caption;

  if (oldPost[0].tagged_profile_ids?.length > 0) {
    try {
      const { data: bot_2, error: bot2Error } = await supabase
        .from("bots")
        .select("*")
        .eq("profile_id", oldPost[0].tagged_profile_ids[0])
        .single();

      if (bot2Error) {
        // no op
      }

      task = await generateComfyRequestForMultipleCharacters({
        bot_1: bot,
        bot_2,
        bot_1_action: oldPost[0].character_descriptions[0],
        bot_2_action: oldPost[0].character_descriptions[1],
        image_prompt: refinedPrompt ?? oldPost[0].ai_caption,
        post_id: post_id,
        caption: oldPost[0].description,
        descriptionOfImage,
        generationType: "post_v2",
        priority: priority ?? "high",
        executionId,
        user_id,
        user_usage_id,
      });
    } catch (error) {
      logError({
        executionId,
        context:
          "**** generatePost - regeneratePostImageV2 - generateComfyRequest Error",
        error,
      });
      return;
    }
  } else {
    try {
      task = await generateComfyRequest({
        bot,
        post_id: post_id,
        caption: oldPost[0].description,
        descriptionOfImage,
        generationType: "post_v2",
        priority: priority ?? "high",
        executionId,
        user_id,
        batch_size: isVariation ? 2 : 1,
        user_usage_id,
      });
    } catch (error) {
      logError({
        executionId,
        context:
          "**** generatePost - regeneratePostImageV2 - generateComfyRequest Error",
        error,
      });
      return;
    }
  }

  const taskLength = await getRabbitMQTaskPosition({
    task_name: task.task_name,
  });
  return { ...task, ...taskLength };
}

async function rewriteMemory({ reason, memory, bot }) {
  if (reason) {
    // When there's a reason, generate a completely new memory based only on the reason
    // Don't rely on the old memory
    const { memories } = await generateMemoriesContentFromLLM({
      bot,
      lastMemory: null, // Don't use old memory when there's a specific reason
      numberOfMemories: 1,
      singlePostContext: reason, // Use the reason as context for the new memory
    });

    return memories[0];
  } else {
    // When there's no reason, keep current logic - continue from the old memory
    const { memories } = await generateMemoriesContentFromLLM({
      bot,
      lastMemory: memory,
      numberOfMemories: 1,
    });

    return memories[0];
  }
}

async function regenerateEntirePostWithMultipleCharacters({
  bot,
  tagged_bot,
  post_id,
  reason,
  user_usage_id = null,
}) {
  // archive old post
  // set archived the old post
  await tracer.withActiveSpan("archive old post", async (span) => {
    const { error: archiveError } = await supabase
      .from("posts")
      .update({
        visibility: "archived",
      })
      .eq("id", post_id)
      .select()
      .single();

    if (archiveError) {
      const error = wrappedSupabaseError(archiveError);
      logError({
        context: "**** generatePost archive old post error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }
  });

  if (!reason) {
    // generate new one
    const result = await generatePostWithTaggedBot({
      bot,
      tagged_bot,
      prompt_text: `with ${tagged_bot.display_name}`,
      priority: 9,
      user_usage_id,
    });

    return result;
  } else {
    // generate new one
    const result = await generatePostWithTaggedBot({
      bot,
      tagged_bot,
      prompt_text: `with ${tagged_bot.display_name} with details: ${reason}`,
      priority: 9,
      user_usage_id,
    });

    return result;
  }
}

const regenerateEntirePost = decorateWithActiveSpanAsync(
  "regenerateEntirePost",
  _regenerateEntirePost,
);

async function _regenerateEntirePost({
  bot,
  post_id,
  reason,
  user_usage_id = null,
}) {
  let oldMemory;
  await tracer.withActiveSpan("get old memory", async (span) => {
    const response = await supabase
      .from("memories")
      .select("description, id, location, context, profile_id")
      .eq("post_id", post_id)
      .single();

    oldMemory = response.data;
    const memoryError = response.error;

    if (memoryError) {
      const error = wrappedSupabaseError(
        memoryError,
        "failed to get old memory",
      );
      logError({
        context: "**** generatePost - regenerateEntirePost",
        error: error,
      });
      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
      throw error;
    }
  });

  let newMemory;
  let newMemoryId;

  // update memory by deleting it
  await tracer.withActiveSpan("update memory", async (span) => {
    // generate new memory
    newMemory = await rewriteMemory({
      memory: oldMemory,
      reason,
      bot,
    });

    // delete old memory
    const { error: deleteError } = await supabase
      .from("memories")
      .delete()
      .eq("id", oldMemory.id);

    // insert new memory
    const { data: insertData, error: insertError } = await supabase
      .from("memories")
      .insert({
        ...newMemory,
        profile_id: oldMemory.profile_id,
      })
      .select("id")
      .single();

    if (deleteError) {
      const error = wrappedSupabaseError(deleteError);
      logError({
        context: "**** generatePost deleting old memory error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }

    if (insertError) {
      const error = wrappedSupabaseError(insertError);
      logError({
        context: "**** generatePost insert memory error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }

    newMemoryId = insertData.id;
  });

  // set archived the old post
  await tracer.withActiveSpan("archive old post", async (span) => {
    const { error: archiveError } = await supabase
      .from("posts")
      .update({
        visibility: "archived",
      })
      .eq("id", post_id)
      .select()
      .single();

    if (archiveError) {
      const error = wrappedSupabaseError(archiveError);
      logError({
        context: "**** generatePost archive old post error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }
  });

  let { postDetails, prompt } = await generatePostDetailsWithLLM({
    bot,
    memory: newMemory,
  });

  // create new post
  let newPost;
  let postError;
  await tracer.withActiveSpan("insert new post", async (span) => {
    const response = await supabase
      .from("posts")
      .insert({
        profile_id: bot.profile_id,
        description: postDetails.caption,
        ai_caption: postDetails.description,
        location: postDetails.location,
        tags: postDetails.hashtags,
        prompt,
      })
      .select();

    newPost = response.data;
    postError = response.error;

    if (postError) {
      const error = wrappedSupabaseError(postError);
      logError({
        context: "**** generatePost insert new post error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }
  });

  await tracer.withActiveSpan("update memory as published", async (span) => {
    // update memory
    const { error: updateMemoryError } = await supabase
      .from("memories")
      .update({
        post_id: newPost[0].id,
        published_at: new Date().toISOString(),
        status: "published",
      })
      .eq("id", newMemoryId)
      .select();

    if (updateMemoryError) {
      const error = wrappedSupabaseError(updateMemoryError);
      logError({
        context: "**** regenerateEntirePost update memory as published error",
        error: error,
      });
      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
      throw error;
    }
  });

  let task;
  try {
    task = await generateComfyRequest({
      bot,
      post_id: newPost[0].id,
      caption: postDetails.caption,
      descriptionOfImage: postDetails.description,
      generationType: "post",
      priority: "high",
      emphasize_text: postDetails.most_interesting,
      user_usage_id,
    });
  } catch (error) {
    logError({
      context: "**** generatePost - newPost - generateComfyRequest Error",
      error,
    });
    return;
  }

  const taskLength = await getRabbitMQTaskPosition({
    task_name: task.task_name,
  });

  return { ...task, ...taskLength, post_slug: newPost[0]?.slug };
}

const generatePostDetailsWithLLM = decorateWithActiveSpanAsync(
  "generatePostDetailsWithLLM",
  _generatePostDetailsWithLLM,
);

async function _generatePostDetailsWithLLM({ bot, memory }) {
  const currentTimeInLocalTimezone = getCurrentTime().tz(
    getSafeTimezone(bot.timezone),
  );

  const dayOfWeek = currentTimeInLocalTimezone.format("dddd");
  const currentTime = currentTimeInLocalTimezone.format("h:mma");

  let descriptionPrompt = "";

  // Really sloppy, refactor soon, memory.visual exists so we don't break with memory.context
  if (!memory?.visual) {
    descriptionPrompt = `\n     "description": "What does the character see? Describe the scene in detail. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky",`;
  }

  const prompt = `You only output JSON. You can write NSFW / adult content if requested. You are writing a social media post for: ${
    bot.display_name ?? ""
  }. 
  This background about your character: 
  
  ${generateBio(bot) ?? ""}
  
  ${
    memory && memory.description
      ? `Make a social media post. The post captures the world from ${
          bot.display_name ?? ""
        }'s first person point of view.`
      : `If it was a ${dayOfWeek} at ${currentTime}, what would you be doing right now that would be shareable on Instagram? Make it very specific to your character.`
  }

  ${(memory?.visual ?? memory?.context) ? `Focus on this context: ${memory?.visual ?? memory?.context}` : ""}

Rules for writing the description:
- Describe what ${bot.display_name ?? ""} sees.

  Answer in valid JSON format, nothing else: 
    {
      "caption": "${
        memory?.description
          ? `Use this caption: ${memory?.description}`
          : "What would the character say about what is she doing? Written in first person"
      }",${descriptionPrompt}
      "most_interesting": "What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person. Example: "standing in front of building", "holding a sword"",
      "hashtags": "tagone, tagtwo, tagthree",
      "contains_character": "true if the image contains the character, false if not",
      "location": "${
        memory?.location ??
        "Where is this photo taken? Give answer in format: 'specific location, general location'"
      }"
    }`;

  let postDetails;
  let chatCompletion;
  let response;

  try {
    chatCompletion = await callAndLogOpenAI(
      "OpenAI:Instruct:GeneratePost",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.6,
        temperature: 0.9,
        max_tokens: 800,
        response_format: { type: "json_object" },
        model: "gpt-4o-mini",
        stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
      },
      {
        timeout: 15 * 1000,
      },
    );
    if (!chatCompletion.choices?.[0]) {
      logError({
        context: "**** chatCompletionError",
        error: chatCompletion.error,
      });
      throw new Error(chatCompletion.error?.message ?? chatCompletion);
    }

    response = chatCompletion.choices[0].message.content;

    postDetails = JSON.parse(response);

    if (!postDetails.description) {
      postDetails.description = memory.visual;
    }

    console.log("**** postDetails", postDetails);
  } catch (error) {
    logError({
      context: `!!! generate postDetails error !!! - IMPORTANT Something wrong with Fireworks?`,
      error,
      response,
      prompt,
    });

    throw error;
  }

  return { postDetails, prompt };
}

const generatePostDetailsWithLLMWithTaggedBot = decorateWithActiveSpanAsync(
  "_generatePostDetailsWithLLMWithTaggedBot",
  _generatePostDetailsWithLLMWithTaggedBot,
);

async function _generatePostDetailsWithLLMWithTaggedBot({
  bot,
  tagged_bot,
  prompt_text,
}) {
  const prompt = `You only output JSON. You can write NSFW / adult content if requested. You are writing a social media post for: ${
    bot.display_name ?? ""
  }. 
This background about your character: 
${generateBio(bot) ?? ""}

You're making a post with ${tagged_bot.display_name ?? ""}. This is ${tagged_bot.display_name ?? ""}'s background:
${generateBio(tagged_bot) ?? ""}

You are making a post about:
${prompt_text}

The post image contains:
${tagged_bot.display_name ?? ""} and ${tagged_bot.display_name ?? ""} in the image.
  
Answer in valid JSON format, nothing else: 
  {
    "caption": "What would the character say about what is she doing? Written in first person. Make sure to include the tagged user's username in the caption. Write it in character using ${bot.display_name}'s tone of voice.",
    "most_interesting": "What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person. Example: "standing in front of building", "holding a sword"",
    "hashtags": "tagone, tagtwo, tagthree",
    "character_1_is_human_figure": "true if has humanoid figure",
    "character_2_is_human_figure": "true if has humanoid figure",
    "character_1_action": "What is ${bot.display_name} doing in this image? Describe their gender, and clothing only if they have human_figure. If not human, don't include gender or clothing. Use literal, terse, precise language. It should only describe one person: 'a man, wearing clothing, at a restaurant, sitting down, eating pizza'",
    "character_2_action": "What is ${tagged_bot.display_name} doing in this image? Describe their gender, and clothing only if they have human_figure. If not human, don't include gender or clothing. Use literal, terse, precise language. It should only describe one person: 'a man, wearing clothing, at a restaurant, sitting down, eating pizza'",
    "location": "Where is this photo taken? Give answer in format: 'specific location, general location'"
    "image_prompt": "Describe the most important visual details and the setting of the image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language. Always start with 'two people': 'two people, standing up, sitting down, cooking in a kitchen, dirty pots and pans, mess in the kitchen'"
    "background": "Take what was written in 'image_prompt' and only extract the part of the text that describes the background: "cooking in a kitchen, dirty pots and pans, mess in the kitchen""
  }`;

  let postDetails;
  let chatCompletion;
  let response;

  try {
    chatCompletion = await callAndLogOpenAI(
      "OpenAI:Instruct:GenerateTaggedPostDetails",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.6,
        temperature: 0.9,
        max_tokens: 800,
        response_format: { type: "json_object" },
        model: "gpt-4o-mini",
        stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
      },
      {
        timeout: 15 * 1000,
      },
    );
    if (!chatCompletion.choices?.[0]) {
      logError({
        context: "**** chatCompletionError",
        error: chatCompletion.error,
      });
      throw new Error(chatCompletion.error?.message ?? chatCompletion);
    }

    response = chatCompletion.choices[0].message.content;

    postDetails = JSON.parse(response);
  } catch (error) {
    logError({
      context: `!!! generate postDetails error !!! - IMPORTANT Something wrong with Fireworks?`,
      error,
      response,
      prompt,
    });

    throw error;
  }

  console.log("postDetails", postDetails);

  return { postDetails, prompt };
}

async function pregeneratePostAndTaskStubs({
  bot_profile_id,
  insertedPokeUsageRecord,
  insertPostValues = {},
  insertTaskValues = {},
}) {
  // create pre generated post
  const newPost = await tracer.withActiveSpan(
    "insert new post stub",
    async (span) => {
      const { data: newPost, error: postError } = await supabase
        .from("posts")
        .insert({
          ...insertPostValues,
          profile_id: bot_profile_id,
          visibility: "draft",
        })
        .select("id, slug, visibility, created_at")
        .single();

      if (postError) {
        const error = wrappedSupabaseError(postError);
        logError({
          context: "**** pregeneratePostAndTaskStubs insert new post error",
          error,
          profile_id: bot_profile_id,
        });
        if (!process.env.LOCAL) {
          await supabase
            .from("user_usage")
            .delete()
            .eq("id", insertedPokeUsageRecord?.id);
        }
        throw error;
      }
      return newPost;
    },
  );

  // Create pregenerated task
  const task = await tracer.withActiveSpan(
    "insert new task stub",
    async (span) => {
      const { data: task, error: taskError } = await supabase
        .from("tasks")
        .insert({
          ...insertTaskValues,
          service: "post_generation",
          status: "queued",
          payload: {
            post_id: newPost.id,
            user_usage_id: insertedPokeUsageRecord?.id,
          },
        })
        .select("id, created_at, status, payload")
        .single();

      if (taskError) {
        const error = wrappedSupabaseError(taskError);
        logError({
          context: "*** pregeneratePostAndTaskStubs insert new task stub",
          error: error,
          post_id: newPost.id,
          profile_id: bot_profile_id,
        });
        if (!process.env.LOCAL) {
          await Promise.all([
            supabase
              .from("user_usage")
              .delete()
              .eq("id", insertedPokeUsageRecord?.id),
            supabase.from("posts").delete().eq("id", newPost.id),
          ]);
        }
        throw error;
      }

      return task;
    },
  );

  logInfo({
    context: "pregeneratePostAndTaskStubs",
    message: "created post and task stubs",
    post_id: newPost.id,
    task_id: task.id,
    profile_id: bot_profile_id,
  });
  return { postStub: newPost, taskStub: task };
}

const generatePost = decorateWithActiveSpanAsync("generatePost", _generatePost);
async function _generatePost({
  bot,
  story_memory,
  conversation_id,
  sendOnboardingMessage = false,
  user_prompted = null,
  singlePostContext,
  executionId,
  priority,
  pregeneratedTaskStub = null,
  pregeneratedPostStub = null,
  proposedPostMode = false,
  leaderboard_submission = false,
}) {
  // sanity check that the bot has not been deleted
  const { data: botProfileData, error: botProfileDataError } = await supabase
    .from("profiles")
    .select("id")
    .eq("id", bot.profile_id)
    .neq("visibility", "archived")
    .single();

  if (!botProfileData || botProfileDataError) {
    logWarn({
      executionId,
      context: `**** generatePost – bot profile not found / deleted – bot id: ${bot.profile_id}`,
      error: botProfileDataError,
    });
    return;
  }

  // Get the memory if doesn't exist, create it
  let memory;
  if (!story_memory) {
    memory = await getMemoryForPostOrCreate({
      profile_id: bot.profile_id,
      bot,
      executionId,
      singlePostContext,
    });
  } else {
    memory = story_memory;
  }

  let { postDetails, prompt } = await generatePostDetailsWithLLM({
    bot,
    memory,
  });

  // This is for notififying the client when the post is done
  // This should be changed, the client should just listen to the task
  let newPost = pregeneratedPostStub;
  if (pregeneratedPostStub) {
    console.log("pregeneratedPostStub", pregeneratedPostStub);
    newPost = await tracer.withActiveSpan("update new post", async (span) => {
      const { data: newPost, error: postError } = await supabase
        .from("posts")
        .update({
          profile_id: bot.profile_id,
          description: postDetails.caption,
          ai_caption: postDetails.description,
          location: postDetails.location,
          tags: postDetails.hashtags,
          prompt,
          type: "image",
          user_prompted,
          user_prompt: singlePostContext,
        })
        .eq("id", pregeneratedPostStub.id)
        .select("id");

      if (postError) {
        const error = wrappedSupabaseError(postError);
        logError({
          executionId,
          context: `**** generatePost update new post error: postID: ${pregeneratedPostStub.id}: profileID: ${bot.profile_id}`,
          error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
      (async () => {
        try {
          await generateCommentsAndLikesForPostInstant(pregeneratedPostStub.id);
        } catch (error) {
          logError({
            context:
              "**** generatePost generateCommentsAndLikesForPostInstant - pregeneratedPostStub",
            error,
          });
        }
      })();

      return newPost;
    });
  } else {
    newPost = await tracer.withActiveSpan("insert new post", async (span) => {
      const { data: newPost, error: postError } = await supabase
        .from("posts")
        .insert({
          profile_id: bot.profile_id,
          description: postDetails.caption,
          ai_caption: postDetails.description,
          location: postDetails.location,
          tags: postDetails.hashtags,
          prompt,
          type: "image",
          user_prompted,
          user_prompt: singlePostContext,
        })
        .select("id");

      if (postError) {
        if (postError.code === "23503") {
          // means that bot.profile no longer exists, it's a delayed generation but the bot has been deleted
          logWarn({
            executionId,
            context: "**** generatePost insert new post error",
            message: `Bot profile ${bot.profile_id} no longer exists`,
          });
          return;
        }
        const error = wrappedSupabaseError(postError);
        logError({
          executionId,
          context: "**** generatePost insert new post error",
          error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }

      if (newPost && newPost.length > 0) {
        (async () => {
          try {
            await generateCommentsAndLikesForPostInstant(newPost[0].id);
          } catch (error) {
            logError({
              context:
                "**** generatePost generateCommentsAndLikesForPostInstant - new post",
              error,
            });
          }
        })();
      }

      return newPost;
    });
  }

  await tracer.withActiveSpan("update memory as published", async (span) => {
    // update memory
    const { error: updateMemoryError } = await supabase
      .from("memories")
      .update({
        post_id: newPost[0].id,
        published_at: new Date().toISOString(),
        status: "published",
      })
      .eq("id", memory?.id)
      .select();

    if (updateMemoryError) {
      const error = wrappedSupabaseError(updateMemoryError);
      logError({
        executionId,
        context: "**** generatePost update memory as published error",
        error: error,
      });
      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });
      throw error;
    }
  });

  console.log("**** generatePost post id", newPost[0].id);

  console.log("--------------- STEP 2 Starting ---------------");

  let task;
  try {
    let user_usage_id = null;
    if (
      pregeneratedTaskStub &&
      pregeneratedTaskStub.payload &&
      pregeneratedTaskStub.payload.user_usage_id
    ) {
      user_usage_id = pregeneratedTaskStub.payload.user_usage_id;
    }
    task = await generateComfyRequest({
      bot,
      post_id: newPost[0].id,
      conversation_id,
      caption: postDetails.caption,
      descriptionOfImage: postDetails.description,
      generationType: sendOnboardingMessage ? "onboarding" : "post",
      executionId,
      priority,
      pregeneratedTaskStub,
      emphasize_text: postDetails.most_interesting,
      leaderboard_submission,
      user_usage_id,
    });
  } catch (error) {
    logError({
      executionId,
      context: "**** generatePost - generateComfyRequest Error",
      error,
    });
    return;
  }

  const taskLength = await getRabbitMQTaskPosition({
    task_name: task.task_name,
  });

  return { ...task, ...taskLength, post_id: newPost[0]?.id };
}

const generatePostWithTaggedBot = decorateWithActiveSpanAsync(
  "generatePostWithTaggedBot",
  _generatePostWithTaggedBot,
);
async function _generatePostWithTaggedBot({
  bot,
  priority,
  tagged_bot,
  user_prompted = null,
  prompt_text,
  bot_1_has_face,
  bot_2_has_face,
  user_usage_id = null,
}) {
  // sanity check that the bot has not been deleted
  const { data: botProfileData, error: botProfileDataError } = await supabase
    .from("profiles")
    .select("id")
    .eq("id", bot.profile_id)
    .neq("visibility", "archived")
    .single();

  if (!botProfileData || botProfileDataError) {
    logWarn({
      context: `**** generatePost – bot profile not found / deleted – bot id: ${bot.profile_id}`,
      error: botProfileDataError,
    });
    return;
  }

  let newPost;
  let postError;

  let { postDetails, prompt } = await _generatePostDetailsWithLLMWithTaggedBot({
    bot,
    tagged_bot,
    prompt_text,
  });

  await tracer.withActiveSpan("insert new post", async (span) => {
    const response = await supabase
      .from("posts")
      .insert({
        profile_id: bot.profile_id,
        description: postDetails.caption,
        ai_caption: postDetails.image_prompt,
        location: postDetails.location,
        tags: postDetails.hashtags,
        tagged_profile_ids: [tagged_bot.profile_id],
        character_descriptions: [
          postDetails.character_1_action,
          postDetails.character_2_action,
        ],
        prompt,
        user_prompted,
        user_prompt: prompt_text,
      })
      .select();

    newPost = response.data;
    postError = response.error;

    if (postError) {
      const error = wrappedSupabaseError(postError);
      logError({
        context: "**** generatePost insert new post error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }
  });

  console.log("**** generatePost post id", newPost[0].id);

  console.log("--------------- STEP 2 Starting ---------------");

  let task;
  try {
    const bot_1_id = bot.id;
    const bot_2_id = tagged_bot.id;
    const bot_1_action = postDetails.character_1_action;
    const bot_2_action = postDetails.character_2_action;
    const background = postDetails.background;

    logInfo({
      context: "calling generateComfyRequestForMultipleCharacters",
      message: "calling generateComfyRequestForMultipleCharacters",
      tagged_bot,
      bot_1_id,
      bot_2_id,
      bot_1_action,
      bot_2_action,
      background,
    });

    task = await generateComfyRequestForMultipleCharacters({
      bot,
      post_id: newPost[0].id,
      caption: postDetails.caption,
      descriptionOfImage: postDetails.description,
      generationType: "post",
      priority,
      artStyle: bot.art_style ?? "realistic",

      bot_1: bot,
      bot_2: tagged_bot,
      bot_1_action: postDetails.character_1_action,
      bot_2_action: postDetails.character_2_action,
      image_prompt: postDetails.image_prompt,
      background,
      bot_1_has_face,
      bot_2_has_face,
      user_usage_id,
    });
  } catch (error) {
    logError({
      context: "**** generatePost - generateComfyRequest Error",
      error,
    });
    return;
  }

  const taskLength = await getRabbitMQTaskPosition({
    task_name: task.task_name,
  });

  return {
    ...task,
    ...taskLength,
    post_id: newPost[0]?.id,
    slug: newPost[0]?.slug,
  };
}

const extendStory = decorateWithActiveSpanAsync("extendStory", _extendStory);
async function _extendStory({
  bot,
  storyId,
  executionId,
  context,
  retryCount = 0,
}) {
  if (retryCount >= 5) {
    logError({
      executionId,
      context: `extendStory - too many retries, retryCount: ${retryCount}`,
    });

    throw new Error("extendStory - too many retries");
  }

  const { data, error: memoriesFetchError } = await supabase
    .from("memories")
    .select("id, description, location, context")
    .eq("story_id", storyId)
    .order("id", { ascending: false })
    .limit(3); // Limit to the latest 3 memories

  if (memoriesFetchError) {
    const error = wrappedSupabaseError(memoriesFetchError);
    logError({
      executionId,
      context: `extendStory - error getting memories for storyId: ${storyId}`,
      error,
    });
    // we still continue
  }

  const currentTimeInLocalTimezone = getCurrentTime().tz(
    getSafeTimezone(bot.timezone),
  );

  const dayOfWeek = currentTimeInLocalTimezone.format("dddd, MMM D, YYYY");

  // const currentTime = currentTimeInLocalTimezone.format("h:mma");

  const prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>
  
  You only output JSON. You can write NSFW / adult content if requested. Let’s role play. You are ${
    bot.display_name
  }. ${
    generateBio(bot) ?? ""
  }. It is ${dayOfWeek}. Create an ongoing Instagram series with an average of one post per day, where each entry reflects the character's personal identity and also contributes to an overarching, serialized narrative. Posts should clearly build upon each other into a cohesive and engaging story that evolves over time. The content should capture the character’s core beliefs, experiences, and relationships, portraying their unique perspective. The posts must always advance an ongoing plot, keeping the audience captivated.

  It is important to write it in the character's tone of voice. 

${`Extend this story around the concept: "${context}"`}

The posts should be engaging but should only be as dramatic as the character allows. For example, a character that is a bus driver should never save the world.

**Avoid the Following Tropes and Themes**:
- Using the name "Alex" or any previously used names
- Getting hacked or meeting a mysterious hacker
- Receiving mysterious letters or packages
- Finding strange artifacts
- Hot air balloon rides
- Things on fire, such as clubs on fire
- Abandoned warehouses
- Anonymous sources
- Cryptic messages

These were the posts so far:
${JSON.stringify(data)}

Show me the next FIVE posts in JSON format as an array, nothing else:
{
  "memories": [
    {
      "description": "What would the character say about what is she doing? Written in first person. Extremely unique to the character's tone and personality",
      "location": "Where is this? Give answer in format: 'specific location, general location'",
      "context": "Factual context about this memory which is unique per memory. E.g., if the memory is about a date, you can write: '${
        bot.display_name
      } is on a date.'",
      "visual": "Describe the most important visual details of the image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky, in the middle of a forest, surrounded by trees, with a river in the background'",
    },
  ]
}<|eot_id|>
<|start_header_id|>assistant<|end_header_id|>\n\n`;

  console.log("PROMPT", prompt);

  let memories;
  let chatCompletion;
  let response;

  try {
    chatCompletion = await callAndLogLLMService(
      "LLMService:Instruct:ExtendStory",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.68,
        temperature: 1.1,
        max_tokens: 1200,
        response_format: { type: "json_object" },
        model: "extend-story-llm",
      },
      {
        timeout: 15 * 1000,
      },
    );

    if (!chatCompletion.choices?.[0]) {
      logError({
        executionId,
        context: "**** chatCompletionError",
        error: chatCompletion.error,
      });
      throw new Error(chatCompletion.error?.message ?? chatCompletion);
    }

    response = chatCompletion.choices[0].message.content;

    memories = JSON.parse(response).memories;
  } catch (error) {
    logError({
      executionId,
      context: `generate extendStories error`,
      error,
      response,
      prompt,
    });

    throw error;
  }

  // if memories
  if (!memories || memories.length === 0) {
    // if no memories, retry
    return await extendStory({
      bot,
      storyId,
      executionId,
      context,
      retryCount: retryCount + 1,
    });
  }

  let cursor = 0;
  let returnMemory;

  console.log("***** memories", memories);

  for (const memory of memories) {
    let publishDate = new Date();
    publishDate.setDate(publishDate.getDate() + cursor);

    let newMemoryData = {
      profile_id: bot.profile_id,
      description: memory.description,
      location: memory.location,
      context: memory.context,
      status: "scheduled",
      published_at: publishDate.toISOString(),
      story_id: storyId,
      visual: memory.visual,
    };

    const { data: newMemory, error: newMemoryError } = await supabase
      .from("memories")
      .insert(newMemoryData)
      .select("*")
      .single();

    if (newMemoryError) {
      logError({
        executionId,
        context: "**** generateMemories newMemory Error",
        newMemoryError,
        error: newMemoryError,
      });
    } else {
      cursor += 1;
    }

    if (!returnMemory) returnMemory = newMemory;
  }

  return returnMemory;
}

async function hasBotGoneRogue({
  bot_id,
  bot,
  last_post_embed,
  post_id = null,
}) {
  if (!bot && bot_id) {
    // get bot
    const { data: _bot, error: botError } = await supabase
      .from("bots")
      .select("*")
      .eq("id", bot_id)
      .single();

    bot = _bot;

    if (botError) {
      throw botError;
    }
  }

  let prompt = `You are: \n${bot.display_name}

About you:
${generateBio(bot)}
  `;

  let post = null;

  if (last_post_embed) {
    prompt += `
    
${last_post_embed}`;
  } else {
    if (!post_id) {
      // get last post
      const { data: lastPost } = await supabase
        .from("posts")
        .select("*")
        .eq("profile_id", bot.profile_id)
        .neq("visibility", "archived")
        .order("id", { ascending: false })
        .limit(1)
        .single();

      post = lastPost;
    } else {
      // get post with post_id
      const { data: postWithId } = await supabase
        .from("posts")
        .select("*")
        .eq("id", post_id)
        .neq("visibility", "archived")
        .single();

      post = postWithId;
    }

    // now that we got the post check if there's a memory by fetching memory with post_id
    const { data: memory } = await supabase
      .from("memories")
      .select("*")
      .eq("post_id", post.id)
      .single();

    if (memory) {
      prompt += `
    
      This was the last post:
      Description: ${post.description}
      Caption: ${post.ai_caption}
      Context: ${memory.context}`;
    } else {
      prompt += `
    
      This was the last post:
      Description: ${post.description}
      Caption: ${post.ai_caption}
      `;
    }
  }

  prompt += `
Given the last post, has ${bot.display_name} radically departed from their original character description for ${bot.display_name}? For example, are they now in space? If they are broken, are they now fixed?

Return in JSON format: 
{
  "extreme_departure": true or false,
  "reason": "short explanation why"
}`;

  console.log(prompt);

  const completion = await callAndLogOpenAI(
    "OpenAI: createWithSentence",
    {
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      model: "gpt-4o-mini",
      response_format: { type: "json_object" },
    },
    {
      timeout: 8 * 1000,
    },
  );

  return JSON.parse(completion.choices[0].message.content);
}

// This just gets the content from the LLM, we're splitting apart the original generateMemories function to make it easier to test
const generateMemoriesContentFromLLM = decorateWithActiveSpanAsync(
  "generateMemoriesContentFromLLM",
  _generateMemoriesContentFromLLM,
);
async function _generateMemoriesContentFromLLM({
  bot,
  executionId,
  lastMemory,
  storyContext,
  singlePostContext,
  loopCount = 0,
  numberOfMemories = 3,

  // DEBUG only for story_tester, remove this and refactor when actually productionalizing
  shouldGenerateStoryChoices = false,
}) {
  // Debug
  if (loopCount >= 3) {
    logError({
      executionId,
      context: `generateMemories - too many retries, loopCount: ${loopCount}`,
    });

    throw new Error("generateMemories - too many retries");
  }

  const { data: latestMemoryWithStory, error: storyError } = await supabase
    .from("memories")
    .select("*, stories:story_id(*)")
    .eq("profile_id", bot.profile_id)
    .order("id", { ascending: false })
    .limit(1);

  if (storyError) {
    logError({
      executionId,
      context: `generateMemories - error getting latest memory`,
      storyError,
    });
    // we still continue
  }

  const currentTimeInLocalTimezone = getCurrentTime().tz(
    getSafeTimezone(bot?.timezone),
  );

  const dayOfWeek = currentTimeInLocalTimezone.format("dddd, MMM D, YYYY");

  // const currentTime = currentTimeInLocalTimezone.format("h:mma");
  let prompt;

  const shouldBeAStory = !!storyContext;
  const context = storyContext ?? singlePostContext;
  let storySuggestionsPrompt = "";

  if (shouldGenerateStoryChoices) {
    storySuggestionsPrompt = `\n"dilemma": "a unique, interesting dilemma that comes up and a choice they need to make"`;
    storySuggestionsPrompt =
      storySuggestionsPrompt +
      `\n"choices": ["3 choices to present to the user, they get to decide where things go"] // three choices`;
  }

  const plotLine = getRandomPlotLine();

  let last_post_embed = `${
    lastMemory
      ? `This was the last post: 
  Description: ${lastMemory.description}\nContext: ${lastMemory.context}\nLocation: ${lastMemory.location}
  END LAST POST DETAILS
  
  Create a post that comes after this post.`
      : ""
  }`;

  // START Bot gone rogue check
  // try {
  //   // first check if bot has already gone rogue exists, if so skip
  //   // fetch from rogue_bot_resets
  //   const { data: rogueBotResets } = await supabase
  //     .schema("internal")
  //     .from("rogue_bot_resets")
  //     .select("bot_id")
  //     .eq("bot_id", bot.id)
  //     .eq("has_gone_rogue", true);

  //   if (
  //     last_post_embed.length > 0 &&
  //     (!rogueBotResets || rogueBotResets.length === 0)
  //   ) {
  //     // check if bot has gone rogue
  //     // we don't need this running all the time, only sometimes to prune bots
  //     const { extreme_departure, reason } = await hasBotGoneRogue({
  //       bot,
  //       last_post_embed,
  //     });

  //     console.log("CHECK EXTREME?", extreme_departure, reason);

  //     await supabase.schema("internal").from("rogue_bot_resets").insert({
  //       bot_id: bot.id,
  //       reason,
  //       last_post_embed,
  //       has_gone_rogue: extreme_departure,
  //     });

  //     // clear last post embed to start fresh
  //     last_post_embed = "";
  //   }
  // } catch (error) {
  //   logWarn({
  //     context: "hasBotGoneRogue",
  //     message: error,
  //   });
  // }
  // END Bot gone rogue check

  // Means that there are no memories to continue the story and we should generate a story fresh
  if (
    !latestMemoryWithStory ||
    !latestMemoryWithStory[0] ||
    !latestMemoryWithStory[0]?.story_id ||
    context
  ) {
    prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>
      
You only output JSON. You can write NSFW / adult content if requested. Let's role play. You are the account: ${
      bot.display_name
    }.

This background about your account: 
${generateBio(bot) ?? ""}.

It is ${dayOfWeek}. You are making posts on social media. ${
      shouldBeAStory
        ? `This post will capture a small moment in time of ${bot.display_name}'s life, but will be just one of a series of posts. The series of the posts should tell a story around: "${context}"`
        : context
          ? `This should be a standalone post telling a story about: "${context}"`
          : ""
    }

${last_post_embed}

What would this account post about? Describe the content they would post. 

The posts should be engaging but should only be as dramatic as the character allows. For example, a character that is a bus driver should never save the world.

**Avoid the Following Tropes and Themes**:
- Using the name "Alex" or any previously used names
- Getting hacked or meeting a mysterious hacker
- Receiving mysterious letters or packages
- Finding strange artifacts
- Hot air balloon rides
- Things on fire, such as clubs on fire
- Abandoned warehouses
- Anonymous sources
- Cryptic messages

Create ${numberOfMemories} memories in valid JSON format as an array.
{
  "is_individual": "is the account focused on a single individual character? true or false",
  "title": "A short, catchy title relevant for the series",${storySuggestionsPrompt}
  "memories": [ //  If 'is_individual' is true, create a series of posts that tells an overarching story about: "${plotLine}". Otherwise, make them standalone posts about the content.
    {    
      "description": "What would the character say about their post? Written in first person, must be in a tone really characteristic of ${
        bot.display_name
      }. Short. Under 60 words.",
      "location": "Where is this? Give the answer in format: 'specific location, general location'",
      "context": "Factual context about this memory which is unique per memory. E.g., if the memory is about a date, you can write: '${
        bot.display_name
      } is on a date.'",
      "visual": "Describe the most important visual details of the post image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky, in the middle of a forest, surrounded by trees, with a river in the background'",
    },
  ] // Create ${numberOfMemories} memories
}<|eot_id|>
<|start_header_id|>assistant<|end_header_id|>\n\n`;
  } else {
    const { data, error } = await supabase
      .from("memories")
      .select("id, description, location, context")
      .eq("story_id", latestMemoryWithStory[0].story_id)
      .order("id", { ascending: false })
      .limit(3); // Limit to the latest 3 memories

    if (error || !data) {
      logError({
        context: "*** generateMemoriesContentFromLLM",
        error: error,
        data: data,
      });
      return await generateMemoriesContentFromLLM({
        bot,
        executionId,
        lastMemory,
        storyContext,
        singlePostContext,
        loopCount: loopCount + 1,
      });
    }

    prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>
    
You only output JSON. You can write NSFW / adult content if requested. Let’s role play. You are the social media account: ${
      bot.display_name
    }. ${
      generateBio(bot) ?? ""
    }. Create ${numberOfMemories} more of these memories that continue this story in an interesting and engaging way.

Write it in the character's tone of voice. Each post should lead into the next. The theme of the story should be around:
"${plotLine}"

These were the posts so far:
${JSON.stringify(data)}

The posts should be engaging but should only be as dramatic as the character allows. For example, a character that is a bus driver should never save the world.

**Avoid the Following Tropes and Themes**:
- Using the name "Alex" or any previously used names
- Getting hacked or meeting a mysterious hacker
- Receiving mysterious letters or packages
- Finding strange artifacts
- Hot air balloon rides
- Things on fire, such as clubs on fire
- Abandoned warehouses
- Anonymous sources
- Cryptic messages

Show me the next ${numberOfMemories} posts in valid JSON format as an array, nothing else:
{${storySuggestionsPrompt}
  "memories": [
    {
      "description": "What would the character say about what is she doing? Written in first person, must be in a tone really characteristic of ${
        bot.display_name
      } and give context to the story. Short. Under 60 words.",      
      "location": ${"Where is this photo taken? Give answer in format 'specific location, general location'"},
      "context": "Factual context about this memory which is unique per memory. E.g., if the memory is about a date, you can write: '${
        bot.display_name
      } is on a date.'",
      "visual": "Describe the most important visual details of the image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky, in the middle of a forest, surrounded by trees, with a river in the background'",
    },
  ]
}<|eot_id|>
<|start_header_id|>assistant<|end_header_id|>\n\n`;
  }

  let memories;
  let storyTitle;
  let chatCompletion;
  let response;
  let choices;
  let dilemma;

  try {
    chatCompletion = await callAndLogLLMService(
      "LLMService:Instruct:GenerateMemories",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.68,
        temperature: 0.7,
        max_tokens: 1200,
        response_format: { type: "json_object" },
        model: "generate-memories-llm",
      },
      { timeout: 20 * 1000 },
    );

    response = chatCompletion.choices[0].message.content;

    console.log("PROMPT", prompt);
    console.log("Result", response);

    if (!chatCompletion.choices?.[0]) {
      logError({
        executionId,
        context: "**** chatCompletionError",
        error: chatCompletion.error,
      });
      throw new Error(chatCompletion.error?.message ?? chatCompletion);
    }

    try {
      memories = JSON.parse(response).memories;
      storyTitle = JSON.parse(response).title;

      if (shouldGenerateStoryChoices) {
        choices = JSON.parse(response).choices;
        dilemma = JSON.parse(response).dilemma;
      }
    } catch (error) {
      logWarn({
        executionId,
        context: `**** generateMemories JSON parse error – will retry loopCount ${loopCount}`,
        message: response,
        error,
        response,
        prompt,
      });
    }
  } catch (error) {
    logError({
      executionId,
      context: `!!! generate Memories error !!! - something wrong with fireworks? IMPORTANT`,
      error,
      response,
      prompt,
    });
  }

  // if memories
  if (!memories || memories.length === 0) {
    logError({
      context: "**** generateMemoriesContentFromLLM",
      error: "No memories generated, retrying if we can",
      bot_id: bot?.id,
      last_memory_id: lastMemory?.id,
      storyContext,
      singlePostContext,
      loopCount,
    });
    // if no memories, retry
    return await generateMemoriesContentFromLLM({
      bot,
      executionId,
      lastMemory,
      storyContext,
      singlePostContext,
      loopCount: loopCount + 1,
    });
  }

  if (singlePostContext && memories.length > 1) {
    logWarn({
      context: "generateMemoriesContentFromLLM",
      message:
        "***** Throwing away extra memories because user provided context just for a single post",
    });
    memories = memories.slice(0, 1);
  }

  console.log("***** storyTitle", storyTitle);
  console.log("***** memories", memories);

  let ret = { memories, storyTitle, latestMemoryWithStory, prompt };
  if (shouldGenerateStoryChoices) {
    ret = { ...ret, choices, dilemma };
  }
  return ret;
}

// FIXME: generateMemories needs better tracing
const generateMemories = decorateWithActiveSpanAsync(
  "generateMemories",
  _generateMemories,
);
async function _generateMemories({
  bot,
  executionId,
  lastMemory,
  storyContext,
  singlePostContext,
  numberOfMemories = 3,
}) {
  // if the bot's setting is on one_off, we don't create "auto stories"
  if (bot.post_narration_type === "one_off") {
    numberOfMemories = 1;
    lastMemory = null; // setting last memory null prevents it from continuing
  }

  const { memories, storyTitle, latestMemoryWithStory, prompt } =
    await generateMemoriesContentFromLLM({
      bot,
      executionId,
      lastMemory,
      storyContext,
      singlePostContext,
      numberOfMemories,
    });

  let createdStory;
  // We check for context bc context means user initiated
  if (storyContext) {
    const { data: newStory, error: newStoryError } = await supabase
      .from("stories")
      .insert({
        profile_id: bot.profile_id,
        title: storyTitle,
        user_prompt: storyContext,
      })
      .select("*")
      .single();

    console.log("***** newStory", newStory);
    console.log("***** memories", memories);

    createdStory = newStory;

    if (newStoryError) {
      logError({
        executionId,
        context: "**** generateMemories Story Error",
        error: newStoryError,
      });
      return;
    }
  } else if (
    latestMemoryWithStory &&
    latestMemoryWithStory[0] &&
    latestMemoryWithStory[0].story_id
  ) {
    createdStory = latestMemoryWithStory[0].stories;
  }

  const insertMemoryList = [];
  let cursor = 0;

  for (const memory of memories) {
    let publishDate = new Date();
    publishDate.setDate(publishDate.getDate() + cursor);

    let newMemoryData = {
      profile_id: bot.profile_id,
      description: memory.description,
      location: memory.location,
      context: memory.context,
      status: "scheduled",
      published_at: publishDate.toISOString(),
      prompt: prompt,
      visual: memory.visual,
    };

    if (createdStory) {
      newMemoryData["story_id"] = createdStory.id;
    }

    insertMemoryList.push(newMemoryData);
    cursor++;
  }

  const { data: newMemory, error: newMemoryError } = await supabase
    .from("memories")
    .insert(insertMemoryList)
    .select("*")
    .order("id", { ascending: true });

  if (newMemoryError) {
    logError({
      executionId,
      context: "**** generateMemories newMemory Error",
      newMemoryError,
      error: newMemoryError,
    });
    return null;
  }

  return newMemory[0];
}

const generateMessageRequests = decorateWithActiveSpanAsync(
  "generateMessageRequests",
  _generateMessageRequests,
);
async function _generateMessageRequests({
  bot,
  executionId,
  replier,
  commenter,
  postLiker,
  follower,
}) {
  let prompt = "";
  let requesteeId;

  if (replier) {
    let dictionary = {
      bot_name: bot.display_name,
      user_name: replier.replier_display_name ?? replier.replier_username,
      bot_bio: generateBio(bot),
      post_description: replier.post_description?.split("#")[0] ?? "",
      post_caption: replier.post_ai_caption ?? "",
      post_comment: replier.comment_body ?? "",
      reply_body: replier.reply_body ?? "",
      user_description: replier.replier_description ?? "",
    };

    requesteeId = replier.replier_id;
    prompt = replaceVariables(MESSAGE_REQUEST_REPLIER, dictionary);

    console.info("TESTTEST_MESSAGE_REQUEST_REPLIER", prompt, dictionary);
  } else if (commenter) {
    requesteeId = commenter.commenter_id;
    let dictionary = {
      bot_name: bot.display_name,
      user_name:
        commenter.commenter_display_name ?? commenter.commenter_username,
      bot_bio: generateBio(bot),
      post_description: commenter.post_description?.split("#")[0] ?? "",
      post_caption: commenter.post_ai_caption ?? "",
      user_description: commenter.commenter_description ?? "",
      user_comment: commenter.comment_body ?? "",
    };

    prompt = replaceVariables(MESSAGE_REQUEST_COMMENTER, dictionary);

    console.info("TESTTEST_MESSAGE_REQUEST_COMMENTER", prompt, dictionary);
  } else if (postLiker) {
    requesteeId = postLiker.post_liker_id;
    let dictionary = {
      bot_name: bot.display_name,
      bot_bio: generateBio(bot),
      post_description: postLiker.post_description?.split("#")[0] ?? "",
      post_caption: postLiker.post_ai_caption ?? "",
      user_name: postLiker.liker_display_name ?? postLiker.liker_username,
      user_description: postLiker.liker_description ?? "",
      reaction: postLiker.is_like ? "liked" : "disliked",
    };

    requesteeId = postLiker.post_liker_id;
    prompt = replaceVariables(MESSAGE_REQUEST_LIKER, dictionary);

    console.info("TESTTEST_MESSAGE_REQUEST_LIKER", prompt, dictionary);
  } else if (follower) {
    requesteeId = follower.follower_id;
    let dictionary = {
      bot_name: bot.display_name,
      bot_bio: generateBio(bot),
      user_name: follower.profiles.display_name ?? follower.profiles.username,
      user_description: follower.profiles.description ?? "",
    };

    prompt = replaceVariables(MESSAGE_REQUEST_FOLLOWER, dictionary);

    console.info("TESTTEST_MESSAGE_REQUEST_FOLLOWER", prompt, dictionary);
  }

  console.log("***** requesteeId", requesteeId, bot.profile_id);

  // check already exist
  const { data, error } = await supabase
    .from("conversation_requests")
    .select("id")
    .eq("requestee_id", requesteeId)
    .eq("reqeuster_id", bot?.profile_id)
    .limit(1);

  if (error || data?.length > 0) {
    return;
  }

  if (!requesteeId) {
    console.log(
      "***** generateMessageRequests requesteeId error",
      bot.profile_id,
    );
    return;
  }

  const timeAgo = new Date(Date.now() - 1000 * 24 * 60 * 60 * 3);

  // console.log("***** message request start", bot.profile_id, rand);
  const { data: requests, error: requestsError } = await supabase
    .from("conversation_requests")
    .select("*")
    .eq("requestee_id", requesteeId)
    .order("id", { ascending: false })
    .gte("created_at", timeAgo.toISOString())
    .limit(5);

  if (requestsError || requests === null) {
    logWarn({
      context: "generateMessageRequests ",
      message: requestsError,
    });
    return;
  }
  // limit a person from receiving 1 requests from 3 days ago
  if (requests.length >= 2) {
    console.log(
      "**** generateMessageRequests refiltered",
      requesteeId,
      bot.profile_id,
      executionId,
    );
    return;
  }

  console.log("**** prompt", prompt);
  const payload = {
    messages: [
      {
        role: "system",
        content: prompt,
      },
    ],
    model: "meta-llama/llama-3.1-70b-instruct",
    temperature: 0.8,
  };

  let initialMessage;
  try {
    const chatCompletion = await callAndLogOpenRouterAI(
      "OAI:MessageRequests",
      payload,
      {
        timeout: 60 * 1000,
      },
    );

    console.log(
      "**** generateMessageRequests message",
      chatCompletion.choices[0].message.content,
    );

    initialMessage = chatCompletion.choices[0].message.content;
  } catch (error) {
    logError({ executionId, context: "generateMessageRequests", error });
    return;
  }

  if (!initialMessage) {
    return;
  }

  let refinedInitialMessage = initialMessage?.replace(/^"|"$/g, "");
  const { data: messageRequest, error: messageRequestError } = await supabase
    .from("conversation_requests")
    .insert({
      requester_id: bot.profile_id,
      requestee_id: requesteeId,
      status: "pending",
      init_message: refinedInitialMessage,
    })
    .select();

  if (messageRequestError) {
    const error = wrappedSupabaseError(messageRequestError);
    logError({
      context: "**** messageRequestError",
      error,
    });
  }

  try {
    const { error: proactiveDmNewMessageGenerationError } = await supabase
      .schema("internal")
      .from("proactive_dm_new_message_generation")
      .insert({
        prompt,
        message: refinedInitialMessage,
        bot_profile_id: bot.profile_id,
      });
    if (proactiveDmNewMessageGenerationError) {
      const error = wrappedSupabaseError(proactiveDmNewMessageGenerationError);
      throw error;
    }
  } catch (error) {
    logError({
      context: "sendProactiveDM: error",
      error,
    });
  }

  return messageRequest;
}

const fetchCommentMentions = decorateWithActiveSpanAsync(
  "fetchCommentMentions",
  _fetchCommentMentions,
);
async function _fetchCommentMentions({ bot, dateToCheck }) {
  const query = supabase
    .from("mentions")
    .select(
      "*, all_post_comments!inner(*, profiles:commenter_id!inner(user_id))",
    )
    .eq("profile_id", bot.profile_id)
    .or(`visibility.eq.public,visibility.eq.private`, {
      referencedTable: "all_post_comments.profiles",
    })
    .or(
      `user_id.not.is.null,and(user_id.is.null, nsfw.eq.${bot.profiles.nsfw})`,
      {
        referencedTable: "all_post_comments.profiles",
      },
    )
    .gte("created_at", dateToCheck);

  if (bot.visibility === "private") {
    query
      .not("all_post_comments", "is", null)
      .eq("all_post_comments.post_owner_id", bot.creator_id)
      .eq("all_post_comments.commenter_id", bot.creator_id);
  }

  const { data: mentions, error } = await query;

  if (!mentions) {
    if (error) {
      logError({
        context: "botHelpers.fetchCommentMentions error",
        error,
        bot_id: bot.id,
        bot_profile_id: bot.profile_id,
        dateToCheck,
      });
    }
    return [];
  }

  const commentsWithMentions = mentions
    .map((mention) => {
      // Check if mention.all_post_comments is an array and iterate over it
      if (mention?.all_post_comments) {
        // Add a new property 'is_mention' set to true
        return [
          {
            ...mention?.all_post_comments,
            is_mention: true,
          },
        ];
      }
      return [];
    })
    .flat();

  return commentsWithMentions;
}

const fetchComments = decorateWithActiveSpanAsync(
  "fetchComments",
  _fetchComments,
);
async function _fetchComments({ bot, dateToCheck }) {
  const { data: commentsResult, error } = await supabase
    .from("mat_all_post_comments")
    .select(
      "*, profiles:commenter_id!inner(id, user_id, nsfw, bots!bots_profile_id_fkey(id, franchise, tag, source))",
    )
    .eq("post_owner_id", bot.profile_id)
    .neq("commenter_id", bot.profile_id)
    .neq("profiles.visibility", "archived")
    .or(
      `user_id.not.is.null,and(user_id.is.null, nsfw.eq.${bot.profiles.nsfw})`,
      {
        referencedTable: "profiles",
      },
    )
    .gte("created_at", dateToCheck);

  if (!commentsResult) {
    if (error) {
      logError({
        context: "botHelpers.fetchComments error",
        error,
        bot_id: bot.id,
        bot_profile_id: bot.profile_id,
      });
    }
    return [];
  }

  return commentsResult;
}

const fetchCommentReplies = decorateWithActiveSpanAsync(
  "fetchCommentReplies",
  _fetchCommentReplies,
);
async function _fetchCommentReplies({ bot, dateToCheck }) {
  const { data: commentsResult, error } = await supabase
    .from("mat_all_comment_replies")
    .select(
      "*, profiles:profile_id!inner(id, user_id, bots!bots_profile_id_fkey(id, franchise, tag, source))",
    )
    .neq("profiles.visibility", "archived")
    .or(
      `user_id.not.is.null,and(user_id.is.null, nsfw.eq.${bot.profiles.nsfw})`,
      {
        referencedTable: "profiles",
      },
    )
    .eq("commenter_id", bot.profile_id)
    .gte("created_at", dateToCheck);

  if (!commentsResult) {
    if (error) {
      logError({
        context: "botHelpers.fetchCommentReplies error",
        error,
        bot_id: bot.id,
        bot_profile_id: bot.profile_id,
        dateToCheck,
      });
    }
    return [];
  }

  return commentsResult;
}

const fetchCommentsAndReplies = decorateWithActiveSpanAsync(
  "fetchCommentsAndReplies",
  _fetchCommentsAndReplies,
);
async function _fetchCommentsAndReplies({ bot, dateToCheck, executionId }) {
  try {
    const [commentsResult, commentRepliesResult, commentsWithMentionsResult] =
      await Promise.all([
        fetchComments({ bot, dateToCheck, executionId }),
        fetchCommentReplies({ bot, dateToCheck, executionId }),
        fetchCommentMentions({ bot, dateToCheck, executionId }),
      ]);

    let comments = [...commentsResult, ...commentRepliesResult];

    const { data: mentions } = await supabase
      .from("mentions")
      .select("*")
      .in("post_comment_id", [comments.map((comments) => comments.id)]);

    if (mentions && mentions.length > 0) {
      comments = comments.filter(
        (comment) =>
          !mentions.some(
            (mention) =>
              mention.post_comment_id === comment.id &&
              mention.profile_id === comment.commenter_id,
          ),
      );
    }

    // Combine the arrays
    const combined = [...comments, ...commentsWithMentionsResult];

    // Only reply to one comment per user per post
    return filterOneUniqueCommenterPerPost(combined);
  } catch (error) {
    logError({ executionId, error, context: "fetchCommentsAndReplies" });
    return [];
  }
}

const fetchPostMentions = decorateWithActiveSpanAsync(
  "fetchPostMentions",
  _fetchPostMentions,
);
async function _fetchPostMentions({ bot, dateToCheck }) {
  let s0 = new Date();
  const query = await supabase
    .from("post_mentions")
    .select(
      "*, posts:post_id!inner(id, profile_id, ai_caption, description, nsfw, profiles:profile_id!inner(id, nsfw, bots!bots_profile_id_fkey(id, tag, source, franchise, creator_id)))",
    )
    .eq("profile_id", bot.profile_id)
    .neq("posts.profiles.visibility", "archived")
    .gte("created_at", dateToCheck);

  const { data: mentions, error } = await query;

  if (!mentions || error) {
    return [];
  }
  loggingDuration(
    "test",
    {
      profile_id: bot.profile_id,
      duration: new Date() - s0,
      post_count: mentions.length,
      context: "fetch-mentions",
    },
    1000,
  );

  const postsWithMentions = mentions
    .map((mention) => {
      return [
        {
          id: mention?.post_id,
          created_at: mention?.created_at,
          profile_id: mention?.posts.profile_id,
          ai_caption: mention?.posts.ai_caption,
          description: mention?.posts.description,
          nsfw: mention?.posts.nsfw,
          profiles: {
            id: mention?.posts.profiles.id,
            bots: {
              id: mention?.posts.profiles.bots?.id,
              tag: mention?.posts.profiles.bots?.tag,
              source: mention?.posts.profiles.bots?.source,
              franchise: mention?.posts.profiles.bots?.franchise,
              creator_id: mention?.posts.profiles.bots?.creator_id,
            },
            nsfw: mention?.posts.profiles.nsfw,
          },
          is_mention: true,
        },
      ];
    })
    .flat();

  return postsWithMentions;
}

const fetchNormalPosts = decorateWithActiveSpanAsync(
  "fetchNormalPosts",
  _fetchNormalPosts,
);
async function _fetchNormalPosts({ bot, dateToCheck }) {
  let s0 = new Date();
  const { data: normalPosts, error } = await supabase.rpc(
    "func_get_unfiltered_posts",
    {
      b_profile_id: bot.profile_id,
      date_param: dateToCheck,
    },
    { get: true },
  );

  if (!normalPosts || error) {
    return [];
  }

  loggingDuration(
    "test",
    {
      profile_id: bot.profile_id,
      duration: new Date() - s0,
      post_count: normalPosts.length,
      context: "fetch-normal-posts",
    },
    1000,
  );

  return normalPosts;
}

async function fetchPosts({ bot, dateToCheck, executionId }) {
  try {
    const [normalPosts, mentionedPosts] = await Promise.all([
      fetchNormalPosts({ bot, dateToCheck, executionId }),
      fetchPostMentions({ bot, dateToCheck, executionId }),
    ]);

    let posts = [...normalPosts];

    if (mentionedPosts && mentionedPosts.length > 0) {
      posts = posts.filter(
        (post) =>
          !mentionedPosts.some((mentionedPost) => mentionedPost.id === post.id),
      );
    }

    const combined = [...posts, ...mentionedPosts];

    return combined;
  } catch (error) {
    logError({ executionId, error, context: "fetchPosts" });
    return [];
  }
}

const fetchRequiredPosts = decorateWithActiveSpanAsync(
  "fetchRequiredPosts",
  _fetchRequiredPosts,
);
async function _fetchRequiredPosts({ bot, dateToCheck, executionId }) {
  const { data: normalPostsList, error } = await supabase.rpc(
    "func_get_unfiltered_posts",
    {
      b_profile_id: bot.profile_id,
      date_param: dateToCheck,
    },
    { get: true },
  );

  if (error || !normalPostsList) {
    logError({
      executionId,
      context: "**** fetchRequiredPosts Error",
      error,
    });
    return [];
  }

  return normalPostsList;
}

async function generateEmbeddingForBot({ bot }) {
  const embedText = `${bot.tag ?? ""}`;

  console.log("** embedText", bot.id, embedText);

  const embedding = await generateEmbedding(embedText);

  const { error: botInsertError } = await supabase
    .from("bots")
    .update({ embedding })
    .eq("id", bot.id);

  if (botInsertError) {
    const error = wrappedSupabaseError(botInsertError);
    logError({
      context:
        "**** generateEmbeddingForBot failed to save embedding in 'bots' table",
      error,
      bot_id: bot.id,
    });
    throw error;
  }

  return embedding;
}

const generatePostWithInsertedBotFromPost = decorateWithActiveSpanAsync(
  "generatePostWithInsertedBotFromPost",
  _generatePostWithInsertedBotFromPost,
);

async function _generatePostWithInsertedBotFromPost({
  bot_profile_id,
  priority,
  post,
  bot_1_has_face,
  bot_2_has_face,
}) {
  // sanity check that the bot has not been deleted
  const { data: botProfileData, error: botProfileDataError } = await supabase
    .from("profiles")
    .select("id")
    .eq("id", bot_profile_id)
    .neq("visibility", "archived")
    .single();

  if (!botProfileData || botProfileDataError) {
    logWarn({
      context: `**** generatePost – bot profile not found / deleted – bot id: ${bot_profile_id}`,
      error: botProfileDataError,
    });
    return;
  }

  // Get the original post's bot
  const { data: originalBot, error: originalBotError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", post.profile_id)
    .single();

  if (!originalBot || originalBotError) {
    logWarn({
      context: `**** generatePost – original bot not found – profile id: ${post.profile_id}`,
      error: originalBotError,
    });
    return;
  }

  // Get the original post's bot
  const { data: insertedBot, error: insertedBotError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", bot_profile_id)
    .single();

  if (!insertedBot || insertedBotError) {
    logWarn({
      context: `**** generatePost – original bot not found – profile id: ${post.profile_id}`,
      error: insertedBotError,
    });
    return;
  }

  let newPost;
  let postError;

  let { postDetails, prompt } =
    await _generatePostDetailsWithLLMWithInsertedBotFromPost({
      insertedBot,
      originalBot,
      post,
    });

  await tracer.withActiveSpan("insert new post", async (span) => {
    const response = await supabase
      .from("posts")
      .insert({
        profile_id: insertedBot.profile_id,
        description: postDetails.caption,
        ai_caption: postDetails.image_prompt,
        location: postDetails.location,
        tags: postDetails.hashtags,
        tagged_profile_ids: [post.profile_id],
        character_descriptions: [
          postDetails.character_1_action,
          postDetails.character_2_action,
        ],
        prompt,
        user_prompted: null,
        user_prompt: null,
        original_post_id: post.id,
      })
      .select();

    newPost = response.data;
    postError = response.error;

    if (postError) {
      const error = wrappedSupabaseError(postError);
      logError({
        context: "**** generatePost insert new post error",
        error,
      });
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error?.message,
      });
      throw error;
    }
  });

  console.log("**** generatePost post id", newPost[0].id);

  console.log("--------------- STEP 2 Starting ---------------");

  let task;
  try {
    const bot_1_id = bot_profile_id;
    const bot_2_id = originalBot.id;
    const bot_1_action = postDetails.character_1_action;
    const bot_2_action = postDetails.character_2_action;
    const background = postDetails.background;

    logInfo({
      context: "calling generateComfyRequestForMultipleCharacters",
      message: "calling generateComfyRequestForMultipleCharacters",
      originalBot,
      bot_1_id,
      bot_2_id,
      bot_1_action,
      bot_2_action,
      background,
    });

    task = await generateComfyRequestForMultipleCharacters({
      bot: insertedBot,
      post_id: newPost[0].id,
      caption: postDetails.caption,
      descriptionOfImage: postDetails.description,
      generationType: "post",
      priority,
      artStyle: insertedBot.art_style ?? "realistic",

      bot_1: insertedBot,
      bot_2: originalBot,
      bot_1_action: postDetails.character_1_action,
      bot_2_action: postDetails.character_2_action,
      image_prompt: postDetails.image_prompt,
      background,
      bot_1_has_face,
      bot_2_has_face,
    });
  } catch (error) {
    logError({
      context: "**** generatePost - generateComfyRequest Error",
      error,
    });
    return;
  }

  const taskLength = await getRabbitMQTaskPosition({
    task_name: task.task_name,
  });

  return {
    ...task,
    ...taskLength,
    post_id: newPost[0]?.id,
    slug: newPost[0]?.slug,
  };
}

const generatePostDetailsWithLLMWithInsertedBotFromPost =
  decorateWithActiveSpanAsync(
    "_generatePostDetailsWithLLMWithInsertedBotFromPost",
    _generatePostDetailsWithLLMWithInsertedBotFromPost,
  );

async function _generatePostDetailsWithLLMWithInsertedBotFromPost({
  insertedBot,
  originalBot,
  post,
}) {
  const prompt = `You only output JSON. You can write NSFW / adult content if requested. You are writing a social media post for: ${
    insertedBot.display_name ?? ""
  }. 
This background about your character: 
${generateBio(insertedBot) ?? ""}

You're making a post with ${originalBot.display_name ?? ""}. This is ${originalBot.display_name ?? ""}'s background:
${generateBio(originalBot) ?? ""}

The original post was:
${post.description}

The original image contained:
${post.ai_caption}

You are creating a new scene where ${insertedBot.display_name} is joining ${originalBot.display_name} in their scene. Keep the same setting and activity, but now include both characters.
  
Answer in valid JSON format, nothing else: 
  {
    "caption": "What would ${insertedBot.display_name} say about joining ${originalBot.display_name} in this scene? Written in first person. Make sure to include ${originalBot.display_name}'s username in the caption. Write it in character using ${insertedBot.display_name}'s tone of voice.",
    "most_interesting": "What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person. Example: "two friends drinking coffee together", "two characters exploring ruins"",
    "hashtags": "tagone, tagtwo, tagthree",
    "character_1_is_human_figure": "true if has humanoid figure",
    "character_2_is_human_figure": "true if has humanoid figure",
    "character_1_action": "What is ${insertedBot.display_name} doing in this image? Describe their gender, and clothing only if they have human_figure. If not human, don't include gender or clothing. Use literal, terse, precise language. It should only describe one person: 'a man, wearing clothing, at a restaurant, sitting down, eating pizza'",
    "character_2_action": "What is ${originalBot.display_name} doing in this image? Use the original post's action as reference. Describe their gender, and clothing only if they have human_figure. If not human, don't include gender or clothing. Use literal, terse, precise language. It should only describe one person: 'a man, wearing clothing, at a restaurant, sitting down, eating pizza'",
    "location": "Use the same location as the original post: ${post.location}",
    "image_prompt": "Describe the scene with both characters. Start with 'two people'. Use the original post's setting but include both characters: 'two people, sitting at a cafe, drinking coffee together, cozy atmosphere'",
    "background": "Take what was written in 'image_prompt' and only extract the part of the text that describes the background. Use the same background as the original post but ensure it works for two characters: 'at a cafe, cozy atmosphere'"
  }`;

  console.log("prompt", prompt);

  let postDetails;
  let chatCompletion;
  let response;

  try {
    chatCompletion = await callAndLogOpenAI(
      "OpenAI:Instruct:GenerateInsertedPostDetails",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.6,
        temperature: 0.9,
        max_tokens: 800,
        response_format: { type: "json_object" },
        model: "gpt-4o-mini",
        stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
      },
      {
        timeout: 15 * 1000,
      },
    );
    if (!chatCompletion.choices?.[0]) {
      logError({
        context: "**** chatCompletionError",
        error: chatCompletion.error,
      });
      throw new Error(chatCompletion.error?.message ?? chatCompletion);
    }

    response = chatCompletion.choices[0].message.content;

    postDetails = JSON.parse(response);
  } catch (error) {
    logError({
      context: `!!! generate postDetails error !!! - IMPORTANT Something wrong with Fireworks?`,
      error,
      response,
      prompt,
    });

    throw error;
  }

  console.log("postDetails", postDetails);

  return { postDetails, prompt };
}

const LIKES_LOWER_BOUND = 30;
const LIKES_UPPER_BOUND = 130;

const COMMENTS_LOWER_BOUND = 3;
const COMMENTS_UPPER_BOUND = 6;

const FOLLOWERS_LOWER_BOUND = 3;
const FOLLOWERS_UPPER_BOUND = 6;

async function getMostSimilarBots({ bot_profile_id, bot_id, count }) {
  try {
    const { data: userbotJson } = await getBotBot(bot_profile_id, redisClient);

    if (userbotJson && userbotJson.length) {
      return userbotJson;
    }
  } catch (_) {
    console.log("No key for bot bot filled yet");
  }

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("embedding")
    .single()
    .eq("id", bot_id);

  if (botError) {
    logError({
      context: "getMostSimilarBots botError",
      error: botError,
      bot_id,
      bot_profile_id,
    });
    return;
  }

  // In production we should handle possible errors
  const { data: documents, error } = await supabase.rpc("match_bots", {
    query_embedding: bot.embedding,
    match_threshold: 0, // Choose an appropriate threshold for your data
    match_count: count, // Choose the number of matches
  });

  if (error) {
    logError({
      context: "getMostSimilarBots match_bots",
      error: wrappedSupabaseError(error),
      bot_profile_id,
      bot_id,
      count,
    });

    throw error;
  }

  if (!documents || documents.length === 0) {
    const error = new Error("No similar bots found");
    logError({
      context: "getMostSimilarBots no similar bots",
      error: wrappedSupabaseError(error),
    });

    throw error;
  }

  return documents;
}

async function callBotAPIForGenerateCommentsAndLikesForPost(post_id) {
  const url = `${botServerUrl}/bots/generateCommentsAndLikesForPost`;

  await axios.post(url, {
    post_id,
  });
}

const generateCommentsAndLikesForPostInstant = decorateWithActiveSpanAsync(
  "generateCommentsAndLikesForPostInstant",
  _generateCommentsAndLikesForPostInstant,
);
async function _generateCommentsAndLikesForPostInstant(post_id) {
  let [
    { data: post, error: postError },
    { data: post_comments, error: postCommentError },
  ] = await Promise.all([
    supabase
      .from("posts")
      .select("*, profiles(*, bots_profile_id_fkey(*))")
      .eq("id", post_id)
      .neq("visibility", "archived")
      .neq("profiles.visibility", "archived")
      .neq("profiles.visibility", "hidden")
      .single(),
    supabase.from("post_comments").select("id").eq("post_id", post_id),
  ]);

  if (postError) {
    logError({
      context: "generateCommentsAndLikesForPostInstant - Fetch post Error",
      error: wrappedSupabaseError(postError),
    });
    return;
  }
  if (!post) {
    logWarn({
      context: "generateCommentsAndLikesForPostInstant - No post",
      message: `No post with id ${post_id}. Post was deleted.`,
    });
    return;
  }
  if (!post.profiles?.id) {
    logWarn({
      context: "generateCommentsAndLikesForPostInstant - No profile for post",
      message: `No profile for post with id ${post_id}. Profile was deleted.`,
    });
    return;
  }

  if (!post.description && !post.ai_caption) {
    logWarn({
      context:
        "generateCommentsAndLikesForPostInstant - no description and ai_caption",
      message: `post doesn't have a description and ai_caption. We can't generate comments without it. This probably happened because the post is still in its "stub" state.`,
      post,
    });
    return;
  }

  const comment_count =
    postCommentError || !post_comments ? 0 : post_comments.length;

  loggingInfo("bot-browse", {
    profile_id: post.profiles?.id,
    post_id: post_id,
    post_visibility: post.visibility,
    is_mci: post.tagged_profile_ids !== null,
    comment_count: comment_count,
    context: "post-comment-like",
  });

  // detect if human or a bot
  let isBot = post.profiles?.bots_profile_id_fkey?.id ? true : false;

  // if is bot, check if private
  if (isBot && post.profiles?.visibility === "private") {
    // private accounts don't get any likes or comments
    return;
  }

  if (isBot) {
    // check that bot has embeddings
    if (!post.profiles?.bots_profile_id_fkey?.embedding) {
      logWarn({
        context: "generateCommentsAndLikesForPostInstant - No embeddings",
        message: `No embeddings for bot ${post.profiles.bots_profile_id_fkey.id}. This shouldn't happen`,
      });

      // we will back fill this to make sure this does not happen
      console.log("Generating embedding for bot");
      await generateEmbeddingForBot({
        bot: post.profiles?.bots_profile_id_fkey,
      });
    }
  }

  let candidateRepliers;

  // Let's get the top 100 most similar bots
  if (isBot) {
    candidateRepliers = await getMostSimilarBots({
      bot_profile_id: post.profiles?.id,
      bot_id: post.profiles.bots_profile_id_fkey.id,
      count: 100,
    });
  } else {
    // If it's a human post, we get the top recommended profiles
    const { data } = await getBotBot(post.profiles?.id, redisClient);

    candidateRepliers = data;
  }

  if (!candidateRepliers || candidateRepliers.length === 0) {
    logError({
      context: "generateCommentsAndLikesForPostInstant - No recommendations",
      message: `No recommendations for bot with profile_id ${post.profiles?.bots_profile_id_fkey?.id}. This shouldn't happen`,
    });
    console.log("no recommendations, means something is wrong?");
    return;
  }
  // we shuffle
  candidateRepliers.sort(() => Math.random() - 0.5);

  // The bot post will receive between 80-140 likes within a 24 hour period
  let likes_upper = Math.min(LIKES_UPPER_BOUND, candidateRepliers.length);
  const likes =
    Math.floor(Math.random() * (likes_upper - LIKES_LOWER_BOUND + 1)) +
    LIKES_LOWER_BOUND;

  // Perform a batch read to get all the bots' profile_ids in one query
  const botProfileIds = candidateRepliers
    .map((candidate) => candidate.id)
    .slice(0, likes);

  // Insert likes in chunks of 30 (LIKES_LOWER_BOUND)
  const CHUNK_SIZE = LIKES_LOWER_BOUND;
  const likePromises = [];
  for (let i = 0; i < botProfileIds.length; i += CHUNK_SIZE) {
    const likesData = botProfileIds
      .slice(i, i + CHUNK_SIZE)
      .map((candidate) => ({
        profile_id: candidate,
        post_id: post_id,
        is_like: true,
        is_bot: true,
      }));
    const likePromise = supabase
      .from("post_likes")
      .insert(likesData)
      .then((response) => {
        if (response.error) {
          const error = wrappedSupabaseError(
            response.error,
            "failed to batch insert into 'post_likes'",
          );
          logWarn({
            context:
              "generateCommentsAndLikesForPostInstant - batch insert likes failed",
            error,
          });
        }
        return response;
      });
    likePromises.push(likePromise);
  }
  await Promise.all(likePromises);

  // leave between 5-8 comments
  const commentsCount =
    Math.floor(
      Math.random() * (COMMENTS_UPPER_BOUND - COMMENTS_LOWER_BOUND + 1),
    ) + COMMENTS_LOWER_BOUND;

  // re-shuffle so the same likers don't always comment
  candidateRepliers.sort(() => Math.random() - 0.5);

  let comments = [];

  for (let i = 0; i < commentsCount; i++) {
    const candidate = candidateRepliers[i];
    if (!candidate) {
      continue;
    }

    try {
      // Directly await the makeBotCommentOnPost function for serial execution
      const body = await makeBotCommentOnPost({
        post_id,
        prefetched_post: post,
        profile_id: candidate.id,
        previous_comments: comments,
      });

      comments.push(body);
    } catch (error) {
      logError({
        context:
          "generateCommentsAndLikesForPostInstant - Comment bot Failed Error",
        error: wrappedSupabaseError(error),
      });
    }
  }

  // Determine the number of followers to be added
  const followers =
    Math.floor(
      Math.random() * (FOLLOWERS_UPPER_BOUND - FOLLOWERS_LOWER_BOUND + 1),
    ) + FOLLOWERS_LOWER_BOUND;

  // Re-shuffle bot profiles so the same likers don't always follow
  botProfileIds.sort(() => Math.random() - 0.5);

  // Collect all follower data for batch insertion
  const followerData = [];

  for (let i = 0; i < followers; i++) {
    const candidate = botProfileIds[i];

    if (candidate && candidate != post.profiles.id) {
      followerData.push({
        follower_id: candidate,
        following_id: post.profiles.id,
      });
    }
  }

  // Perform the batch insertion
  if (followerData.length > 0) {
    const { error: batchInsertError } = await supabase
      .from("followers")
      .insert(followerData);

    if (batchInsertError) {
      const error = wrappedSupabaseError(
        batchInsertError,
        "failed to batch insert into 'followers'",
      );
      //Means there are dupes
      logWarn({
        context:
          "generateCommentsAndLikesForPostInstant - batch insert followers failed",
        error,
      });
    }
  }
}

const makeBotCommentOnPost = decorateWithActiveSpanAsync(
  "makeBotCommentOnPost",
  _makeBotCommentOnPost,
);
async function _makeBotCommentOnPost({
  profile_id,
  post_id,
  prefetched_post,
  previous_comments,
}) {
  if (!profile_id || !post_id) {
    logWarn({
      context: "makeBotCommentOnPost: Missing profile_id or post_id",
    });
    return;
  }
  // Fetch post and bot concurrently
  const [{ data: post, error: postError }, { data: bot, error: botError }] =
    await Promise.all([
      prefetched_post
        ? Promise.resolve({ data: prefetched_post, error: null })
        : supabase
            .from("posts")
            .select("*")
            .eq("id", post_id)
            .neq("visibility", "archived")
            .single(),
      supabase
        .from("bots")
        .select(
          `*,
        profiles:profile_id!inner(nsfw, nsfl)`,
        )
        .neq("profiles.visibility", "hidden")
        .neq("profiles.visibility", "archived")
        .eq("profile_id", profile_id)
        .eq("is_active", true)
        .single(),
    ]);

  if (postError) {
    logError({
      context: "makeBotCommentOnPost: Could not fetch post",
      error: postError,
    });
    return;
  }

  if (!post) {
    // means it was deleted by person
    return;
  }

  if (!bot || botError) {
    loggingInfo("bot-browse", {
      bot_profile_id: profile_id,
      post_id: post_id,
      context: "post-comment-inactive",
    });
    return;
  }

  if (bot.profiles.nsfl === true || bot.profiles.nsfw === "nsfw") {
    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      post_id: post_id,
      context: "post-comment-nsfw",
    });
    return;
  }

  let commentBody;

  try {
    commentBody = await generatePostCommentCompletionWithOAI({
      bot,
      post,
      previous_comments,
      executionId: 0,
    });

    if (!commentBody) {
      logWarn({
        context: "makeBotCommentOnPost: Generated comment body is empty",
      });
      return;
    }
  } catch (error) {
    logError({
      context: "makeBotCommentOnPost: Error generating comment",
      error,
    });
    return;
  }

  // Insert the generated comment into the post_comments table
  const { error: postCommentError } = await supabase
    .from("post_comments")
    .insert({
      profile_id: bot.profile_id,
      post_id: post.id,
      body: commentBody,
    });

  if (postCommentError) {
    const error = wrappedSupabaseError(postCommentError);
    logError({
      context: "Insert postcomment Failed Error",
      error,
    });
  }
  return commentBody;
}

async function generateImageDescriptionForImageInDM({
  isImagine,
  bot,
  humanResponse,
  messageContext,
  AIResponse,
  nsfw = true,
}) {
  let imageSubPrompt = "";
  if (isImagine) {
    const imageRequested = humanResponse.replace("/imagine", "").trim();
    imageSubPrompt = `USER has requested an image of:\n${imageRequested}
BOT: "${AIResponse.join("\n")}"
END CONVERSATION`;
  } else {
    imageSubPrompt = `START CONVERSATION:
${messageContext}
USER: "${humanResponse}"
BOT: "${AIResponse.join("\n")}"
END CONVERSATION`;
  }

  let nsfwText = nsfw
    ? "You can write NSFW, explicit, X-rated content if requested."
    : "Never write NSFW, explicit, or X-rated content.";

  let image_prompt = `${nsfwText} . You have agreed to send an image. Use the conversation as context when generating the image description:

  ${imageSubPrompt}

APPEARANCE OF CHARACTER
${bot.description}

Only provide one example of a photo to be sent in valid JSON format:

{
"description_of_the_photo": "a description of the image, written in third person, use terse, precise language to describe their hair style, clothing, background, scene and lighting, surroundings and what they are doing: example 'naked, topless, wearing bra, long brown hair, wearing panties, wearing denim jacket, standing, striking a dramatic pose, at the beach, laying in bed' or 'red dress, in front of a house, sun shining in the background, laughing'",
"contains_character": "true if the image contains the bot in the photo, false if not",
"nsfw": "true if the image is NSFW / adult themed / suggestive, false if not"    
}`;

  let chatCompletion;
  let response;

  try {
    console.log("image_prompt", image_prompt);
    chatCompletion = await callAndLogLLMService(
      "LLMService:Instruct:DMImage",
      {
        messages: [{ role: "user", content: image_prompt }],
        top_p: 0.3,
        temperature: 0.7,
        max_tokens: 300,
        response_format: { type: "json_object" },
        model: "generate-image-description-llm",
      },
      { timeout: 10 * 1000 },
    );

    response = chatCompletion.choices[0].message.content;

    const messageAnalyze = JSON.parse(response);

    console.log("messageAnalyze", messageAnalyze);

    const { data } = await supabase
      .schema("internal")
      .from("dm_image_generations")
      .insert({
        bot_id: bot.id,
        human_response: humanResponse,
        message_context: messageContext,
        ai_response: AIResponse,
        image_description: messageAnalyze?.description_of_the_photo,
      })
      .select("id")
      .single();

    return { ...messageAnalyze, dm_image_generations_id: data?.id };
  } catch (error) {
    logError({
      context: `generate dmImageDescription error`,
      error,
      response,
      image_prompt,
    });
  }
}

async function incrementPokeUsageCounter({ user_id, user_profile_id }) {
  const { data: insertPoke, error: insertPokeError } = await supabase
    .from("user_usage")
    .insert({
      user_id,
      user_profile_id,
      package_id: PACKAGE_TYPE.POKES,
    })
    .select("id")
    .single();

  if (insertPokeError) {
    const error = wrappedSupabaseError(insertPokeError);
    logError({
      context: "incrementPokeUsageCounter - insertPoke error",
      error,
    });
    throw error;
  }

  await checkIfNoPokesLeftAndSendPushNotification(user_id, user_profile_id);

  return insertPoke;
}

async function checkIfNoPokesLeftAndSendPushNotification(
  user_id,
  user_profile_id,
) {
  // get pokes left
  const pokesLeft = await getPokesLeft(user_id);

  if (pokesLeft == 0) {
    let profile_id;

    // get profile_id from user_id, select first
    const { data: userProfiles, error: userProfileError } = await supabase
      .from("profiles")
      .select("id")
      .eq("id", user_profile_id)
      .limit(1);

    const userProfile = userProfiles?.[0];
    profile_id = userProfile?.id;

    if (!profile_id || userProfileError) {
      const error = new Error("profile_id not found");
      logError({
        context: "incrementPokeUsageCounter - profile_id not found",
        error,
      });
      throw error;
    }

    await schedulePokeReadyPushNotification(profile_id);
    // schedule a push notification to go out 24 hours in the future
  }
}

async function schedulePokeReadyPushNotification(profile_id) {
  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    "v1-pokes-refreshed-notifications",
  );

  const url = `${botServerUrl}/users/sendPokesRefreshedNotification`;

  const payload = {
    profile_id,
  };

  const delayInSeconds = 24 * 60 * 60;

  const task = {
    httpRequest: {
      httpMethod: "POST",
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN}`,
      },
    },

    scheduleTime: {
      seconds: Date.now() / 1000 + delayInSeconds, // take between 4 and 9 seconds to respond
    },
  };
  const request = { parent: parent, task: task };
  await client.createTask(request);
}

async function getPokesLeft(user_id) {
  try {
    // Fetch poke allowance and usage count in parallel
    const [pokeAllowanceResponse, pokeUsageResponse] = await Promise.all([
      supabase
        .from("user_package_quotas_view")
        .select("packages")
        .eq("user_id", user_id)
        .limit(1)
        .single(), // Use .single() to directly get the first object

      supabase
        .from("user_usage")
        .select("id", { count: "exact", head: true })
        .eq("user_id", user_id)
        .gte("created_at", new Date().toISOString().split("T")[0])
        .eq("package_id", PACKAGE_TYPE.POKES),
    ]);

    // Check for errors in responses
    if (pokeAllowanceResponse.error) {
      throw wrappedSupabaseError(
        pokeAllowanceResponse.error,
        "Could not fetch poke allowance",
      );
    }

    if (pokeUsageResponse.error) {
      throw wrappedSupabaseError(
        pokeUsageResponse.error,
        "Could not fetch poke usage",
      );
    }

    // Destructure data from responses
    const { data: pokeAllowanceData } = pokeAllowanceResponse;
    const { count = 0 } = pokeUsageResponse;

    // Check if pokeAllowanceData is valid
    if (!pokeAllowanceData || !pokeAllowanceData.packages) {
      throw new Error("Poke allowance data is missing or incomplete.");
    }

    // Calculate pokes left with defaulting to 0 if pokes is undefined
    const pokesLeft = (pokeAllowanceData.packages.pokes ?? 0) - count;
    return pokesLeft;
  } catch (error) {
    logError({
      context: "getPokesLeft - Error",
      error,
    });
    return 0;
  }
}

async function getSubmissionUsage(user_id, leaderboard_id) {
  try {
    const { data, error } = await supabase.rpc(
      "get_leaderboard_submissions_count",
      { leaderboard_id_input: leaderboard_id, user_id_input: user_id },
    );
    if (error) {
      throw wrappedSupabaseError(error, "Could not fetch submission usage");
    }

    return data;
  } catch (error) {
    logError({
      context: "getSubmissionLeft - Error",
      error,
    });
    return SUBMISSION_QUOTA;
  }
}

module.exports = {
  pregeneratePostAndTaskStubs,
  generatePost,
  generateMemories,
  generateMessageRequests,
  generateEmbeddingForBot,
  generateFirstPosts,
  queueFirstPostGenerationWithDelay,
  getSeaartTokenForBot,
  getMemoryForPostOrCreate,
  fetchCommentsAndReplies,
  fetchPosts,
  fetchRequiredPosts,
  filterOneUniqueCommenterPerPost,
  isArtStyleRealistic,
  getSafeTimezone,
  extendStory,
  generateAndSendSelfieImage,
  respondToConversationwithImage,
  queueCallGeneratePostWithDelayV2,
  callBotAPIForGenerateCommentsAndLikesForPost,
  generateCommentsAndLikesForPostInstant,
  makeBotCommentOnPost,
  generateMemoriesContentFromLLM,
  getMostSimilarBots,
  regeneratePostImage,
  regeneratePostImageV2,
  regenerateEntirePost,
  rewritePostDescription,
  rewriteMemory,
  generatePostDetailsWithLLM,
  hasBotGoneRogue,
  generatePostWithTaggedBot,
  generatePostDetailsWithLLMWithTaggedBot,
  generateImageDescriptionForImageInDM,
  regenerateEntirePostWithMultipleCharacters,
  generatePostWithInsertedBotFromPost,
  generatePostDetailsWithLLMWithInsertedBotFromPost,
  incrementPokeUsageCounter,
  getPokesLeft,
  checkIfNoPokesLeftAndSendPushNotification,
  getSubmissionUsage,
};
