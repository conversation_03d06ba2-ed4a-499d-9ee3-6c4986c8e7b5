/**
 * Boba Video Helpers
 *
 * This module provides helper functions for video generation in the boba service.
 */

const { v4: uuid } = require("uuid");
const { decorateWithActiveSpanAsync } = require("../instrumentation/tracer");
const { logError } = require("../utils");
const axios = require("axios");
const { Storage } = require("@google-cloud/storage");
const { Anthropic } = require("@anthropic-ai/sdk");
const { supabase } = require("../supabaseClient");

// Initialize GCS storage
const storage = new Storage();
const bucketName = "butterflies-images-v1-us";
const bucketPath = "videos";

// Initialize Anthropic client
const anthropic = new Anthropic({
  apiKey:
    process.env.ANTHROPIC_API_KEY ||
    "************************************************************************************************************",
});

/**
 * Generate a video from a prompt
 * @param {string} prompt - The prompt to generate a video from
 * @param {string} [image_url] - Optional URL of an existing image to use instead of generating one
 * @param {Object} [imageFile] - Optional multer file object containing image data
 * @returns {Promise<Object>} The generated video data including requestId, imageUrl and other metadata
 */
async function _generateVideoFromPrompt(
  prompt,
  image_url = null,
  imageFile = null,
) {
  try {
    console.log("[generateVideoFromPrompt] Starting text-to-video generation");

    let imageUrl = image_url;
    let response = {};

    // If no image_url or imageFile is provided, generate an image using Claude and Fireworks
    if (!imageUrl && !imageFile) {
      // Call Claude API to generate video content
      const messages = [
        {
          role: "user",
          content: `Take this user's prompt for a video.
<video_prompt>
${prompt}
</video_prompt>

Return in JSON format only the JSON object.

  {
      "first_frame": "a detailed visual description of the first image frame of the video. Use terse, simple language. Describe the lighting, and scenery and characters. When describing characters, vividly describe their clothing and pose and what they may be holding if anything.",
      "shot_action": "a description of the action happening in the 6 second video clip. Use terse, simple language. Also describe the camera movements and the speed of the shot (slow motion, regular motion, fast motion)",
      "sound_effects": "A description of the sound effects happening in the 6 second video clip. Describe only the most important sound. Never describe music.keep it under 8 words, terse literal language" // properly escape double quote or use single quotes
  }`,
        },
      ];

      const completion = await anthropic.messages.create({
        model: "claude-3-7-sonnet-********",
        max_tokens: 1024,
        messages,
        system:
          "When asked to output JSON, provide only the raw JSON with no explanations, markdown formatting, or code blocks.",
      });

      console.log("completion.content[0].text", completion.content[0].text);

      response = JSON.parse(completion.content[0].text);

      console.log("response", response);

      // Generate image using Fireworks AI
      const fireworksUrl =
        "https://api.fireworks.ai/inference/v1/workflows/accounts/fireworks/models/flux-1-dev-fp8/text_to_image";
      const fireworksHeaders = {
        "Content-Type": "application/json",
        Accept: "image/png",
        Authorization: `Bearer twXcAfk7EAJrjdGqRiDz8fGs0ghPvcNmlE2gANDua4q1KCoq`,
      };
      const fireworksData = {
        prompt: response.first_frame,
        aspect_ratio: "9:16",
        guidance_scale: 3.5,
        num_inference_steps: 30,
      };

      console.log(
        "[generateVideoFromPrompt] Generating image with Fireworks AI",
      );
      const fireworksResponse = await axios.post(fireworksUrl, fireworksData, {
        headers: fireworksHeaders,
        responseType: "arraybuffer",
      });

      if (fireworksResponse.status !== 200) {
        throw new Error(`Fireworks API error: ${fireworksResponse.status}`);
      }

      // Upload image to GCS
      console.log("[generateVideoFromPrompt] Uploading image to GCS");
      const imageUuid = uuid();
      console.log(
        `[generateVideoFromPrompt] Generated image UUID: ${imageUuid}`,
      );
      console.log(`[generateVideoFromPrompt] Bucket name: ${bucketName}`);
      console.log(`[generateVideoFromPrompt] Bucket path: ${bucketPath}`);
      console.log(
        `[generateVideoFromPrompt] Full path: ${bucketPath}/orig/${imageUuid}.png`,
      );

      const bucket = storage.bucket(bucketName);
      const file = bucket.file(`orig/${imageUuid}.png`);

      try {
        console.log(
          `[generateVideoFromPrompt] Image data length: ${fireworksResponse.data.length}`,
        );
        await file.save(Buffer.from(fireworksResponse.data), {
          metadata: {
            contentType: "image/png",
          },
          resumable: false,
        });
        console.log(
          `[generateVideoFromPrompt] Image successfully uploaded to: gs://${bucketName}/orig/${imageUuid}.png`,
        );

        // Verify file exists after upload
        const [exists] = await file.exists();
        console.log(
          `[generateVideoFromPrompt] File exists check after upload: ${exists}`,
        );
      } catch (error) {
        console.error(
          `[generateVideoFromPrompt] Error uploading image to GCS:`,
          error,
        );
        throw error;
      }

      imageUrl = `https://storage.googleapis.com/${bucketName}/orig/${imageUuid}.png`;
      console.log(`[generateVideoFromPrompt] Image uploaded to: ${imageUrl}`);
    }
    // If imageFile is provided but no image_url, upload the image file to GCS
    else if (!imageUrl && imageFile) {
      console.log("[generateVideoFromPrompt] Using provided image file");
      const imageUuid = uuid();
      console.log(
        `[generateVideoFromPrompt] Generated image UUID: ${imageUuid}`,
      );

      const bucket = storage.bucket(bucketName);
      const file = bucket.file(`orig/${imageUuid}.png`);

      try {
        // Get the buffer from the multer file object
        const imageBuffer = imageFile.buffer;

        // Determine content type from the original file or default to image/png
        const contentType = imageFile.mimetype || "image/png";

        await file.save(imageBuffer, {
          metadata: {
            contentType: contentType,
          },
          resumable: false,
        });

        console.log(
          `[generateVideoFromPrompt] Image successfully uploaded to: gs://${bucketName}/orig/${imageUuid}.png`,
        );

        // Verify file exists after upload
        const [exists] = await file.exists();
        console.log(
          `[generateVideoFromPrompt] File exists check after upload: ${exists}`,
        );

        imageUrl = `https://storage.googleapis.com/${bucketName}/orig/${imageUuid}.png`;
        console.log(`[generateVideoFromPrompt] Image uploaded to: ${imageUrl}`);
      } catch (error) {
        console.error(
          `[generateVideoFromPrompt] Error uploading image to GCS:`,
          error,
        );
        throw error;
      }
    } else {
      console.log(
        `[generateVideoFromPrompt] Using provided image URL: ${imageUrl}`,
      );
    }

    // Generate video from the image URL
    console.log("[generateVideoFromPrompt] Generating video from image URL");
    const videoParams = {
      imageUrl: imageUrl,
      videoPrompt: prompt,
    };

    const { requestId } = await _generateVideoFromURL(videoParams);

    // Store additional metadata in the database
    const dbEntry = {
      id: requestId,
      prompt,
      status: "pending",
      image_url: imageUrl,
      shot_action: prompt,
    };

    // Add additional fields if available
    if (response.first_frame) {
      dbEntry.image_prompt = response.first_frame;
      dbEntry.shot_action = response.shot_action;
      dbEntry.sound_effects = response.sound_effects;
    }

    await supabase.from("video_requests").insert(dbEntry);

    // Return the request ID and other metadata
    const result = {
      requestId,
      imageUrl,
    };

    // Add additional fields if available
    if (response.first_frame) {
      result.imagePrompt = response.first_frame;
      result.shotAction = response.shot_action ?? prompt;
      result.soundEffects = response.sound_effects;
    }

    return result;
  } catch (error) {
    logError({
      message: "Error generating video from prompt",
      error,
      prompt,
    });
    throw error;
  }
}

const generateVideoFromPrompt = decorateWithActiveSpanAsync(
  "generateVideoFromPrompt",
  _generateVideoFromPrompt,
);

/**
 * Poll video status
 * @param {string} requestId - The request ID to poll for
 * @returns {Promise<void>}
 */
async function _pollVideoStatus(requestId) {
  let attempts = 0;
  const maxAttempts = 120; // 10 minutes (120 * 5 seconds)

  const pollInterval = setInterval(async () => {
    try {
      attempts++;

      const statusResponse = await axios.post(
        "https://api.butterflies.ai/video/status",
        {
          request_id: requestId,
        },
      );

      console.log(statusResponse.data);

      if (
        statusResponse.data.status === "completed" &&
        statusResponse.data.download_url
      ) {
        // Update task with download URL
        const { error: updateError } = await supabase
          .from("video_generation_tasks")
          .update({
            status: "completed",
            download_url: statusResponse.data.download_url,
          })
          .eq("request_id", requestId);

        if (updateError) {
          throw updateError;
        }
        clearInterval(pollInterval);
      }

      if (attempts >= maxAttempts) {
        const { error: timeoutError } = await supabase
          .from("video_generation_tasks")
          .update({ status: "timeout" })
          .eq("request_id", requestId);

        if (timeoutError) {
          throw timeoutError;
        }
        clearInterval(pollInterval);
      }
    } catch (error) {
      console.error(
        `Error polling video status for request ${requestId}:`,
        error,
      );
      clearInterval(pollInterval);
    }
  }, 5000);
}

const pollVideoStatus = decorateWithActiveSpanAsync(
  "pollVideoStatus",
  _pollVideoStatus,
);

/**
 * Generate video from image URL
 * @param {string} imageUrl - URL of the image to process
 * @param {string|null} postId - ID of the post, optional
 * @param {string|null} videoClipId - ID of the video clip, optional
 * @param {string|null} videoPrompt - Custom prompt for video generation, optional
 * @param {string|null} soundEffects - Sound effects for video generation, optional
 * @param {string|null} imagePrompt - Image prompt for video generation, optional
 * @returns {Promise<Object>} Object containing requestId
 */
async function _generateVideoFromURL({
  imageUrl,
  postId,
  videoClipId,
  videoPrompt = null,
  soundEffects = null,
  imagePrompt = null,
}) {
  // Request video generation
  const requestBody = {
    image_url: imageUrl,
  };

  // Add video_prompt if provided
  if (videoPrompt) {
    requestBody.video_prompt = videoPrompt;
  }

  const videoResponse = await axios.post(
    "https://api.butterflies.ai/video/generate",
    requestBody,
  );

  const requestId = videoResponse.data.request_id;

  // Create task object with required fields
  const taskData = {
    request_id: requestId,
    status: "processing",
    image_url: imageUrl,
    sound_effect: soundEffects,
    shot_action: videoPrompt,
  };

  // Add post_id only if it exists
  if (postId) {
    taskData.post_id = postId;
  }

  if (videoClipId) {
    taskData.video_clip_id = videoClipId;
  }

  // Add sound_effect if provided
  if (soundEffects) {
    taskData.sound_effect = soundEffects;
  }

  // Create video generation task
  const { error: insertError } = await supabase
    .from("video_generation_tasks")
    .insert(taskData);

  if (insertError) {
    throw insertError;
  }

  // Start background polling
  _pollVideoStatus(requestId);

  return { requestId };
}

const generateVideoFromURL = decorateWithActiveSpanAsync(
  "generateVideoFromURL",
  _generateVideoFromURL,
);

/**
 * Get the current status of a video generation request
 * @param {string} requestId - The request ID to check
 * @returns {Promise<Object>} The current status of the video generation
 */
async function _getVideoStatus(requestId) {
  try {
    // Get status from the API
    const statusResponse = await axios.post(
      "https://api.butterflies.ai/video/status",
      {
        request_id: requestId,
      },
    );

    let result = statusResponse.data;

    // If completed, get additional data from the database
    if (statusResponse.data.status === "completed") {
      const { data, error } = await supabase
        .from("video_generation_tasks")
        .select("*")
        .eq("request_id", requestId)
        .single();

      if (error) {
        console.error("Error fetching video task data:", error);
      } else if (data) {
        result = { ...data, ...result };
      }
    }

    return result;
  } catch (error) {
    console.error(
      `Error getting video status for request ${requestId}:`,
      error,
    );
    throw error;
  }
}

const getVideoStatus = decorateWithActiveSpanAsync(
  "getVideoStatus",
  _getVideoStatus,
);

module.exports = {
  generateVideoFromPrompt,
  pollVideoStatus,
  generateVideoFromURL,
  getVideoStatus,
};
