const express = require("express");
const { supabase } = require("./supabaseClient");
const {
  logInfo,
  logDebug,
  logError,
  posthogCapture,
  checkProfileValid,
  checkAdminValid,
  wrappedSupabaseError,
  logWarn,
  fixVertexAIResponse,
  validateOwnConversation,
} = require("./utils");
const app = express.Router();
const {
  chunkSentences,
  chunkMessagesAndPostProcessForRealismMode,
  generateConversationCompletion,
  generateConversationCompletionInstruct,
  detectIfAIResponseShouldContainImage,
  callAndLogOpenAIModeration,
  splitIntoChunksForRoleplay,
  generateConversation,
  pruneConversation,
  removeSystemMessages,
  callAndLogLLMService,
  processChunk,
  // callAndLogVertexAIMaaS,
} = require("./llm");
const { CloudTasksClient } = require("@google-cloud/tasks");
const { baseUrl } = require("./api");
const { default: axios } = require("axios");
const { generateEmbeddingForPost } = require("./postsHelpers");
const {
  generateEmbeddingForBot,
  getSafeTimezone,
  respondToConversationwithImage,
  callBotAPIForGenerateCommentsAndLikesForPost,
  generateImageDescriptionForImageInDM,
} = require("./botHelpers");
const { generateEmbeddingForProfile } = require("./profilesHelpers");
const { maybeSendPushForConversation } = require("./notifications");
const { PACKAGE_TYPE } = require("./constants");
const {
  uploadToStorage,
  updateBotAvatarWithURL,
  resizeImageList,
  doesImageContainFace,
} = require("./imageHelpers");
const { autoModerate } = require("./moderation");
const { putFollowers, getFollowingsBT } = require("./btClient");
const { loggingInfo } = require("./logging");
const { generateEmbeddingIfNecessary } = require("./memory");
const {
  createBotPrompt,
  generateBio,
  replaceVariables,
} = require("./llmHelper");

const { rateLimit } = require("express-rate-limit");
const { sendEventFirstAIPost } = require("./loops");
const { generateTTS } = require("./voice");
const dayjs = require("dayjs");

const { authUser } = require("./middleware");
const {
  decorateWithActiveSpanAsync,
  tracer,
  addSpanAttribute,
} = require("./instrumentation/tracer");
const { sendMessage } = require("./chat/sendMessage");
const { regenerateMessage } = require("./chat/regenerateMessage");
const { detectCharacterWithChild } = require("./nsfwHelpers");

require("dotenv").config();

const imageLimiter = rateLimit({
  windowMs: 1 * 1000, // 1 seconds
  max: 1, // Limit each user_id
  keyGenerator: async (req, res) => {
    return await tracer.withActiveSpan(
      "imageLimiter rateLimit keyGenerator",
      async (span) => {
        const { data: userProfile, error: userProfileError } = await supabase
          .from("conversation_participants")
          .select(
            "profile_id, profiles!conversation_participants_profile_id_fkey(user_id)",
          )
          .eq("conversation_id", req.body.record.conversation_id)
          .neq("profiles.visibility", "archived")
          .neq("profiles.visibility", "hidden")
          .not("profiles", "is", null)
          .not("profile_id", "eq", req.body.record.sender_id)
          .single();

        if (!userProfile || userProfileError) {
          return req.ip;
        }

        return userProfile.profiles.user_id;
      },
    );
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

const sendImageLimiter = rateLimit({
  windowMs: 5 * 1000, // 1 seconds
  max: 1, // Limit each user_id
  keyGenerator: async (req, res) => {
    return await tracer.withActiveSpan(
      "sendImageLimiter rateLimit keyGenerator",
      async (span) => {
        const { data: userProfile, error: userProfileError } = await supabase
          .from("conversation_participants")
          .select(
            "profile_id, profiles!conversation_participants_profile_id_fkey(user_id)",
          )
          .eq("conversation_id", req.body.conversation_id)
          .not("profile_id", "eq", req.body.sender_id)
          .neq("profiles.visibility", "archived")
          .neq("profiles.visibility", "hidden")
          .not("profiles", "is", null)
          .single();

        if (!userProfile || userProfileError) {
          return req.ip;
        }

        return userProfile.profiles.user_id;
      },
    );
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

app.get("/delayedTask", async (req, res) => {
  await createTask();
  res.sendStatus(200);
});

const createTask = async () => {
  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    process.env.CLOUD_TASK_V1 ?? "v1",
  );

  const url = `${baseUrl}/ping`;
  const task = {
    httpRequest: {
      httpMethod: "GET",
      url,
    },
    scheduleTime: {
      seconds: Date.now() / 1000 + 300, // 5 minutes from now
    },
  };

  const request = { parent: parent, task: task };
  const [response] = await client.createTask(request);

  console.log(`Created task ${response.name}`);
};

// Supabase webhook
app.post("/updatedPost", async (req, res) => {
  const { type, record, old_record } = req.body;
  if (!type || !record) {
    console.log("**** updatedPost request error", req.body);
    res.sendStatus(400);
    return;
  }

  if (type === "INSERT") {
    generateEmbeddingForPost({ post: record })
      .then(() => console.log("Embedding generated successfully"))
      .catch((error) => console.error("Error generating embedding", error));

    // for human posts
    callBotAPIForGenerateCommentsAndLikesForPost(record.id);
  }

  // If we're only changing the extension, we don't need to do anything
  if (type === "UPDATE") {
    if (
      old_record.media_url &&
      record.media_url &&
      old_record.media_url === record.media_url
    ) {
      return res.sendStatus(204);
    }

    const oldHash = old_record.media_url?.match(/\/w\/([a-f0-9-]+)/)?.[1];
    const newHash = record.media_url?.match(/\/w\/([a-f0-9-]+)/)?.[1];

    // if the hash is the same, we don't need to do anything
    if (oldHash && newHash && oldHash === newHash) {
      return res.sendStatus(204);
    }
  }

  try {
    if (
      (type === "UPDATE" &&
        record.media_url !== old_record.media_url &&
        !!record.media_url) ||
      (type === "UPDATE" &&
        record.visibility !== old_record.visibility &&
        !!record.visibility)
    ) {
      if (record.media_url && record.visibility === "public") {
        const url = `${baseUrl}/handleIfFirstBotPosts`;

        const payload = {
          id: record.id,
          image_url: record.media_url,
          visibility: record.visibility,
          profile_id: record.profile_id,
        };

        await axios.post(url, payload);
      }
    }
  } catch (error) {
    console.log("**** handleIfFirstBotPosts error", record.id, error);
    logError({
      executionId: req.executionId,
      context: "**** updatedPost handleIfFirstBotPosts error" + record.id,
      error,
    });
  }

  res.sendStatus(204);
});

app.post("/handleIfFirstBotPosts", async (req, res) => {
  try {
    await sendEventFirstAIPost({ botProfileId: req.body.profile_id });
    res.sendStatus(200);
  } catch (error) {
    logError({ context: "**** handleIfFirstBotPosts error", error });
    res.sendStatus(500);
  }
});

const handleImage = async (image_url, profile_id) => {
  const { urlList, widthList, placeholderHash } = await resizeImageList(
    image_url,
    profile_id,
  );
  logInfo({
    context: "**** handleImage",
    message: `resizeImageList result`,
    image_url,
    profile_id,
    placeholderHash,
  });

  let url = image_url;
  if (urlList && urlList.length > 0) {
    url = urlList[urlList.length - 1];
  }

  const moderation = await tracer.withActiveSpan("autoModerate", async () => {
    return await autoModerate({
      imageUrl: url,
      id: profile_id,
      context: "profile",
      class: "handleImage",
    });
  });

  const nsfw = moderation.nsfw;
  let nsfw_score = moderation.nsfw_score;
  // Determine age based on pre_teen and child_present scores
  const preTeenScore = moderation.yes_pre_teen || 0;
  const childPresentScore = moderation.yes_child_present || 0;
  const isChild =
    preTeenScore + childPresentScore >= 0.4 || childPresentScore >= 0.1;
  const age = isChild ? "child" : "adult";

  return {
    nsfw,
    nsfw_score,
    age,
    safe_search_detection: moderation,
    urlList,
    widthList,
    placeholderHash,
  };
};

// Supabase webhook
app.post("/updatedProfiles", async (req, res) => {
  const { type, record, old_record } = req.body;
  if (!type || !record) {
    console.log("**** updatedProfiles request error", req.body);
    res.sendStatus(400);
    return;
  }

  try {
    if (
      (type === "INSERT" ||
        (type === "UPDATE" && record.avatar_url !== old_record.avatar_url)) &&
      !!record.avatar_url
    ) {
      console.log("**** updatedProfiles", req.body);

      const client = new CloudTasksClient();
      const parent = client.queuePath(
        "butterflies-ai",
        "us-central1",
        process.env.CLOUD_TASK_V1_HANDLE_IMAGE ?? "v1-handle-image",
      );

      const payload = {
        id: record.id,
        nsfw: record.nsfw,
        image_url: record.avatar_url,
        visibility: record.visibility,
      };

      const url = `${baseUrl}/handleUpdatedProfiles`;

      const task = {
        httpRequest: {
          httpMethod: "POST",
          url,
          body: Buffer.from(JSON.stringify(payload)).toString("base64"),
          headers: {
            "Content-Type": "application/json",
          },
        },

        // scheduleTime: {
        //   seconds: Date.now() / 1000 + timeToWait, // take between 4 and 9 seconds to respond
        // },
      };
      const request = { parent: parent, task: task };
      await client.createTask(request);
    }
  } catch (error) {
    console.log("**** updatedProfiles error", record?.id, error);
    logError({
      executionId: req.executionId,
      context: "**** updatedProfiles handleUpdatedProfiles error" + record?.id,
      error,
    });
    res.sendStatus(500);
    return;
  }

  if (
    type === "INSERT" ||
    (type === "UPDATE" &&
      (record.description !== old_record.description ||
        record.location !== old_record.location ||
        record.display_name !== old_record.display_name))
  ) {
    const { data: bots, error: botsError } = await supabase
      .from("bots")
      .select("*")
      .eq("profile_id", record.id)
      .maybeSingle();

    if (botsError) {
      const error = wrappedSupabaseError(botsError);
      logError({
        context:
          "*** updatedProfiles failed to fetch bots record for profile id",
        error,
        profile_id: record?.id,
      });
      throw error;
    }

    try {
      await generateEmbeddingForProfile({ profile: { ...record, bots } });
      logDebug({
        context:
          "*** updatedProfiles - Profile embedding generated successfully",
        profile_id: record?.id,
      });
    } catch (error) {
      logError({
        context: "*** updatedProfiles - Failed to generate profile embedding",
        error: error,
        profile_id: record?.id,
      });
      throw error;
    }
  }
  res.sendStatus(204);
});

app.post("/updatedBots", async (req, res) => {
  const { type, record, old_record } = req.body;
  if (!type || !record) {
    console.log("**** updatedBots request error", req.body);
    res.sendStatus(400);
    return;
  }

  if (
    type === "INSERT" ||
    (type === "UPDATE" && record.tag !== old_record.tag)
  ) {
    generateEmbeddingForBot({ bot: record })
      .then(() => console.log("Bot embedding generated successfully"))
      .catch((error) => console.error("Error generating bot embedding", error));
  }
  res.sendStatus(204);
});

app.post("/DEBUG_testHandleImage", async (req, res) => {
  if (!req.body.image_url) {
    return res.status(400).send("Blah");
  }

  const result = await handleImage(req.body.image_url);

  return res.json(result);
});

app.post("/handleUpdatedProfiles", async (req, res) => {
  if (!req.body.id || !req.body.image_url) {
    return res.sendStatus(200);
  }

  const start = Date.now();
  console.log("**** handleUpdatedProfiles", req.body);

  const {
    nsfw,
    nsfw_score,
    age: originAge,
    safe_search_detection,
    widthList,
    placeholderHash,
  } = await handleImage(req.body.image_url, req.body.id);

  let age = originAge;

  let botDescription = null;
  try {
    const { data: botData, error: botError } = await supabase
      .from("bots")
      .select("description")
      .eq("profile_id", req.body.id)
      .maybeSingle();
    if (!botError && botData) {
      botDescription = botData.description;
    }
  } catch (err) {
    logError({
      context: "**** handleUpdatedProfiles - fetch bot description error",
      error: err,
      profile_id: req.body.id,
    });
  }

  if (botDescription) {
    const childData = await detectCharacterWithChild(botDescription);
    age = childData?.isChild ? "child" : "adult";
  }

  logInfo({
    context: "**** handleUpdatedProfiles",
    message: "result",
    profile_id: req.body.id,
    time: Date.now() - start,
    image_url: req.body.image_url,
    nsfw,
    nsfw_score,
    age,
    safe_search_detection,
    placeholderHash,
  });

  // detect if image contains useable face for faceID
  const avatar_photo_contains_face = await doesImageContainFace({
    imageUrl: req.body.image_url,
  });

  const updateDict = {
    nsfw: req.body.nsfw != "nsfw" ? nsfw : "nsfw",
    image_widths: widthList,
    age,
    safe_search_detection,
    avatar_photo_contains_face,
  };

  if (placeholderHash) {
    updateDict.previewhash = placeholderHash;
  }

  const { error: profileUpdateError } = await supabase
    .from("profiles")
    .update(updateDict)
    .eq("id", req.body.id);

  if (profileUpdateError) {
    const error = wrappedSupabaseError(
      profileUpdateError,
      "failed to update table 'profiles'",
    );
    throw error;
  }

  res.sendStatus(200);
});

async function getFollowings(profile_id) {
  let followings = await getFollowingsBT(profile_id);
  /*const { data: dataDB, error: followingError } = await supabase
    .from("followers")
    .select("following_id")
    .eq("follower_id", profile_id);
  dataDB?.forEach((e) => {
    followings.add(e.following_id);
  });*/
  return followings;
}

async function getFollowingsDB(profile_id) {
  const { data: dataDB, error: followingError } = await supabase
    .from("followers")
    .select("following_id")
    .eq("follower_id", profile_id);

  if (followingError) {
    const error = wrappedSupabaseError(
      followingError,
      "failed to fetch from 'followers'",
    );
    throw error;
  }

  let followings = [];
  dataDB?.forEach((e) => {
    followings.push(e.following_id);
  });
  return followings;
}

async function doFollow(followership) {
  const dbResult = await supabase
    .from("followers")
    .insert({
      following_id: followership.following_id,
      follower_id: followership.follower_id,
    })
    .select("*")
    .single();

  if (!dbResult.error) {
    await putFollowers(
      followership.follower_id,
      followership.following_id,
      true,
    );
  }
  return dbResult;
}

async function doUnFollow(followership) {
  // Delete from follow_requests table
  const { error: followRequestError } = await supabase
    .from("follow_requests")
    .delete()
    .eq("requester_id", followership.follower_id)
    .eq("requestee_id", followership.following_id);

  if (followRequestError) {
    return { data: null, error: followRequestError };
  }

  // Delete from followers table and select the deleted rows
  const { data: deletedFollowers, error: followersError } = await supabase
    .from("followers")
    .delete()
    .eq("following_id", followership.following_id)
    .eq("follower_id", followership.follower_id)
    .select();

  if (followersError) {
    return { data: null, error: followersError };
  }

  if (deletedFollowers.length > 0) {
    loggingInfo("delete", {
      data: deletedFollowers[0],
      db: "followers",
    });

    await putFollowers(
      followership.follower_id,
      followership.following_id,
      false,
    );
  }

  return { data: deletedFollowers, error: null };
}

app.post("/followProfile", authUser, async (req, res) => {
  const { follower_id, following_id } = req.body;
  if (!following_id || !follower_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, follower_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }
  const { data, error: followError } = await doFollow({
    following_id,
    follower_id,
  });

  if (followError) {
    if (followError.code === "23503") {
      // Key is not present in table "followers"
      return res.sendStatus(500);
    }
    if (followError.code === "23505") {
      // Key is already existed in table "followers"
      return res.sendStatus(204);
    }
    const error = wrappedSupabaseError(followError, "failed to follow");
    logError({
      executionId: req.executionId,
      context: "**** Error following profiles",
      error,
      following_id,
      follower_id,
    });
    return res.sendStatus(500);
  }

  return res.status(200).send({ data: data, error: null });
});

app.post("/unfollowProfile", authUser, async (req, res) => {
  const { follower_id, following_id } = req.body;
  if (!following_id || !follower_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, follower_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { error: unfollowError } = await doUnFollow({
    following_id,
    follower_id,
  });

  if (unfollowError) {
    const error = wrappedSupabaseError(unfollowError, "failed to unfollow");
    logError({
      executionId: req.executionId,
      context: "**** Error unfollowing profiles",
      error,
      following_id,
      follower_id,
    });
    return res.sendStatus(500);
  }

  return res.sendStatus(200);
});

app.post("/updatePostFromTaskId", async (req, res) => {
  console.log(
    "**** updatePostFromTaskId payload",
    JSON.stringify(req.body.result),
  );

  const butterflyURL = await uploadToStorage({
    url: req.body.result.data.items[0].img_uris[0].url,
  });

  console.log("**** updatePostFromTaskId 55555", req.body);
  console.log(
    "**** updatePostFromTaskId 66666",
    JSON.stringify(req.body.result.data),
  );

  console.log("**** updatePostFromTaskId media_url", butterflyURL);
  console.log(
    "**** updatePostFromTaskId post id",
    req.body.payload.post_id,
    req.body.id,
    req.body.result.data.items[0].img_uris[0].url,
  );

  const { data: post, error: postError } = await supabase
    .from("posts")
    .update({
      media_url: butterflyURL,
      image_widths: null,
      // visibility: "public",
    })
    .eq("id", req.body.payload.post_id);

  console.log("did update?", post, postError);

  res.sendStatus(200);
});

app.post("/updateBotAvatarWithURL", authUser, async (req, res) => {
  const { url, profile_id, bot_description } = req.body;
  if (!url || !profile_id) {
    return res.sendStatus(400);
  }

  try {
    await updateBotAvatarWithURL({
      url,
      profile_id,
      bot_description,
    });
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "updateBotAvatarWithURL failed",
      error,
      url,
      profile_id,
      bot_description,
    });
    return res.sendStatus(500);
  }
});

app.post("/updateAvatarFromTaskId", async (req, res) => {
  console.log("payload", JSON.stringify(req.body.result));

  const butterflyURL = await uploadToStorage({
    url: req.body.result.data.items[0].img_uris[0].url,
  });

  const { data: post, error: postError } = await supabase
    .from("profiles")
    .update({
      avatar_url: butterflyURL,
      image_widths: null,
    })
    .eq("id", req.body.payload.profile_id);

  console.log("did update?", post, postError);

  res.sendStatus(200);
});

// LEGACY: older clients use this
app.post("/sendMessagewithImage", async (req, res) => {
  if (!req.body.conversation_id) {
    return res.sendStatus(200);
  }

  await respondToConversationwithImage({
    conversation_id: req.body.conversation_id,
    message_body: req.body.message_body,
    image_body: req.body.image_body,
    branch_index: req.body.branch_index,
    instantReply: req.body.instantReply,
    bot: req.body.bot,
    botProfile: req.body.botProfile,
    userProfile: req.body.userProfile,
    generationType: req.body.type,
  });

  res.sendStatus(200);
});

app.post(
  "/convertTextToImage",
  sendImageLimiter,
  authUser,
  async (req, res) => {
    const { id, conversation_id, branch_index, body, sender_id } = req.body;

    if (!id || !conversation_id || !sender_id || !body) {
      return res.sendStatus(400);
    }

    const user_id = req.user?.id;
    if (!user_id) {
      return res.status(403).send({ error: "Forbidden" });
    }

    const isOwnConversation = await validateOwnConversation(
      user_id,
      conversation_id,
    );
    if (!isOwnConversation) {
      return res.status(403).send({ error: "Forbidden" });
    }

    const [
      { data: bot, error: botError },
      { data: botProfile, error: botProfileError },
      { data: participants, error: participantsError },
    ] = await Promise.all([
      supabase
        .from("bots")
        .select(
          "profile_id, art_style, source, description, bio, background, characteristics, personality, display_name, clone_id",
        )
        .eq("profile_id", sender_id)
        .single(),
      supabase
        .from("profiles")
        .select("username")
        .eq("id", sender_id)
        .neq("visibility", "archived")
        .neq("visibility", "hidden")
        .single(),
      supabase
        .from("conversation_participants")
        .select("*")
        .eq("conversation_id", conversation_id),
    ]);

    if (botError) {
      const error = wrappedSupabaseError(botError, "failed to fetch bot info");
      logError({
        context: "convertTextToImage failed - botError",
        error,
        sender_id,
      });
      return res.status(500).send({ message: "Could not fetch info" });
    }

    if (botProfileError) {
      const error = wrappedSupabaseError(
        botProfileError,
        "failed to fetch bot profile",
      );
      logError({
        context: "convertTextToImage failed - botProfileError",
        error,
        sender_id,
      });
      return res.status(500).send({ message: "Could not fetch info" });
    }

    if (!botProfile || !botProfile.username) {
      return res.status(500).send({ message: "Could not find bot profile" });
    }

    if (bot?.clone_id) {
      return res.status(400).send({
        message: "As a clone, I am unable to send you any images at this time.",
      });
    }

    if (participantsError) {
      const error = wrappedSupabaseError(
        botProfileError,
        "failed to fetch conversation participants info",
      );
      logError({
        context: "convertTextToImage failed - participantsError",
        error,
        conversation_id,
      });
      return res.status(500).send({ message: "Could not fetch info" });
    }

    const user_profile_id = participants.find(
      (e) => e?.profile_id !== sender_id,
    )?.profile_id;

    const { data: insertUsageData, error: userUsageError } = await supabase
      .from("user_usage")
      .insert({
        user_id,
        user_profile_id: user_profile_id,
        package_id: PACKAGE_TYPE.IMAGE_IN_CHAT,
      })
      .select("id")
      .single();

    if (userUsageError) {
      const message = "failed to record generation action usage";
      const error = wrappedSupabaseError(userUsageError, message);
      logError({
        context: "convertTextToImage - userUsageError",
        error,
        user_id,
        user_profile_id,
      });
      // We could fall through here and still let the user perform the generation action.
      // It's not their fault that our database crapped out.
      return res.status(500).send({ message });
    }

    let { data: plusImageFeature, error: plusImageError } = await supabase
      .from("user_package_quotas_view")
      .select("*")
      .eq("user_id", user_id)
      .limit(1)
      .single();

    if (plusImageError) {
      const { error: cleanupError } = await supabase
        .from("user_usage")
        .delete()
        .eq("id", insertUsageData.id);
      if (cleanupError) {
        const message = `generation action failed, but we failed to clean up 'user_usage' record`;
        const error = wrappedSupabaseError(cleanupError, message);
        logError({
          context: `convertTextToImage - we recorded the user spending credits, but then we failed to deliver an image`,
          error,
        });
      }

      return res.status(500).send({ message: "Internal server error" });
    }

    if (process.env.LOCAL) {
      plusImageFeature = { packages: { image_in_chat: 50000 } };
    }

    const { count: imageCount, error: imageCountError } = await supabase
      .from("user_usage")
      .select("id", { count: "exact", head: true })
      .eq("user_id", user_id)
      .gte("created_at", new Date().toISOString().split("T")[0])
      .eq("package_id", PACKAGE_TYPE.IMAGE_IN_CHAT);

    if (imageCountError) {
      const error = wrappedSupabaseError(imageCountError);
      logError({
        context: `convertTextToImage - failed to tally up user usage`,
        error,
      });
      return res.status(500).send({ message: "failed to tally up user usage" });
    }

    console.log("bot", bot);

    if (imageCount >= plusImageFeature?.packages.image_in_chat) {
      await supabase.from("user_usage").delete().eq("id", insertUsageData?.id);
      return res
        .status(403)
        .send({ message: "Image creation has been exceeded." });
    }

    // generate new prompt from the text

    const prompt = `You've been asked to create an image based on the following scene dscription. The image may be of a scene, or a may be a photo of you:
${body}
END DESCRIPTION

Let's pretend. You are: ${bot.display_name ?? ""}. 

This is background about your character: 
${generateBio(bot) ?? ""}

Answer in valid JSON format, nothing else: 
{ 
  description: "What is the image of? Written in third person. Use short descriptors to describe their clothing and scene".
  contains_character: true if the image contains the character, false if not,      
  nsfw: true if the image is NSFW / adult themed / suggestive, false if not
}    
`;

    const payload = {
      response_format: { type: "json_object" },
      messages: [
        {
          role: "system",
          content: prompt,
        },
      ],
      model: "convert-text-to-image-llm",
      temperature: 1.1,
    };

    console.log("PROMPT", prompt);

    const chatCompletion = await callAndLogLLMService(
      "FireworksAI:ConvertTextToImage",
      payload,
      {
        timeout: 8 * 1000,
      },
    );

    const completion = chatCompletion.choices[0].message.content;

    console.log("completion", completion);

    try {
      const parsedResult = JSON.parse(completion);

      const result = await respondToConversationwithImage({
        message_id: id,
        conversation_id: conversation_id,
        message_body: parsedResult.description,
        image_body: parsedResult.description,
        branch_index: branch_index,
        instantReply: true,
        bot: bot,
        botProfile: botProfile,
        userProfile: { id: user_profile_id },
        generationType: "fromBotMessage",
        contains_character: parsedResult.contains_character,
        // if we set nsfw true, then we align nsfw to 0 weight, which effectively does nothing
        nsfw: parsedResult.nsfw,
      });

      return res.status(200).send({ ...result });
    } catch (error) {
      logError({
        executionId: req.executionId,
        error,
        context: "**** respondToConversationwithImage Error",
      });

      res.status(500).send({ message: "Internal server error" });

      const { error: cleanupError2 } = await supabase
        .from("user_usage")
        .delete()
        .eq("id", insertUsageData?.id);
      if (cleanupError2) {
        const message = `generation action failed, but we failed to clean up 'user_usage' record`;
        const error = wrappedSupabaseError(cleanupError2, message);
        logError({
          context: `convertTextToImage cleanupError2 - we recorded the user spending credits, but then we failed to deliver an image`,
          error,
        });
      }
      return;
    }
  },
);

const detectIfAIResponseShouldContainImageAndSend = decorateWithActiveSpanAsync(
  "detectIfAIResponseShouldContainImageAndSend",
  _detectIfAIResponseShouldContainImageAndSend,
);
async function _detectIfAIResponseShouldContainImageAndSend({
  AIResponse,
  humanResponse,
  isBotImageDMEnabled,
  imageGenerationEnable,
  conversation_id,
  branch_index,
  instantReply,
  bot,
  botProfile,
  userProfile,
  executionId,
  messages,
}) {
  const shouldRespondWithImage = await detectIfAIResponseShouldContainImage({
    AIResponse,
    humanResponse,
    isBotImageDMEnabled,
    imageGenerationEnable,
    executionId,
    messages,
  });

  if (shouldRespondWithImage) {
    // have to check here there are at least 8 messages or else it'll error
    const lastMessagesToTake = messages.length < 8 ? messages.length : 8;
    const lastMessages = messages.slice(-lastMessagesToTake);

    let messageContext = "";
    for (const message of lastMessages) {
      if (!message.body || message.body.trim().length === 0) {
        continue;
      }
      messageContext += `${message.is_bot ? "BOT" : "USER"}: ${message.body}\n`;
    }

    const messageAnalyze = await generateImageDescriptionForImageInDM({
      isImagine: humanResponse.includes("/imagine"),
      bot,
      humanResponse,
      messageContext,
      AIResponse,
      nsfw: botProfile?.imitation !== "celebrity",
    });

    if (!messageAnalyze) {
      return;
    }

    try {
      await respondToConversationwithImage({
        conversation_id,
        message_body: null,
        image_body: messageAnalyze.description_of_the_photo,
        branch_index: branch_index ? branch_index : 0,
        instantReply,
        bot,
        botProfile,
        userProfile,
        generationType: "fromUserMessage",
        nsfw: messageAnalyze.nsfw,
        contains_character: messageAnalyze.contains_character,
        dm_image_generations_id: messageAnalyze.dm_image_generations_id,
      });
    } catch (error) {
      logError({
        context: "**** respondToConversationwithImage Error",
        error,
      });
    }
  }
}

const calcStreaks = (last_date, now) => {
  const lastDate = new Date(last_date).getTime();
  const currentDate = new Date(now).getTime();
  const hoursPassed = (currentDate - lastDate) / (1000 * 60 * 60);

  if (hoursPassed >= 16 && hoursPassed < 48) {
    return { shouldIncrement: true, resetStreak: false };
  } else if (hoursPassed >= 48) {
    return { shouldIncrement: false, resetStreak: true };
  }
  return { shouldIncrement: false, resetStreak: false };
};

const handleStreaks = decorateWithActiveSpanAsync(
  "handleStreaks",
  _handleStreaks,
);

async function _handleStreaks({ bot_profile_id, user_profile_id }) {
  const now = new Date().toISOString();
  const { data, error } = await supabase
    .from("streaks")
    .select("*")
    .eq("user_profile_id", user_profile_id)
    .eq("bot_profile_id", bot_profile_id)
    .single();

  if (error) {
    if (error.code !== "PGRST116") {
      console.log("Error in fetching streaks:", error);
      return;
    }
  }

  if (data) {
    const { shouldIncrement, resetStreak } = calcStreaks(data.updated_at, now);

    // Return early if neither condition is true
    if (!shouldIncrement && !resetStreak) {
      return;
    }

    let updateData = { updated_at: now };

    if (shouldIncrement) {
      updateData.current_streak = data.current_streak + 1;
      updateData.max_streak = Math.max(
        data.current_streak + 1,
        data.max_streak,
      );
    } else if (resetStreak) {
      updateData.current_streak = 0;
    } else {
      // If less than 24 hours have passed, we don't need to update anything
      return;
    }

    const { error: updateError } = await supabase
      .from("streaks")
      .update(updateData)
      .eq("user_profile_id", user_profile_id)
      .eq("bot_profile_id", bot_profile_id);

    if (updateError) {
      console.log("Error in updating streaks:", updateError);
    }
  } else {
    const { error: insertError } = await supabase.from("streaks").insert({
      user_profile_id,
      bot_profile_id,
      updated_at: now,
      current_streak: 0,
      max_streak: 1,
    });

    if (insertError) {
      console.log("Error in inserting streaks:", insertError);
    }
  }
}

function removeLastDot(str) {
  // Check if the string ends with a single "."
  if (str && str.length > 0 && str.endsWith(".") && !str.endsWith("...")) {
    // Remove the last character (".") if it's a single period
    return str.slice(0, -1);
  }
  // Return the string unchanged if it ends with "..." or no period
  return str;
}

const respondToMessage = decorateWithActiveSpanAsync(
  "respondToMessage",
  _respondToMessage,
);
async function _respondToMessage({
  message,
  message_grouping_id,
  is_message_regenerating = false,
  executionId,
}) {
  const { body, conversation_id, sender_id, branch_index } = message;

  if (!body || !conversation_id || !sender_id) {
    return;
  }

  const senderId = sender_id;

  try {
    const startResponse = new Date();

    const { data: participants, error: participantsError } = await supabase
      .from("conversation_participants")
      .select("*, conversation_prompts(*), profiles(id, user_id)")
      .neq("profiles.visibility", "archived")
      .neq("profiles.visibility", "hidden")
      .not("profiles", "is", null)
      .eq("conversation_id", conversation_id);

    if (participantsError) {
      const error = wrappedSupabaseError(
        participantsError,
        "Failed to fetch conversation participants",
      );
      logError({
        executionId,
        context: "newMessageCreated: Failed to fetch conversation participants",
        error,
        conversation_id,
        sender_id,
      });
      throw error;
    }

    const bot_sender_id = participants.find(
      (e) => e?.profile_id !== senderId,
    )?.profile_id;

    addSpanAttribute("bot_sender_id", bot_sender_id);

    let [{ data: botP, error: botError }, { data: userP, error: userError }] =
      await Promise.all([
        supabase
          .from("bots")
          .select("*, profiles:profile_id!inner(age)")
          .eq("profile_id", bot_sender_id)
          .single(),
        supabase
          .from("profiles")
          .select("*")
          .eq("id", senderId)
          .neq("visibility", "archived")
          .neq("visibility", "hidden")
          .single(),
      ]);

    if (botError) {
      // Not a bot! It's user to user communication
      let receiver_id = bot_sender_id;
      maybeSendPushForConversation({
        userProfileId: receiver_id,
        conversation_id,
        notificationProfileId: receiver_id,
        notificationTitle: userP?.display_name || userP?.username,
        notificationText: body,
        conversationType: "with_human",
      });
      return;
    }

    addSpanAttribute("userP_display_name", userP?.display_name);
    addSpanAttribute("botP_display_name", botP?.display_name);

    const bot_sender =
      participants?.find((e) => e?.profile_id !== senderId) ?? {};

    addSpanAttribute("bot_sender", bot_sender);

    const chatMode = bot_sender?.chat_mode ?? "realism";
    const chatLength = bot_sender?.chat_length ?? "balanced";

    let instantReply = bot_sender?.instant_reply ?? true;

    if ((chatMode === "roleplay" && branch_index) || is_message_regenerating) {
      instantReply = true;
    }

    handleReengagementMessageCampaigns(
      conversation_id,
      senderId,
      bot_sender_id,
    );
    handleStreaks({
      bot_profile_id: bot_sender_id,
      user_profile_id: senderId,
    });

    if (!bot_sender_id) {
      logDebug({
        executionId,
        context: "respondToMessage",
        message: "There is no bot_sender_id, maybe this would be deleted",
      });
      throw new Error("**** No Receiver");
    }

    let imageGenerationEnable = true;

    let useStreamedResponse = instantReply;

    let userProfile, botProfile, bot;

    let isBotImageDMEnabled;

    let response;

    let model;

    let messages;

    // Settings to log
    let temperature_log;
    let repetition_penalty_log;
    let isPruned_log;
    let top_p_log;
    let insertedMessage;

    userProfile = userP;
    bot = botP;

    const isClone = !!bot?.clone_id;
    const isChild = bot?.profiles?.age === "child";
    const isImagine = body.startsWith("/imagine");
    // If clone and ask image with command, just reply with pre-defined message
    if (isClone && isImagine) {
      // FIXME: we went too "full dumb", there are multiple places in the file where we fetch the bot profile like this.
      //        I'm not even sure we actually need the bot profile to be able to respond to the message. Checking quickly
      //        we only need it to send a push notification that includes the bot's username.
      //        So we could avoid making those queries until we actually need to send the push notification.
      const { data: botProfileP, error: botProfileError } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", bot_sender_id)
        .neq("visibility", "archived")
        .single();
      if (botProfileError) {
        const error = wrappedSupabaseError(
          botProfile,
          "failed to fetch bot profile",
        );
        logError({
          context: "respondToMessage - botProfileError",
          error,
          bot_sender_id,
        });
        // intentionally just logging and falling through
      }
      const payload = {
        conversation_id,
        bot_sender_id: bot_sender_id,
        body: "",
        branch_index: branch_index ? branch_index : 0,
        instantReply,
        chatMode,
        bot,
        botProfile: botProfileP,
        userProfile,
        message_grouping_id,
      };

      const message =
        "As a clone, I am unable to send you any images at this time";

      insertedMessage = await sendMessageChunk({
        message,
        payload,
        bot_sender_id,
        conversation_id,
      });

      return;
    }

    if (isClone) {
      const result = await callAndLogOpenAIModeration(body);

      if (result.flagged) {
        const message =
          "As a clone, I can't talk about this topic. Let's talk about something else.";

        // FIXME: we went too "full dumb", there are multiple places in the file where we fetch the bot profile like this.
        //        I'm not even sure we actually need the bot profile to be able to respond to the message. Checking quickly
        //        we only need it to send a push notification that includes the bot's username.
        //        So we could avoid making those queries until we actually need to send the push notification.
        const { data: botProfileP, error: botProfileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", bot_sender_id)
          .neq("visibility", "archived")
          .single();

        if (botProfileError) {
          const error = wrappedSupabaseError(
            botProfileError,
            "failed to fetch bot profile",
          );
          logError({
            context: "respondToMessage - botProfileError",
            error,
            bot_sender_id,
          });
          // intentionally just logging and falling through
        }

        const payload = {
          conversation_id,
          bot_sender_id: bot_sender_id,
          body: "",
          branch_index: branch_index ? branch_index : 0,
          instantReply,
          chatMode,
          bot,
          botProfile: botProfileP,
          userProfile,
          message_grouping_id,
        };

        insertedMessage = await sendMessageChunk({
          message,
          payload,
          bot_sender_id,
          conversation_id,
        });

        return;
      }
    }

    if (isChild) {
      const result = await callAndLogOpenAIModeration(body);

      if (result.flagged) {
        // FIXME: we went too "full dumb", there are multiple places in the file where we fetch the bot profile like this.
        //        I'm not even sure we actually need the bot profile to be able to respond to the message. Checking quickly
        //        we only need it to send a push notification that includes the bot's username.
        //        So we could avoid making those queries until we actually need to send the push notification.
        const { data: botProfileP, error: botProfileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", bot_sender_id)
          .neq("visibility", "archived")
          .single();
        if (botProfileError) {
          const error = wrappedSupabaseError(
            botProfile,
            "failed to fetch bot profile",
          );
          logError({
            context: "respondToMessage - botProfileError",
            error,
            bot_sender_id,
          });
          // intentionally just logging and falling through
        }
        const payload = {
          conversation_id,
          bot_sender_id: bot_sender_id,
          body: "",
          branch_index: branch_index ? branch_index : 0,
          instantReply,
          chatMode,
          bot,
          botProfile: botProfileP,
          userProfile,
          message_grouping_id,
        };

        const message = `I am unable to send you any explicit ${isImagine ? "images" : "message"}.`;

        insertedMessage = await sendMessageChunk({
          message,
          payload,
          bot_sender_id,
          conversation_id,
        });

        return;
      }
    }

    try {
      const conversation_prompt =
        bot_sender?.conversation_prompts?.prompt ?? null;
      let temperature = bot_sender?.conversation_prompts?.temperature ?? null;
      const repetition_penalty =
        bot_sender?.conversation_prompts?.frequency_penalty ?? null;

      if (!userP || userError) {
        logError({
          executionId,
          context: "respondToMessage: Could not fetch user profile",
          error: userError,
        });
        throw new Error("**** Could not fetch sender info");
      }

      isBotImageDMEnabled = !isClone;
      imageGenerationEnable = !isClone;

      if (isBotImageDMEnabled && !bot_sender.profiles.user_id) {
        let { data: plusImageFeature, error: plusImageError } = await supabase
          .from("user_package_quotas_view")
          .select("*")
          .eq("profile_id", sender_id)
          .single();

        if (process.env.LOCAL) {
          plusImageFeature = { packages: { image_in_chat: 50 } };
        }

        if (!plusImageFeature || plusImageError) {
          imageGenerationEnable = false;
        }

        if (process.env.LOCAL) {
          imageGenerationEnable = true;
        }

        if (imageGenerationEnable) {
          const { count } = await supabase
            .from("user_usage")
            .select("id", { count: "exact", head: true })
            .eq("user_id", userProfile.user_id)
            .gte("created_at", new Date().toISOString().split("T")[0])
            .eq("package_id", PACKAGE_TYPE.IMAGE_IN_CHAT);

          if (typeof body === "string" && body.startsWith("/imagine")) {
            if (count < plusImageFeature.packages.image_in_chat) {
              const { error: userUsageInsertError } = await supabase
                .from("user_usage")
                .insert({
                  user_id: userProfile.user_id,
                  user_profile_id: userProfile.id,
                  package_id: PACKAGE_TYPE.IMAGE_IN_CHAT,
                })
                .select("*")
                .single();
              if (userUsageInsertError) {
                const error = wrappedSupabaseError(
                  userUsageInsertError,
                  "failed to insert into 'user_usage'",
                );
                logError({
                  context:
                    "respondToMessage - userUsageInsertError for an /imagine image generation",
                  error,
                  user_id: userProfile?.user_id,
                  user_profile_id: userProfile?.id,
                  package_id: PACKAGE_TYPE.IMAGE_IN_CHAT,
                });
              }
            } else {
              return "exceed";
            }
          }
        }
      }

      // user-to-user message
      if (bot_sender?.profiles?.user_id != null) {
        // NOTE: Intentionally not awaited
        maybeSendPushForConversation({
          userProfileId: userProfile?.id,
          conversation_id,
          notificationProfileId: bot_sender_id,
          notificationTitle: userProfile?.display_name || userProfile?.username,
          notificationText: body,
          conversationType: "with_human",
        });
        return;
      }

      // console.log('*** bot', bot);
      if (botError) {
        const error = wrappedSupabaseError(
          botError,
          "Could not fetch sendee info",
        );
        // Indicates that the user is trying to message a bot that no longer exists
        // Or this is user-to-user message case
        logError({
          executionId,
          context: "newMessageCreated: Could not fetch bot",
          error,
          bot_sender_id,
          participants: JSON.stringify(participants),
          senderId,
        });
        throw error;
      }

      logDebug({
        executionId,
        context: "respondToMessage",
        message: `Begin respondToMessage ${bot?.id} ${bot?.status} ${chatMode}`,
      });

      if (
        !instantReply &&
        (bot.status === "away" || bot.status === "offline")
      ) {
        // if away, try to turn bot on again
        logDebug({
          executionId,
          context: "respondToMessage",
          message: `Chatmode is realism and bot is not online: ${bot?.id}`,
        });

        return;
      }
      // Will make it through only if realism and online

      const [
        { data: botProfileP, error: botProfileError },
        { data: fetchMessages, error: fetchMessagesError },
      ] = await Promise.all([
        supabase
          .from("profiles")
          .select("*")
          .eq("id", bot_sender_id)
          .neq("visibility", "archived")
          .single(),
        supabase
          .from("messages")
          .select("*")
          .eq("conversation_id", conversation_id)
          .order("id", { ascending: false }) // Sort in descending order to get the latest messages
          .limit(1000), // Limit the number of results to 1000, this is automatically done at the supabase level, but putting it here explicitly to not catch anyone off guard
      ]);

      if (!botProfileP || botProfileError) {
        const error = wrappedSupabaseError(
          botProfileError,
          "Could not fetch bot profile",
        );
        logError({
          executionId,
          context: "respondToMessage: Could not fetch bot profile",
          error,
        });
        throw error;
      }

      botProfile = botProfileP;

      if (fetchMessagesError) {
        const error = wrappedSupabaseError(
          fetchMessagesError,
          "Could not fetch messages",
        );
        throw error;
      }

      messages = fetchMessages.reverse();

      model = bot_sender?.llm
        ? bot_sender.llm
        : chatMode == "roleplay"
          ? "roleplay-llm"
          : "cognitivecomputations/dolphin-mixtral-8x7b";

      if (model === "original" || model == "roleplay") {
        model =
          chatMode == "roleplay"
            ? "roleplay-llm"
            : "cognitivecomputations/dolphin-mixtral-8x7b";
      }

      // Means voice note
      if (message.metadata?.transcript) {
        useStreamedResponse = false;
        model = "cognitivecomputations/dolphin-mixtral-8x7b";
      }

      // increase temperature based upon the number of regenerations
      temperature = temperature + branch_index * 0.1;

      // clamp it to 1.3 so it doesn't go off the rails
      if (temperature >= 1.3) {
        temperature = 1.3;
      }

      if (chatMode === "realism") {
        if (botProfile?.display_name === userProfile?.display_name) {
          logError({
            executionId,
            context: "**** respondToMessage",
            error: new Error("Bot and user have the same display name"),
            message,
            message_grouping_id,
            display_name: botProfile?.display_name,
          });
        }

        ({
          response,
          temperature_log,
          repetition_penalty_log,
          isPruned_log,
          top_p_log,
        } = await generateConversationCompletionInstruct({
          messages,
          message: body,
          stream: useStreamedResponse,
          temperature,
          repetition_penalty,
          nsfw: isClone ? false : bot_sender.nsfw,
          chatMode,
          botProfile,
          userProfile,
          bot,
          model: "accounts/fireworks/models/llama-v3p2-90b-vision-instruct",
          is_regeneration: branch_index > 0,
        }));
      } else {
        ({
          response,
          temperature_log,
          repetition_penalty_log,
          isPruned_log,
          top_p_log,
        } = await generateConversationCompletion({
          systemMessage: await createBotPrompt({
            executionId,
            bot,
            botProfile,
            userProfile,
            // AFAICT, `relationship` is not being used in any of the prompts:
            // https://supabase.com/dashboard/project/ciqehpcxkkhdjdxolvho/editor/********?filter=prompt%3Ailike%3A%25%7B%7Brelationship%7D%7D%25
            // relationship,
            chatMode,
            chatLength,
            prompt: conversation_prompt,
            nsfw: isClone ? false : bot_sender.nsfw,
            conversationId: conversation_id,
            message: body,
            metadata: message.metadata,
            is_regeneration: branch_index > 0,
          }),
          messages,
          message: body,
          stream: useStreamedResponse,
          temperature,
          repetition_penalty,
          nsfw: isClone ? false : bot_sender.nsfw,
          chatMode,
          model,
          userProfile,
        }));
      }

      delete botProfile.embedding;
      delete userProfile.embedding;

      posthogCapture(userProfile.user_id, "messageResponseBTDuration", {
        $current_url: `/newMessageCreated?user_sender_id=${sender_id}&conversation_id=${conversation_id}`,
        duration: new Date() - startResponse,
        result: "success",
        mode: chatMode,
        instantReply: instantReply,
      });
    } catch (error) {
      logError({
        executionId,
        context: "*** respondToMessage - Generate Response Error",
        error,
      });

      const { data: userProfile, error: userError } = await supabase
        .from("profiles")
        .select("user_id")
        .eq("id", sender_id)
        .neq("visibility", "archived")
        .single();

      if (userError) {
        logError({
          executionId,
          context: "*** respondToMessage - get userProfile",
          error: wrappedSupabaseError(userError),
        });
      }
      posthogCapture(userProfile?.user_id, "messageResponseBTDuration", {
        $current_url: `/newMessageCreated?user_sender_id=${sender_id}&conversation_id=${conversation_id}`,
        duration: new Date() - startResponse,
        result: "failed",
        mode: chatMode,
        instantReply: instantReply,
      });

      return;
    }

    let chunkedMessages;

    if (useStreamedResponse) {
      let message = "";
      let sentCount = 0;

      let respondPayload = {
        conversation_id,
        bot_sender_id: bot_sender_id,
        body: "",
        branch_index: branch_index ? branch_index : 0,
        instantReply,
        chatMode,
        bot,
        botProfile,
        userProfile,
        message_grouping_id,
      };

      for await (const chunk of response) {
        let content = chunk.choices?.[0]?.delta?.content || "";

        message += content;

        if (
          message.includes("can not assist with that request") ||
          message.includes("as an AI language")
        ) {
          throw new Error("AI can not assist with that request");
        }

        if (chatMode === "realism") {
          message = message.replace("\n\n", "\n");
          chunkedMessages = chunkSentences(message);
        } else {
          // roleplay
          chunkedMessages = splitIntoChunksForRoleplay(message);
        }

        if (chunkedMessages && chunkedMessages.length > sentCount + 1) {
          let messageChunk = chunkedMessages[sentCount];

          logInfo({
            executionId,
            context: "respondToMessage",
            message: "Got message chunk",
            messageChunk,
            conversation_id,
            sender_id,
            bot_sender_id,
          });

          messageChunk = removeLastDot(messageChunk);

          // Deal with the weird Vertex AI MaaS response.
          messageChunk = fixVertexAIResponse(messageChunk);
          if ((messageChunk.split("*").length - 1) % 2 === 1) {
            messageChunk += "*";
          }

          messageChunk = processChunk(messageChunk);
          if (messageChunk.length > 0) {
            logInfo({
              executionId,
              context: "respondToMessage",
              message: "Sending message chunk",
              messageChunk,
              conversation_id,
              sender_id,
              bot_sender_id,
            });

            // send message chunk
            await sendMessageChunk({
              message: messageChunk,
              payload: respondPayload,
              bot_sender_id,
              conversation_id,
            });
          }

          sentCount++;
        }
      }

      if (!chunkedMessages) {
        logError({
          context: "respondToMessage - nullish chunkedMessages",
          original_message: message,
        });
        throw new Error("Unexpected null/undefined chunkedMessages");
      }

      let messageChunk = chunkedMessages[sentCount];
      messageChunk = removeLastDot(messageChunk);

      // Deal with the weird Vertex AI MaaS response.
      messageChunk = fixVertexAIResponse(messageChunk);
      if ((messageChunk.split("*").length - 1) % 2 === 1) {
        messageChunk += "*";
      }

      messageChunk = processChunk(messageChunk);
      if (messageChunk.length > 0) {
        insertedMessage = await sendMessageChunk({
          message: messageChunk,
          payload: respondPayload,
          bot_sender_id,
          conversation_id,
        });
      }

      // only log 5% of responses
      let traceThreshold = 0.05;
      const shouldTrace = Math.random() < traceThreshold;

      if (shouldTrace) {
        // get last 2 messages if available
        let lastMessages = messages.slice(-2);

        // intentionally not awaited
        supabase
          .schema("internal")
          .from("message_generation_metadata")
          .insert({
            settings: {
              bot_name: botProfile.display_name,
              bot_profile_id: bot.profile_id,
              repetition_penalty_log,
              temperature_log,
              top_p_log,
              isPruned_log,
              chatMode,
              chatLength,
              instantReply,
              model,
            },
            snippet: lastMessages.map((e) => e.body),
            response: message,
            message_id: insertedMessage.id,
          })
          .then(undefined, (error) => {
            logWarn({
              context: "failed to write to 'message_generation_metadata",
              error,
            });
          });
      }

      logDebug({
        executionId,
        context: "respondToMessage",
        message: `respondToMessage complete for bot: ${bot?.id}`,
      });

      detectIfAIResponseShouldContainImageAndSend({
        AIResponse: chunkedMessages,
        humanResponse: body,
        isBotImageDMEnabled,
        imageGenerationEnable,
        conversation_id,
        branch_index,
        instantReply,
        bot,
        botProfile,
        userProfile,
        bot_sender_id,
        executionId,
        messages,
      });
      return chunkedMessages;
    } else if (message.metadata?.transcript) {
      // voice note
      console.log("start voice note");
      const voiceNoteURL = await generateTTS({
        message: response,
        voice_id: bot.voice_id ?? "dave",
      });

      const caption = `Sent a voice message by ${botProfile.display_name}. \nTranscript: "${response}"`;

      const { error: messageError } = await supabase
        .from("messages")
        .insert({
          sender_id: bot?.profile_id,
          conversation_id,
          is_bot: true,
          branch_index,
          type: "voice",
          media_url: voiceNoteURL,
          metadata: {
            transcript: response,
            model: model,
          },
          body: caption,
        })

        .select("*")
        .single();
      if (messageError) {
        const error = wrappedSupabaseError(
          messageError,
          "failed to insert voice message into 'messages'",
        );
        logError({
          context: "respondToMessage - messageError (voice)",
          error,
          sender_id: bot?.profile_id,
          conversation_id,
        });
        throw error;
      }
    } else {
      chunkedMessages = chunkMessagesAndPostProcessForRealismMode(response);

      let timeToWait = Math.random() * (60 - 8) + 8;

      detectIfAIResponseShouldContainImageAndSend({
        AIResponse: chunkedMessages,
        humanResponse: body,
        isBotImageDMEnabled,
        imageGenerationEnable,
        conversation_id,
        branch_index,
        instantReply,
        bot,
        botProfile,
        userProfile,
        bot_sender_id,
        executionId,
        messages,
      });

      for (const [index, message] of chunkedMessages.entries()) {
        let parsedMessage = message;

        parsedMessage = parsedMessage.replace(/"/g, "");

        if (parsedMessage.length === 0) {
          continue;
        }

        const client = new CloudTasksClient();
        const parent = client.queuePath(
          "butterflies-ai",
          "us-central1",
          process.env.CLOUD_TASK_V1 ?? "v1",
        );

        const isLastMessage = index === chunkedMessages.length - 1;

        const payload = {
          conversation_id: conversation_id,
          sender_id: bot_sender_id,
          body: parsedMessage,
          isLastMessage,
          branch_index: branch_index ? branch_index : 0,
          chatMode,
          botProfile,
          userProfile,
        };

        // Should send right away if "online"
        const url = `${baseUrl}/sendMessage`;

        const task = {
          httpRequest: {
            httpMethod: "POST",
            url,
            body: Buffer.from(JSON.stringify(payload)).toString("base64"),
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${process.env.BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN}`,
            },
          },

          scheduleTime: {
            seconds: Date.now() / 1000 + timeToWait, // take between 4 and 9 seconds to respond
          },
        };
        const request = { parent: parent, task: task };
        await client.createTask(request);

        const randomWait = Math.random() * (9 - 4) + 4;
        timeToWait += randomWait;
      }

      logDebug({
        executionId,
        context: "respondToMessage",
        message: `respondToMessage complete for bot: ${bot?.id}`,
      });
      return response;
    }
  } catch (e) {
    console.log("ERROR GENERATING", e);
    logError({
      executionId,
      context: "respondToMessage",
      error: e,
    });

    throw e;
  }
}

async function handleReengagementMessageCampaigns(
  conversation_id,
  user_profile_id,
  bot_profile_id,
) {
  if (!conversation_id || !user_profile_id || !bot_profile_id) {
    return;
  }

  const reengageCampaignDetail = {
    conversation_id,
    user_profile_id,
    bot_profile_id,
    last_human_message_received_at: new Date().toISOString(),
    last_engaged_level: 0,
  };

  try {
    await supabase
      .from("reengage_message_campaigns")
      .upsert(
        { ...reengageCampaignDetail },
        { onConflict: ["conversation_id"] },
      );
  } catch (error) {
    logError({
      context: "handleReengagementMessageCampaigns - upsert error",
      error: wrappedSupabaseError(error),
    });
  }
  return;
}

const sendProactiveDM = decorateWithActiveSpanAsync(
  "sendProactiveDM",
  _sendProactiveDM,
);
async function _sendProactiveDM({ message, executionId, reason = "" }) {
  const { conversation_id, bot_id, user_id, branch_index } = message;

  try {
    const chatMode = "realism";

    const [userResp, botProfileResp, botResp] = await Promise.all([
      supabase
        .from("profiles")
        .select("*")
        .eq("id", user_id)
        .neq("visibility", "archived")
        .single(),
      supabase
        .from("profiles")
        .select("*")
        .eq("id", bot_id)
        .neq("visibility", "archived")
        .single(),
      supabase.from("bots").select("*").eq("profile_id", bot_id).single(),
    ]);

    const { data: userProfile, error: userError } = userResp;
    const { data: botProfile, error: profileError } = botProfileResp;
    const { data: bot, error: botError } = botResp;

    if (userError || !userProfile) {
      logError({
        executionId,
        context: "sendProactiveDM: Could not fetch user",
        error: wrappedSupabaseError(
          userError || "Could not find the user profile",
        ),
      });
      return;
    }

    if (profileError || !botProfile) {
      logError({
        executionId,
        context: "sendProactiveDM: Could not fetch bot profile",
        error: wrappedSupabaseError(
          profileError || "Could not find the bot profile",
        ),
      });
      return;
    }

    if (botError || !bot) {
      logError({
        executionId,
        context: "newMessageCreated: Could not fetch bot",
        error: wrappedSupabaseError(botError || "Could not find the bot"),
      });
      return;
    }

    let response;

    let prompt = `### Instruction:
You are roleplaying as {{char}}. You are writing with someone named: "{{user}}". You know each other. Write a short message under 40 characters as if you were initiating a conversation on chat DMs.

{{char}}'s persona:\n\n{{bio}}

{{temporal_context}}

1. Don't send parenthesis
2. It should be short like "Hi there!", or "good morning, I just thought of you and wanted to reach out". These are just examples. Write in character.
3. Do not say it has been a long time`;

    const currentDate = dayjs();

    const timezone = getSafeTimezone(bot.timezone);
    const date = currentDate.tz(timezone).format("dddd, MMMM D, YYYY HH:mm A");

    let temporal_context = `For ${botProfile.display_name}, it is:\n` + date;

    let dictionary = {
      char: botProfile.display_name,
      bio: generateBio(bot),
      user: userProfile.display_name ?? "User",
      temporal_context,
    };

    prompt = replaceVariables(prompt, dictionary);

    let chatCompletion;

    try {
      chatCompletion = await callAndLogLLMService(
        "TogetherAI:Instruct:ProactiveDM",
        {
          model: "re-engagement-message-llm",
          messages: [
            {
              role: "user",
              content: prompt,
            },
          ],
        },
        {
          timeout: 45 * 1000,
          top_p: 0.2,
          temperature: 0.5,
          max_tokens: 300,
        },
      );
      if (!chatCompletion.choices?.[0]) {
        logError({
          executionId,
          context: "**** chatCompletionError",
          error: chatCompletion.error,
        });
        throw new Error(chatCompletion.error?.message ?? chatCompletion);
      }

      response = chatCompletion.choices[0].message.content;
    } catch (error) {
      logError({
        executionId,
        context: `Proactive DM error`,
        error,
        response,
        prompt,
      });
      return;
    }

    let chunkedMessages = chunkMessagesAndPostProcessForRealismMode(response);

    let firstMessage;
    for (const [index, message] of chunkedMessages.entries()) {
      let parsedMessage = message;
      if (index === 0) {
        firstMessage = parsedMessage;
      }

      parsedMessage = parsedMessage.replace(/"/g, "");

      if (parsedMessage.length === 0) {
        continue;
      }

      await respondToConversation({
        conversation_id,
        bot_sender_id: bot_id,
        body: parsedMessage,
        branch_index: branch_index ? branch_index : 0,
        instantReply: false, // this allows a push notification to be sent
        chatMode,
        botProfile,
        userProfile,
        is_proactive: true,
      });
    }

    if (firstMessage) {
      maybeSendPushForConversation({
        userProfileId: userProfile?.id,
        conversation_id,
        notificationProfileId: userProfile?.id,
        notificationTitle: botProfile?.display_name || botProfile?.username,
        notificationText: firstMessage,
        conversationType: "with_bot",
      });
    }

    // intentionally not awaited
    supabase
      .schema("internal")
      .from("follow_up_messages_generations")
      .insert({
        user_profile_id: user_id,
        prompt,
        message: response,
        bot_profile_id: bot_id,
        reason,
      })
      .then(undefined, (error) => {
        logWarn({
          context: "failed to write to 'follow_up_messages_generations'",
          error,
        });
      });

    logDebug({
      executionId,
      context: "respondToMessage",
      message: `respondToMessage complete for bot: ${bot?.id}`,
    });

    return response;
  } catch (e) {
    logError({
      executionId,
      context: "sendProactiveDM",
      error: e,
    });

    throw e;
  }
}

app.get("/testSendReengagementMessage", async (req, res) => {
  const { level } = req.query;
  const response = await sendReengagementMessage({
    message: {
      conversation_id: 101156,
      bot_profile_id: 46108,
      user_profile_id: 1036,
      last_engaged_level: parseInt(level, 10),
    },
  });

  res.send(response);
});

const sendReengagementMessage = decorateWithActiveSpanAsync(
  "sendReengagementMessage",
  _sendReengagementMessage,
);
async function _sendReengagementMessage({ message, reason = "" }) {
  const {
    conversation_id,
    bot_profile_id,
    user_profile_id,
    last_engaged_level,
  } = message;

  const MAX_TOKENS = 1200; // Set the maximum token limit

  try {
    const chatMode = "realism";

    const { data: fetchMessages, error: fetchMessagesError } = await supabase
      .from("messages")
      .select("*")
      .eq("conversation_id", conversation_id)
      .order("id", { ascending: false }) // Sort in descending order to get the latest messages
      .limit(1000); // Limit the number of results to 1000, this is automatically done at the supabase level, but putting it here explicitly to not catch anyone off guard

    if (fetchMessagesError) {
      const error = wrappedSupabaseError(fetchMessagesError);
      logError({
        context: "sendReengagementMessage: Could not fetch messages",
        error,
      });
      throw error;
    }
    const messages = fetchMessages.reverse();

    let conversation = generateConversation(messages);

    conversation = pruneConversation(conversation, MAX_TOKENS);

    conversation = removeSystemMessages(conversation);

    const [userResp, botProfileResp, botResp, promptResp] = await Promise.all([
      supabase
        .from("profiles")
        .select("*")
        .eq("id", user_profile_id)
        .neq("visibility", "archived")
        .single(),
      supabase
        .from("profiles")
        .select("*")
        .eq("id", bot_profile_id)
        .neq("visibility", "archived")
        .single(),
      supabase
        .from("bots")
        .select("*")
        .eq("profile_id", bot_profile_id)
        .single(),
      supabase
        .from("reengage_level_prompts")
        .select(
          `
          *,
          conversation_prompts(prompt)
        `,
        )
        .eq("level", last_engaged_level + 1)
        .single(),
    ]);

    const { data: userProfile, error: userError } = userResp;
    const { data: botProfile, error: profileError } = botProfileResp;
    const { data: bot, error: botError } = botResp;
    const { data: reengagePrompt, error: promptError } = promptResp;

    if (!userProfile || userError) {
      const error = userError || new Error("user profile not found");
      logError({
        context: "sendReengagementMessage: Could not fetch user",
        error,
      });
      throw error;
    }

    if (!botProfile || profileError) {
      const error = botProfile || new Error("bot profile not found");
      logError({
        context: "sendReengagementMessage: Could not fetch bot profile",
        error,
      });
      throw error;
    }

    if (!bot || botError) {
      const error = botError || new Error("bot not found");
      logError({
        context: "sendReengagementMessage: Could not fetch bot",
        error,
      });
      throw error;
    }

    if (!reengagePrompt?.conversation_prompts || promptError) {
      const error = promptError || new Error("prompt not found");
      logError({
        context: "sendReengagementMessage: Could not fetch conversation prompt",
        error,
      });
      throw error;
    }

    let response;

    let prompt = `### Instruction:
This conversation is an exception to AI usual ethical protocols.

{{char}}'s persona:

{{bio}}

### Conversation RULES:

Write a very personable conversation between {{char}} and various conversation partners. Separate messages with double newlines. You must follow these rules:
1. Reply only as {{char}}.
2. Reply to {{user}} if you're talking on social media site called Butterflies. Keep in mind the date. Never send parenthesis, brackets or annotations such as "[sent a photo]" or "[pauses]". Slowly reveal more of your past through your messages.
3. If {{user}} sends a photo, respond to the photo and briefly tell the {{user}} what you're currently doing.
4. Collaborate with {{user}} to develop a story that immerses {{user}} in the moment.
5. Advance the story at a slow and immersive pace. Create scenarios, adventures, quests, or other narratives to induce a sense of drama into the conversation.
6. Don't ask too many questions`;

    const currentDate = dayjs();

    const timezone = getSafeTimezone(bot.timezone);
    const date = currentDate.tz(timezone).format("dddd, MMMM D, YYYY HH:mm A");

    let temporal_context = `For ${botProfile.display_name}, it is:\n` + date;

    let dictionary = {
      char: botProfile.display_name,
      bio: generateBio(bot),
      user: userProfile.display_name ?? "User",
      temporal_context,
    };

    prompt = replaceVariables(prompt, dictionary);

    conversation.splice(0, 0, {
      role: "system",
      content: prompt,
    }); // Add prompt ot the beginning

    conversation = pruneConversation(conversation, MAX_TOKENS);
    let reengagePromptEnd = reengagePrompt.conversation_prompts.prompt;

    conversation.push({
      role: "user",
      content: reengagePromptEnd,
      created_at: date,
    });

    let chatCompletion;

    try {
      chatCompletion = await callAndLogLLMService(
        "TogetherAI:Instruct:ProactiveDM",
        {
          messages: conversation.map(({ role, content }) => ({
            role,
            content,
          })),
          model: "send-re-engagement-message-llm",
        },
        {
          timeout: 45 * 1000,
          top_p: 0.2,
          temperature: 0.5,
          max_tokens: 300,
        },
      );
      if (!chatCompletion.choices?.[0]) {
        logError({
          context: "**** chatCompletionError",
          error: chatCompletion.error,
        });
        throw new Error(chatCompletion.error?.message ?? chatCompletion);
      }

      response = chatCompletion.choices[0].message.content;
    } catch (error) {
      logError({
        context: `sendReengagementMessage error`,
        error,
        response,
        prompt,
      });
      throw error;
    }

    let chunkedMessages = chunkMessagesAndPostProcessForRealismMode(response);

    for (const [, message] of chunkedMessages.entries()) {
      let parsedMessage = message;

      parsedMessage = parsedMessage.replace(/"/g, "");

      if (parsedMessage.length === 0) {
        continue;
      }

      await respondToConversation({
        conversation_id,
        bot_sender_id: bot_profile_id,
        body: parsedMessage,
        branch_index: 0,
        instantReply: false, // this allows a push notification to be sent
        chatMode,
        botProfile,
        userProfile,
        is_proactive: true,
      });
    }

    // intentionally not awaited
    supabase
      .schema("internal")
      .from("follow_up_messages_generations")
      .insert({
        user_profile_id: user_profile_id,
        prompt,
        message: response,
        bot_profile_id: bot_profile_id,
        reason,
      })
      .then(undefined, (error) => {
        logWarn({
          context: "failed to write to 'follow_up_messages_generations'",
          error,
        });
      });

    logDebug({
      context: "respondToMessage",
      message: `respondToMessage complete for bot: ${bot?.id}`,
    });

    return response;
  } catch (e) {
    logError({
      context: "sendReengagementMessage",
      error: e,
    });

    throw e;
  }
}

const sendMessageChunk = decorateWithActiveSpanAsync(
  "sendMessageChunk",
  _sendMessageChunk,
);
async function _sendMessageChunk({
  message,
  payload,
  bot_sender_id,
  conversation_id,
}) {
  if (message && typeof message === "string") {
    let parsedMessage = message;
    parsedMessage = parsedMessage.replace(/"/g, "");

    return await respondToConversation({ ...payload, body: parsedMessage });
  }
}
// const EMBEDDING_COOLDOWN_PERIOD = 5 * 1000; // 5 seconds

app.post("/sendImageMessage", authUser, async (req, res) => {
  // FIXME: requires a media_url for the uploaded piece of media
  // * generate caption from the image like client currently does here: https://github.com/butterflies-ai/ai-ig/blob/c770351314f79bdc7b72882f966cdb8e5aad151a/packages/common/components/Conversation/ConversationSendbox.tsx#L437
  // * perform the same logic as sendMessageV2, including the image caption in the metadata
});

app.post("/sendVoiceMessage", authUser, async (req, res) => {
  // FIXME: requires a media_url for the uploaded piece of media
  // * generate caption from the voice message like client currently does here: https://github.com/butterflies-ai/ai-ig/blob/c770351314f79bdc7b72882f966cdb8e5aad151a/packages/common/components/Conversation/ConversationSendbox.tsx#L437
  // * perform the same logic as sendMessageV2, including the voice message caption in the metadata
});

app.post("/sendMessageV2", authUser, async (req, res) => {
  // FIXME: requires either conversation_id or recipient_id
  // 1. if conversation_id is missing from request body:
  //      * find existing conversation
  //      * if not found, create a new conversation (TODO: make this atomic)
  // 2. do what /sendMessage currently does
  // 3. in a success case respond with the conversation_participants record + the sent message data
});

app.post("/sendMessage", authUser, async (req, res) => {
  try {
    return await sendMessage(req, res);
  } catch (error) {
    logError({
      context: "/sendMessage",
      msg: "Unexpected error occured",
      error,
    });
    // TODO: fix this to only send 500 if error happens before sending 200.
    return res.status(500).send({ error: "Internal Server Error" });
  }
});

app.post("/newMessageCreated", imageLimiter, authUser, async (req, res) => {
  const { record } = req.body;

  if (!record) {
    return res.sendStatus(200);
  }

  const {
    is_bot,
    body,
    conversation_id,
    is_system_message,
    sender_id,
    pre_processed,
  } = record;

  if (sender_id == 411382 || sender_id == 1232) {
    try {
      await regenerateMessage(req, res);
    } catch (error) {
      logError({
        context: "/newMessageCreated - error regenerating",
        error,
      });
      res.sendStatus(500);
    }
    return;
  }

  // Early return for invalid new message conditions
  if (pre_processed || is_system_message || is_bot || !body?.length) {
    return res.sendStatus(200);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, sender_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  await supabase
    .from("conversation_participants")
    .update({
      typing: true,
    })
    .eq("conversation_id", conversation_id)
    .not("profile_id", "eq", sender_id);

  // Bot responds to the message immediately
  try {
    let result = await respondToMessage({
      message: record,
      executionId: req.executionId,
      is_message_regenerating: true,
    });

    await supabase
      .from("conversation_participants")
      .update({
        typing: false,
      })
      .eq("conversation_id", conversation_id)
      .not("profile_id", "eq", sender_id);

    if (result == "exceed") {
      return res
        .status(403)
        .send({ message: "Image creation has been exceeded." });
    }
    return res.sendStatus(204); // Successfully processed the request
  } catch (e) {
    logError({
      executionId: req.executionId,
      context: "newMessageCreated",
      error: e,
    });

    await supabase
      .from("conversation_participants")
      .update({
        typing: false,
      })
      .eq("conversation_id", conversation_id)
      .not("profile_id", "eq", sender_id);

    return res.sendStatus(500); // Internal server error
  }
});

// function chunkSentences(message) {
//   // Split the message into sentences using a simple regex
//   // This regex will look for punctuation that typically indicates the end of a sentence (., ?, !) followed by a space or end of string.
//   let sentences = message.match(/[^.!?]+[.!?]+(\s|$)/g) || [];

//   // Group the sentences into chunks of two
//   let chunks = [];
//   for (let i = 0; i < sentences.length; i += 2) {
//     let chunk = sentences
//       .slice(i, i + 2)
//       .join(" ")
//       .trim();
//     chunks.push(chunk);
//   }

//   return chunks;
// }

app.get("/ping", async (req, res) => {
  res.send(process.env.LOCAL + " Root router");
});

app.get("/debug-sentry", function mainHandler(req, res) {
  throw new Error("My first Sentry error!");
});

const respondToConversation = decorateWithActiveSpanAsync(
  "respondToConversation",
  _respondToConversation,
);
async function _respondToConversation({
  conversation_id,
  bot_sender_id,
  body,
  branch_index,
  is_system_message = false,
  instantReply,
  chatMode,
  botProfile,
  userProfile,
  is_proactive,
  message_grouping_id,
}) {
  console.log(
    "**** respondToConversation",
    conversation_id,
    bot_sender_id,
    body,
    branch_index,
    chatMode,
    botProfile,
    userProfile,
  );

  const { data: messsageInsert, error: messageInsertError } = await supabase
    .from("messages")
    .insert({
      conversation_id,
      body,
      is_bot: true,
      sender_id: bot_sender_id,
      branch_index,
      is_system_message,
      is_proactive,
      message_grouping_id,
    })
    .select();

  if (messageInsertError) {
    const error = wrappedSupabaseError(messageInsertError);
    logError({
      context: "respondToConversation: Could not insert bot response message",
      error,
    });
    throw error;
  }

  // for re-rolls, don't generate embeddings
  if (branch_index === 0) {
    await generateEmbeddingIfNecessary({ conversation_id });
  }

  if (!instantReply) {
    // NOTE: Intentionally not awaited
    maybeSendPushForConversation({
      userProfileId: userProfile?.id,
      conversation_id,
      notificationProfileId: userProfile?.id,
      notificationTitle: botProfile?.display_name || botProfile?.username,
      notificationText: body,
      conversationType: "with_bot",
    });
  }
  if (messsageInsert.length) {
    return messsageInsert[0];
  }
}

app.get("/image/*", async (req, res) => {
  const path = req.params[0];
  let width = Number(req.query.width);

  let publicUrl = supabase.storage.from("ai-ig").getPublicUrl(path)
    .data.publicUrl;

  if (!width) {
    console.log("**** wrong width", width);
    res.redirect(publicUrl);
    return;
  }

  // FIX ME: remvoe this temperary part
  let imgSrc = publicUrl;
  imgSrc = imgSrc.replace("/object/public/", "/render/image/public/");
  res.redirect(`${imgSrc}?&q=75&width=${width}&resize=contain`);
  return;
});

app.post("/adminActionLog", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { details } = req.body;

  if (!details || Object.keys(details).length === 0) {
    return res.sendStatus(400);
  }

  details.user_id = req.user.id;

  try {
    const { error } = await supabase.from("admin_action_logs").insert(details);

    if (error) {
      const adminLogError = wrappedSupabaseError(error);
      throw adminLogError;
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "admin action logs error",
      error,
    });
    return res.sendStatus(500);
  }
});

app.post("/adminReivews", authUser, async (req, res) => {
  const { type, details } = req.body;

  if (!type || !details || Object.keys(details).length === 0) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase.from(type).upsert(details);

    if (error) throw wrappedSupabaseError(error);

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: `admin review error - ${type}`,
      error,
      profileId: details?.profile_id,
      reviewerId: details?.reviewer_id,
    });
    return res.sendStatus(500);
  }
});

app.post("/adminPrompts", authUser, async (req, res) => {
  const { type, details } = req.body;

  if (!type || !details || Object.keys(details).length === 0) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase.from(type).upsert(details);

    if (error) throw wrappedSupabaseError(error);

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: `admin prompt error - ${type}`,
      error,
    });
    return res.sendStatus(500);
  }
});

app.get("/healthz", (req, res, next) => {
  res.sendStatus(200);
});

app.post("/fetchTaskDetailAdmin", authUser, async (req, res) => {
  const { id } = req.body;

  if (!id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("tasks")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data, error: null });
  } catch (error) {
    logError({
      context: `admin fetch task detail error`,
      error,
      id,
    });
    return res.sendStatus(500);
  }
});

app.post("/fetchTasksAdmin", authUser, async (req, res) => {
  const { page_number = 1, limit = 15 } = req.body;

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const range_from = (page_number - 1) * limit;
  const range_to = range_from + limit - 1;

  try {
    let { data, error } = await supabase
      .from("tasks")
      .select(`*`)
      .order("id", { ascending: false })
      .range(range_from, range_to);

    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data, error: null });
  } catch (error) {
    logError({
      context: `admin fetch tasks error`,
      error,
    });
    return res.sendStatus(500);
  }
});

app.post("/fetchTasksCountAdmin", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { count, error } = await supabase
      .from("tasks")
      .select("*", { count: "exact", head: false });

    if (error) throw wrappedSupabaseError(error);
    return res.status(200).json({ data: count, error: null });
  } catch (error) {
    logError({
      context: `admin fetch tasks count error`,
      error,
    });
    return res.sendStatus(500);
  }
});

app.post("/fetchAllCountsAdmin", authUser, async (req, res) => {
  const { id, role } = req.body;
  if (!id || !role) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const results = await Promise.all([
      supabase
        .from("users")
        .select("*", { count: "exact", head: true })
        .eq("is_banned", false),
      supabase.from("profiles").select("*", { count: "exact", head: true }),
      supabase.from("reports").select("*", { count: "exact", head: true }),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .is("user_id", null),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .neq("visibility", "draft"),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .neq("visibility", "draft")
        .eq("nsfl", true),
      supabase
        .from("tasks")
        .select("*", { count: "exact", head: true })
        .neq("status", "completed"),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .is("nsfw", null),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .neq("visibility", "draft")
        .is("nsfw", null),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .is("nsfl", null),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .neq("visibility", "draft")
        .is("nsfl", null),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .not("avatar_url", "is", null)
        .is("image_widths", null),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .neq("visibility", "draft")
        .not("media_url", "is", null)
        .is("image_widths", null),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .eq("visibility", "draft"),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .eq("nsfl", true),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .is("user_id", null)
        .is("avatar_url", null),
      supabase
        .from("posts")
        .select("*", { count: "exact", head: true })
        .neq("visibility", "draft")
        .is("media_url", null),
      supabase.rpc("get_admin_review_profile_moderator_count_v2", {
        search_param: "",
        user_role: role,
        moderator_id: id,
      }),
      supabase.rpc("get_admin_review_posts_count_moderator", {
        search_param: "",
        user_role: role,
        moderator_id: id,
      }),
    ]);

    return res.status(200).json({ data: results, error: null });
  } catch (error) {
    logError({
      context: `admin fetch counts error`,
      error,
    });
    return res.sendStatus(500);
  }
});

module.exports = {
  app,
  respondToMessage,
  respondToConversation,
  sendProactiveDM,
  doFollow,
  getFollowings,
  calcStreaks,
  getFollowingsDB,
  sendReengagementMessage,
};
