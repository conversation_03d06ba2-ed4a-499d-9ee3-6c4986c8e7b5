function faceIdPrompt({
  prompt,
  seed,
  nsfw,
  model = "photogasm",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.random() * 100000000000,
        steps: 20,
        cfg: 7,
        sampler_name: "euler",
        scheduler: "karras",
        denoise: 1,
        model: ["11", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${nsfw ? "" : "nsfw"}`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    10: {
      inputs: {
        lora_name: "ip-adapter-faceid-plusv2_sd15_lora.safetensors",
        strength_model: 0.2,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    11: {
      inputs: {
        weight: 0.5,
        noise: 0,
        weight_type: "original",
        start_at: 0,
        end_at: 1,
        faceid_v2: true,
        weight_v2: 3,
        unfold_batch: true,
        ipadapter: ["12", 0],
        clip_vision: ["13", 0],
        insightface: ["14", 0],
        image: ["25", 0],
        model: ["10", 0],
      },
      class_type: "IPAdapterApplyFaceID",
      _meta: {
        title: "Apply IPAdapter FaceID",
      },
    },
    12: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sd15.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "Load IPAdapter Model",
      },
    },
    13: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    14: {
      inputs: {
        provider: "CUDA",
      },
      class_type: "InsightFaceLoader",
      _meta: {
        title: "Load InsightFace",
      },
    },
    21: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["8", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    23: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    25: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "LoadImage //Inspire",
      },
    },
  };
}

module.exports = { faceIdPrompt };
