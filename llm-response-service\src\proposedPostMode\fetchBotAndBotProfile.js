const { logWarn } = require("../logUtils");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");

async function fetchBotAndBotProfile({ botProfileId }) {
  if (!botProfileId) {
    throw new Error("no botProfileId provided");
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      "*, profiles:profile_id!inner(id, visibility, nsfw, nsfl, cyoa_mode, proposed_post_mode, proposed_post_next_generation_date)",
    )
    .neq("profiles.visibility", "archived")
    .eq("profile_id", botProfileId)
    .maybeSingle();

  if (botError) {
    const error = wrappedSupabaseError(botError);
    throw error;
  }

  if (!bot) {
    logWarn({
      context: "fetchBotAndBotProfile",
      message: `no bot found for botProfileId ${botProfileId}`,
    });
    return;
  }

  const botProfile = bot.profiles[0] ?? bot.profiles;
  botProfile.profile_id = botProfile.id;
  if (!botProfile) {
    throw new Error(`no bot profile for botProfileId ${botProfileId}`);
  }

  return { bot, botProfile };
}

module.exports = {
  fetchBotAndBotProfile,
};
