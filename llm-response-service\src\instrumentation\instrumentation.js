require("dotenv").config();

/**
 * BUTTERFLIES: This file is adapted from
 * https://github.com/GoogleCloudPlatform/opentelemetry-operations-js/blob/0e5b46ceeb7022e8fe0164c1b7edfd246d62430d/samples/instrumentation-quickstart/src/instrumentation.ts#L16C1-L102C59
 *
 * which has the below header comment:
 */
/**
 * This file is adapted from
 * https://github.com/open-telemetry/opentelemetry-js-contrib/blob/9df30ea0fd822a69203b818b8fbe34e1e1c8bced/metapackages/auto-instrumentations-node/src/register.ts
 * and only needed until https://github.com/open-telemetry/opentelemetry-js/issues/4551 is
 * fixed. Then we can move to `--require @opentelemetry/auto-instrumentations-node/register`
 */

const { diag } = require("@opentelemetry/api");
const {
  getNodeAutoInstrumentations,
  getResourceDetectors: getResourceDetectorsFromEnv,
} = require("@opentelemetry/auto-instrumentations-node");
const {
  OTLPTraceExporter,
} = require("@opentelemetry/exporter-trace-otlp-grpc");
const {
  OTLPMetricExporter,
} = require("@opentelemetry/exporter-metrics-otlp-grpc");
const {
  ConsoleMetricExporter,
  PeriodicExportingMetricReader,
} = require("@opentelemetry/sdk-metrics");
const opentelemetrySDKNode = require("@opentelemetry/sdk-node");
const {
  // SimpleSpanProcessor,
  BatchSpanProcessor,
  InMemorySpanExporter,
} = require("@opentelemetry/sdk-trace-node");
const {
  RuntimeNodeInstrumentation,
} = require("@opentelemetry/instrumentation-runtime-node");
const { logError, logWarn, logInfo, logDebug } = require("../logUtils");

/////////////////////////////////////////////////////////////////////////////////
function getMetricReader() {
  // Can be removed after https://github.com/open-telemetry/opentelemetry-js/issues/4562
  const exportIntervalMillis = process.env.OTEL_METRIC_EXPORT_INTERVAL
    ? Number.parseFloat(process.env.OTEL_METRIC_EXPORT_INTERVAL)
    : undefined;
  const readerOptions = {
    exportIntervalMillis,
  };
  switch (process.env.OTEL_METRICS_EXPORTER) {
    case undefined:
    case "":
    case "otlp":
      diag.info("using otlp metrics exporter");
      return new PeriodicExportingMetricReader({
        ...readerOptions,
        exporter: new OTLPMetricExporter({
          url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT,
        }),
      });
    case "console":
      diag.info("using console metrics exporter");
      return new PeriodicExportingMetricReader({
        ...readerOptions,
        exporter: new ConsoleMetricExporter(),
      });
    case "none":
      diag.info("disabling metrics reader");
      return undefined;
    case "prometheus":
      diag.info("using prometheus metrics exporter");
      return exports.prometheusExporter;
    default:
      throw Error(
        `no valid option for OTEL_METRICS_EXPORTER: ${process.env.OTEL_METRICS_EXPORTER}`,
      );
  }
}

const OTEL_DIAG_LOG_CONTEXT = "OTEL diag";
class CocoonDiagLogger {
  error = (message, ...args) => {
    logError({
      context: OTEL_DIAG_LOG_CONTEXT,
      message,
      ...args,
    });
  };
  warn = (message, ...args) => {
    logWarn({
      context: OTEL_DIAG_LOG_CONTEXT,
      message,
      ...args,
    });
  };
  info = (message, ...args) => {
    logInfo({
      context: OTEL_DIAG_LOG_CONTEXT,
      message,
      ...args,
    });
  };
  debug = (message, ...args) => {
    logDebug({
      context: OTEL_DIAG_LOG_CONTEXT,
      message,
      ...args,
    });
  };
  verbose = (message, ...args) => {
    logDebug({
      context: OTEL_DIAG_LOG_CONTEXT,
      message,
      ...args,
    });
  };
}

let instrumentationCleanup;
// [START opentelemetry_instrumentation_setup_opentelemetry]
diag.setLogger(
  new CocoonDiagLogger(), // using our own logger that calls into logError, logWarn, logInfo, logDebug to get structured logs in cloud logging
  opentelemetrySDKNode.core.getEnv().OTEL_LOG_LEVEL,
);

// print all env variables
diag.info("OTEL env vars:");
for (const key in process.env) {
  if (key.startsWith("OTEL")) {
    diag.info("ENV: " + key + "=" + process.env[key]);
  }
}

if (process.env.OTEL_EXPORTER_OTLP_ENDPOINT) {
  diag.info(
    "OTEL_EXPORTER_OTLP_ENDPOINT is set, configuring OpenTelemetry SDK",
  );
  let traceExporter;
  if (
    !process.env.OTEL_TRACES_EXPORTER ||
    process.env.OTEL_TRACES_EXPORTER === "" ||
    process.env.OTEL_TRACES_EXPORTER === "otlp"
  ) {
    diag.info("using otlp trace exporter");
    traceExporter = new OTLPTraceExporter({
      url: process.env.OTEL_EXPORTER_OTLP_ENDPOINT,
    });
  } else if (process.env.OTEL_TRACES_EXPORTER === "memory") {
    diag.info("using in-memory trace exporter");
    traceExporter = new InMemorySpanExporter();
  } else if (process.env.OTEL_TRACES_EXPORTER === "none") {
    diag.info("OTEL_TRACES_EXPORTER set to 'none', not exporting traces");
    traceExporter = undefined;
  } else {
    throw Error(
      `no valid option for OTEL_TRACES_EXPORTER: ${process.env.OTEL_TRACES_EXPORTER}`,
    );
  }
  let spanProcessors;
  if (traceExporter) {
    spanProcessors = [
      // new SimpleSpanProcessor(traceExporter), // Use SimpleSpanProcessor to send spans to OpenTelemetry immediately
      new BatchSpanProcessor(traceExporter, {
        maxExportBatchSize: 2048,
        maxQueueSize: 8192,
      }),
    ];
  } else {
    spanProcessors = undefined;
  }
  const sdk = new opentelemetrySDKNode.NodeSDK({
    instrumentations: [
      ...getNodeAutoInstrumentations({
        // TODO: we're not using pino yet
        // "@opentelemetry/instrumentation-pino": {
        //     enabled: true,
        // },
        "@opentelemetry/instrumentation-http": {
          enabled: true,
          ignoreIncomingRequestHook: (req) =>
            req.url === "/metrics" ||
            req.url === "/healthz" ||
            req.url === "/v1/healthz",
          ignoreOutgoingRequestHook: (req) => {
            // Ignore GCS metadata requests
            if (req.pathname) {
              const isGCSMetadataRequest =
                req.pathname.startsWith("/computeMetadata");
              return isGCSMetadataRequest;
            } else {
              return false;
            }
          },
        },
        "@opentelemetry/instrumentation-fs": { enabled: false },
        "@opentelemetry/instrumentation-net": {
          enabled: false,
        },
        "@opentelemetry/instrumentation-dns": {
          enabled: false,
        },
        "@opentelemetry/instrumentation-grpc": {
          enabled: true,
          ignoreGrpcMethods: [
            // we don't want to include Google Cloud Logging grpc calls in our telemetry
            "google.logging.v2.LoggingServiceV2.WriteLogEntries",
            "google.logging.v2.LoggingServiceV2/WriteLogEntries",
            "WriteLogEntries",
          ],
        },
        "@opentelemetry/instrumentation-redis-4": {
          enabled: true,
          requireParentSpan: true,
        },
      }),
      new RuntimeNodeInstrumentation({
        eventLoopUtilizationMeasurementInterval: 5000,
      }),
    ],
    resourceDetectors: getResourceDetectorsFromEnv(),
    metricReader: getMetricReader(),
    spanProcessors: spanProcessors,
  });
  try {
    sdk.start();
    diag.info("OpenTelemetry automatic instrumentation started successfully");
  } catch (error) {
    diag.error(
      "Error initializing OpenTelemetry SDK. Your application is not instrumented and will not produce telemetry",
      error,
    );
  }
  // Gracefully shut down the SDK to flush telemetry when the program exits
  instrumentationCleanup = async () => {
    try {
      await sdk.shutdown();
      diag.debug("OpenTelemetry SDK terminated");
    } catch (error) {
      diag.error("Error terminating OpenTelemetry SDK", error);
    }
  };
} else {
  diag.info("OTEL_EXPORTER_OTLP_ENDPOINT NOT set, not setting up the SDK");
  instrumentationCleanup = async () => {};
}
// [END opentelemetry_instrumentation_setup_opentelemetry]

module.exports = {
  instrumentationCleanup,
};
