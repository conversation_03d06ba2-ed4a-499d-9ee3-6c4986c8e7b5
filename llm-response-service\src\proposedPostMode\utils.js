const { logError, logInfo } = require("../logUtils");
const { countProposedPosts } = require("./countProposedPosts");
const { wrappedSupabaseError, supabase } = require("../supabaseClient");
const {
  scheduleProposedPostGenerationTask,
} = require("./scheduleProposedPostGenerationTask");

function isProposedPost(post) {
  if (!post) {
    logError({
      context: "isProposedPost",
      message: "post is undefined!",
      post: post,
    });
    return false;
  }

  if (typeof post.proposed_post_state === "undefined") {
    logError({
      context: "isProposedPost",
      message:
        "proposed_post_state is undefined, make sure your query includes the field",
      post: post,
    });
    return false;
  }

  return post.proposed_post_state !== null;
}

function getGenerationTriggerDate({ proposedPostNextGenerationDate }) {
  // 10 minutes before proposedPostNextGenerationDate
  const generationTriggerDate = new Date(
    proposedPostNextGenerationDate.getTime() - 10 * 60 * 1000,
  );
  return generationTriggerDate;
}

async function bumpProposedPostGenerationDate({ botProfileId }) {
  const existingProposedPostsCount = await countProposedPosts({ botProfileId });

  let hoursOffset;
  if (existingProposedPostsCount < 2) {
    hoursOffset = 1;
  } else if (existingProposedPostsCount < 3) {
    hoursOffset = 2;
  } else {
    hoursOffset = 24;
  }

  logInfo({
    context: "bumpProposedPostGenerationDate",
    message: `bumping proposed post generation date by ${hoursOffset} hours...`,
    botProfileId,
    existingProposedPostsCount,
    hoursOffset,
  });

  const proposedPostNextGenerationDate = new Date(
    Date.now() + hoursOffset * 60 * 60 * 1000,
  );
  const generationTriggerDate = getGenerationTriggerDate({
    proposedPostNextGenerationDate,
  });

  const { data, error: updateError } = await supabase
    .from("profiles")
    .update({
      proposed_post_next_generation_date:
        proposedPostNextGenerationDate.toISOString(),
    })
    .eq("id", botProfileId);

  if (updateError) {
    const error = wrappedSupabaseError(updateError);
    logError({
      context: "bumpProposedPostGenerationDate",
      error,
    });
    throw error;
  }

  await scheduleProposedPostGenerationTask({
    botProfileId,
    scheduleDate: generationTriggerDate,
  });

  return data;
}

module.exports = {
  isProposedPost,
  getGenerationTriggerDate,
  bumpProposedPostGenerationDate,
};
