const { supabase } = require("../supabaseClient");
const { generateVignetteScenes } = require("./generateVignetteScenes");
const { createVignetteForPost } = require("./vignettes");

/**
 * Test function to generate vignettes for a given post ID
 *
 * @param {string} postId - The post ID to generate vignettes for
 * @param {number} numScenes - Number of scenes to generate (default: 3)
 * @param {boolean} saveToDatabase - Whether to save the vignettes to the database (default: false)
 * @returns {Promise<Object>} The generated vignette data
 */
async function testVignetteGeneration({
  postId,
  numScenes,
  saveToDatabase = false,
}) {
  try {
    console.log(`Starting vignette generation test for post ID: ${postId}`);
    console.log(`Number of scenes: ${numScenes}`);
    console.log(`Save to database: ${saveToDatabase ? "Yes" : "No"}`);

    // Fetch the post data
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select("*")
      .eq("id", postId)
      .single();

    if (postError) {
      console.error("Error fetching post:", postError);
      throw postError;
    }

    if (!post) {
      throw new Error(`No post found with ID: ${postId}`);
    }

    console.log(`Successfully fetched post: ${post.id}`);
    console.log(`Post description: "${post.description}"`);
    console.log(`Post AI caption: "${post.ai_caption}"`);

    let scenes;
    // Save to database if requested
    if (saveToDatabase) {
      console.log(
        `\nGenerating ${numScenes} vignette scenes and saving them to database...`,
      );

      const result = await createVignetteForPost({
        post,
        numScenes,
      });
      scenes = result.scenes;
    } else {
      console.log(`\nGenerating ${numScenes} vignette scenes...`);

      scenes = await generateVignetteScenes({
        post,
        numScenes,
      });
    }

    console.log(`Successfully generated ${scenes.length} vignette scenes`);

    // Display the generated scenes
    scenes.forEach((scene, index) => {
      console.log(`\n--- Scene ${index + 1} ---`);
      console.log(`Description: ${scene.scene_description}`);
      console.log(`Caption: ${scene.scene_caption}`);
      console.log(`Most interesting: ${scene.most_interesting}`);
      console.log(`Contains character: ${scene.contains_character}`);
    });
    return { scenes };
  } catch (error) {
    console.error("Error in testVignetteGeneration:", error);
    throw error;
  }
}

// Check if this script is being run directly
if (require.main === module) {
  // Get post ID from command line arguments or use a default
  const postId = process.argv[2] || "YOUR_DEFAULT_POST_ID";
  const numScenes = parseInt(process.argv[3] || "3", 10);
  const saveToDatabase = (process.argv[4] || "false").toLowerCase() === "true";

  console.log("=== Vignette Generation Test ===");

  testVignetteGeneration({
    postId,
    numScenes,
    saveToDatabase,
  })
    .then((result) => {
      console.log("\n=== Test Completed Successfully ===");
      if (!saveToDatabase) {
        console.log("To save these vignettes to the database, run:");
        console.log(
          `node testVignetteGeneration.js ${postId} ${numScenes} true`,
        );
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n=== Test Failed ===");
      console.error(error);
      process.exit(1);
    });
} else {
  // Export the function if this file is imported as a module
  module.exports = {
    testVignetteGeneration,
  };
}
