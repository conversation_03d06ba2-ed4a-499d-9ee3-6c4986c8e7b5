# Recraft API Integration

This integration allows the Boba service to generate images using the Recraft AI API.

## Configuration

To use the Recraft API, you need to set the following environment variable:

```
RECRAFT_AUTH_TOKEN=your_recraft_auth_token
```

You can get your <PERSON>craft auth token by logging into the Recraft platform.

## Usage

To generate images with <PERSON><PERSON>, send a POST request to the `/boba/generateImage` endpoint with the provider set to "recraft":

```json
{
  "prompt": "a woman wearing a lacy dress with cleavage",
  "provider": "recraft",
  "rendering_style": "photo realistic",
  "num_generations": 1,
  "aspect_ratio": "1:1"
}
```

## Rendering Styles

Recraft supports different rendering styles, which are mapped to specific style IDs:

| Rendering Style | Style ID                             |
| --------------- | ------------------------------------ |
| photo realistic | b9af225b-6b98-43d0-a4b3-16343641e852 |

More style mappings will be added as they become available.

## Implementation Details

- Recraft always uses the "recraftv3" model, regardless of the model parameter passed in the request.
- The `guidance_scale` and `num_inference_steps` parameters are not used by Recraft but are included for API consistency.
- Aspect ratios are supported: "1:1", "16:9", and "9:16".
