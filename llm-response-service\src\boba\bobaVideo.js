/**
 * Boba Video Routes
 *
 * This module provides routes for video generation in the boba service.
 */

const express = require("express");
const multer = require("multer");
const {
  generateVideoFromPrompt,
  pollVideoStatus,
} = require("./bobaVideoHelpers");
const { supabase } = require("../supabaseClient");
const { logError } = require("../utils");

const app = express.Router();

// Set up multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10 MB limit
  },
});

/**
 * Generate a video from a prompt
 *
 * @route POST /generateVideoFromPrompt
 * @param {string} prompt - The prompt to generate a video from
 * @param {string} [image_url] - Optional URL of an existing image to use instead of generating one
 * @param {File} [image] - Optional image file to use instead of generating an image (multipart/form-data)
 * @returns {Object} The request ID for the video generation
 */
app.post(
  "/generateVideoFromPrompt",
  upload.single("image"),
  async (req, res) => {
    console.log("[generateVideoFromPrompt] Starting text-to-video generation");
    try {
      // Get prompt and image_url from either form data or JSON body
      const prompt = req.body.prompt;
      const image_url = req.body.image_url;

      // Get file from multer if it exists
      const imageFile = req.file;

      if (!prompt) {
        return res.status(400).json({
          error: "Missing required parameter: prompt",
        });
      }

      const { requestId, imageUrl, imagePrompt, shotAction, soundEffects } =
        await generateVideoFromPrompt(prompt, image_url, imageFile);

      // Return success response
      return res.status(200).json({
        success: true,
        requestId,
        imageUrl,
        imagePrompt,
        shotAction,
        soundEffects,
      });
    } catch (error) {
      console.error("[generateVideoFromPrompt] Error:", error);
      return res.status(500).json({
        error: error.message || "Internal server error",
        stack: error.stack,
      });
    }
  },
);

/**
 * Get video status
 *
 * @route GET /getVideoStatus
 * @param {string} requestId - The request ID to get status for
 * @returns {Object} The status of the video generation
 */
app.get("/getVideoStatus", async (req, res) => {
  try {
    const { requestId } = req.query;

    if (!requestId) {
      return res.status(400).json({
        success: false,
        error: "Request ID is required",
      });
    }

    const statusResponse = await pollVideoStatus(requestId);

    let ret = statusResponse.data;

    if (statusResponse.data.status === "completed") {
      const { data } = await supabase
        .from("video_generation_tasks")
        .select("*")
        .eq("request_id", requestId)
        .single();

      ret = { ...data, ...ret };
    }

    return res.status(200).json({
      success: true,
      status: ret,
    });
  } catch (error) {
    logError({
      message: "Error getting video status",
      error,
    });

    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = {
  app,
};
