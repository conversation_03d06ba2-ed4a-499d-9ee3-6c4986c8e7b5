/**
 * Boba Image Helpers
 *
 * This module provides helper functions for image generation in the boba service.
 */

const axios = require("axios");
const { decorateWithActiveSpanAsync } = require("../instrumentation/tracer");
const { logInfo, logError } = require("../utils");
const { RunwareServer } = require("@runware/sdk-js");
const RecraftClient = require("./recraft");

/**
 * ModelManager class to handle model mappings and configurations
 */
class ModelManager {
  constructor() {
    this.modelMappings = {
      // Default models
      flux: "runware:101@1",
      realvisxl: "civitai:139562@361593",
      pony: "civitai:257749@290640",
      autism: "civitai:288584@324619",
      autism_8: "civitai:288584@379262",
    };

    // Default settings for different models
    this.modelSettings = {
      flux: {
        scheduler: "FlowMatchEulerDiscreteScheduler",
        steps: 28,
        cfgScale: 3.5,
        maxWidth: 1024,
        maxHeight: 1024,
      },
      realvisxl: {
        scheduler: "default",
        steps: 5,
        cfgScale: 1.5,
        maxWidth: 1024,
        maxHeight: 1024,
      },
      pony: {
        scheduler: "EulerAncestralDiscreteScheduler",
        steps: 30,
        cfgScale: 8.5,
        maxWidth: 1024,
        maxHeight: 1024,
        clipSkip: 2,
      },
      autism: {
        scheduler: "default",
        steps: 30,
        cfgScale: 7.5,
        maxWidth: 1024,
        maxHeight: 1024,
      },
    };

    // Default dimensions for different aspect ratios per model
    this.aspectRatioDimensions = {
      flux: {
        "1:1": { width: 1024, height: 1024 },
        "16:9": { width: 1024, height: 576 },
        "9:16": { width: 576, height: 1024 },
      },
      realvisxl: {
        "1:1": { width: 1024, height: 1024 },
        "16:9": { width: 1024, height: 576 },
        "9:16": { width: 576, height: 1024 },
      },
      pony: {
        "1:1": { width: 1024, height: 1024 },
        "16:9": { width: 1024, height: 576 },
        "9:16": { width: 576, height: 1024 },
      },
      autism: {
        "1:1": { width: 1024, height: 1024 },
        "16:9": { width: 1024, height: 576 },
        "9:16": { width: 576, height: 1024 },
      },
    };

    // Recraft style ID mappings
    this.recraftStyleMappings = {
      "photo realistic": "b9af225b-6b98-43d0-a4b3-16343641e852",
      // Add more style mappings as they become available
    };
  }

  /**
   * Get the model ID for a given model name
   * @param {string} modelName - The model name
   * @returns {string} The model ID
   */
  getModelId(modelName) {
    return this.modelMappings[modelName.toLowerCase()] || modelName;
  }

  /**
   * Get the settings for a given model
   * @param {string} modelName - The model name
   * @param {Object} overrideSettings - Settings to override the defaults
   * @returns {Object} The model settings
   */
  getModelSettings(modelName, overrideSettings = {}) {
    const defaultSettings =
      this.modelSettings[modelName.toLowerCase()] || this.modelSettings.flux;
    return {
      ...defaultSettings,
      ...overrideSettings,
    };
  }

  /**
   * Get dimensions for a specific model and aspect ratio
   * @param {string} modelName - The model name
   * @param {string} aspectRatio - The aspect ratio (e.g., "1:1", "16:9", "9:16")
   * @returns {Object} The width and height
   */
  getDimensions(modelName, aspectRatio) {
    const modelKey = modelName.toLowerCase();
    const modelDimensions =
      this.aspectRatioDimensions[modelKey] || this.aspectRatioDimensions.flux;
    const dimensions = modelDimensions[aspectRatio] || modelDimensions["9:16"]; // Default to 9:16 if aspect ratio not found

    return dimensions;
  }

  /**
   * Get model-specific prompt prefix
   * @param {string} modelName - The model name
   * @param {string} renderingStyle - The rendering style
   * @returns {string} The prompt prefix for the model
   */
  getPromptPrefix(modelName, renderingStyle = "") {
    const model = modelName.toLowerCase();
    let prefix = "";

    // Add model-specific prefixes
    if (model === "autism") {
      prefix += "score_9, score_7_up, ";
    }

    // Add rendering style specific prefixes
    if (
      renderingStyle === "anime" &&
      (model === "pony" || model === "autism")
    ) {
      prefix = prefix + "source_anime, ";
    }

    return prefix;
  }

  /**
   * Get model-specific negative prompt
   * @param {string} modelName - The model name
   * @returns {string} The negative prompt for the model
   */
  getNegativePrompt(modelName) {
    const model = modelName.toLowerCase();
    if (model === "autism") {
      return "score_5, score_4, score_3, monochrome, 3d";
    }
    return "";
  }

  /**
   * Get the Recraft style ID for a given rendering style
   * @param {string} renderingStyle - The rendering style
   * @returns {string} The Recraft style ID
   */
  getRecraftStyleId(renderingStyle) {
    return (
      this.recraftStyleMappings[renderingStyle] ||
      this.recraftStyleMappings["photo realistic"]
    );
  }

  /**
   * Register a new model mapping
   * @param {string} modelName - The model name
   * @param {string} modelId - The model ID
   * @param {Object} settings - The model settings
   * @param {Object} dimensions - The model dimensions for different aspect ratios
   */
  registerModel(modelName, modelId, settings = {}, dimensions = {}) {
    const modelKey = modelName.toLowerCase();
    this.modelMappings[modelKey] = modelId;
    this.modelSettings[modelKey] = {
      ...this.modelSettings.flux, // Use flux as default base
      ...settings,
    };

    if (Object.keys(dimensions).length > 0) {
      this.aspectRatioDimensions[modelKey] = {
        ...this.aspectRatioDimensions.flux, // Use flux as default base
        ...dimensions,
      };
    }
  }

  /**
   * Get all available model names
   * @returns {Array<string>} Array of model names
   */
  getAvailableModels() {
    return Object.keys(this.modelMappings);
  }
}

// Create a singleton instance of the ModelManager
const modelManager = new ModelManager();

// Initialize Runware client
const runware = new RunwareServer({
  apiKey: process.env.RUNWARE_API_KEY || "5s4fLsHINOb4G5WAM2NV7NqNnlVDjaJz",
});

/**
 * Generate images using Runware
 * @param {Object} options - Image generation options
 * @param {string} options.prompt - The prompt to generate images from
 * @param {string} [options.aspect_ratio="9:16"] - The aspect ratio of the image
 * @param {number} [options.guidance_scale=3.5] - The guidance scale for image generation
 * @param {number} [options.num_inference_steps=30] - The number of inference steps
 * @param {number} [options.num_generations=1] - The number of images to generate
 * @param {number} [options.seed] - The seed for image generation
 * @param {string} [options.model="flux"] - The model to use for image generation
 * @param {string} [options.rendering_style] - The rendering style
 * @returns {Promise<Array<Object>>} An array of image objects with buffer and seed properties
 */
async function _generateImagesWithRunware({
  prompt,
  aspect_ratio = "9:16",
  guidance_scale = 3.5,
  num_inference_steps = 30,
  num_generations = 1,
  seed,
  model = "flux",
  rendering_style,
}) {
  if (!prompt) {
    throw new Error("Prompt is required for image generation");
  }

  // Get dimensions for the model and aspect ratio
  const dimensions = modelManager.getDimensions(model, aspect_ratio);

  const images = [];

  // Generate a random seed for each image if seed is not provided
  const getRandomSeed = () => Math.floor(Math.random() * 2147483647);
  const seeds = seed
    ? Array(num_generations).fill(seed)
    : Array(num_generations)
        .fill()
        .map(() => getRandomSeed());

  try {
    logInfo({
      message: `Generating ${num_generations} images with Runware`,
      prompt,
      aspect_ratio,
      guidance_scale,
      num_inference_steps,
      seeds,
      model,
      rendering_style,
    });

    // Ensure connection to Runware
    await runware.ensureConnection();

    // Get model settings
    const modelId = modelManager.getModelId(model);
    const modelSettings = modelManager.getModelSettings(model, {
      steps: num_inference_steps,
      cfgScale: guidance_scale,
    });

    let finalPrompt =
      modelManager.getPromptPrefix(model, rendering_style) + prompt;
    let negativePrompt = modelManager.getNegativePrompt(model);

    // Generate images
    for (let i = 0; i < num_generations; i++) {
      const runwareParams = {
        positivePrompt: finalPrompt,
        width: dimensions.width,
        height: dimensions.height,
        model: modelId,
        numberResults: 1,
        outputType: "URL",
        outputFormat: "PNG",
        steps: modelSettings.steps,
        CFGScale: modelSettings.cfgScale,
        scheduler: modelSettings.scheduler,
        seed: seeds[i],
      };

      // Add clipSkip if it exists in the model settings
      if (modelSettings.clipSkip) {
        runwareParams.clipSkip = modelSettings.clipSkip;
      }

      // Add negative prompt if it exists
      if (negativePrompt) {
        runwareParams.negativePrompt = negativePrompt;
      }

      console.log("Runware params:", runwareParams);

      const runwareImages = await runware.requestImages(runwareParams);

      console.log("runwareImages", runwareImages);

      if (runwareImages && runwareImages.length > 0) {
        // Download the image from the URL
        const imageResponse = await axios.get(runwareImages[0].imageURL, {
          responseType: "arraybuffer",
        });

        images.push({
          buffer: imageResponse.data,
          seed: seeds[i],
        });

        logInfo({
          message: `Successfully generated image ${i + 1}/${num_generations}`,
          seed: seeds[i],
          url: runwareImages[0].imageURL,
        });
      } else {
        logError({
          message: `Failed to generate image ${i + 1}/${num_generations}`,
          seed: seeds[i],
        });
      }
    }

    return { images, finalPrompt };
  } catch (error) {
    logError({
      message: "Error generating images with Runware",
      error,
      prompt,
    });
    return [];
  }
}

const generateImagesWithRunware = decorateWithActiveSpanAsync(
  "generateImagesWithRunware",
  _generateImagesWithRunware,
);

/**
 * Generate images using Fireworks AI
 * @param {Object} options - Image generation options
 * @param {string} options.prompt - The prompt to generate images from
 * @param {string} [options.aspect_ratio="9:16"] - The aspect ratio of the image
 * @param {number} [options.guidance_scale=3.5] - The guidance scale for image generation
 * @param {number} [options.num_inference_steps=30] - The number of inference steps
 * @param {number} [options.num_generations=1] - The number of images to generate
 * @param {number} [options.seed] - The seed for image generation
 * @param {string} [options.model="flux"] - The model to use for image generation
 * @param {string} [options.rendering_style] - The rendering style
 * @returns {Promise<Array<Object>>} An array of image objects with buffer and seed properties
 */
async function _generateImagesWithFireworks({
  prompt,
  aspect_ratio = "9:16",
  guidance_scale = 3.5,
  num_inference_steps = 30,
  num_generations = 1,
  seed,
  model = "flux",
  rendering_style,
}) {
  if (!prompt) {
    throw new Error("Prompt is required for image generation");
  }

  try {
    const seeds = [];
    const imageBuffers = [];

    // Get width and height based on aspect ratio
    modelManager.getDimensions(model, aspect_ratio);

    // Add model-specific prompt prefix if needed
    const finalPrompt =
      modelManager.getPromptPrefix(model, rendering_style) + prompt;
    const negativePrompt = modelManager.getNegativePrompt(model);

    // Generate random seeds if not provided
    if (seed) {
      for (let i = 0; i < num_generations; i++) {
        seeds.push(seed + i);
      }
    } else {
      for (let i = 0; i < num_generations; i++) {
        seeds.push(Math.floor(Math.random() * 1000000));
      }
    }

    console.log("Generating images with Fireworks AI:", {
      prompt: finalPrompt,
      aspect_ratio,
      guidance_scale,
      num_inference_steps,
      seeds,
    });

    for (let i = 0; i < num_generations; i++) {
      const fireworksData = {
        prompt: finalPrompt,
        aspect_ratio,
        guidance_scale,
        num_inference_steps,
        seed: seeds[i],
        negative_prompt: negativePrompt,
      };

      console.log("fireworksData", fireworksData);

      const fireworksResponse = await axios.post(
        "https://api.fireworks.ai/inference/v1/completions",
        fireworksData,
        {
          headers: {
            "Content-Type": "application/json",
            Accept: "image/png",
            Authorization: `Bearer ${process.env.FIREWORKS_API_KEY ?? "twXcAfk7EAJrjdGqRiDz8fGs0ghPvcNmlE2gANDua4q1KCoq"}`,
          },
          responseType: "arraybuffer",
        },
      );

      if (fireworksResponse.status === 200) {
        imageBuffers.push({
          buffer: fireworksResponse.data,
          seed: seeds[i],
        });

        logInfo({
          message: `Successfully generated image ${i + 1}/${num_generations}`,
          seed: seeds[i],
        });
      } else {
        logError({
          message: `Failed to generate image ${i + 1}/${num_generations}`,
          status: fireworksResponse.status,
          seed: seeds[i],
        });
      }
    }

    return { images: imageBuffers, finalPrompt };
  } catch (error) {
    logError({
      message: "Error generating images with Fireworks AI",
      error,
      prompt,
    });
    return [];
  }
}

const generateImagesWithFireworks = decorateWithActiveSpanAsync(
  "generateImagesWithFireworks",
  _generateImagesWithFireworks,
);

/**
 * Generate images using Recraft AI
 * @param {Object} options - Image generation options
 * @param {string} options.prompt - The prompt to generate images from
 * @param {string} [options.aspect_ratio="9:16"] - The aspect ratio of the image
 * @param {number} [options.guidance_scale] - Not used by Recraft but included for API consistency
 * @param {number} [options.num_inference_steps] - Not used by Recraft but included for API consistency
 * @param {number} [options.num_generations=1] - The number of images to generate
 * @param {number} [options.seed] - The seed for image generation
 * @param {string} [options.model] - Not used directly as Recraft always uses "recraftv3"
 * @param {string} [options.rendering_style="photo realistic"] - The rendering style to use
 * @returns {Promise<Object>} Object containing images array with buffer and seed properties, and finalPrompt
 */
async function _generateImagesWithRecraft({
  prompt,
  aspect_ratio = "9:16",
  guidance_scale,
  num_inference_steps,
  num_generations = 1,
  seed,
  model,
  rendering_style = "photo realistic",
}) {
  try {
    // Get dimensions based on aspect ratio
    let width = 1024;
    let height = 1024;

    if (aspect_ratio === "16:9") {
      width = 1024;
      height = 576;
    } else if (aspect_ratio === "9:16") {
      width = 576;
      height = 1024;
    }

    // Get style ID from the ModelManager
    const styleId = modelManager.getRecraftStyleId(rendering_style);

    // Initialize Recraft client with auth token from environment
    const recraftClient = new RecraftClient(
      process.env.RECRAFT_AUTH_TOKEN ??
        "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Inl6LVhoT1NKdnZPcUtZUUVLTE42SzQzckVYOU56U013UWd3UXVUS0hsbVUifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ABJ5w2IU-WeUq89TXd1cN2EYWi4gPv2dWMmMoffIYp2n4-5Ia7rdCmlizwyMDAY9H4ZHYDILM2JOo9lohevWHX9MWxz-8hJZWZyrVN5NolZmr13iLz4vNsMOQyNJQHuLUOZlUNpQPtjAFOzV_pby4wPQLGzKb8E_xu19hrkUKiyfIHXbFLpbGB9jN-FoAdeGXn0e7gKl4LTx9LesSkbtSi61qMySiN747U12ntbaH9CN5Wsb2LYI0fyjnjytoxyj09bWBEc_q-g3-RmmTOy3OpKvdriNPLkruKd0UoAlJRIGzjC8_t9LdMXqWTAYIpKQZPMziryNFzp9Y1hZfryFAg",
    );

    // Generate images
    const results = [];
    const randomSeed = seed || Math.floor(Math.random() * **********);

    for (let i = 0; i < num_generations; i++) {
      // Use a different seed for each generation if multiple are requested
      const currentSeed = seed ? seed + i : randomSeed + i;

      const result = await recraftClient.generateImage({
        prompt,
        layerSize: { width, height },
        randomSeed: currentSeed,
        styleId,
        numImagesPerPrompt: 1,
        transformModel: "recraftv3", // Always use recraftv3 as specified
      });

      if (result.images && result.images.length > 0) {
        // Download the image from the URL
        const imageUrl = recraftClient.getImageUrl(result.images[0].image_id);
        const imageResponse = await axios.get(imageUrl, {
          responseType: "arraybuffer",
        });

        results.push({
          buffer: Buffer.from(imageResponse.data),
          seed: currentSeed,
        });
      }
    }

    return {
      images: results,
      finalPrompt: prompt,
    };
  } catch (error) {
    logError({
      message: "Error generating images with Recraft",
      error,
    });
    throw error;
  }
}

const generateImagesWithRecraft = decorateWithActiveSpanAsync(
  "generateImagesWithRecraft",
  _generateImagesWithRecraft,
);

module.exports = {
  generateImagesWithFireworks,
  generateImagesWithRunware,
  generateImagesWithRecraft,
  modelManager,
};
