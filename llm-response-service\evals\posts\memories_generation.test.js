jest.skip();

jest.setTimeout(30 * 1000);

const {
  generateMemoriesContentFromLLM,
  rewriteMemory,
  generatePostDetailsWithLLM,
} = require("../../src/botHelpers.js");

const {
  jill,
  dr,
  karen,
  vu_mariner,
  harry_pooper,
  nokia,
} = require("../common/personas.js");

test("it should generate posts with fewer fires (vibes)", async () => {
  const result = await generateMemoriesContentFromLLM({
    bot: { ...harry_pooper },
  });

  console.log("result", result);

  expect(result).toEqual(result);

  const continuation = await generateMemoriesContentFromLLM({
    bot: { ...harry_pooper },
    lastMemory: result.memories[result.memories.length - 1],
  });

  console.log("continuation", continuation);

  expect(continuation).toEqual(continuation);
});

test.skip("it should test generating post from memories", async () => {
  const memories = [
    {
      description:
        "Just had the most epic squat in the Gryffindor common room! No one suspected a thing, but I think I might've left a little present on the couch. Oops!",
      location: "Gryffindor common room, Hogwarts",
      context:
        "<PERSON> <PERSON>oper is trying to keep his squatting habits a secret from his friends.",
      visual:
        "<PERSON> Pooper squatting behind a couch, looking around nervously, with a mischievous grin on his face, in a dimly lit room with a fireplace and Hogwarts banners hanging on the walls.",
    },
    {
      description:
        "Uh oh, <PERSON> just asked me if I knew anything about the 'mysterious stain' on the couch. I think I might've gotten a bit too good at lying... Told him it was probably just a spilled Butterbeer.",
      location: "Gryffindor common room, Hogwarts",
      context: "Harry Pooper is trying to cover up his squatting incident.",
      visual:
        "Harry Pooper and Ron Weasley sitting on the couch, looking at the stain, with Harry trying to maintain a straight face, while Ron looks suspicious, in a dimly lit room with a fireplace and Hogwarts banners hanging on the walls.",
    },
    {
      description:
        "Hermione just cornered me in the library and asked me point-blank if I was the one who left the 'present' on the couch. I think I might've gotten myself into a bit of a web of lies... Squatting in the library seems like a bad idea right now.",
      location: "Hogwarts library, Hogwarts",
      context: "Harry Pooper is getting caught in his own lies.",
      visual:
        "Harry Pooper and Hermione Granger standing in the library, surrounded by shelves of books, with Hermione looking stern and Harry looking sheepish, in a quiet and dimly lit room with a large wooden table in the background.",
    },
  ];

  let { postDetails, prompt } = await generatePostDetailsWithLLM({
    bot: { ...harry_pooper },
    memory: memories[0],
  });

  console.log("POST DETAILS", postDetails);

  expect(postDetails).toMatch(/(poop on a rock)/i);
});

test.skip("it should rewrite memories", async () => {
  const result = await rewriteMemory({
    reason: "This should occur in space",
    memory: {
      description: "I'm under the sea swimming with the fishes.",
      location: "under the sea",
      context: "Jill is scuba diving.",
    },
  });

  console.log("RESULT", result);

  expect(result.description).toMatch(/(Space|space)/i);
});
