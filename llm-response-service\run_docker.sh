#!/bin/bash

docker stop butterflies_api_container
docker rm butterflies_api_container

# Define your image and container names
IMAGE_NAME="butterflies_api_image"
CONTAINER_NAME="butterflies_api_container"

# Path to your Dockerfile (if it's in the same directory, just use ".")
DOCKERFILE_PATH="."

# Build the Docker image
echo "Building Docker image..."
docker build -t $IMAGE_NAME $DOCKERFILE_PATH

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Docker image built successfully."
else
    echo "Error building Docker image."
    exit 1
fi

# Run the Docker container
echo "Running Docker container..."
docker run -d \
  -p 8080:8080 \
  -e PROJECT_ID=butterflies-ai \
  -e SUPABASE_URL="https://ciqehpcxkkhdjdxolvho.supabase.co" \
  -e SUPABASE_SECRET_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNpcWVocGN4a2toZGpkeG9sdmhvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5NTA2ODgyMiwiZXhwIjoyMDEwNjQ0ODIyfQ.lk-fZcA-A8yUhIdR_MAiBKSzn47IuHZPCDUH_FEY0Ac" \
  -e GOOGLE_APPLICATION_CREDENTIALS="./google.json" \
  -e OPENAI_API_KEY="***************************************************" \
  -e CLOUD_TASK_V1_IMAGE_GENERATION="v1-image-generation" \
  -e CLOUD_TASK_V1="v1" \
  -e SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNpcWVocGN4a2toZGpkeG9sdmhvIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTUwNjg4MjIsImV4cCI6MjAxMDY0NDgyMn0.FB_5BGU67kNaH_Wi1ZW_GxWrvrxNsj_zK5cEqww9U8c" \
  -e GPT_TEXT_MODEL="gpt-3.5-turbo-0125" \
  -e CLOUD_TASK_V1_RUN_BOTS="v1-run-bots" \
  --name $CONTAINER_NAME $IMAGE_NAME

# Check if container started successfully
if [ $? -eq 0 ]; then
    echo "Docker container started successfully."
    echo "Your Express API should now be accessible at http://localhost:8080"
else
    echo "Error running Docker container."
    exit 1
fi
