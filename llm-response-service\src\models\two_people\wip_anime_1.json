{"last_node_id": 105, "last_link_id": 133, "nodes": [{"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": {"0": 315, "1": 106}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2, 54], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 1024, 1]}, {"id": 21, "type": "UltralyticsDetectorProvider", "pos": [1168, 605], "size": {"0": 315, "1": 78}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [18], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["segm/person_yolov8m-seg.pt"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": {"0": 210, "1": 46}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10, 19], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 30, "type": "SegsToCombinedMask", "pos": [2595, 649], "size": {"0": 210, "1": 26}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 26}], "outputs": [{"name": "MASK", "type": "MASK", "links": [28, 37], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}}, {"id": 43, "type": "EmptyImage", "pos": [2742, 898], "size": {"0": 315, "1": 130}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [35], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [768, 1024, 1, 255]}, {"id": 26, "type": "SegsToCombinedMask", "pos": [2590, 324], "size": {"0": 210, "1": 26}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 22}], "outputs": [{"name": "MASK", "type": "MASK", "links": [23, 42], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}}, {"id": 50, "type": "ImageCompositeMasked", "pos": [3531, 341], "size": {"0": 315, "1": 146}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 40, "slot_index": 0}, {"name": "source", "type": "IMAGE", "link": 76, "slot_index": 1}, {"name": "mask", "type": "MASK", "link": 42, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [43], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 10, "type": "PreviewImage", "pos": [1608, -149], "size": {"0": 621.2821044921875, "1": 475.7831115722656}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 47, "type": "ImageCompositeMasked", "pos": [2956, 797], "size": {"0": 315, "1": 146}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 35, "slot_index": 0}, {"name": "source", "type": "IMAGE", "link": 36, "slot_index": 1}, {"name": "mask", "type": "MASK", "link": 37, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [40], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 33, "type": "MaskToImage", "pos": [2860, 680], "size": {"0": 210, "1": 26}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 28}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [36, 46, 75], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 79, "type": "PreviewImage", "pos": [3499.7288818359375, 148.83082580566406], "size": {"0": 210, "1": 246}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 77}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 51, "type": "PreviewImage", "pos": [4087, 296], "size": {"0": 210, "1": 246}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 43}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 80, "type": "CLIPVisionLoader", "pos": [3637, 836], "size": {"0": 315, "1": 58}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [80], "shape": 3}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"]}, {"id": 81, "type": "PreviewImage", "pos": [3513.9392700195312, 1173.3584594726562], "size": {"0": 210, "1": 246}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 81}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 75, "type": "ToIPAdapterPipe //Inspire", "pos": [4042, 718], "size": {"0": 330, "1": 86}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "ipadapter", "type": "IPADAPTER", "link": 108, "slot_index": 0}, {"name": "model", "type": "MODEL", "link": 85, "slot_index": 1}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 80, "slot_index": 2}, {"name": "insightface", "type": "INSIGHTFACE", "link": null}], "outputs": [{"name": "IPADAPTER_PIPE", "type": "IPADAPTER_PIPE", "links": [68], "shape": 3}], "properties": {"Node name for S&R": "ToIPAdapterPipe //Inspire"}}, {"id": 87, "type": "UltralyticsDetectorProvider", "pos": [4844, 511], "size": {"0": 315, "1": 78}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [91], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6, 53, 99, 125], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 27, "type": "MaskToImage", "pos": [2863, 377], "size": {"0": 228.43026733398438, "1": 29.10413360595703}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 23, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [44, 76, 77], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 63, "type": "PrepImageForClipVision", "pos": [3145, 1567], "size": {"0": 315, "1": 106}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 58, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [61, 120], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "center", 0]}, {"id": 60, "type": "PreviewImage", "pos": [6613, -967], "size": {"0": 739.8458251953125, "1": 970.06494140625}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 57}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 99, "type": "CLIPVisionLoader", "pos": [4959, 1217], "size": {"0": 315, "1": 58}, "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [113, 121], "shape": 3}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"]}, {"id": 86, "type": "ImpactSimpleDetectorSEGS", "pos": [6189, -238], "size": {"0": 315, "1": 310}, "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 91, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 90, "slot_index": 1}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [92, 101], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 101, "type": "IPAdapterAdvanced", "pos": [6410, 964], "size": {"0": 315, "1": 278}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 118}, {"name": "ipadapter", "type": "IPADAPTER", "link": 119, "slot_index": 1}, {"name": "image", "type": "IMAGE", "link": 120, "slot_index": 2}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": null}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 121, "slot_index": 5}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [122], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [1, "linear", "concat", 0, 1, "V only"]}, {"id": 102, "type": "PreviewImage", "pos": [9178.970214355468, 208.94777435302728], "size": {"0": 700.2921752929688, "1": 1006.5166625976562}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 128}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 100, "type": "Detailer<PERSON>or<PERSON>ach", "pos": [8421, 93], "size": {"0": 400, "1": 600}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 117, "slot_index": 0}, {"name": "segs", "type": "SEGS", "link": 123}, {"name": "model", "type": "MODEL", "link": 122}, {"name": "clip", "type": "CLIP", "link": 126, "slot_index": 3}, {"name": "vae", "type": "VAE", "link": 127}, {"name": "positive", "type": "CONDITIONING", "link": 124, "slot_index": 5}, {"name": "negative", "type": "CONDITIONING", "link": 125, "slot_index": 6}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [128], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Detailer<PERSON>or<PERSON>ach"}, "widgets_values": [384, true, 600, 218250296902126, "randomize", 20, 8, "euler", "normal", 0.5, 5, true, true, "", 1, false, 20]}, {"id": 89, "type": "Detailer<PERSON>or<PERSON>ach", "pos": [6468, 75], "size": {"0": 400, "1": 600}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 94}, {"name": "segs", "type": "SEGS", "link": 93}, {"name": "model", "type": "MODEL", "link": 115}, {"name": "clip", "type": "CLIP", "link": 96, "slot_index": 3}, {"name": "vae", "type": "VAE", "link": 97}, {"name": "positive", "type": "CONDITIONING", "link": 98}, {"name": "negative", "type": "CONDITIONING", "link": 99}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [100, 117], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Detailer<PERSON>or<PERSON>ach"}, "widgets_values": [384, true, 600, 678597700332220, "randomize", 20, 8, "euler", "normal", 0.5, 5, true, true, "", 1, false, 20]}, {"id": 84, "type": "PreviewImage", "pos": [4536, 1201], "size": {"0": 210, "1": 246}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 88}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 83, "type": "MaskToImage", "pos": [4236, 1163], "size": {"0": 210, "1": 26}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 87}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [88], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 91, "type": "ImpactSEGSOrderedFilter", "pos": [6136, 572], "size": {"0": 315, "1": 150}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 101}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [123], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 1, 1]}, {"id": 88, "type": "ImpactSEGSOrderedFilter", "pos": [5842, 143], "size": {"0": 315, "1": 150}, "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 92, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [93], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 0, 1]}, {"id": 90, "type": "PreviewImage", "pos": [7347, 191], "size": {"0": 763.6897583007812, "1": 936.2122802734375}, "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 100}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 72, "type": "ApplyRegionalIPAdapters //Inspire", "pos": [4376, 885], "size": {"0": 294, "1": 86}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "ipadapter_pipe", "type": "IPADAPTER_PIPE", "link": 68, "slot_index": 0}, {"name": "regional_ipadapter1", "type": "REGIONAL_IPADAPTER", "link": 73}, {"name": "regional_ipadapter2", "type": "REGIONAL_IPADAPTER", "link": 74}, {"name": "regional_ipadapter3", "type": "REGIONAL_IPADAPTER", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [78], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ApplyRegionalIPAdapters //Inspire"}}, {"id": 78, "type": "PreviewImage", "pos": [3373, 581], "size": {"0": 210, "1": 246}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 75}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 29, "type": "ImpactSEGSOrderedFilter", "pos": [2246, 663], "size": {"0": 315, "1": 150}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 133, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [26], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 1, 2]}, {"id": 59, "type": "VAEDecode", "pos": [5002, 53], "size": {"0": 210, "1": 46}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 55, "slot_index": 0}, {"name": "vae", "type": "VAE", "link": 56}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [57, 90, 94, 129], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 58, "type": "K<PERSON><PERSON><PERSON>", "pos": [4670, 141], "size": {"0": 315, "1": 262}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 78, "slot_index": 0}, {"name": "positive", "type": "CONDITIONING", "link": 52, "slot_index": 1}, {"name": "negative", "type": "CONDITIONING", "link": 53, "slot_index": 2}, {"name": "latent_image", "type": "LATENT", "link": 54, "slot_index": 3}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [55], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [48489, "fixed", 20, 10, "euler", "karras", 1]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [855, 165], "size": {"0": 315, "1": 262}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [48489, "fixed", 15, 10, "euler", "karras", 1]}, {"id": 65, "type": "PrepImageForClipVision", "pos": [3107, 969], "size": {"0": 315, "1": 106}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 59, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [60, 81, 112], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "center", 0]}, {"id": 6, "type": "CLIPTextEncode", "pos": [372, 223], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4, 52, 98, 124], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["two people, at the restaurant, high quality, facing camera"]}, {"id": 24, "type": "ImpactSEGSOrderedFilter", "pos": [2252, 391], "size": {"0": 315, "1": 150}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 132, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [22], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 0, 1]}, {"id": 23, "type": "ImpactSimpleDetectorSEGS", "pos": [1196, 480], "size": {"0": 315, "1": 310}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 18}, {"name": "image", "type": "IMAGE", "link": 19, "slot_index": 1}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [130], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 104, "type": "ImpactSEGSRangeFilter", "pos": [1567, 559], "size": {"0": 315, "1": 150}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 130, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [131], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSRangeFilter"}, "widgets_values": ["area(=w*h)", true, 0, 67108864]}, {"id": 105, "type": "ImpactSEGSOrderedFilter", "pos": [1862, 587], "size": {"0": 315, "1": 150}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 131, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [132, 133], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 2]}, {"id": 103, "type": "PreviewImage", "pos": [5305, -47], "size": [674.0367309570302, 898.5175617980952], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 129}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 98, "type": "IPAdapterAdvanced", "pos": [5341, 918], "size": {"0": 315, "1": 278}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 114}, {"name": "ipadapter", "type": "IPADAPTER", "link": 110}, {"name": "image", "type": "IMAGE", "link": 112, "slot_index": 2}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": null}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 113, "slot_index": 5}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [115], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [1, "linear", "concat", 0, 1, "V only"]}, {"id": 94, "type": "IPAdapterModelLoader", "pos": [4273, 1703], "size": {"0": 315, "1": 58}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "IPADAPTER", "type": "IPADAPTER", "links": [108, 110, 119], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["ip-adapter-anime.safetensors"]}, {"id": 61, "type": "LoadImage", "pos": [2657, 1080], "size": {"0": 320, "1": 314}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [59], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["640 (1).jpeg", "image"]}, {"id": 62, "type": "LoadImage", "pos": [2698, 1489], "size": {"0": 315, "1": 314.0000305175781}, "flags": {}, "order": 8, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [58], "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["7d0cf5ea-afca-417e-b8eb-7d60d8e25f8a_0.png", "image"]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [2823, 181], "size": {"0": 315, "1": 98}, "flags": {}, "order": 9, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1, 85, 114, 118], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5, 96, 126], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [8, 56, 97, 127], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["meinamix.safetensors"]}, {"id": 53, "type": "RegionalIPAdapterColorMask //Inspire", "pos": [3868, 1365], "size": {"0": 367.79998779296875, "1": 314}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "color_mask", "type": "IMAGE", "link": 46, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 61}, {"name": "neg_image", "type": "IMAGE", "link": null}], "outputs": [{"name": "REGIONAL_IPADAPTER", "type": "REGIONAL_IPADAPTER", "links": [74], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "RegionalIPAdapterColorMask //Inspire"}, "widgets_values": ["#FFFFFF", 1, 0.5, "original", 0.2, 1, false, false, 3, "concat"]}, {"id": 15, "type": "RegionalIPAdapterColorMask //Inspire", "pos": [3789, 971], "size": {"0": 367.79998779296875, "1": 314}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "color_mask", "type": "IMAGE", "link": 44, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 60}, {"name": "neg_image", "type": "IMAGE", "link": null}], "outputs": [{"name": "REGIONAL_IPADAPTER", "type": "REGIONAL_IPADAPTER", "links": [73], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [87], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "RegionalIPAdapterColorMask //Inspire"}, "widgets_values": ["#FFFFFF", 1, 0.5, "original", 0.2, 1, false, true, 3, "concat"]}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 8, 0, 10, 0, "IMAGE"], [18, 21, 0, 23, 0, "BBOX_DETECTOR"], [19, 8, 0, 23, 1, "IMAGE"], [22, 24, 0, 26, 0, "SEGS"], [23, 26, 0, 27, 0, "MASK"], [26, 29, 0, 30, 0, "SEGS"], [28, 30, 0, 33, 0, "MASK"], [35, 43, 0, 47, 0, "IMAGE"], [36, 33, 0, 47, 1, "IMAGE"], [37, 30, 0, 47, 2, "MASK"], [40, 47, 0, 50, 0, "IMAGE"], [42, 26, 0, 50, 2, "MASK"], [43, 50, 0, 51, 0, "IMAGE"], [44, 27, 0, 15, 0, "IMAGE"], [46, 33, 0, 53, 0, "IMAGE"], [52, 6, 0, 58, 1, "CONDITIONING"], [53, 7, 0, 58, 2, "CONDITIONING"], [54, 5, 0, 58, 3, "LATENT"], [55, 58, 0, 59, 0, "LATENT"], [56, 4, 2, 59, 1, "VAE"], [57, 59, 0, 60, 0, "IMAGE"], [58, 62, 0, 63, 0, "IMAGE"], [59, 61, 0, 65, 0, "IMAGE"], [60, 65, 0, 15, 1, "IMAGE"], [61, 63, 0, 53, 1, "IMAGE"], [68, 75, 0, 72, 0, "IPADAPTER_PIPE"], [73, 15, 0, 72, 1, "REGIONAL_IPADAPTER"], [74, 53, 0, 72, 2, "REGIONAL_IPADAPTER"], [75, 33, 0, 78, 0, "IMAGE"], [76, 27, 0, 50, 1, "IMAGE"], [77, 27, 0, 79, 0, "IMAGE"], [78, 72, 0, 58, 0, "MODEL"], [80, 80, 0, 75, 2, "CLIP_VISION"], [81, 65, 0, 81, 0, "IMAGE"], [85, 4, 0, 75, 1, "MODEL"], [87, 15, 1, 83, 0, "MASK"], [88, 83, 0, 84, 0, "IMAGE"], [90, 59, 0, 86, 1, "IMAGE"], [91, 87, 0, 86, 0, "BBOX_DETECTOR"], [92, 86, 0, 88, 0, "SEGS"], [93, 88, 0, 89, 1, "SEGS"], [94, 59, 0, 89, 0, "IMAGE"], [96, 4, 1, 89, 3, "CLIP"], [97, 4, 2, 89, 4, "VAE"], [98, 6, 0, 89, 5, "CONDITIONING"], [99, 7, 0, 89, 6, "CONDITIONING"], [100, 89, 0, 90, 0, "IMAGE"], [101, 86, 0, 91, 0, "SEGS"], [108, 94, 0, 75, 0, "IPADAPTER"], [110, 94, 0, 98, 1, "IPADAPTER"], [112, 65, 0, 98, 2, "IMAGE"], [113, 99, 0, 98, 5, "CLIP_VISION"], [114, 4, 0, 98, 0, "MODEL"], [115, 98, 0, 89, 2, "MODEL"], [117, 89, 0, 100, 0, "IMAGE"], [118, 4, 0, 101, 0, "MODEL"], [119, 94, 0, 101, 1, "IPADAPTER"], [120, 63, 0, 101, 2, "IMAGE"], [121, 99, 0, 101, 5, "CLIP_VISION"], [122, 101, 0, 100, 2, "MODEL"], [123, 91, 0, 100, 1, "SEGS"], [124, 6, 0, 100, 5, "CONDITIONING"], [125, 7, 0, 100, 6, "CONDITIONING"], [126, 4, 1, 100, 3, "CLIP"], [127, 4, 2, 100, 4, "VAE"], [128, 100, 0, 102, 0, "IMAGE"], [129, 59, 0, 103, 0, "IMAGE"], [130, 23, 0, 104, 0, "SEGS"], [131, 104, 0, 105, 0, "SEGS"], [132, 105, 0, 24, 0, "SEGS"], [133, 105, 0, 29, 0, "SEGS"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}