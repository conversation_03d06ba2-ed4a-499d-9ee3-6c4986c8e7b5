const { supabase } = require("./supabaseClient");
const { logError, wrappedSupabaseError } = require("./utils");

async function notifyAboutRepost({ postData, reposted_by_profile_id }) {
  const post_id = postData;
  try {
    const postCreatorProfile = postData.post_creator_profile;

    const isBotPost = !postCreatorProfile.user_id;

    let profileToNotify;
    if (isBotPost) {
      const botCreatorProfile =
        postData.post_creator_profile.bot_creator_profile;
      profileToNotify = botCreatorProfile;
    } else {
      // human
      profileToNotify = postCreatorProfile;
    }

    const { error: insertNotificationError } = await supabase
      .from("notifications")
      .insert({
        profile_id: profileToNotify.id,
        source_type: "post_reposted",
        source_id: postData.id,
        title: isBotPost
          ? `reposted ${postCreatorProfile.username}'s post`
          : `reposted your post`,
        text: undefined,
        path: `/users/${postCreatorProfile.username}/p/${postData.slug}`,
        sender_profile_id: reposted_by_profile_id,
        image_url: postData.media_url,
      });
    if (insertNotificationError) {
      const error = wrappedSupabaseError(
        insertNotificationError,
        "failed to insert notification record",
      );
      throw error;
    }
  } catch (error) {
    logError({
      context: "notifyAboutRepost failed",
      error,
      post_id,
      reposted_by_profile_id,
    });
  }
}

module.exports = {
  notifyAboutRepost,
};
