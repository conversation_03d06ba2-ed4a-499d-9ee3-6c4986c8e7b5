const express = require("express");
const app = express.Router();
const {
  logErrorV2,
  wrappedSupabaseError,
  // checkProfileValid,
} = require("./utils");
const { supabase } = require("./supabaseClient");
const { authUser } = require("./middleware");
const {
  submitPostToLeaderboard,
  getLeaderboardSubmissionRanking,
} = require("./leaderboardHelpers");

app.get("/ping", async (req, res) => {
  return res.status(200).send("leaderboard ping");
});

/**f
 * Fetch posts available for voting in a specific leaderboard
 * @param {string} profile_id - The ID of the profile requesting posts to vote on
 * @param {string} leaderboard_id - The ID of the leaderboard to fetch posts from
 * @returns {Array} Array of posts available for voting
 */
app.get("/fetchPostsToVote", async (req, res) => {
  try {
    const { profile_id, leaderboard_id } = req.query;

    if (!profile_id || !leaderboard_id) {
      return res.status(400).json({
        error: "Missing required fields",
        details: "profile_id and leaderboard_id are required",
      });
    }

    let { data, error } = await supabase.rpc(
      "get_filtered_leaderboard_submissions_v2",
      { leaderboard_id_input: leaderboard_id, profile_id_input: profile_id },
    );

    // Filter out posts from bots where the user is the creator
    if (data) {
      data = data.filter((submission) => {
        const bot = submission.posts?.profiles?.bot;
        return !bot || Number(bot.creator.id) !== Number(profile_id);
      });
    }

    if (error) {
      logErrorV2("Error fetching unvoted submissions:", error);
      throw wrappedSupabaseError(error);
    }

    // Shuffle the results in memory
    const shuffledSubmissions = data?.sort(() => Math.random() - 0.5) || [];

    res.json(shuffledSubmissions);
  } catch (error) {
    logErrorV2("Error in fetchPostsToVote:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
      details:
        process.env.NODE_ENV === "development" ? error.toString() : undefined,
    });
  }
});

/**
 * Vote on a post in a leaderboard
 * @param {string} profile_id - The ID of the profile voting
 * @param {string} post_id - The ID of the post being voted on
 * @param {boolean} upvote - Whether this is an upvote (true) or downvote (false)
 * @param {string} leaderboard_submission_id - The ID of the leaderboard submission
 * @returns {Object} Result of the vote operation
 */
app.post("/vote", async (req, res) => {
  // // Get the profile_id from the request body
  // const { profile_id } = req.body;
  // const user_id = req.user?.id;

  // // Ensure image and profile_id are provided
  // if (!req.file || !profile_id) {
  //   return res.status(400).send("Missing required fields.");
  // }

  // const isValid = await checkProfileValid(user_id, profile_id);
  // if (!isValid) {
  //   return res.status(403).send({ error: "Forbidden" });
  // }

  try {
    const { profile_id, post_id, upvote, leaderboard_submission_id } = req.body;

    // Validate required fields
    if (
      !profile_id ||
      !post_id ||
      typeof upvote !== "boolean" ||
      !leaderboard_submission_id
    ) {
      return res.status(400).json({
        error: "Missing required fields",
        details:
          "profile_id, post_id, upvote, and leaderboard_submission_id are required",
      });
    }

    // First check if the submission exists and is valid
    const { data: submission, error: submissionError } = await supabase
      .from("leaderboard_submissions")
      .select("*")
      .eq("id", leaderboard_submission_id)
      .single();

    if (submissionError) {
      logErrorV2("Error fetching leaderboard submission:", submissionError);
      throw wrappedSupabaseError(submissionError);
    }

    if (!submission) {
      return res.status(404).json({
        error: "Leaderboard submission not found",
      });
    }

    // Check if user has already voted
    const { data: existingVote, error: voteCheckError } = await supabase
      .from("leaderboard_votes")
      .select("*")
      .eq("profile_id", profile_id)
      .eq("leaderboard_submission_id", leaderboard_submission_id)
      .single();

    if (voteCheckError && voteCheckError.code !== "PGRST116") {
      // PGRST116 is "not found" which is expected
      logErrorV2("Error checking existing vote:", voteCheckError);
      throw wrappedSupabaseError(voteCheckError);
    }

    let result;
    if (existingVote) {
      // Update existing vote if different
      if (existingVote.upvote !== upvote) {
        const { data, error: updateError } = await supabase
          .from("leaderboard_votes")
          .update({ upvote })
          .eq("id", existingVote.id)
          .select()
          .single();

        if (updateError) {
          logErrorV2("Error updating vote:", updateError);
          throw wrappedSupabaseError(updateError);
        }
        result = data;
      } else {
        result = existingVote;
      }
    } else {
      // Create new vote
      const { data, error: insertError } = await supabase
        .from("leaderboard_votes")
        .insert([
          {
            profile_id,
            post_id,
            upvote,
            leaderboard_submission_id,
          },
        ])
        .select()
        .single();

      if (insertError) {
        logErrorV2("Error inserting vote:", insertError);
        throw wrappedSupabaseError(insertError);
      }
      result = data;
    }

    // Record XP event for the vote
    const { error: xpError } = await supabase.from("xp_events").insert({
      profile_id,
      event_type: "leaderboard_vote_sent",
      amount: 1,
      event_reference_id: result.id,
    });

    if (xpError) {
      logErrorV2("Error creating XP event:", xpError);
      // Don't throw here since the vote was successful
    }

    if (upvote) {
      // The profile that should receive XP and notifications - either bot creator or original poster
      const recipientProfileId = submission.owner_profile_id;

      // Record XP event for post owner receiving the vote
      const { error: ownerXpError } = await supabase.from("xp_events").insert({
        profile_id: recipientProfileId,
        event_type: "leaderboard_vote_received",
        amount: 40,
        event_reference_id: leaderboard_submission_id,
      });

      if (ownerXpError) {
        logErrorV2("Error creating XP event for post owner:", ownerXpError);
      }

      // Get sender's profile info for notification
      const { data: senderProfile, error: senderError } = await supabase
        .from("profiles")
        .select("username, display_name")
        .eq("id", profile_id)
        .single();

      if (senderError) {
        logErrorV2("Error getting profile info:", senderError);
      }

      const sender_name =
        senderProfile?.display_name || senderProfile?.username;

      const submissionRanking = await getLeaderboardSubmissionRanking({
        leaderboard_id: result.leaderboard_id,
        post_id,
        leaderboard_submission_id,
      });

      // Calculate which page this submission would appear on (0-based page number * page size)
      const cursor = Math.floor((submissionRanking.rank - 1) / 20) * 20;

      const { error: notificationError } = await supabase
        .from("notifications")
        .insert({
          profile_id: recipientProfileId,
          sender_profile_id: profile_id,
          source_id: leaderboard_submission_id,
          source_type: "leaderboard_vote_received",
          title: `+40 XP!`,
          text: `🔥 ${sender_name} swiped right on your post!`,
          path: `/challenge?active=leaderboard&leaderboard_id=${submission?.leaderboard_id}&leaderboard_submission_id=${leaderboard_submission_id}&cursor=${cursor}&page_size=20`,
          image_url: submissionRanking?.submission?.posts?.media_url,
        });

      if (notificationError) {
        logErrorV2("Error creating XP notification event", notificationError);
      }
    }

    res.json(result);
  } catch (error) {
    logErrorV2("Error in vote:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
      details:
        process.env.NODE_ENV === "development" ? error.toString() : undefined,
    });
  }
});

/**
 * Creates a new leaderboard if there are no active ones
 * @returns {Object} Result containing whether a new leaderboard was created
 */
app.post("/createNewLeaderboardIfNecessary", async (req, res) => {
  try {
    // First check for active leaderboards
    const { data: activeLeaderboards, error: fetchError } = await supabase
      .from("leaderboards")
      .select("*")
      .gt("end_at", new Date().toISOString())
      .order("end_at", { ascending: true });

    if (fetchError) {
      logErrorV2("Error fetching active leaderboards:", fetchError);
      throw wrappedSupabaseError(fetchError);
    }

    // If we have active leaderboards, return early
    if (activeLeaderboards && activeLeaderboards.length > 0) {
      return res.json({
        success: true,
        created: false,
        message: "Active leaderboard already exists",
        leaderboard: activeLeaderboards[0],
      });
    }

    // Calculate tomorrow at 4:00 PM PT
    const now = new Date();
    // Create tomorrow's date at 4:00 PM PT
    const tomorrow = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() + 1,
    );
    // Set to 4:00 PM PT
    tomorrow.setHours(16, 0, 0, 0);

    const newLeaderboardData = {
      start_at: new Date(
        tomorrow.getTime() - 24 * 60 * 60 * 1000,
      ).toISOString(),
      end_at: tomorrow.toISOString(),
      active: true,
    };

    // Log the data we're about to insert
    console.log("Creating new leaderboard with data:", newLeaderboardData);

    // Create new leaderboard
    const { data: newLeaderboard, error: createError } = await supabase
      .from("leaderboards")
      .insert([newLeaderboardData])
      .select()
      .single();

    if (createError) {
      logErrorV2("Error creating new leaderboard:", createError);
      throw wrappedSupabaseError(createError);
    }

    if (!newLeaderboard) {
      throw new Error("Failed to create leaderboard - no data returned");
    }

    console.log("Successfully created new leaderboard:", newLeaderboard);

    res.json({
      success: true,
      created: true,
      message: "Created new leaderboard",
      leaderboard: newLeaderboard,
    });
  } catch (error) {
    logErrorV2("Error in createNewLeaderboardIfNecessary:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
      details:
        process.env.NODE_ENV === "development" ? error.toString() : undefined,
    });
  }
});

/**
 * Fetch leaderboard data with optional active filter
 * @param {Object} req.query
 * @param {boolean} [req.query.active] - If true, only returns active leaderboards (end_at > current time)
 * @param {string} [req.query.date] - If provided, returns leaderboards where the date falls between start_at and end_at
 * @returns {Array} Leaderboard data based on the filter
 */
app.get("/fetchLeaderboards", async (req, res) => {
  try {
    const { active, date } = req.query;
    let query = supabase.from("leaderboards").select("*");

    if (active === "true") {
      // For active leaderboards, get ones that haven't ended yet
      query = query.gt("end_at", new Date().toISOString());
    } else if (date) {
      // If date is provided, get leaderboards where the date falls between start_at and end_at
      query = query.lte("start_at", date).gte("end_at", date);
    }

    const { data: leaderboards, error } = await query.order("end_at", {
      ascending: true,
    });

    if (error) {
      throw wrappedSupabaseError(error);
    }

    res.json(leaderboards);
  } catch (error) {
    logErrorV2("Error in fetchLeaderboards:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * Fetch paginated rankings for a specific leaderboard
 * @param {string} leaderboard_id - The ID of the leaderboard to fetch rankings for
 * @param {number} [page_size=20] - Number of results per page
 * @param {number} [cursor] - Cursor for pagination, represents the last total_xp seen
 * @returns {Object} Paginated leaderboard submissions sorted by total_xp
 */
app.get("/fetchLeaderboardRankings", async (req, res) => {
  try {
    const { leaderboard_id, page_size = 20, cursor = 0, prev_ids } = req.query;

    if (!leaderboard_id) {
      return res.status(400).json({ error: "Leaderboard ID is required" });
    }

    const pageSize = parseInt(page_size, 10);
    const from = parseInt(cursor, 10);
    const to = from + pageSize - 1;

    let query = supabase
      .from("leaderboard_submissions")
      .select(
        `
        id, post_id, total_xp, leaderboard_id, owner_profile_id,
        posts:post_id (
          id, created_at, updated_at, profile_id, media_url, previewhash, location, slug, description, tags, visibility, nsfw,
          profiles:profiles_with_bots (
            id,
            username,
            avatar_url,
            user_id,
            visibility,
            nsfw,
            creator_id,
            display_name,
            bot:bots!bots_profile_id_fkey (
              creator:creator_id (
                id,
                username,
                avatar_url,
                display_name,
                nsfw
              )
            )
          )
        )
      `,
      )
      .eq("leaderboard_id", leaderboard_id)
      .eq("status", "public")
      .not("posts", "is", null)
      .eq("posts.visibility", "public")
      .not("posts.media_url", "is", null)
      .order("total_xp", { ascending: false });

    if (prev_ids) {
      const prevIdsArray = JSON.parse(prev_ids);
      const formattedPrevIds = `(${prevIdsArray.join(",")})`;
      query = query.not("id", "in", formattedPrevIds);
    }

    query = query.range(from, to);
    const { data: submissions, error } = await query;

    if (error) {
      throw wrappedSupabaseError(error);
    }

    // Get the cursor for the next page
    const nextCursor = submissions.length === pageSize ? to + 1 : null;

    res.json({
      submissions,
      next_cursor: nextCursor,
      has_more: submissions.length === pageSize,
    });
  } catch (error) {
    logErrorV2("Error in fetchLeaderboardRankings:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * Automatically updates leaderboards and sends notifications when appropriate
 * Checks for:
 * 1. Ended leaderboards to mark as inactive
 * 2. Leaderboards approaching end time (2 hours remaining) to send notifications
 */
app.post("/autoUpdateLeaderboard", async (req, res) => {
  try {
    // Get all active leaderboards
    const { data: activeLeaderboards, error: fetchError } = await supabase
      .from("leaderboards")
      .select("*")
      .eq("active", true);

    if (fetchError) {
      logErrorV2("Error fetching active leaderboards:", fetchError);
      throw wrappedSupabaseError(fetchError);
    }

    // Check if there are any active leaderboards to process
    if (!activeLeaderboards || activeLeaderboards.length === 0) {
      return res.status(200).json({
        message: "No active leaderboards found",
      });
    }

    const currentTime = new Date();

    for (const leaderboard of activeLeaderboards) {
      const endTime = new Date(leaderboard.end_at);

      // Check if leaderboard has ended
      if (currentTime > endTime) {
        const { error: updateError } = await supabase
          .from("leaderboards")
          .update({ active: false })
          .eq("id", leaderboard.id);

        if (updateError) {
          logErrorV2("Error updating leaderboard status:", updateError);
          continue;
        }

        // Only send winning notifications if they haven't been sent already
        if (!leaderboard.has_sent_winning_notifications) {
          // Get top 3 submissions ordered by votes
          const { data: topSubmissions, error: topSubmissionsError } =
            await supabase
              .from("leaderboard_submissions")
              .select(
                `
              id,
              post_id,
              posts!inner (
                id,
                media_url,
                previewhash,
                description,
                profile_id,
                profiles!inner (
                  id,
                  username,
                  avatar_url,
                  display_name,
                  bot:bots!bots_profile_id_fkey (
                    creator:creator_id!inner (
                      id,
                      username,
                      avatar_url,
                      display_name
                    )
                  )
                )
              )
            `,
              )
              .eq("leaderboard_id", leaderboard.id)
              .order("total_xp", { ascending: false })
              .limit(3);

          if (topSubmissionsError) {
            logErrorV2("Error fetching top submissions:", topSubmissionsError);
            continue;
          }

          // Get all participants for broadcasting winner announcement
          const { data: allParticipants, error: participantsError } =
            await supabase
              .from("leaderboard_submissions")
              .select(
                `
                post_id,
                posts!inner (
                  id,
                  media_url,
                  previewhash,
                  description,
                  profile_id,
                  profiles!inner (
                    id,
                    username,
                    avatar_url,
                    display_name,
                    bot:bots!bots_profile_id_fkey (
                      creator:creator_id!inner (
                        id,
                        username,
                        avatar_url,
                        display_name
                      )
                    )
                  )
                )
              `,
              )
              .eq("leaderboard_id", leaderboard.id);

          if (participantsError) {
            logErrorV2("Error fetching participants:", participantsError);
            continue;
          }

          // Send notifications to winners and grant XP rewards
          const xpRewards = [400, 200, 100]; // XP for 1st, 2nd, 3rd place
          const placements = ["1st", "2nd", "3rd"];

          for (let i = 0; i < topSubmissions.length; i++) {
            const winner = topSubmissions[i];
            const profileId = winner.posts.profile_id;

            // Grant XP reward to the winner
            const { error: xpError } = await supabase.from("xp_events").insert({
              profile_id: profileId,
              amount: xpRewards[i],
              event_type: "leaderboard_win",
              event_reference_id: leaderboard.id,
            });

            if (xpError) {
              logErrorV2("Error granting XP reward:", xpError);
            }

            // Send notification to the winner
            const { error: winnerNotificationError } = await supabase
              .from("notifications")
              .insert({
                profile_id: profileId,
                source_type: "leaderboard_lifecycle",
                source_id: leaderboard.id,
                title: `🏆 +${xpRewards[i]} XP!`,
                text: `You won ${placements[i]} place in today's challenge!`,
                path: `/challenge?active=leaderboard`,
              });

            if (winnerNotificationError) {
              logErrorV2(
                "Error creating winner notification:",
                winnerNotificationError,
              );
            }
          }

          // If we have a winner, broadcast to all other participants
          if (topSubmissions.length > 0) {
            const winner = topSubmissions[0];

            // Filter out winners from the participants list
            const nonWinnerParticipants = allParticipants.filter(
              (participant) =>
                !topSubmissions.some(
                  (w) => w.posts.profile_id === participant.posts.profile_id,
                ),
            );

            // Process in batches of 50 participants
            const BATCH_SIZE = 50;
            const notificationBatches = [];

            // Create notification objects for all non-winner participants
            for (let i = 0; i < nonWinnerParticipants.length; i += BATCH_SIZE) {
              const batch = nonWinnerParticipants
                .slice(i, i + BATCH_SIZE)
                .map((participant) => ({
                  profile_id: participant.posts.profile_id,
                  source_type: "leaderboard_lifecycle",
                  source_id: leaderboard.id,
                  title: `🎉 Challenge Complete!`,
                  text: `${winner.posts.profiles.username} won today's challenge and gained ${xpRewards[0]} bonus XP!`,
                  path: `/users/${winner.posts.profiles.username}`,
                }));

              notificationBatches.push(batch);
            }

            // Process all batches in parallel
            const batchResults = await Promise.allSettled(
              notificationBatches.map(async (batch) => {
                if (batch.length > 0) {
                  const { error } = await supabase
                    .from("notifications")
                    .insert(batch);

                  return { success: !error, error };
                }
                return { success: true };
              }),
            );

            // Log any errors from batch processing
            batchResults.forEach((result, index) => {
              if (
                result.status === "rejected" ||
                (result.value && !result.value.success)
              ) {
                logErrorV2(
                  `Error creating broadcast notifications for batch ${index}:`,
                  result.status === "rejected"
                    ? result.reason
                    : result.value.error,
                );
              }
            });
          }

          // update leaderboard has_sent_winning_notifications to true
          const { error: updateWinningNotificationError } = await supabase
            .from("leaderboards")
            .update({ has_sent_winning_notifications: true })
            .eq("id", leaderboard.id);

          if (updateWinningNotificationError) {
            logErrorV2(
              "Error updating winning notification status:",
              updateWinningNotificationError,
            );
            continue;
          }
        }
      } else {
        // not ended

        // Check if it's 2 hours before end time and notification hasn't been sent
        const twoHoursBeforeEnd = new Date(
          endTime.getTime() - 2 * 60 * 60 * 1000,
        );

        if (
          currentTime >= twoHoursBeforeEnd &&
          currentTime <= endTime &&
          !leaderboard.has_sent_time_remaining_notification
        ) {
          // Get all users in this leaderboard
          const { data: submissions, error: submissionsError } = await supabase
            .from("leaderboard_submissions")
            .select(
              `
              post_id,
              posts!inner (
                id,
                media_url,
                previewhash,
                description,
                profile_id,
                profiles!inner (
                  id,
                  username,
                  avatar_url,
                  display_name,
                  bot:bots!bots_profile_id_fkey (
                    creator:creator_id!inner (
                      id,
                      username,
                      avatar_url,
                      display_name
                    )
                  )
                )
              )
            `,
            )
            .eq("leaderboard_id", leaderboard.id);

          if (submissionsError) {
            logErrorV2(
              "Error fetching leaderboard submissions:",
              submissionsError,
            );
            continue;
          }

          // Send notifications to all participants using batch processing
          if (submissions && submissions.length > 0) {
            // Process in batches of 50 participants
            const BATCH_SIZE = 50;
            const notificationBatches = [];

            // Create notification objects for all participants
            for (let i = 0; i < submissions.length; i += BATCH_SIZE) {
              const batch = submissions
                .slice(i, i + BATCH_SIZE)
                .map((submission) => ({
                  profile_id: submission.posts.profile_id,
                  source_type: "leaderboard_lifecycle",
                  source_id: leaderboard.id,
                  title: `⏳ 2 hours left in the challenge!`,
                  text: "View the leaderboard to see where you stand",
                  path: `/challenge?active=leaderboard`,
                }));

              notificationBatches.push(batch);
            }

            // Process all batches in parallel
            const batchResults = await Promise.allSettled(
              notificationBatches.map(async (batch) => {
                if (batch.length > 0) {
                  const { error } = await supabase
                    .from("notifications")
                    .insert(batch);

                  return { success: !error, error };
                }
                return { success: true };
              }),
            );

            // Log any errors from batch processing
            batchResults.forEach((result, index) => {
              if (
                result.status === "rejected" ||
                (result.value && !result.value.success)
              ) {
                logErrorV2(
                  `Error creating time remaining notifications for batch ${index}:`,
                  result.status === "rejected"
                    ? result.reason
                    : result.value.error,
                );
              }
            });
          }

          // Mark notification as sent
          const { error: notificationUpdateError } = await supabase
            .from("leaderboards")
            .update({ has_sent_time_remaining_notification: true })
            .eq("id", leaderboard.id);

          if (notificationUpdateError) {
            logErrorV2(
              "Error updating notification status:",
              notificationUpdateError,
            );
            continue;
          }
        }
      }
    }

    return res.status(200).json({
      message: "Leaderboard update check completed successfully",
    });
  } catch (error) {
    logErrorV2("Error in autoUpdateLeaderboard:", error);
    return res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Fetch the ranking of a specific post in a leaderboard
 * @param {string} leaderboard_id - The ID of the leaderboard
 * @param {string} [post_id] - The ID of the post (optional if leaderboard_submission_id is provided)
 * @param {string} [leaderboard_submission_id] - The ID of the leaderboard submission (optional if post_id is provided)
 * @returns {Object} The ranking information of the post
 */
app.get("/fetchLeaderboardRankingDetails", async (req, res) => {
  try {
    const { leaderboard_id, post_id, leaderboard_submission_id } = req.query;

    if (!leaderboard_id || (!post_id && !leaderboard_submission_id)) {
      return res.status(400).json({
        error:
          "Either leaderboard_id and post_id, or leaderboard_submission_id is required",
      });
    }

    const result = await getLeaderboardSubmissionRanking({
      leaderboard_id,
      post_id,
      leaderboard_submission_id,
    });

    res.status(200).json(result);
  } catch (error) {
    logErrorV2("Error in fetchLeaderboardRanking:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Fetch a specific leaderboard submission by ID
 * @param {string} leaderboard_submission_id - The ID of the leaderboard submission to fetch
 * @returns {Object} The leaderboard submission data
 */
app.get("/fetchLeaderboardSubmission", async (req, res) => {
  try {
    const { leaderboard_submission_id } = req.query;

    if (!leaderboard_submission_id) {
      return res.status(400).json({
        error: "Bad Request",
        message: "leaderboard_submission_id is required",
      });
    }

    const { data: submission, error } = await supabase
      .from("leaderboard_submissions")
      .select(
        `
        *,
        posts!inner (
            id,
            media_url,
            previewhash,
            description,
            profile_id,
            profiles!inner (
              id,
              username,
              avatar_url,
              display_name,
              bot:bots!bots_profile_id_fkey (
                creator:creator_id!inner (
                  id,
                  username,
                  avatar_url,
                  display_name
                )
              )
            )
          )

        `,
      )
      .eq("id", leaderboard_submission_id)
      .single();

    if (error) {
      logErrorV2("Error fetching leaderboard submission:", error);
      return res.status(500).json({
        error: "Internal server error",
        message: error.message,
      });
    }

    if (!submission) {
      return res.status(404).json({
        error: "Not Found",
        message: "Leaderboard submission not found",
      });
    }

    res.json(submission);
  } catch (error) {
    logErrorV2("Error in fetchLeaderboardSubmission:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Update a leaderboard submission status
 * @param {Object} req.body - The request body containing leaderboard_submission_id and status
 * @param {string} req.body.leaderboard_submission_id - ID of the submission to update
 * @param {string} req.body.status - New status for the submission
 * @returns {Object} The updated leaderboard submission data
 */
app.post("/updateLeaderboardSubmission", authUser, async (req, res) => {
  try {
    const { leaderboard_submission_id, status } = req.body;

    if (!leaderboard_submission_id) {
      return res.status(400).json({
        error: "Bad Request",
        message: "leaderboard_submission_id is required",
      });
    }

    if (!status) {
      return res.status(400).json({
        error: "Bad Request",
        message: "status is required",
      });
    }

    const { data: submission, error } = await supabase
      .from("leaderboard_submissions")
      .update({ status })
      .eq("id", leaderboard_submission_id)
      .select()
      .single();

    if (error) {
      logErrorV2("Error updating leaderboard submission:", error);
      return res.status(500).json({
        error: "Internal server error",
        message: error.message,
      });
    }

    if (!submission) {
      return res.status(404).json({
        error: "Not Found",
        message: "Leaderboard submission not found",
      });
    }

    if (status === "public" || status === "archived") {
      // Update the corresponding post visibility as public
      const { error: updatePostError } = await supabase
        .from("posts")
        .update({ visibility: status })
        .eq("id", submission.post_id);

      if (updatePostError) {
        logErrorV2("Error updating post visibility:", updatePostError);
      }
    }

    if (status === "public") {
      const submissionRanking = await getLeaderboardSubmissionRanking({
        leaderboard_id: submission.leaderboard_id,
        post_id: submission.post_id,
        leaderboard_submission_id,
      });
      // Calculate which page this submission would appear on (0-based page number * page size)
      const cursor = Math.floor((submissionRanking.rank - 1) / 20) * 20;
      submission.cursor = cursor;
    }
    return res.json(submission);
  } catch (error) {
    logErrorV2("Error in updateLeaderboardSubmission:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Submit a post to a leaderboard
 * @param {string} leaderboard_id - The ID of the leaderboard to submit to
 * @param {string} post_id - The ID of the post to submit
 * @returns {Object} The created leaderboard submission
 */
app.post("/submitToLeaderboard", async (req, res) => {
  try {
    const { leaderboard_id, post_id } = req.body;

    const result = await submitPostToLeaderboard(leaderboard_id, post_id);

    if (result.error) {
      return res.status(400).json({
        error: result.error,
        submission: result.submission,
      });
    }

    res.status(201).json({
      message: "Successfully submitted post to leaderboard",
      submission: result.submission,
    });
  } catch (error) {
    logErrorV2("Error in submitToLeaderboard:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Fetch the last 30 completed leaderboards with formatted end dates
 * @returns {Array} Array of objects containing leaderboard_id and formatted_date
 */
app.get("/fetchPastLeaderboards", async (req, res) => {
  try {
    const { data: leaderboards, error } = await supabase
      .from("leaderboards")
      .select("*")
      .order("end_at", { ascending: false }) // Most recent first
      .limit(30);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    // Format the dates
    const formattedLeaderboards = leaderboards.map((board) => ({
      leaderboard_id: board.id,
      active: board.active,
      formatted_date: new Date(board.end_at).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      }),
    }));

    res.json(formattedLeaderboards);
  } catch (error) {
    logErrorV2("Error in fetchPastLeaderboards:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

/**
 * Fetch pending leaderboard submissions for a specific profile and leaderboard
 * @param {string} profile_id - The ID of the profile to fetch submissions for
 * @param {string} leaderboard_id - The ID of the leaderboard to fetch submissions from
 * @returns {Object} The pending leaderboard submissions
 */
app.get("/fetchPendingSubmissions", async (req, res) => {
  try {
    const { profile_id, leaderboard_id } = req.query;

    if (!profile_id || !leaderboard_id) {
      return res.status(400).json({
        error: "Bad Request",
        message: "profile_id and leaderboard_id are required",
      });
    }

    const { data: submissions, error } = await supabase
      .from("leaderboard_submissions")
      .select(
        `
        *,
        posts!inner (
          id,
          media_url,
          previewhash,
          description,
          profile_id,
          profiles!inner (
            id,
            username,
            avatar_url,
            display_name,
            bot:bots!bots_profile_id_fkey (
              creator:creator_id!inner (
                id,
                username,
                avatar_url,
                display_name
              )
            )
          )
        )
      `,
      )
      .eq("owner_profile_id", profile_id)
      .eq("status", "pending")
      .eq("leaderboard_id", leaderboard_id);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    res.json(submissions);
  } catch (error) {
    logErrorV2("Error in fetchPendingSubmissions:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

/**
 * Validate date string format (YYYY-MM-DD)
 * @param {string} dateString - Date string to validate
 * @returns {boolean} Whether the date string is valid
 */
// function isValidDate(dateString) {
//   const regex = /^\d{4}-\d{2}-\d{2}$/;
//   if (!regex.test(dateString)) return false;

//   const date = new Date(dateString);
//   return date instanceof Date && !isNaN(date);
// }

module.exports = { app };
