const { supabase, wrappedSupabaseError } = require("../supabaseClient");

// FIXME: fetch via blanket
async function fetchRecentMessages({
  limit = 10,
  user_profile_id,
  bot_profile_id,
}) {
  const { data, error: supabaseError } = await supabase
    .schema("internal")
    .from("pillow_messages")
    .select("*")
    .or(
      `and(sender_profile_id.eq.${user_profile_id},receiver_profile_id.eq.${bot_profile_id}),and(sender_profile_id.eq.${bot_profile_id},receiver_profile_id.eq.${user_profile_id})`,
    )
    .order("created_at", { ascending: false })
    .limit(limit);

  if (supabaseError) {
    throw wrappedSupabaseError(supabaseError);
  }

  const messages = data.reverse();
  return { messages };

  // // Prototyping dify code here lol
  // const now = new Date();
  // const formatted_messages = messages.map((message) => {
  //   const isFromUser = message.sender_profile_id === user_profile_id;
  //   const actor = isFromUser ? "user" : "bot";

  //   console.log("isFromUser", isFromUser);

  //   const messageDate = new Date(message.created_at);
  //   const diffMs = now.getTime() - messageDate.getTime();
  //   const diffMins = Math.floor(((diffMs % 86400000) % 3600000) / 60000); // minutes

  //   return `${actor}: ${diffMins}min ago\nimage_description: ${message.image_description}\ntext: ${message.message}`;
  // });
  // return { messages, formatted_messages };
}

module.exports = {
  fetchRecentMessages,
};
