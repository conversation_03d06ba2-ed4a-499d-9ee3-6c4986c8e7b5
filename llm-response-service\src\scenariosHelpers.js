const { generateBio } = require("./llmHelper");
const { callAndLogOpenAI } = require("./llm");
const dayjs = require("dayjs");
const { supabase } = require("./supabaseClient");
const { sendPushNotificationsForProfileId } = require("./notifications");
const { getEstimatedTimezone } = require("./usersHelpers");

async function checkForActiveScenario({ profile_id }) {
  console.log("profile id", profile_id);
  const now = dayjs().toISOString();

  const { data: scenario } = await supabase
    .from("scenarios")
    .select("*")
    .eq("profile_id", profile_id)
    .is("post_id", null)
    .gte("expires_at", now)
    .order("created_at", { ascending: false });

  if (scenario && scenario.length > 0) {
    return scenario[0];
  }

  return null;
}

async function alertUserAboutNewScenario({ bot, profile_id }) {
  // get bot profile
  const { data: botProfile } = await supabase
    .from("profiles")
    .select("username")
    .eq("id", profile_id)
    .single();

  const notificationProfileId = bot.creator_id;
  const notificationTitle = `⚠️ ${bot.display_name} needs you ⚠️`;
  const notificationText = `${bot.display_name} needs your help deciding what to post next! 1 hour left!`;

  // send push notification on new scenario
  sendPushNotificationsForProfileId({
    notification: {
      id: Math.floor(Math.random() * 1000000 + 1),
      priority: 10,
    },
    profileId: notificationProfileId,
    notificationTitle,
    notificationText,
    notificationLink: `/users/${botProfile.username}?open_scenario=true`,
    notificationData: {
      notifyType: "scenario",
      username: botProfile.username,
    },
  });
}

async function checkIfUserIsActive({ humanProfileId }) {
  // get profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("user_id")
    .eq("id", humanProfileId)
    .single();

  if (profile && profile.user_id) {
    const timezone = await getEstimatedTimezone({ user_id: profile.user_id });

    console.log("timezone", timezone);

    const now = dayjs().tz(timezone);
    const isUserActive = now.isBetween(
      now.startOf("day").hour(9),
      now.startOf("day").hour(21),
    );

    return isUserActive;
  } else {
    return false;
  }
}

async function returnOrCreateScenarioIfNecessary({
  profile_id,
  loopCount = 0,
  shouldCheckActiveHours = true,
}) {
  const scenario = await checkForActiveScenario({ profile_id });

  if (scenario) {
    console.log("Active scenario found");
    return scenario;
  }

  // const scenarioWaitTime = 12;

  // debug to 2 for now
  const scenarioWaitTime = 6;

  // check if last scenario expired more than 12 hours ago
  const { data: lastScenario } = await supabase
    .from("scenarios")
    .select("*")
    .eq("profile_id", profile_id)
    .order("expires_at", { ascending: false })
    .limit(1);

  // get profile bot
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", profile_id)
    .single();

  if (!bot || botError) {
    return null;
  }

  // check if expires_at was more than 12 hours ago
  if (
    lastScenario &&
    lastScenario.length > 0 &&
    dayjs().isAfter(
      dayjs(lastScenario[0].expires_at).add(scenarioWaitTime, "hour"),
    )
  ) {
    // check if scenarios has post_id
    if (!lastScenario[0].post_id) {
      // update the scenario expiry time to be right now
      const { data: updatedScenario } = await supabase
        .from("scenarios")
        .update({ expires_at: dayjs().add(60, "minute").toISOString() })
        .eq("id", lastScenario[0].id)
        .select("*");

      if (shouldCheckActiveHours) {
        const humanProfileId = bot.creator_id;

        const isUserActive = await checkIfUserIsActive({
          humanProfileId,
        });

        if (!isUserActive) {
          console.log("USER IS NOT ACTIVE");
          return null;
        }
      }

      await alertUserAboutNewScenario({ bot, profile_id });

      // return the scenario
      return updatedScenario;
    }
    // no op and create new scenario
  }

  // if the last scenario was less than 12 hours ago, return, we don't need to create a new one yet
  if (
    lastScenario &&
    lastScenario.length > 0 &&
    dayjs().isBefore(
      dayjs(lastScenario[0].expires_at).add(scenarioWaitTime, "hour"),
    )
  ) {
    console.log("Last scenario was less than 12 hours ago");
    return null;
  }

  if (shouldCheckActiveHours) {
    const humanProfileId = bot.creator_id;

    const isUserActive = await checkIfUserIsActive({
      humanProfileId,
    });

    if (!isUserActive) {
      console.log("USER IS NOT ACTIVE");
      return null;
    }
  }

  // else, no scenarios at all, or the last one was more than 12 hours ago, we can create one

  // get last post of profile
  const { data: lastPosts } = await supabase
    .from("posts")
    .select("*")
    .eq("profile_id", profile_id)
    .order("created_at", { ascending: false })
    .limit(1);

  const lastPost = lastPosts?.length > 0 ? lastPosts[0] : null;

  let lastPostInsert = `PREVIOUS POST:
  There were no previous posts. This is actually their first post.`;

  if (lastPost) {
    lastPostInsert = `PREVIOUS POST:
  Visual description: ${lastPost.ai_caption}
  
  Caption: ${lastPost.description}`;
  }

  const scenarioPrompt = `You are managing the social media profile for ${bot.display_name}. This is their background:
  ${generateBio(bot) ?? ""}
  
  ${lastPostInsert}
  
  In JSON format, generate 4 suggestions of what they could post about next. The suggestions should be based on the last post they made, either continuing the story, or continuing the theme.
  
  The format of the suggestion should be less than 10 words long. The suggestions should all be a bit whacky and very creative and different from each other.

  The following JSON needs these exact fields, using these exact field names:  
  {
    "less_extreme": "A post that continues the story / theme but is less extreme than the last post. One single sentence explaining what the post could be about. Written in tone of the character. Very creative.",
    "adjacent": "A post that is similar to the last post. Unique, interesting.  Very creative.",
    "more_extreme": "A post that is more extreme than the last post. Unique, interesting. Very creative.",
    "random": "A random post idea. Unique, interesting",
    "less_extreme_title": "1-4 words witty title for the less extreme choice",
    "adjacent_title": "1-4 words witty title for the adjacent choice",
    "more_extreme_title": "1-4 words witty title for the extreme choice",
    "random_title": "1-4 words witty title for the random choice",
    "advice_question": "written in tone of voice of character asking user for advice on what to post. Should be a short question. Two sentences max. It should be related to the options above and include the previous post context. If there was no previous post, just write one sentence asking what they should post first? Examples:: "My previous post was about watching 
  Star Wars, what do you suggest I post next? Should I post about watching a classic movie, or do someting else like learn BJJ?""
  }`;

  console.log("START GENERATING FOR", profile_id, bot.display_name);

  const chatCompletion = await callAndLogOpenAI(
    "FireworksAI:Instruct:GeneratePost",
    {
      messages: [{ role: "user", content: scenarioPrompt }],
      top_p: 0.6,
      temperature: 0.9,
      max_tokens: 800,
      response_format: { type: "json_object" },
      model: "gpt-4o",
    },
    {
      timeout: 15 * 1000,
    },
  );

  if (chatCompletion.choices[0].message) {
    const result = JSON.parse(chatCompletion.choices[0].message.content);

    const {
      less_extreme,
      adjacent,
      more_extreme,
      random,
      advice_question,
      less_extreme_title,
      adjacent_title,
      more_extreme_title,
      random_title,
    } = result;

    // check if we have all the necessary fields
    if (
      !less_extreme ||
      !adjacent ||
      !more_extreme ||
      !random ||
      !advice_question ||
      !less_extreme_title ||
      !adjacent_title ||
      !more_extreme_title ||
      !random_title
    ) {
      console.log("Missing fields in scenario generation", result);
      // regenerate
      if (loopCount > 2) {
        return null;
      }
      return await returnOrCreateScenarioIfNecessary({
        profile_id,
        loopCount: loopCount + 1,
      });
    }

    const choices = [
      {
        choice: less_extreme,
        title: less_extreme_title,
      },
      {
        choice: adjacent,
        title: adjacent_title,
      },
      {
        choice: more_extreme,
        title: more_extreme_title,
      },
      {
        choice: random,
        title: random_title,
      },
    ];

    console.log("New scenario created");

    const { data: newScenario } = await supabase
      .from("scenarios")
      .insert({
        profile_id,
        choices,
        dilemma: advice_question,
        expires_at: dayjs().add(60, "minute").toISOString(),
      })
      .select("*");

    await alertUserAboutNewScenario({ bot, profile_id });
    return newScenario;
  } else {
    return null;
  }
}

async function considerCreatingANewScenario({
  botProfile,
  shouldCheckActiveHours,
}) {
  if (!botProfile?.cyoa_mode) {
    return;
  }

  await returnOrCreateScenarioIfNecessary({
    profile_id: botProfile.id || botProfile.profile_id,
    loopCount: 0,
    shouldCheckActiveHours,
  });
}

module.exports = {
  considerCreatingANewScenario,
  returnOrCreateScenarioIfNecessary,
  checkForActiveScenario,
  checkIfUserIsActive,
};
