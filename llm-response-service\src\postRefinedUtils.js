const { logError } = require("./logUtils");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { sendPushNotificationsForProfileId } = require("./notifications");

async function onPostRefinedTaskSucceeded(workflow_payload) {
  const { post_id, profile_id, username, user_id } = workflow_payload;
  try {
    const [postSlug, selectedProfile, userActive] = await Promise.all([
      supabase.from("posts").select("slug").eq("id", post_id).single(),
      supabase
        .from("bots")
        .select("creator_id")
        .eq("profile_id", profile_id)
        .single(),
      supabase.from("users").select("is_app_active").eq("id", user_id).single(),
    ]);
    const { data: slugData, error: slugError } = postSlug;
    const { data: selectedData, error: selectedProfileError } = selectedProfile;
    const { data: activeData, error: activeError } = userActive;

    if (slugError) {
      throw wrappedSupabaseError(
        slugError,
        "failed to fetch post slug for notification",
      );
    }

    if (selectedProfileError) {
      throw wrappedSupabaseError(
        selectedProfileError,
        "failed to fetch profile id for notification",
      );
    }

    if (activeError) {
      throw wrappedSupabaseError(
        activeError,
        "failed to fetch app active for notification",
      );
    }

    if (activeData.is_app_active) {
      return;
    }

    sendPushNotificationsForProfileId({
      profileId: selectedData.creator_id,
      notification: {
        id: Math.floor(Math.random() * 1000000 + 1),
        source_type: "post_refined",
        image_url: "",
      },
      notificationTitle: `Post is Ready`,
      notificationText: `Check out @${username}'s refined post`,
      notificationLink: `/users/${username}/p/${slugData.slug}?type=post_refined`,
      notificationData: {
        notifyType: "post_refined",
      },
    });
  } catch (error) {
    logError({
      context: "onPostRefinedTaskSucceeded failed",
      error,
    });
    return;
  }
}

module.exports = {
  onPostRefinedTaskSucceeded,
};
