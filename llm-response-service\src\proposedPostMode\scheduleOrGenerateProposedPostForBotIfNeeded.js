const { logInfo, logWarn } = require("../logUtils");
const {
  generateProposedPostForBotIfNeeded,
} = require("./generateProposedPostForBotIfNeeded");
const {
  scheduleProposedPostGenerationTask,
} = require("./scheduleProposedPostGenerationTask");
const { getGenerationTriggerDate } = require("./utils");

async function scheduleOrGenerateProposedPostForBotIfNeeded({
  bot,
  botProfile,
  nowDate = new Date(),
}) {
  // TODO: lower to logDebug
  logInfo({
    context: "scheduleOrGenerateProposedPostForBotIfNeeded",
    bot: bot,
    botProfile: botProfile,
    nowDate,
  });

  if (!botProfile.proposed_post_mode) {
    logWarn({
      context: "scheduleOrGenerateProposedPostForBotIfNeeded",
      message: "bot does not have proposed post mode enabled",
      botProfileId: botProfile.id,
    });
    return;
  }

  const proposedPostNextGenerationDate = new Date(
    botProfile.proposed_post_next_generation_date,
  );
  const generationTriggerDate = getGenerationTriggerDate({
    proposedPostNextGenerationDate,
  });
  if (generationTriggerDate > nowDate) {
    logInfo({
      context: "scheduleOrGenerateProposedPostForBotIfNeeded",
      message:
        "NOT ready to generate proposed post yet, scheduling for later...",
      botProfileId: botProfile.id,
      proposedPostNextGenerationDate,
      generationTriggerDate,
      nowDate,
    });
    await scheduleProposedPostGenerationTask({
      botProfileId: botProfile.id,
      scheduleDate: generationTriggerDate,
    });
  } else {
    logInfo({
      context: "scheduleOrGenerateProposedPostForBotIfNeeded",
      message: "READY to generate proposed post!",
      botProfileId: botProfile.id,
      proposedPostNextGenerationDate,
      generationTriggerDate,
      nowDate,
    });
    await generateProposedPostForBotIfNeeded({
      bot,
      botProfile,
      nowDate,
    });
  }
}

module.exports = {
  scheduleOrGenerateProposedPostForBotIfNeeded,
};
