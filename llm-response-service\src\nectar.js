const { authUser } = require("./middleware");
const { supabase } = require("./supabaseClient");
const { wrappedSupabaseError, logError, logInfo } = require("./utils");

const express = require("express");
const nectarRouter = express.Router();

nectarRouter.post("/getUserBalance", authUser, async (req, res) => {
  const user_id = req.user?.id;
  if (!user_id) {
    return res.sendStatus(403); // Forbidden: user is not authenticated
  }

  try {
    const { data, error } = await supabase.rpc("internal.get_balance", {
      user_id,
    });

    if (error) {
      throw wrappedSupabaseError(error, "failed to get user balance");
    }

    logInfo({
      context: "getUserBalance",
      user_id,
      data: data,
    });

    return res.json({
      balance: data._balance,
      free_balance: data._free_balance,
    });
  } catch (error) {
    logError({
      context: "getUserBalance Error",
      error,
      user_id,
    });
    return res.sendStatus(500);
  }
});

nectarRouter.post("/getPrices", authUser, async (req, res) => {
  // At some point we may give different prices to users, but for now
  // we just return the same prices to everyone.
  const user_id = req.user?.id;
  if (!user_id) {
    return res.sendStatus(403); // Forbidden: user is not authenticated
  }

  try {
    const { data, error } = await supabase
      .from("internal.nectar_prices")
      .select("*");
    if (error) {
      throw wrappedSupabaseError(error, "failed to get prices");
    }
    return res.json(data);
  } catch (error) {
    logError({
      context: "getPrices Error",
      error,
      user_id,
    });
    return res.sendStatus(500);
  }
});

nectarRouter.post("/claimEligible", authUser, async (req, res) => {
  const user_id = req.user?.id;
  if (!user_id) {
    return res.sendStatus(403); // Forbidden: user is not authenticated
  }

  const { item, quantity = 1 } = req.body; // Destructure with default value for quantity
  if (!item) {
    return res.sendStatus(400);
  }

  try {
    const { data, error } = await supabase.rpc("internal.claim_eligible", {
      item,
      quantity,
      user_id,
    });
    if (error) {
      throw wrappedSupabaseError(error, "failed to get claim eligible");
    }
    return res.json(data);
  } catch (error) {
    logError({
      context: "claimEligible Error",
      error,
      user_id,
      item,
      quantity,
    });
    return res.sendStatus(500);
  }
});

nectarRouter.post("/buy", authUser, async (req, res) => {
  const user_id = req.user?.id;
  if (!user_id) {
    return res.sendStatus(403); // Forbidden: user is not authenticated
  }

  const { item, quantity = 1 } = req.body; // Destructure with default value for quantity

  if (!item) {
    return res.sendStatus(400);
  }

  try {
    const { data, error } = await supabase.rpc("internal.buy_item", {
      item_name: item,
      quantity,
      user_id,
    });

    if (error) {
      throw wrappedSupabaseError(error, "failed to buy item");
    }

    return res.json(data);
  } catch (error) {
    logError({
      context: "buyItem Error",
      error,
      user_id,
      item,
      quantity,
    });
    return res.sendStatus(500);
  }
});

module.exports = {
  nectarRouter,
};
