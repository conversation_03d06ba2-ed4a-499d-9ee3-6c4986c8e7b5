const { pregeneratePostAndTaskStubs, generatePost } = require("../botHelpers");
const { logInfo, logWarn } = require("../logUtils");
const { fetchCurrentProposedPost } = require("./fetchCurrentProposedPost");
const { getGenerationTriggerDate } = require("./utils");

async function generateProposedPostForBotIfNeeded({
  bot,
  botProfile,
  nowDate,
}) {
  // TODO: lower to logDebug once we're confident this is working
  logInfo({
    context: "generateProposedPostForBotIfNeeded",
    bot: bot,
    botProfile: botProfile,
    nowDate,
  });

  if (!botProfile.proposed_post_mode) {
    logWarn({
      context: "generateProposedPostForBotIfNeeded",
      message: `generateProposedPostForBotIfNeeded called for a bot profile whose proposed_post_mode is NOT set to true, bailing out`,
      bot_profile_id: botProfile.id,
    });
    return;
  }

  const proposedPostNextGenerationDate = new Date(
    botProfile.proposed_post_next_generation_date,
  );
  const generationTriggerDate = getGenerationTriggerDate({
    proposedPostNextGenerationDate,
  });
  // additional -5 minutes just in case this gets called a bit early
  const thresholdDate = new Date(
    generationTriggerDate.getTime() - 5 * 60 * 1000,
  );
  if (thresholdDate > nowDate) {
    logWarn({
      context: "generateProposedPostForBotIfNeeded",
      message: `threshold date date is in the future, bailing out`,
      bot_profile_id: botProfile.id,
      proposedPostNextGenerationDate,
      generationTriggerDate,
      thresholdDate,
      nowDate,
    });
    return;
  }

  const currentProposedPost = await fetchCurrentProposedPost({
    bot_profile_id: botProfile.id,
    includeGenerating: true,
  });

  if (currentProposedPost) {
    logWarn({
      context: "generateProposedPostForBotIfNeeded",
      message: `already have a proposed post, bailing out`,
      bot_profile_id: botProfile.id,
      current_post_id: currentProposedPost.id,
    });
    return;
  }

  const pregeneratedStubs = await pregeneratePostAndTaskStubs({
    bot_profile_id: botProfile.id,
    insertPostValues: {
      proposed_post_state: "generating",
    },
  });

  const result = await generatePost({
    bot: bot,
    priority: 5,
    pregeneratedTaskStub: pregeneratedStubs.taskStub,
    pregeneratedPostStub: pregeneratedStubs.postStub,
    proposedPostMode: true,
  });

  // TODO: lower to logDebug once we're confident this is working
  logInfo({
    context: "generateProposedPostForBotIfNeeded",
    message: `${bot.id} finished generateProposedPostForBotIfNeeded`,
    bot_id: bot.id,
    bot_profile_id: botProfile.id,
    result,
  });

  return result;
}

module.exports = {
  generateProposedPostForBotIfNeeded,
};
