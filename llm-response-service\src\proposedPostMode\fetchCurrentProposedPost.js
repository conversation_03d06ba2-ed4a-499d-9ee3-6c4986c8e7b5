const { logError, logInfo } = require("../logUtils");
const { wrappedSupabaseError, supabase } = require("../supabaseClient");

async function fetchCurrentProposedPost({
  bot_profile_id,
  includeGenerating = false,
}) {
  if (!bot_profile_id) {
    throw new Error("invalid bot_profile_id");
  }

  const partialQuery = supabase
    .from("posts")
    .select(
      `id, created_at, profile_id, media_url, location, slug, description, type, tags, visibility, nsfw, nsfl, previewhash, proposed_post_state, ai_caption, 
      profiles!posts_profile_id_fkey(id, bots!bots_profile_id_fkey(id))
      `,
    )
    .eq("profile_id", bot_profile_id)
    .not("visibility", "in", "(hidden,archived)")
    .order("created_at", { ascending: false });

  let query;
  if (includeGenerating) {
    // include generating posts that were created in the last hour.
    // if the post has been generating for more than an hour, image generation
    // has probably shit the bed.
    const oneHourAgo = new Date(Date.now() - 1 * 60 * 60 * 1000);
    query = partialQuery.or(
      `proposed_post_state.eq.proposed,and(proposed_post_state.eq.generating,created_at.gte.${oneHourAgo.toISOString()})`,
    );
  } else {
    query = partialQuery.eq("proposed_post_state", "proposed");
  }

  const { data: proposedPost, error: proposedPostError } = await query
    .limit(1)
    .maybeSingle();

  logInfo({
    context: "fetchCurrentProposedPost",
    message: "proposedPost",
    proposedPost: proposedPost,
    includeGenerating: includeGenerating,
  });

  if (proposedPostError) {
    const error = wrappedSupabaseError(
      proposedPostError,
      "failed to fetch current proposed post",
    );
    logError({
      bot_profile_id: bot_profile_id,
      context: "fetchCurrentProposedPost",
      error: error,
    });
    throw error;
  }

  return proposedPost;
}

module.exports = {
  fetchCurrentProposedPost,
};
