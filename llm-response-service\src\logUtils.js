const { messageWithCauses, stackWithCauses } = require("pony-cause");
const { collectActiveSpanMetadata } = require("./instrumentation/tracer");

function makeLoggingPayload(payload = {}) {
  const activeSpanMetadata = collectActiveSpanMetadata();

  if (!activeSpanMetadata) {
    return payload;
  }

  return {
    ...payload,
    spanId: undefined, // we want to override the provided spanId which is using the old `executionId` value
    ...activeSpanMetadata,
  };
}

function errorToLoggingPayloadMessageProperties(error) {
  if (!error) {
    return {};
  }
  if (typeof error === "string" || error instanceof String) {
    return {
      error: error,
    };
  }
  return {
    error: messageWithCauses(error) || error?.message,
    stack: stackWithCauses(error) || error?.stack,
    cause: error?.cause,
    code: error?.code || undefined, // supabase errors often have a 'code'
    // PostgREST errors can have a 'details' and 'hint' https://postgrest.org/en/stable/references/errors.html#errors-from-postgresql
    details: error?.details || undefined,
    hint: error?.hint || undefined,
  };
}

function logFatal({ context, error, __logEntryRoot = {}, ...otherProps }) {
  let {
    // executionId,
    ...rest
  } = otherProps;
  const errorMessageProps = errorToLoggingPayloadMessageProperties(error);
  console.error(
    JSON.stringify(
      makeLoggingPayload({
        severity: "CRITICAL",
        message: {
          context,
          ...errorMessageProps,
          detect_json: true,
          ...(rest ?? {}),
        },
        ...__logEntryRoot,
      }),
    ),
  );
}

function logErrorV2(context, error) {
  const errorMessageProps = errorToLoggingPayloadMessageProperties(error);
  console.error(
    JSON.stringify(
      makeLoggingPayload({
        severity: "ERROR",
        message: {
          context,
          ...errorMessageProps,
          detect_json: true,
        },
      }),
    ),
  );
}

function logError({ context, error, __logEntryRoot = {}, ...otherProps }) {
  let { executionId, ...rest } = otherProps;
  // Stop using executionId
  if (!executionId) {
    executionId = 0;
  }
  const errorMessageProps = errorToLoggingPayloadMessageProperties(error);
  console.error(
    JSON.stringify(
      makeLoggingPayload({
        severity: "ERROR",
        message: {
          context,
          ...errorMessageProps,
          detect_json: true,
          ...(rest ?? {}),
        },
        ...__logEntryRoot,
      }),
    ),
  );
}

function logWarn({
  context,
  message,
  error = undefined,
  __logEntryRoot = {},
  ...otherProps
}) {
  let { executionId, ...rest } = otherProps;
  if (!executionId) {
    executionId = 0;
  }
  const errorMessageProps = errorToLoggingPayloadMessageProperties(error);
  console.warn(
    JSON.stringify(
      makeLoggingPayload({
        severity: "WARN",
        message: {
          context,
          ...errorMessageProps,
          message,
          detect_json: true,
          ...(rest ?? {}),
        },
        ...__logEntryRoot,
      }),
    ),
  );
}

function logInfo({
  context,
  message,
  error = undefined,
  __logEntryRoot = {},
  ...otherProps
}) {
  let { executionId, ...rest } = otherProps;
  if (!executionId) {
    executionId = 0;
  }
  const errorMessageProps = errorToLoggingPayloadMessageProperties(error);
  console.info(
    JSON.stringify(
      makeLoggingPayload({
        severity: "INFO",
        message: {
          context,
          ...errorMessageProps,
          message,
          detect_json: true,
          ...(rest ?? {}),
        },
        ...__logEntryRoot,
      }),
    ),
  );
}

function logDebug({ context, message, __logEntryRoot = {}, ...otherProps }) {
  let { executionId, ...rest } = otherProps;
  if (!executionId) {
    executionId = 0;
  }
  if (process.env.LOG_DEBUG_ENABLED !== "1") return;

  if (message && message?.length > 2000) {
    return;
  }

  console.debug(
    JSON.stringify(
      makeLoggingPayload({
        severity: "DEBUG",
        message: {
          context,
          message,
          detect_json: true,
          ...(rest ?? {}),
        },
        ...__logEntryRoot,
      }),
    ),
  );
}

const stringifyArgs = (args) => {
  return args
    .map((arg) => {
      if (arg instanceof Error) {
        return messageWithCauses(arg);
      } else if (arg && arg.message) {
        return arg.message;
      } else {
        return arg.toString();
      }
    })
    .join(" ");
};

// This function is a bridge between console.error and structured logging
function __console_error_do_not_use(...args) {
  if (args.length === 0) {
    return;
  }

  try {
    const lastValue = args[args.length - 1];
    const isLastValueErrorLike =
      lastValue instanceof Error || (lastValue && lastValue.message);

    if (args.length === 2 && typeof args[0] === "string") {
      if (isLastValueErrorLike) {
        return logError({ context: args[0], error: args[1] });
      } else {
        return logError({ context: args[0], error: JSON.stringify(args[1]) });
      }
    }

    if (isLastValueErrorLike) {
      const argsWithoutLastValue = args.slice(0, -1);
      const stringifiedArgs = stringifyArgs(argsWithoutLastValue);
      return logError({ context: stringifiedArgs, error: lastValue });
    }

    const stringifiedArgs = stringifyArgs(args);

    return logError({
      message: stringifiedArgs,
    });
  } catch (error) {
    // This means we failed to log using the structured log function, fall back
    console.error(
      "**** Failed to log using logError: ",
      error,
      "falling back to console.error",
    );
    console.error(...args);
  }
}

// This function is a bridge between console.log/console.info and structured logging
function __console_info_do_not_use(...args) {
  if (args.length === 0) {
    return;
  }

  try {
    const stringifiedArgs = stringifyArgs(args);
    return logInfo({ context: stringifiedArgs });
  } catch (error) {
    // This means we failed to log using the structured log function, fall back
    console.error(
      "**** Failed to log using logInfo: ",
      error,
      "falling back to console.info",
    );
    console.info(...args);
  }
}

// This function is a bridge between console.warn and structured logging
function __console_warn_do_not_use(...args) {
  if (args.length === 0) {
    return;
  }

  try {
    const stringifiedArgs = stringifyArgs(args);
    return logWarn({ context: stringifiedArgs });
  } catch (error) {
    // This means we failed to log using the structured log function, fall back
    console.error(
      "**** Failed to log using logWarn: ",
      error,
      "falling back to console.warn",
    );
    console.warn(...args);
  }
}

module.exports = {
  logFatal,
  logError,
  logErrorV2,
  logWarn,
  logInfo,
  logDebug,
  __console_error_do_not_use,
  __console_info_do_not_use,
  __console_warn_do_not_use,
};
