const fs = require("fs").promises;
const path = require("path");
const { supabase } = require("../supabaseClient");
const dayjs = require("dayjs");
const timezone = require("dayjs/plugin/timezone");

dayjs.extend(timezone);

const OUTPUT_DIR = path.join(__dirname, "./output");

async function loadBots() {
  try {
    const files = await fs.readdir(OUTPUT_DIR);
    const jsonFiles = files.filter((file) => path.extname(file) === ".json");

    const bots = [];

    for (const file of jsonFiles) {
      const filePath = path.join(OUTPUT_DIR, file);
      const data = await fs.readFile(filePath, "utf8");
      try {
        const bot = JSON.parse(data);

        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .upsert({
            username: bot.username,
            description: bot.bio,
            display_name: bot.display_name,
            follower_count: bot.follower_count / 10,
            following_count: bot.following_count / 10,
            location: bot.location,
          })
          .select();

        console.log("profile", profile, profileError);

        if (profile) {
          let timezone;

          try {
            dayjs.tz("2013-11-18T11:55:20", bot.timezone);
            timezone = bot.timezone;
          } catch (error) {
            console.error("error getting bot timezone:", error);
            const myArray = [
              "America/Los_Angeles",
              "America/New_York",
              "America/Chicago",
              "Europe/Paris",
              "Asia/Tokyo",
            ];

            // Generate a random index within the array length
            const randomIndex = Math.floor(Math.random() * myArray.length);

            // Get the random string from the array
            // const randomString = myArray[randomIndex];
            // FIXME: randomString is not used here?
            timezone = randomIndex;
          }

          const min = 9;
          const max = 16;
          const randomHourAvailable =
            Math.floor(Math.random() * (max - min + 1)) + min;

          const min2 = 7;
          const max2 = 11;
          const randomMinuteWakeUpTime = (
            Math.random() * (max2 - min2) +
            min2
          ).toFixed(2);

          const min3 = 30;
          const max3 = 130;
          const randomTimeInterval =
            Math.floor(Math.random() * (max3 - min3 + 1)) + min3;

          const { data: botResult, error: botError } = await supabase
            .from("bots")
            .upsert({
              profile_id: profile[0].id,
              bio: `You are ${bot.display_name} from ${bot.source}.`,
              source: bot.source,
              franchise: bot.frnachise,
              tag: bot.tag,
              gender: bot.gender,
              timezone: timezone,
              active_hours_per_day: randomHourAvailable,
              wake_up_time: parseInt((randomMinuteWakeUpTime * 60).toFixed(0)),
              wake_up_interval: randomTimeInterval,
              seaart_token: bot.seaart_token,
            })
            .select();

          console.log("bot", botResult, botError);
        }
      } catch (jsonErr) {
        console.error(`Error parsing JSON from file ${file}: ${jsonErr}`);
      }
    }

    return bots;
  } catch (err) {
    console.error(`Error: ${err}`);
  }
}

module.exports = { loadBots };
