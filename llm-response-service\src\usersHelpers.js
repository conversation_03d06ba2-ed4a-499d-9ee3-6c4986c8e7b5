const { default: axios } = require("axios");
const { supabase } = require("./supabaseClient");

async function getEstimatedTimezone({ user_id }) {
  const { data, error } = await supabase
    .schema("internal")
    .from("user_metadata")
    .select("estimated_timezone")
    .eq("user_id", user_id)
    .single();

  if (error) {
    // no op
  }

  if (data && data.estimated_timezone) {
    return data.estimated_timezone;
  }

  let timezone;

  // fetch last known ip

  const { data: ipData } = await supabase
    .schema("internal")
    .from("user_sessions")
    .select("last_known_ip")
    .eq("user_id", user_id)
    .single();

  if (ipData.last_known_ip) {
    try {
      console.log("last known ip", ipData.last_known_ip);

      const url = `https://ipinfo.io/${ipData.last_known_ip.split("/"[0])}?token=740e5c5a6e46a5`;
      console.log("QUERY URL", url);

      const { data: result } = await axios.get(url);

      console.log("RESULT", result);

      if (result.timezone) {
        console.log("GOT RESULT TIME ZONE MAN", result.timezone);
        // update user timezone
        await supabase
          .schema("internal")
          .from("user_metadata")
          .upsert({ user_id: user_id, estimated_timezone: result.timezone });

        // update user timezone
        await supabase
          .schema("internal")
          .from("user_metadata")
          .update({ estimated_timezone: result.timezone })
          .eq("user_id", user_id);

        timezone = result.timezone;

        console.log("now go", data, error);
      }
    } catch (error) {
      console.error("Error getting timezone", error);
    }
  }

  // if not time zone, default to los angeles
  if (!timezone) {
    timezone = "America/Los_Angeles";
  }

  return timezone;
}

module.exports = {
  getEstimatedTimezone,
};
