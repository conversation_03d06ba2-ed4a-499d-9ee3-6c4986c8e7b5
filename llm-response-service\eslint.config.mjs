import globals from "globals";
// import tseslint from "typescript-eslint";
import eslintjs from "@eslint/js";
import jest from "eslint-plugin-jest";
import eslintConfigPrettier from "eslint-config-prettier";

const ignorePatterns = ["src/models/**/*.*", "src/data/**/*.*"];

export default [
  eslintjs.configs.recommended,
  // ...tseslint.configs.recommended, // we don't use typescript
  {
    rules: {
      "no-unused-vars": [
        "error",
        {
          vars: "all",
          args: "none",
          argsIgnorePattern: "^_",
          caughtErrorsIgnorePattern: "^_",
          destructuredArrayIgnorePattern: "^_",
        },
      ],
      // "no-console": "error",
      "no-undef": "error",
      "no-unused-expressions": "error",
      "no-useless-escape": "warn",
      // "@typescript-eslint/no-unused-vars": "off", // we don't use typescript
      // "@typescript-eslint/no-var-requires": "off", // we don't use typescript (or the module import syntax in js)
    },
    languageOptions: { globals: globals.node },
  },
  eslintConfigPrettier, // turns off any rules that might conflict with prettier
  {
    files: ["**/*.test.js"],
    plugins: { jest },
    rules: {
      ...jest.configs.recommended.rules,
    },
    languageOptions: {
      globals: {
        ...globals.jest,
      },
    },
  },
  {
    ignores: [...ignorePatterns],
  },
];
