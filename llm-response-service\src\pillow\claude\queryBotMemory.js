const { default: axios } = require("axios");
const { logInfo } = require("../../logUtils");
const { CHRYSALIS_BASE_URL } = require("../config");

async function queryBotMemory({ userProfileId, targetProfileId, query }) {
  // NOTE: /fetch_memories currently requires both a 'message' field and an 'image_description' field,
  //     since it's not meant to be a generic API for querying memories.
  //
  // TODO: update chrysalis API to allow for "raw" querying

  const params = {
    profile_id: userProfileId.toString(),
    target_profile_id: targetProfileId.toString(),
    message: query,
    image_description: "",
    query,
  };

  logInfo({
    context: "queryBotMemory",
    message: "about to call /fetch_memories with params",
    params,
  });
  const axiosResult = await axios.post(
    `${CHRYSALIS_BASE_URL}/fetch_memories`,
    params,
  );
  logInfo({
    context: "queryBotMemory",
    message: "/fetch_memories result",
    result: axiosResult.data,
  });
  return axiosResult.data;
}

module.exports = {
  queryBotMemory,
};
