apiVersion: v1
kind: Secret
metadata:
  name: cocoon-secret
type: Opaque
stringData:
  SERVER_ENDPOINT: "https://cocoon.butterflies.ai/v1"
  BOT_SERVER_ENDPOINT: "https://cocoon.butterflies.ai/v1"
  SUPABASE_URL: "https://ciqehpcxkkhdjdxolvho.supabase.co"
  SUPABASE_SECRET_KEY: "***"
  SUPABASE_ANON_KEY: "***"
  GPT_TEXT_MODEL: "gpt-3.5-turbo-0125"
  OPENAI_API_KEY: "***"
  CLOUD_TASK_V1: "v1"
  CLOUD_TASK_V1_IMAGE_GENERATION: "v1-image-generation"
  CLOUD_TASK_V1_RUN_BOTS: "v1-run-bots"
  CLOUD_TASK_V1_IMAGE_GENERATION_UPDATE_STATUS: "v1-image-generation-update-status"
  CLOUD_TASK_V1_HANDLE_IMAGE: "v1-handle-image"
  TYPESENSE_HOST: "phxmsn6ezjlo7g1tp-1.a1.typesense.net"
  TYPESENSE_API_KEY: "***"
  NODE_ENV: "production"
  LOG_DEBUG_ENABLED: "0"
  STRIPE_SECRET_KEY: "***"
  STRIPE_WEBHOOK_ENDPOINT_SECRET: "***"
  WEBSITE_URL: "https://www.butterflies.ai"
  BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN: "***"
  OTEL_EXPORTER_OTLP_ENDPOINT: "http://opentelemetry-collector.opentelemetry.svc.cluster.local:4318"
  OTEL_TRACES_EXPORTER: "otlp"
  OTEL_METRICS_EXPORTER: "otlp"
  OTEL_SERVICE_NAME: "butterflies-api"
