const { Logging } = require("@google-cloud/logging");

const { collectActiveSpanMetadata, logWarn } = require("./utils");

const logging = new Logging({
  projectId: "butterflies-ai",
});

const RESOURCE_DESCRIPTOR = {
  labels: {
    location: "us-central1",
    project_id: "butterflies-ai",

    ////////////////////////////////////////////////////////////////////////
    //
    // these values are specific to the Cloud Run environment
    // (and are needed to easily match with other logs from the same resource in Cloud Logging)
    // https://cloud.google.com/run/docs/container-contract#env-vars
    service_name: process.env.K_SERVICE || undefined,
    revision_name: process.env.K_REVISION || undefined,
    configuration_name: process.env.K_CONFIGURATION || undefined,

    ////////////////////////////////////////////////////////////////////////
    //
    // these values are specific to the Kubernetes environment
    // (and are needed to easily match with other logs from the same resource in Cloud Logging)

    // FIXME: not exposed currently via env vars
    // cluster_name: process.env.K8S_CLUSTER_NAME || undefined,

    // FIXME: not exposed currently via env vars
    // container_name: process.env.K8S_CONTAINER_NAME || undefined,

    // FIXME: not exposed currently via env vars
    // namespace_name: process.env.K8S_NAMESPACE_NAME || undefined,

    pod_name: process.env.HOSTNAME || undefined, // is HOSTNAME guaranteed to always match the k8s pod name?
  },
  type: process.env.KUBERNETES_SERVICE_HOST
    ? "k8s_container"
    : "cloud_run_revision",
};

const LOG_INFO = {
  resource: RESOURCE_DESCRIPTOR,
  severity: "INFO",
};

const LOG_WARNING = {
  resource: RESOURCE_DESCRIPTOR,
  severity: "WARNING",
};

const LOG_ERROR = {
  resource: RESOURCE_DESCRIPTOR,
  severity: "ERROR",
};

function loggingDuration(logName, jsonPayLoad, minDuration) {
  if (jsonPayLoad && jsonPayLoad.duration) {
    if (jsonPayLoad.duration >= minDuration) {
      writeLog(LOG_INFO, logName, jsonPayLoad);
    }
  }
}

function loggingInfo(logName, jsonPayLoad) {
  writeLog(LOG_INFO, logName, jsonPayLoad);
}

function loggingWarning(logName, jsonPayLoad) {
  writeLog(LOG_WARNING, logName, jsonPayLoad);
}

async function loggingError(logName, jsonPayLoad) {
  writeLog(LOG_ERROR, logName, jsonPayLoad);
}

async function writeLog(metadata, logName, jsonPayLoad) {
  try {
    const log = logging.log(logName);

    if (logName === "api_calls") {
      if (jsonPayLoad && jsonPayLoad.payload) {
        jsonPayLoad.payloadText = JSON.stringify(jsonPayLoad.payload);
        delete jsonPayLoad.payload;
      }

      if (jsonPayLoad && jsonPayLoad.result) {
        jsonPayLoad.resultText = JSON.stringify(jsonPayLoad.result);
        delete jsonPayLoad.result;
      }
    }

    /** @type {import('@google-cloud/logging/build/src/entry').LogEntry} */
    let decoratedMetadata = metadata;
    const activeSpanMetadata = collectActiveSpanMetadata();
    if (activeSpanMetadata) {
      // https://cloud.google.com/logging/docs/agent/logging/configuration#special-fields
      // decoratedMetadata is LogEntry
      decoratedMetadata["logging.googleapis.com/trace"] =
        activeSpanMetadata["logging.googleapis.com/trace"];
      decoratedMetadata["logging.googleapis.com/spanId"] =
        activeSpanMetadata["logging.googleapis.com/spanId"];
      decoratedMetadata["logging.googleapis.com/trace_sampled"] =
        activeSpanMetadata["logging.googleapis.com/trace_sampled"];
    }

    const entry = log.entry(decoratedMetadata, jsonPayLoad);
    await log.write(entry);
  } catch (error) {
    logWarn({
      context: "**** logging exception",
      message: error?.message,
    });
  }
}

module.exports = { loggingInfo, loggingDuration, loggingWarning, loggingError };
