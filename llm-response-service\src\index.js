const process = require("node:process");
const os = require("os");
const express = require("express");
require("express-async-errors");
const bodyParser = require("body-parser");

const { baseUrl } = require("./api");
const { app: rootRouter } = require("./root.js");
const { app: botsRouter } = require("./bot.js");
const { app: searchRouter } = require("./search.js");
const { app: notificationsRouter } = require("./notifications.js");
const { usersRouter } = require("./users.js");
const { app: postsRouter } = require("./posts.js");
const { app: scenariosRouter } = require("./scenarios.js");

const { app: profilesRouter } = require("./profiles.js");
const { app: promoCodesRouter } = require("./promoCodes.js");
const { app: comfyRouter } = require("./comfy.js");
const { app: memoryRouter } = require("./memory.js");
const { app: moderationRouter } = require("./moderation.js");
const { app: loopsRouter } = require("./loops.js");
const { app: videoRouter } = require("./video.js");
const { app: bobaRouter } = require("./boba/boba.js");

const { activitiesRouter } = require("./activities.js");
const { nectarRouter } = require("./nectar.js");
const { sitemapsRouter } = require("./sitemaps.js");
const { dreamsRouter } = require("./dreams.js");
const { pillowRouter } = require("./pillow.js");
const { proposedPostsRouter } = require("./proposedPostMode/router.js");
const { vignettesRouter } = require("./vignettes/router.js");

const { client } = require("./posthog.js");
const { loggingInfo } = require("./logging");
const { app: invitesRouter } = require("./invites.js");
const { app: messagesRouter } = require("./messages.js");
const { app: billingRouter } = require("./billing.js");
const { app: leaderboardRouter } = require("./leaderboard.js");
const { app: promptsRouter } = require("./prompts.js");
const { app: videoClipsRouter } = require("./videoClips.js");

const {
  instrumentationCleanup,
} = require("./instrumentation/instrumentation.js");
const { generateSpanID, logWarn, logError, logFatal } = require("./utils");
const { gcloudErrorReporting } = require("./errorReporting");
const { meter } = require("./metrics");
const { trace, context } = require("@opentelemetry/api");
const { logInfo } = require("./logUtils");

require("dotenv").config();

const reqsCounter = meter.createCounter("cocoon-requests", {
  monotonic: true,
  labelKeys: ["url", "path", "method", "status_code", "env"],
  description: "Counts total number of requests",
});

const uncaughtExceptionCounter = meter.createCounter(
  "cocoon-uncaught-exceptions",
  {
    monotonic: true,
    labelKeys: ["env"],
    description: "Counts total number of uncaught exceptions",
  },
);

const unhandledRejectionCounter = meter.createCounter(
  "cocoon-unhandled-rejections",
  {
    monotonic: true,
    labelKeys: ["env"],
    description: "Counts total number of unhandled rejections",
  },
);

const warningsCounter = meter.createCounter("cocoon-warnings", {
  monotonic: true,
  labelKeys: ["env"],
  description: "Counts total number of warnings",
});

const latencyHistogram = meter.createHistogram("cocoon-latency", {
  description: "Latency of HTTP requests",
  unit: "ms",
  labelKeys: ["url", "path", "method", "env"],
  boundaries: [1, 10, 100, 1000, 10000],
});

const blockedIps = ["*************", " ************"];

// Uncaught exception, this is really bad and fatal
process.on("uncaughtException", (error, origin) => {
  try {
    uncaughtExceptionCounter.add(1, {
      env: process.env.COCOON_ENV,
    });
    logFatal({
      context: `***** FATAL uncaughtException origin: ${origin}`,
      error: error,
    });
  } catch (structuredLoggingError) {
    console.error(
      "Failed to log uncaughtException using structured logging, falling back on console.error",
      structuredLoggingError,
    );
    console.error(`***** FATAL uncaughtException origin: ${origin}`, error);
  }
  gcloudErrorReporting.report(error);

  instrumentationCleanup().then(() => {
    // See: https://nodejs.org/api/process.html#:~:text=%27uncaughtExceptionMonitor%27%20listener.-,Warning%3A%20Using%20%27uncaughtException%27%20correctly,-%23
    process.exit(1);
  });
});

// Unhandled promise rejection, this is bad but not fatal
process.on("unhandledRejection", (reason, promise) => {
  try {
    unhandledRejectionCounter.add(1, {
      env: process.env.COCOON_ENV,
    });
    logError({
      context: `***** unhandledRejection:`,
      error: reason,
    });
  } catch (structuredLoggingError) {
    console.error(
      "Failed to log unhandledRejection using structured logging, falling back on console.error",
      structuredLoggingError,
    );
    console.error(`***** unhandledRejection:`, reason);
  }
  gcloudErrorReporting.report(reason);
});

process.on("warning", (warning) => {
  warningsCounter.add(1, {
    env: process.env.COCOON_ENV,
  });
  logWarn({
    context: "process.onWarning",
    message: warning.message,
    name: warning.name,
    stack: warning.stack,
  });
});

const signalHandler = (signal) => {
  logError({
    context: `Signal ${signal} received`,
  });
  const exitCode = 128 + os.constants.signals[signal]; // match default Node.JS SIGTERM and SIGINT behavior
  console.log("exitCode will be:", exitCode);
  instrumentationCleanup().then(() => {
    process.exit(exitCode);
  });
};

process.on("SIGINT", signalHandler);
process.on("SIGTERM", signalHandler);

process.on("exit", (code) => {
  if (code === 0) {
    logError({
      context: `About to exit with code: ${code}`,
    });
  } else {
    logWarn({ context: `About to exit with code: ${code}` });
  }
});

const app = express();

// Disable 'console.log' when deployed in production
if (process.env.NODE_ENV === "production") {
  console.log = function () {};
}

app.use((req, res, next) => {
  req.executionId = generateSpanID();
  let requestStartTime = new Date();

  const currentSpan = trace.getSpan(context.active());

  if (currentSpan) {
    const spanContext = currentSpan.spanContext();

    const traceparent = `00-${spanContext.traceId}-${spanContext.spanId}-0${Number(spanContext.traceFlags).toString(16)}`;
    res.setHeader("traceparent", traceparent);
    res.setHeader("X-Cloud-Trace-Context", spanContext.traceId);

    if (spanContext.traceState) {
      res.setHeader("tracestate", spanContext.traceState.serialize());
    }
  }

  var path = req.path;
  // Remove trailing number if path ends with a number.
  // This is to avoid polluting the metrics with unique profile IDs.
  path = path.replace(/\/\d+$/, "");

  if (req.get("X-Forwarded-For")) {
    const ip = req
      .get("X-Forwarded-For")
      .split(",")
      .map((item) => item.trim())[0];
    if (blockedIps.includes(ip)) {
      logInfo({
        context: "HTTP",
        message: "finished handling",
        method: req.method,
        url: req.url,
        path: path,
        userAgent: req.get("User-Agent"),
        ip: req.get("X-Forwarded-For") || req.ip,
        status_code: 403,
      });
      res.status(403).send("Forbidden");
      return;
    }
  }

  res.on("finish", () => {
    reqsCounter.add(1, {
      url: req.baseUrl,
      path: path,
      method: req.method,
      status_code: res.statusCode,
      env: process.env.COCOON_ENV,
    });
    let duration = new Date() - requestStartTime;
    latencyHistogram.record(duration, {
      url: req.baseUrl,
      path: path,
      method: req.method,
      env: process.env.COCOON_ENV,
    });
    if (duration > 9500) {
      logWarn({
        message: "Slow request",
        method: req.method,
        url: req.url,
      });
    }

    if (req.path !== "/healthz" && req.path !== "/v1/healthz") {
      logInfo({
        context: "HTTP",
        message: "finished handling",
        method: req.method,
        url: req.url,
        path: path,
        userAgent: req.get("User-Agent"),
        ip: req.get("X-Forwarded-For") || req.ip,
        status_code: res.statusCode,
      });
    }
    let requestBody = JSON.stringify(req.body);
    loggingInfo("backend", {
      ip: req.get("X-Forwarded-For") || req.ip,
      user_agent: req.get("User-Agent"),
      user_id: req.user?.id ?? null,
      method: req.method,
      base: baseUrl,
      url: req.url,
      body: requestBody,
      duration: duration,
      status_code: res.statusCode,
    });
    // }
  });

  next();
});

app.enable("trust proxy");
app.use(express.text());

// TODO: More strict CORS policy.
//       e.g. We could ensure that only requests to our known origins are allowed
const corsOptions = {
  // See https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Max-Age
  //
  // Firefox caps this at 24 hours (86400 seconds).
  // Chromium (prior to v76) caps at 10 minutes (600 seconds).
  // Chromium (starting in v76) caps at 2 hours (7200 seconds).
  // The default value is 5 seconds.
  maxAge: 86400,
  exposedHeaders: [
    "Traceparent",
    "Tracestate",
    "Cache-Control",
    "Content-Encoding",
    "Content-Location",
    "Content-Range",
    "Content-Type",
    "Date",
    "Location",
    "Server",
    "Transfer-Encoding",
    "Range-Unit",
  ],
};
const cors = require("cors");
app.use(cors(corsOptions));

app.use("/v1/billing", billingRouter); // do not move this line after app.use(bodyParser.json()) and app.use(express.json())
app.use(bodyParser.json({ limit: "10mb" }));
app.use(bodyParser.urlencoded({ limit: "10mb", extended: true }));
app.use(express.json({ limit: "10mb" }));
// app.use(airbrakeExpress.makeMiddleware(airbrake));

app.use("/v1", rootRouter);
app.use("/v1/sd", comfyRouter);
app.use("/v1/memory", memoryRouter);
app.use("/v1/bots", botsRouter);
app.use("/v1/search", searchRouter);
app.use("/v1/notifications", notificationsRouter);
app.use("/v1/users", usersRouter);
app.use("/v1/invites", invitesRouter);
app.use("/v1/leaderboards", leaderboardRouter);
app.use("/v1/prompts", promptsRouter);
app.use("/v1/posts", postsRouter);
app.use("/v1/scenarios", scenariosRouter);
app.use("/v1/profiles", profilesRouter);
app.use("/v1/promoCodes", promoCodesRouter);
app.use("/v1/messages", messagesRouter);
app.use("/v1/moderation", moderationRouter);
app.use("/v1/loops", loopsRouter);
app.use("/v1/videos", videoRouter);
app.use("/v1/boba", bobaRouter);
app.use("/v1/activities", activitiesRouter);
app.use("/v1/nectar", nectarRouter);
app.use("/v1/sitemaps", sitemapsRouter);
app.use("/v1/dreams", dreamsRouter);
app.use("/v1/onefriend", pillowRouter); // support older clients
app.use("/v1/pillow", pillowRouter);
app.use("/v1/proposedPosts", proposedPostsRouter);
app.use("/v1/videoClips", videoClipsRouter);
app.use("/v1/vignettes", vignettesRouter);
app.set("trust proxy", 1);

// Note that express error handling middleware should be attached after all
// the other routes and use() calls. See [express docs][express-error-docs].
app.use((err, req, res, next) => {
  if (err) {
    logError({
      context: "***** Route handler error detected",
      error: err,
      headersSentAlready: res.headersSent,
      __logEntryRoot: {
        httpRequest: {
          requestMethod: req.method,
          requestUrl: req.url,
        },
      },
    });
  }
  if (process.env.NODE_ENV === "production") {
    gcloudErrorReporting.express(err, req, res, next);
  } else {
    next(err);
  }
});

// Export the Express app as a Google Cloud Function
exports.v1 = app;

const port = parseInt(process.env.PORT) || 8080;

app.listen(port, () => {
  console.log(`listening on port ${port} LOCAL? ${process.env.LOCAL}`);
});
client.shutdown();
