const { MaybeImagePromptModel } = require("../model/MaybeImagePromptModel");
const { ForcedImagePromptModel } = require("../model/ForcedImagePromptModel");

async function* messageGenerator(
  ctx,
  textModel,
  {
    imageRequested,
    message,
    messages,
    botProfile,
    forceSfwImage,
    botConfiguration,
  },
) {
  const botResponses = await textModel.run(ctx);

  var botTexts = [];
  for await (const text of botResponses) {
    botTexts.push(text);
    yield {
      type: "text",
      text,
    };
  }
  if (!imageRequested) {
    return;
  }

  let imageModel;
  if (message.startsWith("/imagine")) {
    imageModel = new ForcedImagePromptModel({
      message,
      botResponse: botTexts.join("\n"),
      messages,
      botProfile,
      forceSfwImage,
      botConfiguration,
    });
  } else {
    imageModel = new MaybeImagePromptModel({
      message,
      botResponse: botTexts.join("\n"),
      messages,
      botProfile,
      forceSfwImage,
      botConfiguration,
    });
  }
  const image = await imageModel.run(ctx);

  if (image.agreed) {
    yield {
      type: "image",
      description: image.description_of_the_photo,
      contains_character: image.contains_character,
      nsfw: image.nsfw,
    };
  }
}

module.exports = {
  messageGenerator,
};
