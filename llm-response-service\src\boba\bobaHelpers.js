/**
 * <PERSON>a Helpers
 *
 * This module provides helper functions for the boba service.
 */

const axios = require("axios");
const { decorateWithActiveSpanAsync } = require("../instrumentation/tracer");
const { logInfo, logError } = require("../utils");

/**
 * Generate images using Fireworks AI
 * @param {Object} options - Generation options
 * @param {string} options.prompt - Text prompt for image generation
 * @param {string} [options.aspect_ratio="9:16"] - Aspect ratio of the generated image
 * @param {number} [options.guidance_scale=3.5] - Guidance scale for image generation
 * @param {number} [options.num_inference_steps=30] - Number of inference steps
 * @param {number} [options.num_generations=1] - Number of images to generate
 * @returns {Promise<Array<Buffer>>} Array of image buffers
 */
async function _generateImagesWithFireworks({
  prompt,
  aspect_ratio = "9:16",
  guidance_scale = 3.5,
  num_inference_steps = 30,
  num_generations = 1,
}) {
  if (!prompt) {
    throw new Error("Prompt is required for image generation");
  }

  const fireworksUrl =
    "https://api.fireworks.ai/inference/v1/workflows/accounts/fireworks/models/flux-1-dev-fp8/text_to_image";
  const fireworksHeaders = {
    "Content-Type": "application/json",
    Accept: "image/png",
    Authorization: `Bearer twXcAfk7EAJrjdGqRiDz8fGs0ghPvcNmlE2gANDua4q1KCoq`,
  };

  const images = [];

  try {
    logInfo({
      message: `Generating ${num_generations} images with Fireworks AI`,
      prompt,
      aspect_ratio,
    });

    for (let i = 0; i < num_generations; i++) {
      const fireworksData = {
        prompt,
        aspect_ratio,
        guidance_scale,
        num_inference_steps,
      };

      const fireworksResponse = await axios.post(fireworksUrl, fireworksData, {
        headers: fireworksHeaders,
        responseType: "arraybuffer",
      });

      if (fireworksResponse.status !== 200) {
        throw new Error(`Fireworks API error: ${fireworksResponse.status}`);
      }

      images.push(fireworksResponse.data);
    }

    return images;
  } catch (error) {
    logError({
      message: "Error generating images with Fireworks AI",
      error,
      prompt,
    });
    throw error;
  }
}

const generateImagesWithFireworks = decorateWithActiveSpanAsync(
  "generateImagesWithFireworks",
  _generateImagesWithFireworks,
);

module.exports = {
  generateImagesWithFireworks,
};
