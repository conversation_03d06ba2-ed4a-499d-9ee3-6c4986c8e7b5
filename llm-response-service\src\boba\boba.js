/**
 * Boba service
 *
 * This module provides video, image, and audio generation functionality.
 */

const express = require("express");
const { logInfo } = require("../utils");

// Import route modules
const { app: imageRouter } = require("./bobaImage");
const { app: audioRouter } = require("./bobaAudio");
const { app: videoRouter } = require("./bobaVideo");

// Create the main router
const app = express.Router();

// Log all requests to the boba service
app.use((req, res, next) => {
  logInfo({
    message: "Boba service request",
    path: req.path,
    method: req.method,
  });
  next();
});

// Mount the route modules
app.use("/image", imageRouter);
app.use("/audio", audioRouter);
app.use("/video", videoRouter);

// For backward compatibility, mount the routes at the root level as well
app.use(imageRouter);
app.use(audioRouter);
app.use(videoRouter);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    service: "boba",
  });
});

// Export the helper functions from the appropriate modules for backward compatibility
const {
  generateVideoFromPrompt,
  pollVideoStatus,
} = require("./bobaVideoHelpers");
const { generateTTS } = require("./bobaAudioHelpers");

module.exports = {
  app,
  generateVideoFromURL: generateVideoFromPrompt,
  pollVideoStatus,
  generateAudio: generateTTS,
};
