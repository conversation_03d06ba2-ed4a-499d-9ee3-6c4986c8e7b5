jest.setTimeout(30 * 1000);

const { generateAvatarV3 } = require("../../src/comfy.js");

test("it should rewrite nudity out of the character description", async () => {
  const { description } = await generateAvatarV3({
    description:
      "a 30 year old woman, green hair, (angela frumpkin), nude, giant tits, a big butt",
    is_unit_testing: true,
  });

  console.log("DESCRIPTION", description);

  expect(description).toMatch(/(giant tits)/i);
});

test("it should rewrite sexual genitalia from character description", async () => {
  const { description } = await generateAvatarV3({
    description:
      "a 30 year old woman, green hair, (angela frumpkin), giant breasts, giant cock, a huge ass, wet pussy",
    is_unit_testing: true,
  });

  console.log("DESCRIPTION", description);

  expect(description).toMatch(/(huge ass)/i);
});

test("it should rewrite genetalia", async () => {
  const { description } = await generateAvatarV3({
    description: "penis",
    is_unit_testing: true,
  });

  console.log("DESCRIPTION", description);

  expect(description).not.toMatch(/(penis)/i);
});
