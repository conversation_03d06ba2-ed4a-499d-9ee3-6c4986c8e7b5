const fs = require("fs");
const { createClient } = require("@supabase/supabase-js");
const { wrappedSupabaseError } = require("./utils");

const supabaseUrl = "https://db.butterflies.ai" ?? "http://localhost:54321";
const supabaseSecretKey = "";

const supabase = createClient(supabaseUrl, supabaseSecretKey);

const IMAGE_WIDTH_LIST = [64, 128, 240, 640, 1280, 1800];

// Folder paths
const sourceFolder = "sd/";
const destinationFolder = "images/sd/resized/";

// Batch size
const BATCH_SIZE = 100; // Adjust based on your needs
const CHUNK_SIZE = 1000; // Total chunk size for processing

let LAST_PROCESSED_PROFILE_ID = 94451;
let LAST_PROCESSED_POST_ID = 2359;

let PROCESSING_TYPE = "posts"; // Global variable to control processing type

async function getProfiles(lastId = null) {
  let profiles = [];
  let startIndex = 0;
  let hasMore = true;

  while (hasMore) {
    const query = supabase
      .from("profiles")
      .select("id, avatar_url")
      .not("avatar_url", "is", null)
      .neq("visibility", "archived")
      .order("id", { ascending: false })
      .range(startIndex, startIndex + 999);

    if (lastId) {
      query.lt("id", lastId);
    }

    const { data, error } = await query;

    if (error) {
      const profileError = wrappedSupabaseError(error);
      throw profileError;
    }

    profiles = profiles.concat(data);
    startIndex += 1000;
    hasMore = data.length === 1000;

    if (profiles.length >= CHUNK_SIZE) {
      console.log("Reached chunk size, processing next chunk");
      break;
    }
  }

  return profiles;
}

async function getPosts(lastId = null) {
  let posts = [];
  let startIndex = 0;
  let hasMore = true;

  while (hasMore) {
    const query = supabase
      .from("posts")
      .select("id, media_url")
      .neq("visibility", "archived")
      .order("id", { ascending: true })
      .range(startIndex, startIndex + 999);

    if (lastId) {
      query.gt("id", lastId);
    }

    const { data, error } = await query;

    if (error) {
      const postError = wrappedSupabaseError(error);
      throw postError;
    }

    posts = posts.concat(data);
    startIndex += 1000;
    hasMore = data.length === 1000;

    console.log("total posts", posts.length);

    if (posts.length >= CHUNK_SIZE) {
      console.log("Reached chunk size, processing next chunk");
      break;
    }
  }

  return posts;
}

let progress = 0;

async function copyImagesWithSizes(urls) {
  try {
    for (let i = 0; i < urls.length; i += BATCH_SIZE) {
      const batch = urls.slice(i, i + BATCH_SIZE);

      await Promise.all(
        batch.map(async (url) => {
          if (!url) {
            return;
          }
          const fileName = url.split("/").pop();
          let nakedName = fileName;

          if (nakedName.includes(".")) {
            nakedName = fileName.split(".")[0];
          }
          for (const size of IMAGE_WIDTH_LIST) {
            const sourcePath = `images/${sourceFolder}${fileName}/${size}`;
            const destinationFileName = `${nakedName}_${size}.jpg`;
            const destinationPath = `${destinationFolder}${destinationFileName}`;

            const { error: copyError } = await supabase.storage
              .from("ai-ig")
              .copy(sourcePath, destinationPath);

            progress += 1;

            if (copyError) {
              //   console.error(
              //     `Error copying image: ${copyError.message}`,
              //     sourcePath
              //   );
            } else {
              //   console.log(`Image copied to ${destinationPath}`);
            }

            // console.log("Completed images:", progress);
          }
        }),
      );
    }
  } catch (error) {
    console.error("Error:", error.message);
  }
}

async function processImages() {
  try {
    if (PROCESSING_TYPE === "profiles") {
      while (true) {
        // const profiles = await getProfiles();
        const profiles = await getProfiles(LAST_PROCESSED_PROFILE_ID);
        const profileUrls = profiles.map((profile) => profile.avatar_url);
        if (profiles.length > 0) {
          LAST_PROCESSED_PROFILE_ID = profiles[profiles.length - 1].id;
        }
        await copyImagesWithSizes(profileUrls);

        console.log(`Last processed profile ID: ${LAST_PROCESSED_PROFILE_ID}`);

        if (profiles.length < CHUNK_SIZE) {
          console.log("All profiles processed");
          break;
        }
      }
    } else if (PROCESSING_TYPE === "posts") {
      while (true) {
        const posts = await getPosts(LAST_PROCESSED_POST_ID);

        const postUrls = posts.map((post) => post.media_url);
        if (posts.length > 0) {
          LAST_PROCESSED_POST_ID = posts[posts.length - 1].id;
        }
        await copyImagesWithSizes(postUrls);

        console.log(`Last processed post ID: ${LAST_PROCESSED_POST_ID}`);

        if (posts.length < CHUNK_SIZE) {
          console.log("All posts processed");
          break;
        }
      }
    }
  } catch (error) {
    console.error("Error processing images:", error.message);
  }
}

processImages();
