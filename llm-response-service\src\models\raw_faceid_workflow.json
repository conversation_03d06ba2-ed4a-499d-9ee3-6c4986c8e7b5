{"3": {"inputs": {"seed": 333253630701162, "steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "model": ["11", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "photogasm.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 864, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "woman, at the park, sexy pose", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(worst quality), (low quality), (normal quality), lowres, normal quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "10": {"inputs": {"lora_name": "ip-adapter-faceid-plusv2_sd15_lora.safetensors", "strength_model": 0.2, "model": ["4", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "11": {"inputs": {"weight": 0.5, "noise": 0, "weight_type": "original", "start_at": 0, "end_at": 1, "faceid_v2": true, "weight_v2": 3, "unfold_batch": true, "ipadapter": ["12", 0], "clip_vision": ["13", 0], "insightface": ["14", 0], "image": ["27", 0], "model": ["30", 0]}, "class_type": "IPAdapterApplyFaceID", "_meta": {"title": "Apply IPAdapter FaceID"}}, "12": {"inputs": {"ipadapter_file": "ip-adapter-faceid-plusv2_sd15.bin"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "Load IPAdapter Model"}}, "13": {"inputs": {"clip_name": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "14": {"inputs": {"provider": "CUDA"}, "class_type": "InsightFaceLoader", "_meta": {"title": "Load InsightFace"}}, "21": {"inputs": {"filename_prefix": "ComfyUI", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "23": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "25": {"inputs": {"image": "Screenshot 2024-01-18 at 6.44.07 PM.png", "image_data": "data:image/png;base64", "upload": "image"}, "class_type": "LoadImage //Inspire", "_meta": {"title": "LoadImage //Inspire"}}, "27": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["25", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prepare Image For Clip Vision"}}, "30": {"inputs": {"lora_name": "NSFWFilter.safetensors", "strength_model": -1, "model": ["10", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}}