var apn = require("@parse/node-apn");
var path = require("path");

var stageOptions = {
  token: {
    key: path.join(__dirname, "../AuthKey_MGAJ4V4JWR.p8"), // Resolve absolute path
    keyId: "MGAJ4V4JWR",
    teamId: "FD499WT3M2",
  },
  production: false,
};

const stageApnProvider = new apn.Provider(stageOptions);

var prodOptions = {
  token: {
    key: path.join(__dirname, "../AuthKey_MGAJ4V4JWR.p8"), // Resolve absolute path
    keyId: "MGAJ4V4JWR",
    teamId: "FD499WT3M2",
  },
  production: true,
};

const prodApnProvider = new apn.Provider(prodOptions);

module.exports = { stageApnProvider, prodApnProvider };
