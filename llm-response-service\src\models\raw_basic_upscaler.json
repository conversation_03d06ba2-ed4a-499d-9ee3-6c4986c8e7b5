{"last_node_id": 30, "last_link_id": 44, "nodes": [{"id": 26, "type": "UltralyticsDetectorProvider", "pos": [131, 895], "size": {"0": 315, "1": 78}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [38], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [7, 461], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6, 32], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cross-eyed,sketches, (worst quality), (low quality), (normal quality), lowres, normal quality, bad anatomy, DeepNegative, facing away, tilted head, {Multiple people}, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worstquality, low quality, normal quality, jpegartifacts, signature, watermark, username, blurry, bad feet, cropped, poorly drawn hands, poorly drawn face, mutation, deformed, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, extra fingers, fewer digits, extra limbs, extra arms,extra legs, malformed limbs, fused fingers, too many fingers, long neck, cross-eyed,mutated hands, polar lowres, bad body, bad proportions, gross proportions, text, error, missing fingers, missing arms, missing legs, extra digit, extra arms, extra leg, extra foot"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": {"0": 315, "1": 106}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 1024, 1]}, {"id": 24, "type": "SaveImage", "pos": [2344, -44], "size": {"0": 1009.0655517578125, "1": 1050.6390380859375}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 37}], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 23, "type": "FaceDetailer", "pos": [950, 615], "size": {"0": 506.4000244140625, "1": 880}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 28}, {"name": "model", "type": "MODEL", "link": 31, "slot_index": 1}, {"name": "clip", "type": "CLIP", "link": 36}, {"name": "vae", "type": "VAE", "link": 29}, {"name": "positive", "type": "CONDITIONING", "link": 40}, {"name": "negative", "type": "CONDITIONING", "link": 32}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 38}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [37], "shape": 3, "slot_index": 0}, {"name": "cropped_refined", "type": "IMAGE", "links": null, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": null, "shape": 6}, {"name": "mask", "type": "MASK", "links": null, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null, "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "links": null, "shape": 6}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 1024, 666802320249917, "randomize", 6, 4, "euler", "karras", 0.5, 0, true, true, 0.5, 10, 3, "center-1", 0, 0.93, 0, 0.7, "False", 50, "", 1, false, 10]}, {"id": 6, "type": "CLIPTextEncode", "pos": [96, 122], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4, 40], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["joe biden, jumping out of a plane"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [652, -45], "size": {"0": 315, "1": 262}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 44}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [606935613669641, "fixed", 20, 7, "euler", "karras", 1]}, {"id": 8, "type": "VAEDecode", "pos": [1079, -226], "size": {"0": 210, "1": 46}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 30}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-398, 192], "size": {"0": 315, "1": 98}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [31, 43], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5, 36], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [29, 30], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["photogasm.safetensors"]}, {"id": 30, "type": "LoraLoaderModelOnly", "pos": [101, -146], "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 43}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [44], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["NSFWFilter.safetensors", -1]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [28, 8, 0, 23, 0, "IMAGE"], [29, 4, 2, 23, 3, "VAE"], [30, 4, 2, 8, 1, "VAE"], [31, 4, 0, 23, 1, "MODEL"], [32, 7, 0, 23, 5, "CONDITIONING"], [36, 4, 1, 23, 2, "CLIP"], [37, 23, 0, 24, 0, "IMAGE"], [38, 26, 0, 23, 6, "BBOX_DETECTOR"], [40, 6, 0, 23, 4, "CONDITIONING"], [43, 4, 0, 30, 0, "MODEL"], [44, 30, 0, 3, 0, "MODEL"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}