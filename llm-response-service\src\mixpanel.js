const Mixpanel = require("mixpanel");
const UAParser = require("ua-parser-js");

const mixpanel = Mixpanel.init("c8ae5344450ad02ac50a11cf69dbecbc", {});

function mixpanelTrack(user_id, eventName, properties) {
  mixpanel.track(eventName, {
    distinct_id: user_id,
    ...properties,
  });
}

function mixpanelTrackReq(user_id, eventName, props, req) {
  if (!props) {
    props = {};
  }

  // X-Forwarded-For is the client's IP address, if the request was forwarded by GCP Load Balancer.
  if (req.headers["x-forwarded-for"]) {
    const forwardedFor = req.headers["x-forwarded-for"].split(",")[0].trim();
    props["ip"] = forwardedFor;
  } else if (req.ip) {
    props["ip"] = req.ip;
  }

  let userAgentString = req.headers["user-agent"];
  const parser = new UAParser();
  if (userAgentString) {
    const uaResult = parser.setUA?.(userAgentString);
    if (uaResult) {
      const ua = uaResult?.getResult?.();
      if (ua) {
        props["$browser"] = ua.browser.name;
        props["$browser_version"] = ua.browser.version;
        props["$device"] = ua.device.family;
        props["$os"] = ua.os.name;
        props["$os_version"] = ua.os.version;
      }
    }
  }

  if (req.headers["x-butt-platform"]) {
    props["platform"] = req.headers["x-butt-platform"];
  }
  if (req.headers["x-butt-app-version"]) {
    props["$app_version"] = req.headers["x-butt-app-version"];
  }
  if (req.headers["x-butt-build-sha"]) {
    props["commit"] = req.headers["x-butt-build-sha"];
  }

  mixpanel.track(eventName, {
    distinct_id: user_id,
    ...props,
  });
}

module.exports = {
  mixpanel,
  mixpanelTrack,
  mixpanelTrackReq,
};
