function sdxlFaceIdPrompt({
  prompt,
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
  steps = 5,
  cfg = 1.5,
  faceSteps = 3,
  faceCfg = 1.5,
  faceDenoise = 0.4,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  badquality = 0,
  blurxl = 0,
  envyzoomslider = 0,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps,
        cfg,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["35", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${
          nsfw ? "" : ", (nudity, nsfw, naked)"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    11: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["64", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    33: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    35: {
      inputs: {
        weight: 0.8,
        weight_faceidv2: 3,
        weight_type: "ease in",
        combine_embeds: "concat",
        start_at: 0.3,
        end_at: 1,
        embeds_scaling: "V only",
        model: ["68", 0],
        ipadapter: ["39", 0],
        image: ["11", 0],
        clip_vision: ["41", 0],
        insightface: ["40", 0],
      },
      class_type: "IPAdapterFaceID",
      _meta: {
        title: "IPAdapter FaceID",
      },
    },
    39: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sdxl.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    40: {
      inputs: {
        provider: "CPU",
        model_name: "buffalo_l",
      },
      class_type: "IPAdapterInsightFaceLoader",
      _meta: {
        title: "IPAdapter InsightFace Loader",
      },
    },
    41: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    50: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    57: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 1600,
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 4,
        cfg: 1.5,
        sampler_name: "euler",
        scheduler: "normal",
        denoise: faceDenoise,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["8", 0],
        segs: ["61", 0],
        model: ["67", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
      },
      class_type: "DetailerForEach",
      _meta: {
        title: "Detailer (SEGS)",
      },
    },
    60: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["33", 0],
        image: ["8", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    61: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 3,
        segs: ["60", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    64: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    65: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["57", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    66: {
      inputs: {
        lora_name: "badquality.safetensors",
        strength_model: badquality,
        model: ["50", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    67: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: blurxl,
        model: ["66", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    68: {
      inputs: {
        lora_name: "envyzoomslider.safetensors",
        strength_model: envyzoomslider,
        model: ["67", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
  };
}

module.exports = { sdxlFaceIdPrompt };
