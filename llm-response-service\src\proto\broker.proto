syntax  = "proto3";
package broker;
option go_package="./;pb";

enum ValueType {
    VALUE_TYPE_INT = 0;
    VALUE_TYPE_FLOAT = 1;
    VALUE_TYPE_BOOL = 2;
    VALUE_TYPE_STRING = 3;
}

message Value {
    ValueType value_type = 1;
    oneof value {
        int64 value_int = 2;
        double value_double = 3;
        bool value_bool = 4;
        string value_string = 5;
    }
}

message Task {
    int32 priority = 1;
    string group = 2;

    // If workflow is specified, this is what will be executed, 
    // and template and template_args values will be ignored.
    string workflow = 3;

    // Workflow template to use.
    string template = 4;
    // Arguments for workflow template.
    map<string, Value> template_args = 5;

    string completion_url = 6;
}

message LockRequest {
    string worker_id = 1;

    repeated string group = 2;
}

message LockResponse {
    string task_id = 1;
    Task task = 2;
}

message CompleteRequest {
    string task_id = 1;
    int32 status_code = 2;
    string status_message = 3;
    repeated bytes image = 4;
}

message CompleteResponse {
}

message EnqueueRequest {
    Task task = 1;

    // If given, this is the task ID to be used.
    // If this string is empty, a new ID will be generated.
    string task_id = 2;
}

message EnqueueResponse {
    string task_id = 1;
}

message StatusRequest {
    string task_id = 1;
}

enum TaskStatus {
    TASK_STATUS_QUEUED = 0;
    TASK_STATUS_EXECUTING = 1;
    TASK_STATUS_DONE = 2;
    TASK_STATUS_FAILED = 3;
}

message StatusResponse {
    TaskStatus status = 1;
    int32 queue_position = 2;
    int32 queue_length = 3;
    int32 estimated_wait_time = 4;
    repeated string image_id = 5;
}

message ImageRequest {
    repeated string image_id = 1;
}

message ImageResponse {
    map<string, bytes> image = 1;
}

service ImageServiceBroker {
    rpc Enqueue(EnqueueRequest) returns (EnqueueResponse);
    rpc Status(StatusRequest) returns (StatusResponse);
    rpc GetImage(ImageRequest) returns (ImageResponse);
    rpc Lock(LockRequest) returns (LockResponse);
    rpc Complete(CompleteRequest) returns (CompleteResponse);
}