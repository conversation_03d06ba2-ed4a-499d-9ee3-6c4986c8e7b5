const express = require("express");
const app = express.Router();
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { typesense } = require("./typesenseClient");
const { authUser } = require("./middleware");
require("dotenv").config();

const { logWarn, logError } = require("./utils");
const { getDataWithCache } = require("./btClient");
const redisClient = require("./redisClient");

const CHUNK_SIZE = 30;
const CHUNK_SUPABASE_UPDATE_SIZE = 300;

const { SAFE_PROFILES } = require("./constants");

app.get("/ping", async (req, res) => {
  console.log("**** Search ping", req.executionId);
  res.send("Search ping");
});

function parseIdsT(data) {
  let lines = data.trim().split("\n");

  let ids = lines.map((line) => {
    let result = JSON.parse(line);
    return +result.id;
  });

  return ids;
}

async function deleteUnsearchablePosts() {
  let postIds = [];

  const { data, error: getPostsNotSearchableError } = await supabase.rpc(
    "func_get_posts_not_searchable_v2",
  );

  console.log("data: ", data);
  if (getPostsNotSearchableError) {
    const error = wrappedSupabaseError(getPostsNotSearchableError);
    logError({
      context: "deleteUnsearchablePosts - failed to fetch posts",
      error,
    });
    throw error;
  }

  if (!data || data.length == 0) {
    return;
  }

  postIds = data.map((i) => i.id);

  const deletePosts = await typesense
    .collections("posts_v2")
    .documents()
    .delete({
      filter_by: `id:=[${postIds}]`,
    });

  console.log("**** typeSense", deletePosts);

  const { error: updateError } = await supabase
    .from("posts")
    .update({
      is_indexed: true,
    })
    .in("id", postIds);

  if (updateError) {
    const error = wrappedSupabaseError(updateError);
    logError({
      context: "deleteUnsearchablePosts - failed to update posts",
      error,
    });
    throw error;
  }
  return;
}

app.get("/deleteUnsearchablePosts", async (req, res) => {
  try {
    await deleteUnsearchablePosts();
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "**** deleteUnsearchablePosts error",
      error,
    });
    return res.sendStatus(500);
  }
});

async function deleteUnsearchableProfiles() {
  let profileIds = [];

  const { data, error } = await supabase.rpc(
    "func_get_profiles_unsearchable_v2",
    { batch_size: 200 },
  );

  if (error) {
    const error = wrappedSupabaseError(error);
    logError({
      context: "deleteUnsearchableProfiles - failed to fetch profiles",
      error,
    });
    throw error;
  }

  if (!data || data.length == 0) {
    return;
  }

  profileIds = data.map((i) => i.id);

  const deleteProfiles = await typesense
    .collections("profiles_v2")
    .documents()
    .delete({
      filter_by: `id:=[${profileIds}]`,
    });

  console.log("**** typeSense", deleteProfiles);

  const { error: updateError } = await supabase
    .from("profiles")
    .update({
      is_indexed: true,
    })
    .in("id", profileIds);

  if (updateError) {
    const error = wrappedSupabaseError(updateError);
    logError({
      context: "deleteUnsearchableProfiles - failed to update profiles",
      error,
    });
    throw error;
  }
  return;
}

app.get("/deleteUnsearchableProfiles", async (req, res) => {
  try {
    await deleteUnsearchableProfiles();
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "**** deleteUnsearchableProfiles error",
      error,
    });
    return res.sendStatus(500);
  }
});

app.get("/syncDelete", async (req, res) => {
  const tableName = req.query.tableName;
  // const fetchPosts = await typesense.collections('profiles').documents("14370").retrieve();
  const documents = await typesense.collections(tableName).documents().export({
    include_fields: "id",
  });
  const idsTypesense = parseIdsT(documents);

  const noIdlist = [];
  for (let i = 0; i < idsTypesense.length; i += 500) {
    const chunk = idsTypesense.slice(i, i + 500);
    try {
      const { data: rows, error: rowsError } = await supabase
        .from(tableName)
        .select("id")
        .in("id", chunk);

      if (rowsError) {
        continue;
      }
      const idsSupabase = rows.map((row) => row.id);
      const noExists = chunk.filter((pid) => !idsSupabase.includes(pid));
      noIdlist.push(...noExists);
    } catch (error) {
      console.log("*** syncDelete chunk error", i, error);
    }
  }

  console.log("**** /syncDelete DONE", noIdlist.length);

  // fs.appendFile(tableName + '.log', noIdlist.toString(), function (err) {
  //   if (err) {
  //     // Optionally log any write errors to the console:
  //     console.error(`Error while logging to file: ${err}`);
  //   }
  // });

  try {
    const deleteResult = await typesense
      .collections(tableName)
      .documents()
      .delete({ filter_by: "id: [" + noIdlist.toString() + "]" });
    console.log("**** deleteResult", deleteResult);
  } catch (error) {
    console.log("**** delete error", error);
  }
});

async function searchPosts(data) {
  const searchQuery = data.query || "";
  const category = data.category || "";

  let searchPostParameters = {
    q: searchQuery,
    query_by: ["ai_caption", "description", "tags", "bots", "profiles"],
    filter_by: `visibility:public && nsfw:!=nsfw && profiles.visibility:public`,
    sort_by: "_text_match:desc",
    per_page: data.per_page ?? 50,
    page: data.page ?? 1,
  };

  // If a category is provided, adjust parameters to prioritize it
  if (category) {
    searchPostParameters.q = `${category} ${searchQuery}`.trim();
    searchPostParameters.query_by = [
      "tags",
      "bots.tag",
      "ai_caption",
      "description",
      "profiles",
    ];
  }

  // apple user
  if (data.profile_id === "711" || data.profile_id === "201") {
    searchPostParameters = {
      q: data.query,
      include_fields: "profile_id",
      query_by: ["ai_caption", "description", "tags", "bots", "profiles"],
      filter_by: `profile_id:=[${SAFE_PROFILES}] && visibility:public && nsfw:!=nsfw`,
      sort_by: "_text_match:desc",
      per_page: data.per_page ?? 50,
      page: data.page ?? 1,
    };
  }

  const fetchPosts = await typesense
    .collections("posts_v2")
    .documents()
    .search(searchPostParameters);
  let postData = fetchPosts.hits?.map((hit) => hit.document);
  return postData;
}

async function searchProfiles(data) {
  const searchQuery = data.query || "";
  let searchProfileParameters = {
    q: searchQuery,
    query_by: ["username", "display_name", "bots"],
    filter_by: "visibility:public && nsfw:!=nsfw",
    sort_by: "_text_match:desc",
    per_page: data.per_page ?? 50,
    page: data.page ?? 1,
  };

  // apple user
  if (data.profile_id === "711" || data.profile_id === "201") {
    searchProfileParameters = {
      q: searchQuery,
      query_by: ["username", "display_name", "bots"],
      filter_by: `id:=[${SAFE_PROFILES}] && visibility:public && nsfw:!=nsfw`,
      sort_by: "_text_match:desc",
      per_page: data.per_page ?? 50,
      page: data.page ?? 1,
    };
  }

  const fetchProfiles = await typesense
    .collections("profiles_v2")
    .documents()
    .search(searchProfileParameters);
  let profileData = fetchProfiles.hits?.map((hit) => hit.document);
  return profileData;
}

app.get("/query", async (req, res) => {
  try {
    const result = await Promise.all([
      searchPosts(req.query),
      searchProfiles(req.query),
    ]);
    console.log("**** result", result[0].length, result[1].length);

    let posts = result[0];
    let profiles = result[1];
    res.status(200).json({ posts: posts, profiles: profiles });
  } catch (error) {
    console.log("*** typesense query error", error);
  }
});

async function searchMentionProfilesHandler(req, res) {
  let profile_id, bot_profile_id, query;

  // wraps, shims the POST / GET endpoints
  if (req.method === "GET") {
    profile_id = req.query.profile_id;
    bot_profile_id = req.query.bot_profile_id;
    query = req.query.query || "";
  } else {
    profile_id = req.body.profile_id;
    bot_profile_id = req.body.bot_profile_id;
    query = req.body.query || "";
  }
  if (!profile_id || !bot_profile_id) {
    return res.status(400).json({ error: "Missing required query parameters" });
  }

  const { data: initiatorProfile, error: initiatorError } = await supabase
    .from("bots")
    .select(
      "art_style, profiles!bots_profile_id_fkey(avatar_photo_contains_face, nsfw)",
    )
    .eq("profile_id", bot_profile_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (initiatorError) {
    const error = wrappedSupabaseError(initiatorError);
    logError({
      context: "**** searchMentionProfilesHandler initiatorError",
      error,
    });
    return res.status(500).json({ error: "Profile fetch failed." });
  }

  if (!initiatorProfile) {
    const error = new Error("Profile not found");
    return res.status(404).json({ error });
  }

  if (
    initiatorProfile?.profiles?.nsfw === "nsfw" ||
    initiatorProfile?.art_style === "semi_realistic"
  ) {
    return res
      .status(403)
      .json({ error: "NSFW profile or no face detected in avatar" });
  }

  try {
    // Safe for apple users
    const safes =
      profile_id === "711" || profile_id === "201" ? SAFE_PROFILES : null;

    const buildSearchParameters = (followerCondition) => ({
      q: query,
      query_by: [
        "username",
        "display_name",
        "bots.display_name",
        "bots.franchise",
        "bots.source",
      ],
      filter_by:
        `bots.creator_id:>0 && (visibility:public || (visibility:private && bots.creator_id:=${profile_id})) && nsfw:!=nsfw && ${followerCondition} && bots.profile_id:!=${bot_profile_id} && bots.art_style:!=semi_realistic` +
        (safes ? ` && id:=[${safes}]` : ""),
      sort_by: `profile_weight:desc,_text_match:desc`,
      per_page: 10,
      page: 1,
    });

    const searchProfileParameters1 = buildSearchParameters(
      `follower_ids:=${profile_id}`,
    );
    const searchProfileParameters2 = buildSearchParameters(
      `follower_ids:!=${profile_id}`,
    );

    const [search1, search2] = await Promise.all([
      typesense
        .collections("profiles_v2")
        .documents()
        .search({
          ...searchProfileParameters1,
        }),
      typesense
        .collections("profiles_v2")
        .documents()
        .search({
          ...searchProfileParameters2,
        }),
    ]);
    const processSearchResults = (searchResult) =>
      searchResult.hits.map((hit) => hit.document);

    const filteredSearchResult1 = processSearchResults(search1).slice(0, 5);
    const remainingSlots = 10 - filteredSearchResult1.length;
    const filteredSearchResult2 = processSearchResults(search2).slice(
      0,
      remainingSlots,
    );

    // Combine filtered results
    const combinedResults = [
      ...filteredSearchResult1,
      ...filteredSearchResult2,
    ];

    // Send the combined results as a response
    res.status(200).json(combinedResults);
  } catch (error) {
    console.error("Error searching profiles:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

// Use a POST to not blow the CORS cache when the query parameter changes with the query
app.post("/searchMentionProfiles", authUser, async (req, res) => {
  try {
    await searchMentionProfilesHandler(req, res);
  } catch (error) {
    logError({
      context: "Error in searchMentionProfiles: ",
      error,
    });
    return res.status(500).send({
      message: error?.message ?? new Error("searchMentionProfiles failed."),
    });
  }
});

// Should be able to remove this GET request, since we haven't release a public mobile app build that uses it
app.get("/searchMentionProfiles", async (req, res) => {
  try {
    await searchMentionProfilesHandler(req, res);
  } catch (error) {
    logError({
      context: "Error in searchMentionProfiles: ",
      error,
    });
    return res.status(500).send({
      message: error?.message ?? new Error("searchMentionProfiles failed."),
    });
  }
});

app.get("/indexProfiles", async (req, res) => {
  const { data: profiles, error: profilesError } = await supabase
    .from("profiles")
    .select("id, username, display_name, avatar_url, follower_count, nsfw")
    .neq("profiles.visibility", "archived")
    .eq("visibility", "public");
  // .not("is_indexed", "is", true)
  // .limit(10);

  if (profilesError) {
    const error = wrappedSupabaseError(profilesError);
    logError({
      context: "indexProfiles: failed to query profiles",
      error,
    });
    throw error;
  }

  console.log("*** profiles before", profiles.length);
  profiles.map((profile) => {
    profile.id = profile.id.toString();
  });

  console.log("*** profiles", profiles.length);

  const successIdlist = [];
  for (let i = 0; i < profiles.length; i += CHUNK_SIZE) {
    const chunk = profiles.slice(i, i + CHUNK_SIZE);
    try {
      const result = await typesense
        .collections("profiles_v2")
        .documents()
        .import(chunk, { action: "upsert", return_id: true });

      const successIds = result
        .filter((doc) => doc.success)
        .map((doc) => +doc.id);
      successIdlist.push(...successIds);

      result
        .filter((doc) => !doc.success)
        .map((doc) => console.log("*** import error", doc.error, doc.document));
    } catch (error) {
      console.log("*** indexProfiles error", i, error);
    }
  }

  console.log("***** successIdlist", successIdlist);
  const { error } = await supabase
    .from("profiles")
    .update({ is_indexed: true })
    .in("id", successIdlist);

  console.log("*** error", error);

  res.status(200).json({ error });
});

function parseIds(data) {
  let lines = data.trim().split("\n");

  let ids = lines.map((line) => {
    let result = JSON.parse(line);
    if (result.success) return +result.id;
  });

  return ids;
}

app.get("/indexProfilesJsonV2", async (req, res) => {
  try {
    const { data: profiles, error: profilesError } = await supabase.rpc(
      "get_profiles_ndjson_v2",
    );

    if (profilesError) {
      const error = wrappedSupabaseError(
        profilesError,
        "failed to call 'get_profiles_ndjson_v2' db function",
      );
      logError({
        context: "indexProfilesJsonV2: failed to call get_profiles_ndjson_v2",
        error,
      });
      throw error;
    }

    if (!profiles) {
      res
        .status(204)
        .json({ error: null, message: "No profiles to be indexed" });
      return;
    }

    const result = await typesense
      .collections("profiles_v2")
      .documents()
      .import(profiles, { action: "upsert", return_id: true });

    if (!result) {
      res.status(204).json({ error: null, message: "No profiles are indexed" });
      return;
    }

    const successIds = parseIds(result);
    console.log("**** indexing profiles successIds", successIds.length);

    const chunkedErrors = [];
    for (let i = 0; i < successIds.length; i += CHUNK_SUPABASE_UPDATE_SIZE) {
      const chunkedIds = successIds.slice(i, i + CHUNK_SUPABASE_UPDATE_SIZE);
      const { error: updateProfilesError } = await supabase
        .from("profiles")
        .update({ is_indexed: true })
        .in("id", chunkedIds);

      if (updateProfilesError) {
        const error = wrappedSupabaseError(updateProfilesError);
        logError({
          context: "indexProfilesJsonV2: failed to update is_indexed",
          error,
          chunkedIds,
        });
        chunkedErrors.push(error);
      }
    }

    if (chunkedErrors.length) {
      throw new Error(
        "indexProfilesJsonV2 failed to update is_indexed in chunk. Check log for specific error details.",
      );
    }

    res.status(200).json({ error: null });
  } catch (error) {
    logError({
      context: "indexProfilesJsonV2 error",
      error,
    });
    res.status(500).json({ error });
  }
});

app.get("/indexPosts", async (req, res) => {
  const { data: posts, error: postsError } =
    await supabase.rpc("get_posts_data");

  if (postsError) {
    const error = wrappedSupabaseError(postsError);
    logError({
      context: "indexPosts: failed to call get_posts_data",
      error,
    });
    throw error;
  }

  if (!posts) {
    res.status(200).json({ error: null, message: "no new posts" });
    return;
  }
  console.log("*** posts before", posts.length);
  posts.map((post) => {
    post.id = post.id.toString();
  });

  console.log("*** posts", posts.length);

  const successIdlist = [];
  for (let i = 0; i < posts.length; i += CHUNK_SIZE) {
    const chunk = posts.slice(i, i + CHUNK_SIZE);
    try {
      const result = await typesense
        .collections("posts_v2")
        .documents()
        .import(chunk, { action: "upsert", return_id: true });

      const successIds = result
        .filter((doc) => doc.success)
        .map((doc) => +doc.id);
      successIdlist.push(...successIds);

      result
        .filter((doc) => !doc.success)
        .map((doc) => {
          const error = new Error("import error", { cause: doc.error });
          logError({
            context: "*** indexPosts import error",
            error: error,
            docError: doc.error,
            docDocument: doc.document,
          });
        });
    } catch (error) {
      logError({
        context: "*** indexProfiles error",
        error: error,
      });
    }
  }

  const { error: updateIndexedError } = await supabase
    .from("posts")
    .update({ is_indexed: true })
    .in("id", successIdlist);

  if (updateIndexedError) {
    const error = wrappedSupabaseError(updateIndexedError);
    logError({
      context: "indexPosts: failed to update is_indexed",
      error,
      successIdlist,
    });
    throw error;
  }

  res.status(200).json({ error: updateIndexedError });
});

app.get("/indexPostsJson", async (req, res) => {
  const { data: posts, error: postsError } =
    await supabase.rpc("get_posts_ndjson");

  if (postsError) {
    const error = wrappedSupabaseError(
      postsError,
      "failed to call 'get_posts_ndjson' db function",
    );
    logError({
      context: "indexPostsJson: failed to call get_posts_data",
      error,
    });
    throw error;
  }

  if (!posts) {
    res.status(200).json({ error: null, message: "No posts to be indexed" });
    return;
  }

  try {
    const result = await typesense
      .collections("posts_v2")
      .documents()
      .import(posts, { action: "upsert", return_id: true });

    if (!result) {
      res.status(200).json({ error: null, message: "No posts are indexed" });
      return;
    }

    const successIds = parseIds(result);
    console.log("**** indexing posts successIds", successIds.length);

    const chunkedErrors = [];
    for (let i = 0; i < successIds.length; i += CHUNK_SUPABASE_UPDATE_SIZE) {
      const chunkedIds = successIds.slice(i, i + CHUNK_SUPABASE_UPDATE_SIZE);
      const { error: updateIndexedError } = await supabase
        .from("posts")
        .update({ is_indexed: true })
        .in("id", chunkedIds);

      if (updateIndexedError) {
        const error = wrappedSupabaseError(updateIndexedError);
        logError({
          context: "indexPostsJson: failed to update is_indexed",
          error,
          chunkedIds,
        });
        chunkedErrors.push(error);
      }
    }

    if (chunkedErrors.length) {
      throw new Error(
        "indexPostsJson failed to update is_indexed in chunk. Check log for specific error details.",
      );
    }

    res.status(200).json({ error: null });
  } catch (error) {
    console.log("*** indexProfiles error", error);
    res.status(500).json({ error });
  }
});

app.post("/deleteProfiles", async (req, res) => {
  const { type, old_record } = req.body;
  if (!type || !old_record) {
    console.log("**** deleteProfiles request error", req.body);
    res.sendStatus(400);
    return;
  }

  logWarn({
    executionId: req.executionId,
    context: "deleteProfiles body",
    message: JSON.stringify(req.body),
  });

  try {
    logWarn({
      executionId: req.executionId,
      context: "deleteProfiles data",
      message: { type, id: old_record.id },
    });

    if (type === "DELETE") {
      console.log("**** deleteProfiles", old_record.id);
      const result = await typesense
        .collections("profiles_v2")
        .documents(old_record.id.toString())
        .delete();

      logWarn({
        executionId: req.executionId,
        context: "deleteProfiles result",
        message: `Profile ${old_record.id} is deleted. ${result}`,
      });
    }
  } catch (error) {
    if (error.httpStatus != 404) {
      logError({
        executionId: req.executionId,
        context: "deleteProfiles error",
        error,
      });

      res.sendStatus(500);
      return;
    }
  }
  res.sendStatus(204);
});

app.post("/deletePosts", async (req, res) => {
  const { type, old_record } = req.body;
  if (!type || !old_record) {
    console.log("**** deletePosts request error", req.body);
    res.sendStatus(400);
    return;
  }

  logWarn({
    executionId: req.executionId,
    context: "deletePosts body",
    message: JSON.stringify(req.body),
  });

  try {
    logWarn({
      executionId: req.executionId,
      context: "deletePosts data",
      message: { type, id: old_record.id },
    });

    if (type === "DELETE") {
      console.log("**** deletePosts", old_record.id);
      const result = await typesense
        .collections("posts_v2")
        .documents(old_record.id.toString())
        .delete();

      logWarn({
        executionId: req.executionId,
        context: "deletePosts result",
        message: `Post ${old_record.id} is deleted. ${result}`,
      });
    }
  } catch (error) {
    if (error.httpStatus != 404) {
      logError({
        executionId: req.executionId,
        context: "deletePosts error",
        error,
      });

      res.sendStatus(500);
      return;
    }
  }
  res.sendStatus(204);
});

app.post("/follower", authUser, async (req, res) => {
  const { profile_id, selectedProfile_id, keyword } = req.body;
  if (!profile_id || !selectedProfile_id || !keyword) {
    console.log("**** search followers request error", req.body);
    res.sendStatus(400);
    return;
  }

  const { data, error } = await supabase
    .from("followers")
    .select(
      `
      follower_id,
      profiles:profiles_with_bots!followers_follower_id_fkey!inner(
        id,
        display_name,
        username,
        user_id,
        avatar_url,
        visibility,
        creator_id,
        nsfw,
        followers!followers_following_id_fkey(
          following_id,
          follower_id
        )
      )
      `,
    )
    .order("id", { ascending: false })
    .eq("following_id", profile_id)
    .neq("profiles.visibility", "hidden")
    .neq("profiles.visibility", "archived")
    .or(
      `user_id.not.is.null,visibility.eq.public,creator_id.eq.${selectedProfile_id}`,
      {
        foreignTable: "profiles",
      },
    )
    .or(`display_name.ilike.%${keyword}%,username.ilike.%${keyword}%`, {
      foreignTable: "profiles",
    })
    .eq("profiles.followers.follower_id", selectedProfile_id)
    .limit(100);

  if (error) {
    return res.sendStatus(500);
  }

  res.json({ data });
});

app.post("/following", authUser, async (req, res) => {
  const { profile_id, selectedProfile_id, keyword } = req.body;
  if (!profile_id || !selectedProfile_id || !keyword) {
    res.sendStatus(400);
    return;
  }

  const { data, error } = await supabase
    .from("followers")
    .select(
      `
      following_id,
      profiles:profiles_with_bots!followers_following_id_fkey!inner(
        id,
        display_name,
        username,
        user_id,
        avatar_url,
        visibility,
        creator_id,
        nsfw,
        followers!followers_following_id_fkey(
          following_id,
          follower_id
        )
      )
      `,
    )
    .order("id", { ascending: false })
    .eq("follower_id", profile_id)
    .neq("profiles.visibility", "hidden")
    .neq("profiles.visibility", "archived")
    .or(
      `user_id.not.is.null,visibility.eq.public,creator_id.eq.${selectedProfile_id}`,
      {
        foreignTable: "profiles",
      },
    )
    .or(`display_name.ilike.%${keyword}%,username.ilike.%${keyword}%`, {
      foreignTable: "profiles",
    })
    .eq("profiles.followers.follower_id", selectedProfile_id)
    .limit(100);

  if (error) {
    return res.sendStatus(500);
  }

  res.json({ data });
});

function findTopMatches(input, list, topN = 5) {
  const inputLower = input.toLowerCase();

  // Map each item to its match score and weighted score
  const scoredItems = list.map((item) => {
    const document = `${item.username} ${item.display_name}`.toLowerCase();
    const matchScore = inputLower
      .split(" ")
      .filter((word) => document.includes(word)).length;

    const weightedScore = (1 + item.weight) * matchScore;

    return { ...item, score: matchScore, weightedScore };
  });

  // Sort by weighted score in descending order and take the top N items
  return scoredItems
    .sort((a, b) => b.weightedScore - a.weightedScore)
    .slice(0, topN);
}

async function getFollowingBots(profileId) {
  const { data, error } = await supabase.rpc("get_following_bots", {
    user_profile_id: profileId,
  });

  if (error) {
    throw wrappedSupabaseError(
      error,
      "failed to fetch from 'get_following_bots'",
    );
  }

  return data;
}

app.get("/test", async (req, res) => {
  let profileId = req.query.profile_id;
  let search = req.query.search;
  if (!search) {
    search = "";
  }

  let data = await getDataWithCache(
    getFollowingBots,
    redisClient,
    "following_bots",
    profileId,
    12 * 60 * 60, // 12 hours
  );

  if (!data) {
    res.status(400).json({ error: "Cannot prepare data" });
  }

  const topMatches = findTopMatches(search, data);

  return res.json({ results: topMatches });
});

module.exports = {
  app,
};
