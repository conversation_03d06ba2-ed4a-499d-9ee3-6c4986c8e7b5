jest.setTimeout(5000 * 1000);

const {
  sendSnapToBotsUsingClaudeEndToEnd,
} = require("../../src/pillowClaudeUtils");

const { sendSnapResponseWithImage } = require("../../src/pillowImageUtils");
const {
  sendSnapToBotsUsingPillowConverseEndpoint,
} = require("../../src/pillowUtils");
const { supabase } = require("../../src/supabaseClient");
const { callAndLogOpenAI } = require("../../src/llm");

describe("Conversation Flow Tests", () => {
  const mockTime = new Date("2025-01-02T10:26:39-08:00").getTime();
  const TEST_USER_ID = 8;
  const BOT_TARGET_ID = 372090;

  beforeEach(async () => {
    jest.useFakeTimers();
    jest.setSystemTime(mockTime);

    // Clean up all messages where user is either sender or receiver
    await supabase
      .schema("internal")
      .from("pillow_messages")
      .delete()
      .or(
        `sender_profile_id.eq.${TEST_USER_ID},receiver_profile_id.eq.${TEST_USER_ID}`,
      );
    await supabase
      .schema("internal")
      .from("pillow_memories")
      .delete()
      .eq("conversation", `${TEST_USER_ID}-${BOT_TARGET_ID}`),
      await supabase
        .schema("internal")
        .from("pillow_relationships")
        .delete()
        .eq("user_profile_id", `${TEST_USER_ID}`);

    console.log("DELETED!!");
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  describe("End-to-End Conversation Tests", () => {
    it("should handle a complete conversation flow with Dify N turns", async () => {
      const numTurns = 8; // Set the number of turns you want to test
      let snaps = [
        {
          role: "assistant",
          content: {
            text: "hi!",
            image_description: "a photo of Saniul's face, smiling",
          },
        },
      ];

      const system_prompt = `You are named Saniul an talking to someone on Snapchat for the first time through snaps. Only return JSON in the format:

        {
          text: "your response to the user",
          image_description: "a description of an image you would send as a Snap",
        }
        `;

      for (let turn = 0; turn < numTurns; turn++) {
        // Get the last user message
        const lastUserSnap = snaps[snaps.length - 1];

        // Send snap and get response
        const imageResponse = await sendSnapToBotsUsingPillowConverseEndpoint({
          pillowClientId: TEST_USER_ID,
          userProfileId: TEST_USER_ID,
          botTargetIds: [BOT_TARGET_ID],
          imageUrls: ["https://example.com/user-photo1.jpg"],
          userCaption: lastUserSnap.content.text,
          imageDescription: lastUserSnap.content.image_description,
          testing: true,
        });

        console.log(`Turn ${turn + 1} - Bot Response:`, imageResponse);

        // Add bot's response to snaps
        snaps.push({
          role: "user",
          content: imageResponse,
        });

        // If this isn't the last turn, generate user's next message
        if (turn < numTurns - 1) {
          const messages = [
            {
              role: "system",
              content: system_prompt,
            },
          ];

          // Add all previous snaps to the context
          for (const snap of snaps) {
            messages.push({
              role: snap.role,
              content: JSON.stringify(snap.content),
            });
          }

          console.log("Messages for GPT-4:o", messages);

          // Generate next user message using GPT-4
          const gpt4Response = await callAndLogOpenAI("generate_snap_content", {
            messages,
            temperature: 0.7,
            model: "gpt-4o",
            response_format: { type: "json_object" },
          });

          console.log(
            `Turn ${turn + 1} - Generated User Message:`,
            gpt4Response,
          );

          // Parse and add the generated user message
          const gpt4ResponseJson = JSON.parse(
            gpt4Response.choices[0].message.content,
          );

          snaps.push({
            role: "assistant",
            content: gpt4ResponseJson,
          });
        }
      }

      // iterate through snaps
      // rename "role" user => "tiffany"
      // rename "role" assistant => "gpt4o"

      snaps = snaps.map((snap) => {
        if (snap.role === "user") {
          snap.role = "tiffany";
        } else if (snap.role === "assistant") {
          snap.role = "gpt4o";
        }
        return snap;
      });

      console.log("Final conversation:", snaps);
    });

    // it("should handle a complete conversation flow with claude COT N turns", async () => {
    //   const numTurns = 30; // Set the number of turns you want to test
    //   let snaps = [
    //     {
    //       role: "assistant",
    //       content: {
    //         text: "hi!",
    //         image_description: "a photo of Saniul's face, smiling",
    //       },
    //     },
    //   ];

    //   const system_prompt = `You are named Saniul an talking to someone on Snapchat for the first time through snaps. Only return JSON in the format:

    //     {
    //       text: "your response to the user",
    //       image_description: "a description of an image you would send as a Snap",
    //     }
    //     `;

    //   for (let turn = 0; turn < numTurns; turn++) {
    //     // Get the last user message
    //     const lastUserSnap = snaps[snaps.length - 1];

    //     // Send snap and get response
    //     const imageResponse = await sendSnapToBotsUsingClaudeEndToEnd({
    //       pillowClientId: TEST_USER_ID,
    //       userProfileId: TEST_USER_ID,
    //       botTargetIds: [BOT_TARGET_ID],
    //       imageUrls: ["https://example.com/user-photo1.jpg"],
    //       userCaption: lastUserSnap.content.text,
    //       imageDescription: lastUserSnap.content.image_description,
    //       testing: true,
    //     });

    //     console.log(`Turn ${turn + 1} - Bot Response:`, imageResponse);

    //     // Add bot's response to snaps
    //     snaps.push({
    //       role: "user",
    //       content: imageResponse,
    //     });

    //     // If this isn't the last turn, generate user's next message
    //     if (turn < numTurns - 1) {
    //       const messages = [
    //         {
    //           role: "system",
    //           content: system_prompt,
    //         },
    //       ];

    //       // Add all previous snaps to the context
    //       for (const snap of snaps) {
    //         messages.push({
    //           role: snap.role,
    //           content: JSON.stringify(snap.content),
    //         });
    //       }

    //       console.log("Messages for GPT-4:o", messages);

    //       // Generate next user message using GPT-4
    //       const gpt4Response = await callAndLogOpenAI("generate_snap_content", {
    //         messages,
    //         temperature: 0.7,
    //         model: "gpt-4o",
    //         response_format: { type: "json_object" },
    //       });

    //       console.log(
    //         `Turn ${turn + 1} - Generated User Message:`,
    //         gpt4Response,
    //       );

    //       // Parse and add the generated user message
    //       const gpt4ResponseJson = JSON.parse(
    //         gpt4Response.choices[0].message.content,
    //       );

    //       snaps.push({
    //         role: "assistant",
    //         content: gpt4ResponseJson,
    //       });
    //     }
    //   }

    //   // iterate through snaps
    //   // rename "role" user => "tiffany"
    //   // rename "role" assistant => "gpt4o"

    //   snaps = snaps.map((snap) => {
    //     if (snap.role === "user") {
    //       snap.role = "tiffany";
    //     } else if (snap.role === "assistant") {
    //       snap.role = "gpt4o";
    //     }
    //     return snap;
    //   });

    //   console.log("Final conversation:", snaps);
    // });
  });
});
