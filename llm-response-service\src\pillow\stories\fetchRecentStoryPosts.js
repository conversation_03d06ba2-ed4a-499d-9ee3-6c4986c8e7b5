const { supabase, wrappedSupabaseError } = require("../../supabaseClient");

async function fetchRecentStoryPosts({ bot_profile_id, ascending }) {
  const twentyFourHoursAgo = new Date(
    new Date().getTime() - 24 * 60 * 60 * 1000,
  );

  const { data: storyPosts, error: storyPostsError } = await supabase
    .from("pillow_story_posts")
    .select("*")
    .eq("poster_profile_id", bot_profile_id)
    .gte("created_at", twentyFourHoursAgo.toISOString())
    .order("created_at", { ascending });

  if (storyPostsError) {
    const error = wrappedSupabaseError(storyPostsError);
    throw error;
  }

  return storyPosts;
}

module.exports = {
  fetchRecentStoryPosts,
};
