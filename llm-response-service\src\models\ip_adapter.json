{
  "last_node_id": 41,
  "last_link_id": 37,
  "nodes": [
    {
      "id": 5,
      "type": "EmptyLatentImage",
      "pos": [
        100,
        358
      ],
      "size": {
        "0": 315,
        "1": 106
      },
      "flags": {},
      "order": 0,
      "mode": 0,
      "outputs": [
        {
          "name": "LATENT",
          "type": "LATENT",
          "links": [
            4
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "EmptyLatentImage"
      },
      "widgets_values": [
        768,
        1024,
        1
      ]
    },
    {
      "id": 7,
      "type": "CLIPTextEncode",
      "pos": [
        515,
        672
      ],
      "size": {
        "0": 400,
        "1": 200
      },
      "flags": {},
      "order": 6,
      "mode": 0,
      "inputs": [
        {
          "name": "clip",
          "type": "CLIP",
          "link": 6
        }
      ],
      "outputs": [
        {
          "name": "CONDITIONING",
          "type": "CONDITIONING",
          "links": [
            3,
            27
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "CLIPTextEncode"
      },
      "widgets_values": [
        "(worst quality), (low quality), (normal quality), lowres, normal quality, ${nsfw ? "" : "nsfw"},"
      ]
    },
    {
      "id": 26,
      "type": "UltralyticsDetectorProvider",
      "pos": [
        586,
        988
      ],
      "size": {
        "0": 315,
        "1": 78
      },
      "flags": {},
      "order": 1,
      "mode": 0,
      "outputs": [
        {
          "name": "BBOX_DETECTOR",
          "type": "BBOX_DETECTOR",
          "links": [
            18
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "SEGM_DETECTOR",
          "type": "SEGM_DETECTOR",
          "links": null,
          "shape": 3,
          "slot_index": 1
        }
      ],
      "properties": {
        "Node name for S&R": "UltralyticsDetectorProvider"
      },
      "widgets_values": [
        "bbox/face_yolov8m.pt"
      ]
    },
    {
      "id": 33,
      "type": "ImpactSimpleDetectorSEGS",
      "pos": [
        1039,
        793
      ],
      "size": {
        "0": 315,
        "1": 310
      },
      "flags": {},
      "order": 11,
      "mode": 0,
      "inputs": [
        {
          "name": "bbox_detector",
          "type": "BBOX_DETECTOR",
          "link": 18
        },
        {
          "name": "image",
          "type": "IMAGE",
          "link": 28,
          "slot_index": 1
        },
        {
          "name": "sam_model_opt",
          "type": "SAM_MODEL",
          "link": null
        },
        {
          "name": "segm_detector_opt",
          "type": "SEGM_DETECTOR",
          "link": null
        }
      ],
      "outputs": [
        {
          "name": "SEGS",
          "type": "SEGS",
          "links": [
            19
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "ImpactSimpleDetectorSEGS"
      },
      "widgets_values": [
        0.5,
        10,
        3,
        50,
        0.93,
        0,
        0,
        0.7,
        0
      ]
    },
    {
      "id": 31,
      "type": "ImpactSEGSOrderedFilter",
      "pos": [
        1424,
        792
      ],
      "size": {
        "0": 315,
        "1": 150
      },
      "flags": {},
      "order": 12,
      "mode": 0,
      "inputs": [
        {
          "name": "segs",
          "type": "SEGS",
          "link": 19
        }
      ],
      "outputs": [
        {
          "name": "filtered_SEGS",
          "type": "SEGS",
          "links": [
            21
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "remained_SEGS",
          "type": "SEGS",
          "links": null,
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "ImpactSEGSOrderedFilter"
      },
      "widgets_values": [
        "area(=w*h)",
        true,
        0,
        4
      ]
    },
    {
      "id": 30,
      "type": "LoraLoaderModelOnly",
      "pos": [
        448,
        87
      ],
      "size": {
        "0": 315,
        "1": 82
      },
      "flags": {},
      "order": 4,
      "mode": 0,
      "inputs": [
        {
          "name": "model",
          "type": "MODEL",
          "link": 17
        }
      ],
      "outputs": [
        {
          "name": "MODEL",
          "type": "MODEL",
          "links": [
            35
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "LoraLoaderModelOnly"
      },
      "widgets_values": [
        "NSFWFilter.safetensors",
        -1
      ]
    },
    {
      "id": 35,
      "type": "DetailerForEachDebug",
      "pos": [
        1585,
        339
      ],
      "size": {
        "0": 443.4000244140625,
        "1": 600
      },
      "flags": {},
      "order": 13,
      "mode": 0,
      "inputs": [
        {
          "name": "image",
          "type": "IMAGE",
          "link": 23
        },
        {
          "name": "segs",
          "type": "SEGS",
          "link": 21
        },
        {
          "name": "model",
          "type": "MODEL",
          "link": 22
        },
        {
          "name": "clip",
          "type": "CLIP",
          "link": 24
        },
        {
          "name": "vae",
          "type": "VAE",
          "link": 25
        },
        {
          "name": "positive",
          "type": "CONDITIONING",
          "link": 26
        },
        {
          "name": "negative",
          "type": "CONDITIONING",
          "link": 27
        },
        {
          "name": "detailer_hook",
          "type": "DETAILER_HOOK",
          "link": null
        }
      ],
      "outputs": [
        {
          "name": "image",
          "type": "IMAGE",
          "links": [
            30
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "cropped",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        },
        {
          "name": "cropped_refined",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        },
        {
          "name": "cropped_refined_alpha",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        },
        {
          "name": "cnet_images",
          "type": "IMAGE",
          "links": null,
          "shape": 6
        }
      ],
      "properties": {
        "Node name for S&R": "DetailerForEachDebug"
      },
      "widgets_values": [
        384,
        true,
        1024,
        107651534176815,
        "randomize",
        6,
        8,
        "euler",
        "normal",
        0.5,
        5,
        true,
        true,
        "",
        1,
        false,
        20
      ]
    },
    {
      "id": 8,
      "type": "VAEDecode",
      "pos": [
        1302,
        174
      ],
      "size": {
        "0": 210,
        "1": 46
      },
      "flags": {},
      "order": 10,
      "mode": 0,
      "inputs": [
        {
          "name": "samples",
          "type": "LATENT",
          "link": 7
        },
        {
          "name": "vae",
          "type": "VAE",
          "link": 8
        }
      ],
      "outputs": [
        {
          "name": "IMAGE",
          "type": "IMAGE",
          "links": [
            23,
            28
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "VAEDecode"
      }
    },
    {
      "id": 3,
      "type": "KSampler",
      "pos": [
        1046,
        183
      ],
      "size": {
        "0": 315,
        "1": 262
      },
      "flags": {},
      "order": 9,
      "mode": 0,
      "inputs": [
        {
          "name": "model",
          "type": "MODEL",
          "link": 33,
          "slot_index": 0
        },
        {
          "name": "positive",
          "type": "CONDITIONING",
          "link": 2
        },
        {
          "name": "negative",
          "type": "CONDITIONING",
          "link": 3
        },
        {
          "name": "latent_image",
          "type": "LATENT",
          "link": 4
        }
      ],
      "outputs": [
        {
          "name": "LATENT",
          "type": "LATENT",
          "links": [
            7
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "KSampler"
      },
      "widgets_values": [
        350703209298011,
        "randomize",
        20,
        7,
        "euler",
        "karras",
        1
      ]
    },
    {
      "id": 4,
      "type": "CheckpointLoaderSimple",
      "pos": [
        100,
        130
      ],
      "size": {
        "0": 315,
        "1": 98
      },
      "flags": {},
      "order": 2,
      "mode": 0,
      "outputs": [
        {
          "name": "MODEL",
          "type": "MODEL",
          "links": [
            17,
            22
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "CLIP",
          "type": "CLIP",
          "links": [
            5,
            6,
            24
          ],
          "shape": 3,
          "slot_index": 1
        },
        {
          "name": "VAE",
          "type": "VAE",
          "links": [
            8,
            25
          ],
          "shape": 3,
          "slot_index": 2
        }
      ],
      "properties": {
        "Node name for S&R": "CheckpointLoaderSimple"
      },
      "widgets_values": [
        "meinamix.safetensors"
      ]
    },
    {
      "id": 24,
      "type": "SaveImage",
      "pos": [
        1491,
        -493
      ],
      "size": [
        522.276414794922,
        770.7019140625001
      ],
      "flags": {},
      "order": 14,
      "mode": 0,
      "inputs": [
        {
          "name": "images",
          "type": "IMAGE",
          "link": 30
        }
      ],
      "properties": {},
      "widgets_values": [
        "ComfyUI"
      ]
    },
    {
      "id": 38,
      "type": "LoadImage",
      "pos": [
        1178,
        -562
      ],
      "size": {
        "0": 315,
        "1": 314
      },
      "flags": {},
      "order": 3,
      "mode": 0,
      "outputs": [
        {
          "name": "IMAGE",
          "type": "IMAGE",
          "links": [
            32
          ],
          "shape": 3,
          "slot_index": 0
        },
        {
          "name": "MASK",
          "type": "MASK",
          "links": null,
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "LoadImage"
      },
      "widgets_values": [
        "Screenshot 2024-05-06 at 6.14.46 PM.png",
        "image"
      ]
    },
    {
      "id": 37,
      "type": "IPAdapter",
      "pos": [
        744,
        -480
      ],
      "size": {
        "0": 315,
        "1": 190
      },
      "flags": {
        "collapsed": false
      },
      "order": 8,
      "mode": 0,
      "inputs": [
        {
          "name": "model",
          "type": "MODEL",
          "link": 36,
          "slot_index": 0
        },
        {
          "name": "ipadapter",
          "type": "IPADAPTER",
          "link": 37,
          "slot_index": 1
        },
        {
          "name": "image",
          "type": "IMAGE",
          "link": 32
        },
        {
          "name": "attn_mask",
          "type": "MASK",
          "link": null
        }
      ],
      "outputs": [
        {
          "name": "MODEL",
          "type": "MODEL",
          "links": [
            33
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "IPAdapter"
      },
      "widgets_values": [
        1,
        0,
        1,
        "prompt is more important"
      ]
    },
    {
      "id": 41,
      "type": "IPAdapterUnifiedLoader",
      "pos": [
        842,
        -280
      ],
      "size": {
        "0": 315,
        "1": 78
      },
      "flags": {},
      "order": 7,
      "mode": 0,
      "inputs": [
        {
          "name": "model",
          "type": "MODEL",
          "link": 35
        },
        {
          "name": "ipadapter",
          "type": "IPADAPTER",
          "link": null
        }
      ],
      "outputs": [
        {
          "name": "model",
          "type": "MODEL",
          "links": [
            36
          ],
          "shape": 3
        },
        {
          "name": "ipadapter",
          "type": "IPADAPTER",
          "links": [
            37
          ],
          "shape": 3
        }
      ],
      "properties": {
        "Node name for S&R": "IPAdapterUnifiedLoader"
      },
      "widgets_values": [
        "STANDARD (medium strength)"
      ]
    },
    {
      "id": 6,
      "type": "CLIPTextEncode",
      "pos": [
        798,
        -92
      ],
      "size": {
        "0": 400,
        "1": 200
      },
      "flags": {},
      "order": 5,
      "mode": 0,
      "inputs": [
        {
          "name": "clip",
          "type": "CLIP",
          "link": 5
        }
      ],
      "outputs": [
        {
          "name": "CONDITIONING",
          "type": "CONDITIONING",
          "links": [
            2,
            26
          ],
          "shape": 3,
          "slot_index": 0
        }
      ],
      "properties": {
        "Node name for S&R": "CLIPTextEncode"
      },
      "widgets_values": [
        "green alien, no hair, full body"
      ]
    }
  ],
  "links": [
    [
      2,
      6,
      0,
      3,
      1,
      "CONDITIONING"
    ],
    [
      3,
      7,
      0,
      3,
      2,
      "CONDITIONING"
    ],
    [
      4,
      5,
      0,
      3,
      3,
      "LATENT"
    ],
    [
      5,
      4,
      1,
      6,
      0,
      "CLIP"
    ],
    [
      6,
      4,
      1,
      7,
      0,
      "CLIP"
    ],
    [
      7,
      3,
      0,
      8,
      0,
      "LATENT"
    ],
    [
      8,
      4,
      2,
      8,
      1,
      "VAE"
    ],
    [
      17,
      4,
      0,
      30,
      0,
      "MODEL"
    ],
    [
      18,
      26,
      0,
      33,
      0,
      "BBOX_DETECTOR"
    ],
    [
      19,
      33,
      0,
      31,
      0,
      "SEGS"
    ],
    [
      21,
      31,
      0,
      35,
      1,
      "SEGS"
    ],
    [
      22,
      4,
      0,
      35,
      2,
      "MODEL"
    ],
    [
      23,
      8,
      0,
      35,
      0,
      "IMAGE"
    ],
    [
      24,
      4,
      1,
      35,
      3,
      "CLIP"
    ],
    [
      25,
      4,
      2,
      35,
      4,
      "VAE"
    ],
    [
      26,
      6,
      0,
      35,
      5,
      "CONDITIONING"
    ],
    [
      27,
      7,
      0,
      35,
      6,
      "CONDITIONING"
    ],
    [
      28,
      8,
      0,
      33,
      1,
      "IMAGE"
    ],
    [
      30,
      35,
      0,
      24,
      0,
      "IMAGE"
    ],
    [
      32,
      38,
      0,
      37,
      2,
      "IMAGE"
    ],
    [
      33,
      37,
      0,
      3,
      0,
      "MODEL"
    ],
    [
      35,
      30,
      0,
      41,
      0,
      "MODEL"
    ],
    [
      36,
      41,
      0,
      37,
      0,
      "MODEL"
    ],
    [
      37,
      41,
      1,
      37,
      1,
      "IPADAPTER"
    ]
  ],
  "groups": [],
  "config": {},
  "extra": {},
  "version": 0.4
}