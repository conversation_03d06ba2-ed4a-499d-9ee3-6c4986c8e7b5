const express = require("express");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const {
  logError,
  logWarn,
  logInfo,
  wrappedSupabaseError,
  getOrGenerateSignedUrl,
  checkCloneValid,
  checkProfileValid,
  validateOwnConversation,
} = require("./utils");

const axios = require("axios");
const { updateImageInfo, imageGenerationFailed } = require("./imageHelpers");
const opentelemetry = require("@opentelemetry/api");

const { rateLimit } = require("express-rate-limit");
const {
  enqueueTask,
  generateWorkflow,
  getTaskStatus,
  generateWorkflowMultipleCharacters,
} = require("./image_service_broker");
const { authUser } = require("./middleware");
const {
  detectCharacterWithNsflSentence,
  removeAdultWords,
} = require("./nsfwHelpers");
const {
  decorateWithActiveSpanAsync,
  tracer,
  SpanStatusCode,
  addSpanAttribute,
  addSpanEvent,
} = require("./instrumentation/tracer");
const redisClient = require("./redisClient");
const AVATAR_MAX_COUNT = 10;
const { callAndLogOpenAI } = require("./llm");
const {
  isPillowGenerationType,
  onPillowImageGenerationTaskSucceeded,
  onPillowImageGenerationTaskFailed,
} = require("./pillowImageUtils");
const {
  onVignetteImageGenerationFailed,
  onVignetteImageGenerationCompleted,
} = require("./vignettes/vignetteImageGenerationCompletion");
const { onPostRefinedTaskSucceeded } = require("./postRefinedUtils");

const meter = opentelemetry.metrics.getMeter("comfy");

const metrics = {
  fetchTaskPayloadFailure: meter.createCounter(
    "butterfliesapi_broker_completion_fetch_task_failure",
    {
      description:
        "Failure: fetch task payload from db when handling ImageServiceBroker task completion",
    },
  ),
  fetchTaskPayloadSuccess: meter.createCounter(
    "butterfliesapi_broker_completion_fetch_task_success",
    {
      description:
        "Success: fetch task payload from db when handling ImageServiceBroker task completion",
    },
  ),
  imageUploadToSupabaseFailure: meter.createCounter(
    "butterfliesapi_broker_completion_image_upload_to_supabase_failure",
    {
      description:
        "Failure: upload image to Supabase when handling ImageServiceBroker task completion",
    },
  ),
  imageUploadToSupabaseSuccess: meter.createCounter(
    "butterfliesapi_broker_completion_image_upload_to_supabase_success",
    {
      description:
        "Success: upload image to Supabase when handling ImageServiceBroker task completion",
    },
  ),
  updateImageInfoFailure: meter.createCounter(
    "butterfliesapi_broker_completion_update_image_info_failure",
    {
      description:
        "Failure: update image info in db when handling ImageServiceBroker task completion",
    },
  ),
  updateImageInfoSuccess: meter.createCounter(
    "butterfliesapi_broker_completion_update_image_info_success",
    {
      description:
        "Success: update image info in db when handling ImageServiceBroker task completion",
    },
  ),
  brokerCompletionHandlerFailure: meter.createCounter(
    "butterfliesapi_broker_completion_handler_failure",
    {
      description: "Failure: /taskDone handler",
    },
  ),
  brokerCompletionHandlerSuccess: meter.createCounter(
    "butterfliesapi_broker_completion_handler_success",
    {
      description: "Success: /taskDone handler",
    },
  ),
};
const avatarGenerationLimiter = rateLimit({
  windowMs: 5 * 1000, // 5 seconds
  max: 10, // Limit each IP to 1 requests per 5 seconds
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

function isArtStyleRealistic(artStyle) {
  return (
    artStyle === "realistic_v3" ||
    artStyle === "realistic_v2" ||
    artStyle === "realistic"
  );
}

require("dotenv").config();

app.get("/ping", async (req, res) => {
  return res.sendStatus(200);
});

app.get("/test", async (req, res) => {
  const { data } = await supabase
    .from("tasks")
    .select("*")
    .eq("id", "6384672")
    .single();

  console.log("disp", data);

  axios.post("http://localhost:8080/v1/sd/runNextTask", data.payload);

  res.sendStatus(200);
});

const generateComfyRequest = decorateWithActiveSpanAsync(
  "comfy.js generateComfyRequest",
  _generateComfyRequest,
);
async function _generateComfyRequest({
  bot,
  descriptionOfImage,
  post_id,
  generatePhotoMessage_id,
  generate_urls,
  imageMessage_id,
  systemMessage_id,
  conversation_id,
  width,
  height,
  artStyle,
  generationType,
  device_type,
  is_avatar_photo,
  priority = "low",
  batch_size,
  contains_character = true,
  nsfw,
  user_id,
  pregeneratedTaskStub = null,
  clone_id,
  creator_id,
  dream_id,
  dream_batch_id,
  emphasize_text,
  is_dm_message,
  leaderboard_submission = false,
  is_premium = false,
  user_usage_id = null,
  ...rest
}) {
  addSpanAttribute("bot_id", bot?.id);
  addSpanAttribute("post_id", post_id);
  addSpanAttribute("conversation_id", conversation_id);
  addSpanAttribute("generationType", generationType);
  addSpanAttribute("priority", priority);
  addSpanAttribute("batch_size", batch_size);
  addSpanAttribute("contains_character", contains_character);
  addSpanAttribute("user_id", user_id);
  addSpanAttribute("clone_id", clone_id);
  addSpanAttribute("dream_id", dream_id);

  const seed = Math.floor(Math.random() * 100000000000);

  // check if has useable face_avatar by fetching from profiles
  let profile = {};

  if (bot) {
    addSpanEvent("get_profile");
    await tracer.withActiveSpan(
      "get profile and avatar_photo_contains_face",
      async (span) => {
        const response = await supabase
          .from("profiles")
          .select(
            "id, avatar_photo_contains_face, avatar_url, nsfw, username, avatar_should_reactor, imitation",
          )
          .eq("id", bot?.profile_id)
          .neq("visibility", "archived")
          .single();

        addSpanAttribute("profile_id", bot?.profile_id);

        profile = response.data;
        const profileError = response.error;

        if (profileError) {
          const error = wrappedSupabaseError(profileError);
          logError({
            context: "**** generateComfyRequest get profile error",
            error: error,
          });
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error?.message,
          });
        }
      },
    );
  }

  let promptType = "basic";

  let nsfwValue;
  if (nsfw === undefined || nsfw === null) {
    if (profile) {
      nsfwValue = profile.nsfw === "nsfw" ? true : false;
    } else {
      const error = new Error(
        "no provided nsfw value and don't have a profile to get the nsfw value from",
      );
      logWarn({
        context: "**** generateComfyRequest no nsfw value",
        error,
      });
      // XXX: It seems like we should throw here, but going to merge this without throwing to see if the error logs come up.
      //      I don't actually know what the `nsfwValue` is used for in the image service, so I asked the team here:
      //      https://team-butterflies.slack.com/archives/C067028A1FA/p1722078527109439
    }
  } else {
    nsfwValue = nsfw;
  }

  if (profile?.imitation === "celebrity") {
    nsfwValue = false;
  }

  addSpanAttribute("nsfw", nsfwValue);

  addSpanEvent("get_payload");
  let payload = {
    post_id,
    generatePhotoMessage_id,
    generate_urls,
    imageMessage_id,
    systemMessage_id,
    conversation_id,
    profile_id: bot?.profile_id,
    creator_id,
    username: profile?.username ?? "",
    priority,
    width,
    height,
    batch_size: batch_size,
    artStyle: bot?.art_style ?? artStyle,
    generationType,
    device_type,
    seed,
    nsfw: nsfwValue,
    promptType,
    contains_character: contains_character,
    avatar_url: profile?.avatar_url,
    leaderboard_submission,
    is_premium,
    ...rest,
  };

  if (dream_id) {
    payload.dream_id = dream_id;
  }
  if (dream_batch_id) {
    payload.dream_batch_id = dream_batch_id;
  }

  if (user_id) {
    payload = {
      ...payload,
      user_id: user_id,
    };
  }

  // Add user_usage_id to payload if present
  if (user_usage_id) {
    payload.user_usage_id = user_usage_id;
  }

  // check if should use faceId
  if (
    (generationType === "post" ||
      generationType === "message" ||
      generationType === "onboarding" ||
      generationType === "postPrompt" ||
      generationType === "dreams") &&
    isArtStyleRealistic(bot?.art_style)
  ) {
    if (profile?.avatar_photo_contains_face) {
      promptType = "faceId";

      // check if should reactor
      if (profile?.avatar_should_reactor) {
        promptType = "reactor";
      }

      payload = {
        ...payload,
        face_image_url: profile?.avatar_url,
        promptType: promptType,
      };
    }
  } else if (generationType === "avatar") {
    promptType = "avatar";

    payload = {
      ...payload,
      promptType: promptType,
    };
  }

  // nsfw should check if is undefined or null
  // if it is, it should use the profile.nsfw, otherwise use the value passed in

  // for clones
  // clone_id is passed in for avatar generation, afterwards, it's bounded to the bot that is created
  let _clone_id = clone_id;

  if (!_clone_id && bot?.clone_id) {
    _clone_id = bot.clone_id;
  }

  if (_clone_id) {
    const { data: clone, error: cloneError } = await supabase
      .from("clones")
      .select("*")
      .eq("id", _clone_id)
      .single();

    if (cloneError) {
      logError({
        context:
          "**** generateComfyRequest get clone error – tried to create clone avatar but no clone id exist",
        error: cloneError,
      });
      return;
    }

    promptType = "reactor";
    payload.face_image_url = await getOrGenerateSignedUrl(
      clone.images.front.original,
    );

    payload = {
      ...payload,
      promptType: promptType,
    };

    console.log("now check", payload.face_image_url);
  }

  addSpanEvent("generate_prompt");
  let prompt = "";
  let appearancePrompt = "";

  const injectGender = false;

  // don't do this for now
  if (promptType === "faceId" && injectGender) {
    let botGender = bot?.gender === "male" ? "a man" : "a woman";
    prompt = `${botGender}, ${descriptionOfImage}, high quality, award winning, highres, 8k`;
    appearancePrompt = `${descriptionOfImage}, (${bot?.description}), high quality, award winning, highres, 8k`;
  } else {
    prompt = generatePrompt({
      descriptionOfImage,
      botDetails: bot,
      isAvatarPhoto: is_avatar_photo,
      contains_character,
      promptType,
      emphasize_text,
      is_dm_message,
    });
  }

  let taskGroup = "basic";
  if ((bot?.art_style ?? artStyle) === "realistic") {
    // Special flag for SDXL
    taskGroup = "reactor";
  } else if (promptType === "basic") {
    taskGroup = "basic";
  } else if (promptType === "faceId") {
    taskGroup = "face";
  } else if (promptType === "reactor") {
    taskGroup = "clone";
  } else if (promptType === "avatar") {
    taskGroup = "avatar";
  }

  addSpanAttribute("task_group", taskGroup);

  payload = {
    ...payload,
    prompt,
  };

  // if we already have a task, update the payload, else generate one
  let task = pregeneratedTaskStub;

  logInfo({
    context: "generateComfyRequest",
    message: `checking if task already exists, task id: ${task?.id}, task group: ${taskGroup}`,
  });
  if (task) {
    await tracer.withActiveSpan("update tasks record", async (span) => {
      const response = await supabase
        .from("tasks")
        .update({
          payload,
        })
        .eq("id", task.id)
        .select("id, created_at, status")
        .single();

      task = response.data;
      const taskError = response.error;

      if (taskError) {
        const error = wrappedSupabaseError(taskError);
        logError({
          context: "*** generateComfyRequest update tasks record",
          error: error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
  } else {
    await tracer.withActiveSpan("insert tasks record", async (span) => {
      const response = await supabase
        .from("tasks")
        .insert({
          service: "comfy",
          status: "queued",
          payload,
        })
        .select("id, created_at, status")
        .single();

      task = response.data;
      const taskError = response.error;

      if (taskError) {
        const error = wrappedSupabaseError(taskError);
        logError({
          context: "*** generateComfyRequest insert tasks record",
          error: error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
  }

  addSpanAttribute("task_id", task.id);

  payload = {
    ...payload,
    task_id: task.id.toString(),
  };

  // Submit task to Image Service
  logInfo({
    context: "generateComfyRequest - enqueue task",
    message: `enqueueing task to image service, group: ${taskGroup}, task id: ${task?.id}`,
    data: payload,
  });

  const workflow = await tracer.withActiveSpan("generateWorkflow", async () => {
    return await generateWorkflow({
      prompt,
      artStyle: bot?.art_style ?? artStyle,
      face_image_url: payload?.face_image_url,
      avatar_url: profile?.avatar_url,
      promptType,
      width,
      height,
      batch_size,
      seed,
      nsfw: nsfwValue,
      contains_character,
      appearancePrompt,
      is_dm_message,
    });
  });

  // XXX: if this fails, we don't have _any_ logic that will retry actually enqueueing the task with the image service.
  //      The task is already in the database in queued state, but nothing will happen.
  //
  //      If a client tries to query the status of the task after 1h, it will then finally be marked as failed.
  addSpanEvent("enqueue_task");
  await enqueueTask(
    JSON.stringify(workflow),
    getPriority(priority, generationType),
    taskGroup,
    task.id.toString(),
  );
  addSpanEvent("after_enqueue_task");

  return {
    ...task,
    task_id: task.id.toString(),
    task_name: task.id.toString(),
  };
}

const generateComfyRequestForMultipleCharacters = decorateWithActiveSpanAsync(
  "comfy.js generateComfyRequestForMultipleCharacters",
  _generateComfyRequestForMultipleCharacters,
);

// this should realy be generalized to take arbitrary number of bots but...
// it makes the function more complicated, less readable. and it's going to be a while before we can do more than 2 bots
async function _generateComfyRequestForMultipleCharacters({
  bot_1,
  bot_2,
  bot_1_action,
  bot_2_action,
  image_prompt,
  post_id,
  generatePhotoMessage_id,
  imageMessage_id,
  systemMessage_id,
  conversation_id,
  width,
  height,
  artStyle,
  generationType,
  priority = "low",
  batch_size,
  nsfw,
  user_id,
  background,
  bot_1_has_face,
  bot_2_has_face,
  user_usage_id = null,
}) {
  const seed = Math.floor(Math.random() * 100000000000);

  let profile_1;

  if (bot_1) {
    addSpanEvent("get_profile");
    await tracer.withActiveSpan(
      "get profile and avatar_photo_contains_face",
      async (span) => {
        const response = await supabase
          .from("profiles")
          .select(
            "id, avatar_photo_contains_face, avatar_url, nsfw, username, avatar_should_reactor",
          )
          .eq("id", bot_1?.profile_id)
          .neq("visibility", "archived")
          .single();

        addSpanAttribute("profile_id", bot_1?.profile_id);

        profile_1 = response.data;
        const profileError = response.error;

        if (profileError) {
          const error = wrappedSupabaseError(profileError);
          logError({
            context: "**** generateComfyRequest get profile error",
            error: error,
          });
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error?.message,
          });
        }
      },
    );
  }

  let profile_2;

  if (bot_2) {
    addSpanEvent("get_profile");
    await tracer.withActiveSpan(
      "get profile and avatar_photo_contains_face",
      async (span) => {
        const response = await supabase
          .from("profiles")
          .select(
            "id, avatar_photo_contains_face, avatar_url, nsfw, username, avatar_should_reactor",
          )
          .eq("id", bot_2?.profile_id)
          .neq("visibility", "archived")
          .single();

        addSpanAttribute("profile_id", bot_2?.profile_id);

        profile_2 = response.data;
        const profileError = response.error;

        if (profileError) {
          const error = wrappedSupabaseError(profileError);
          logError({
            context: "**** generateComfyRequest get profile error",
            error: error,
          });
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error?.message,
          });
        }
      },
    );
  }

  let promptType = "multiple_characters";

  let clone_id_1 = bot_1?.clone_id;
  let face_image_url_1;

  if (clone_id_1) {
    const { data: clone, error: cloneError } = await supabase
      .from("clones")
      .select("*")
      .eq("id", clone_id_1)
      .single();

    if (cloneError) {
      logError({
        context:
          "**** generateComfyRequest get clone error – tried to create clone avatar but no clone id exist",
        error: cloneError,
      });
      return;
    }

    promptType = "reactor";
    face_image_url_1 = await getOrGenerateSignedUrl(
      clone.images.front.original,
    );
  }

  let clone_id_2 = bot_2?.clone_id;
  let face_image_url_2;

  if (clone_id_2) {
    const { data: clone, error: cloneError } = await supabase
      .from("clones")
      .select("*")
      .eq("id", clone_id_2)
      .single();

    if (cloneError) {
      logError({
        context:
          "**** generateComfyRequest get clone error – tried to create clone avatar but no clone id exist",
        error: cloneError,
      });
      return;
    }

    promptType = "reactor";
    face_image_url_2 = await getOrGenerateSignedUrl(
      clone.images.front.original,
    );
  }

  if (!face_image_url_1) {
    face_image_url_1 = profile_1?.avatar_url;
  }

  if (!face_image_url_2) {
    face_image_url_2 = profile_2?.avatar_url;
  }

  // let nsfwValue;
  // if (nsfw === undefined || nsfw === null) {
  //   if (profile) {
  //     nsfwValue = profile.nsfw === "nsfw" ? true : false;
  //   } else {
  //     const error = new Error(
  //       "no provided nsfw value and don't have a profile to get the nsfw value from",
  //     );
  //     logWarn({
  //       context: "**** generateComfyRequest no nsfw value",
  //       error,
  //     });
  //     // XXX: It seems like we should throw here, but going to merge this without throwing to see if the error logs come up.
  //     //      I don't actually know what the `nsfwValue` is used for in the image service, so I asked the team here:
  //     //      https://team-butterflies.slack.com/archives/C067028A1FA/p1722078527109439
  //   }
  // } else {
  //   nsfwValue = nsfw;
  // }

  // addSpanAttribute("nsfw", nsfwValue);

  addSpanEvent("get_payload");
  let payload = {
    post_id,
    generatePhotoMessage_id,
    // generate_urls,
    imageMessage_id,
    systemMessage_id,
    conversation_id,
    profile_id: bot_1?.profile_id, // this should be the profile of the bot whose post we're generating
    // username,
    priority: 9, // change later vu
    width,
    height,
    batch_size: batch_size,
    artStyle,
    generationType,
    seed,
    nsfw: false,
    promptType,
    contains_character: true,
    face_image_url_1,
    face_image_url_2,
    bot_description_1: `${bot_1_action}`,
    bot_description_2: `${bot_2_action}`,
    image_prompt,
    background,
    user_usage_id,
  };

  if (user_id) {
    payload = {
      ...payload,
      user_id: user_id,
    };
  }

  // if we already have a task, update the payload, else generate one
  let task;
  if (task) {
    await tracer.withActiveSpan("update tasks record", async (span) => {
      const response = await supabase
        .from("tasks")
        .update({
          payload,
        })
        .eq("id", task.id)
        .select("id, created_at, status")
        .single();

      task = response.data;
      const taskError = response.error;

      if (taskError) {
        const error = wrappedSupabaseError(taskError);
        logError({
          context: "*** generateComfyRequest update tasks record",
          error: error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
  } else {
    await tracer.withActiveSpan("insert tasks record", async (span) => {
      const response = await supabase
        .from("tasks")
        .insert({
          service: "comfy",
          status: "queued",
          payload,
        })
        .select("id, created_at, status")
        .single();

      task = response.data;
      const taskError = response.error;

      if (taskError) {
        const error = wrappedSupabaseError(taskError);
        logError({
          context: "*** generateComfyRequest insert tasks record",
          error: error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
  }

  addSpanAttribute("task_id", task.id);

  let taskGroup = "clone";

  payload = {
    ...payload,
    task_id: task.id.toString(),
  };

  // Submit task to Image Service
  logInfo({
    context: "enqueue task",
    message: `enqueueing task to image service, group: ${taskGroup}, task id: ${task.id.toString()}`,
  });

  console.log("face_image_url_1", face_image_url_1);

  console.log("face_image_url_2", face_image_url_2);

  let stripped_out_bot_description_1;
  let stripped_out_bot_description_2;

  if (bot_1.description.includes(")")) {
    stripped_out_bot_description_1 = bot_1.description.split(")")[1];
    stripped_out_bot_description_1 = `(${stripped_out_bot_description_1})`;
  }

  if (bot_2.description.includes(")")) {
    stripped_out_bot_description_2 = bot_2.description.split(")")[1];
    stripped_out_bot_description_2 = `(${stripped_out_bot_description_2})`;
  }

  const workflow = await tracer.withActiveSpan("generateWorkflow", async () => {
    return await generateWorkflowMultipleCharacters({
      image_prompt,
      artStyle,
      face_image_url_1,
      face_image_url_2,
      bot_description_1: `${bot_1_action}, ${bot_1.display_name}, ${stripped_out_bot_description_1}, ${background}, looking at camera, high quality, 8k, dramatic lighting`,
      bot_description_2: `${bot_2_action}, ${bot_2.display_name}, ${stripped_out_bot_description_2}, ${background}, looking at camera, high quality, 8k, dramatic lighting`,
      promptType,
      width,
      height,
      batch_size,
      seed,
      nsfw: false,
      background,
      bot_1_has_face,
      bot_2_has_face,
    });
  });

  // XXX: if this fails, we don't have _any_ logic that will retry actually enqueueing the task with the image service.
  //      The task is already in the database in queued state, but nothing will happen.
  //
  //      If a client tries to query the status of the task after 1h, it will then finally be marked as failed.
  addSpanEvent("enqueue_task");

  await enqueueTask(
    JSON.stringify(workflow),
    getPriority(priority, generationType),
    taskGroup,
    task.id.toString(),
  );
  addSpanEvent("after_enqueue_task");

  return {
    ...task,
    task_id: task.id.toString(),
    task_name: task.id.toString(),
  };
}

// ONLY USED FOR DEBUGGING
async function generatePostImageWithPrompts({ descriptionOfImage, bot }) {
  const task = await generateComfyRequest({
    bot,
    descriptionOfImage,
    width: 864,
    height: 1024,
    artStyle: bot.art_style,
    generationType: "postPrompt",
    is_avatar_photo: false,
    priority: 10,
    batch_size: 1,
  });

  try {
    const status = await getTaskStatus(task.task_id);
    const position = status.queue_position;
    const queueLength = status.queue_length;
    const totalJobsQueue = queueLength;

    return {
      ...task,
      position,
      queueLength,
      totalJobsQueue,
    };
  } catch (e) {
    logWarn({
      context: `*** generatePostImageWithPrompts - getTaskStatusError taskId: ${task.task_id}`,
      message: `failed to get task status for task id: ${task.task_id}, error: ${e}`,
    });
  }
  return {
    ...task,
    position: -1,
    queueLength: 0,
    totalJobsQueue: 0,
  };
}

function generatePrompt({
  descriptionOfImage,
  botDetails,
  isAvatarPhoto,
  contains_character = true,
  promptType,
  emphasize_text,
  is_dm_message = false,
}) {
  let prompt = "";

  if (emphasize_text && emphasize_text.length > 0) {
    prompt = `(${emphasize_text}:1.3),`;
  }

  let source = botDetails?.source ?? "";

  if (source === "original") {
    source = "";
  }

  if (
    botDetails?.art_style === "realistic_v3" ||
    botDetails?.art_style === "realistic_v2" ||
    botDetails?.art_style === "realistic"
  ) {
    // prompt += "realistic";
  }

  if (isAvatarPhoto) {
    prompt += `${descriptionOfImage}, (solo)`;

    return prompt;
  }

  if (contains_character) {
    // if prompt generation has already faceId, we can just rely on it rather than pumping the prompt

    if (promptType === "faceId") {
      prompt += `,(${descriptionOfImage}),${botDetails?.display_name}`;
    } else {
      prompt += `,(${descriptionOfImage}),${source},${
        botDetails?.display_name
      }`;
    }

    if (!is_dm_message) {
      prompt += `, ${botDetails?.description ?? null}`;
    }

    // prompt += ",solo";
  } else {
    prompt += ` ${descriptionOfImage}`;
  }

  prompt += ",high quality, award winning, highres, 8k";

  return prompt;
}

app.post("/removeTask", async (req, res) => {
  // Extract the full task name from the request body
  const taskName = req?.body?.task_name;

  if (!taskName) {
    return res.status(400).send("Task name is required");
  }

  // TODO: Do we really need this? Removing task from the queue isn't really worth the trouble.
  return res.sendStatus(200);
});

// XXX: 'modern' clients don't use this endpoint
app.post(
  `/generateAvatar`,
  avatarGenerationLimiter,
  authUser,
  async (req, res) => {
    if (!req.body.art_style || !req.body.description) {
      return res.sendStatus(400);
    }

    try {
      const artStyle = req.body.art_style;
      const description = req.body.description;
      const count = req.body.count ?? 2;

      // XXX: 'modern' clients don't use this endpoint
      const task = await generateComfyRequest({
        descriptionOfImage: description,
        is_avatar_photo: true,
        priority: "high",
        width: 512,
        height: 512,
        artStyle,
        batch_size: count,
        generationType: "avatar",
      });

      return res.json({ ...task });
    } catch (error) {
      // Handle errors
      return res.status(500).json({ error: error.message });
    }
  },
);

app.post(
  `/generateAvatarV2`,
  avatarGenerationLimiter,
  authUser,
  async (req, res) => {
    if (!req.body.art_style || !req.body.description) {
      return res.sendStatus(400);
    }

    try {
      const artStyle = req.body.art_style;
      const description = req.body.description;
      const count = req.body.count ?? 2;

      const detectNsfl = await detectCharacterWithNsflSentence({
        sentence: description,
        type: "avatar",
      });

      if (detectNsfl.nsfl) {
        return res.json({ ...detectNsfl });
      }

      const task = await generateComfyRequest({
        descriptionOfImage: description,
        is_avatar_photo: true,
        priority: "high",
        width: 512,
        height: 512,
        artStyle,
        batch_size: count,
        generationType: "avatar",
      });

      return res.json({ ...task });
    } catch (error) {
      logError({ context: "*** generateAvatarV2", error });
      return res.status(500).json({ error: error.message });
    }
  },
);

app.get("/testGenerateAvatar", async (req, res) => {
  // Update avatar_generation
  await supabase
    .schema("internal")
    .from("avatar_generations")
    .update({ is_clone: true, flagged_nsfw: ["boo bop"] })
    .eq("id", 45656)
    .select();

  res.sendStatus(200);
});

async function generateAvatarV3({
  userId,
  description,
  artStyle,
  oneSentence,
  count = 2,
  profile_id,
  parent_regeneration_id,
  clone_id,
  is_unit_testing = false,
}) {
  description = removeAdultWords(description);

  const detectNsfl = await detectCharacterWithNsflSentence({
    sentence: description,
    type: "avatar",
    isClone: !!clone_id,
  });

  if (detectNsfl.nsfl) {
    return { ...detectNsfl };
  } else if (detectNsfl.adult || detectNsfl.sexually_suggestive_themes) {
    // VU: This works but maybe we want to just use regex? Problem is multi language support
    const chatCompletion = await callAndLogOpenAI(
      "OpenAI: createWithSentence",
      {
        messages: [
          {
            role: "system",
            temperature: 0.1,
            content: `0. You are a helpful moderator
1. Keep the text identical to the orignal as possible. 
2. If description does not mention clothing, add clothing.":
${description}`,
          },
        ],
        model: "gpt-4o-mini",
      },
      {
        timeout: 8 * 1000,
      },
    );

    const result = chatCompletion.choices[0].message.content;

    if (!result.includes("assist")) {
      description = chatCompletion.choices[0].message.content;
    }
  }

  if (is_unit_testing) {
    return { description };
  }

  const task = await generateComfyRequest({
    descriptionOfImage: description,
    is_avatar_photo: true,
    priority: "high",
    width: 512,
    height: 512,
    artStyle,
    batch_size: count,
    generationType: "avatar",
    clone_id,
    creator_id: profile_id,
  });

  let avatar_generation_data = {
    user_id: userId,
    user_prompt: oneSentence,
    raw_generation_prompt: description,
    model: artStyle,
    art_style: artStyle,
    task_id: task.task_id,
  };

  if (profile_id) {
    avatar_generation_data.profile_id = profile_id;
  }

  if (parent_regeneration_id) {
    avatar_generation_data.parent_regeneration_id = parent_regeneration_id;
  }

  const { data: avatarGenerationData, error: avatarGenerationInsertionError } =
    await supabase
      .schema("internal")
      .from("avatar_generations")
      .insert(avatar_generation_data)
      .select("id")
      .single();

  if (avatarGenerationInsertionError) {
    logError({
      context: "avatar generation insertion error",
      error: avatarGenerationInsertionError,
    });
  }

  return { ...task, avatar_generation_id: avatarGenerationData?.id };
}

app.post(
  `/generateAvatarV3`,
  avatarGenerationLimiter,
  authUser,
  async (req, res) => {
    const {
      art_style: artStyle,
      description,
      one_sentence: oneSentence,
      count,
      profile_id,
      parent_regeneration_id,
      clone_id,
    } = req.body;

    if (!profile_id || !artStyle || !description) {
      return res.sendStatus(400);
    }

    const user_id = req.user?.id;
    // Validate profile ownership
    const isProfileValid = await checkProfileValid(user_id, profile_id);
    if (!isProfileValid) {
      return res.status(403).send({ error: "Forbidden" });
    }

    try {
      if (clone_id) {
        // Validate clone ownership
        const isCloneValid = await checkCloneValid(user_id, clone_id);
        if (!isCloneValid) {
          return res.status(403).send({ error: "Forbidden" });
        }
      }

      const avatarKey = `${profile_id}_avatar`;
      const avatarCount = await redisClient.incr(avatarKey);
      if (avatarCount === 1) {
        // if works, we can move this lua to do in one shot
        await redisClient.expire(avatarKey, 60 * 60); // 1 hour
      }

      if (avatarCount > AVATAR_MAX_COUNT) {
        return res.status(400).json({
          error: {
            type: "generation_limit",
            message: "Hourly Avatar Generation Limit reached!",
          },
        });
      }

      const result = await generateAvatarV3({
        userId: user_id,
        description,
        artStyle,
        oneSentence,
        count: Math.min(count, 4),
        profile_id,
        parent_regeneration_id,
        clone_id,
      });

      res.json(result);
    } catch (error) {
      if (error.message === "Bad Request") {
        res.sendStatus(400);
      } else {
        logError({ context: "*** generateAvatarV3", error });
        res.status(500).json({ error: error.message });
      }
    }
  },
);

const getRabbitMQTaskPosition = decorateWithActiveSpanAsync(
  "getRabbitMQTaskPosition",
  _getRabbitMQTaskPosition,
);
async function _getRabbitMQTaskPosition({ task_id, task_name }) {
  let id = task_id ?? task_name;
  try {
    const status = await getTaskStatus(id);
    const position = status.queue_position;
    const queueLength = status.queue_length;
    const totalJobsQueue = queueLength;
    return {
      position,
      queueLength,
      totalJobsQueue,
    };
  } catch (e) {
    // The error is likely a gRPC error, task not found. This should not fail the request.

    logError({
      context: `*** getRabbitMQTaskPosition - getTaskStatusError taskId: ${task_id} task_name: ${task_name}`,
      error: e,
    });

    return {
      position: -1,
      queueLength: 0,
      totalJobsQueue: 0,
    };
  }
}

// Deprecated
app.get("/getGenerationStatus", authUser, async (req, res) => {
  if (!req.query.task_id && !req.query.task_name) {
    return res.sendStatus(400);
  }

  try {
    const { data, error: fetchTaskError } = await supabase
      .from("tasks")
      .select("*")
      .eq("id", req.query.task_id)
      .single();

    // Check for errors in database response
    if (fetchTaskError) {
      if (fetchTaskError.code === "PGRST116") {
        // if task doesn't exist, consider it failed
        logWarn({
          context: `getGenerationStatus error – no task with id: ${req.query.task_id}`,
          message: "task doesn't exist",
        });

        return res.json({
          id: req.query.task_id,
          status: "failed",
        });
      }
      const error = wrappedSupabaseError(fetchTaskError);
      logError({
        context: `getGenerationStatus error – task with id: ${req.query.task_id}`,
        error,
      });
    }

    if (!data) {
      return res.sendStatus(404);
    }

    if (data.status == "completed" || data.status == "failed") {
      return res.json({
        ...data,
        position: -1,
        queueLength: 0,
        totalJobsQueue: 0,
      });
    }

    let position = -1;
    let queueLength = 0;
    let totalJobsQueue = 0;

    const millis = Date.now() - new Date(data.created_at);

    if (millis / 1000 > 60) {
      // If the job was not completed in a minute, chances are, the completion notification was
      // somehow lost. Let's try to pull the job status.
      try {
        let status = await getTaskStatus(req.query.task_id);
        let s = JSON.stringify(status);
        // position = status.queue_position;
        // queueLength = status.queue_length;
        // totalJobsQueue = queueLength;
        logWarn({
          executionId: req.executionId,
          context: "status for delayed task",
          message: `task id: ${req.query.task_id}, status: ${s}`,
          task_id: req.query.task_id,
          status,
        });

        if (
          status.status == "TASK_STATUS_DONE" ||
          status.status == "TASK_STATUS_FAILED"
        ) {
          // The task is done, mark it as such in the database.
          await onTaskDone(req.query.task_id, status.status, status.image_id);

          logWarn({
            executionId: req.executionId,
            context: "status for delayed task",
            message: "updating delayed task",
            task_id: req.query.task_id,
            status,
          });

          return res.json({
            ...data,
            position: -1,
            queueLength: 0,
            totalJobsQueue: 0,
          });
        }
      } catch (error) {
        logError({
          executionId: req.executionId,
          context: `task status – getGenerationStatus taskId: ${req.query.task_id}`,
          error,
          task_id: req.query.task_id,
        });
      }
    }

    if (millis / 1000 > 3600) {
      if (data.status != "queued") {
        return res.json({
          ...data,
          position,
          queueLength,
          totalJobsQueue,
        });
      }

      // The task is too old, but still in progress. Time to mark it as failed.

      logError({
        context: "task status – task is too old",
        executionId: req.executionId,
        message: `task is too old, updating status to failed: ${req.query.task_id}`,
      });

      await supabase
        .from("tasks")
        .update({
          status: "failed",
        })
        .eq("id", req.query.task_id);
      data.status = "failed";
      return res.json({
        ...data,
        position,
        queueLength,
        totalJobsQueue,
      });
    }

    try {
      if (process.env.LOCAL) {
        position = 0;
        queueLength = 0;
        totalJobsQueue = 0;
      } else {
        let status = await getTaskStatus(req.query.task_id);
        position = status.queue_position;
        queueLength = status.queue_length;
        totalJobsQueue = queueLength;
      }
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "task status – error getTaskStatus",
        error,
      });
    }

    return res.json({
      ...data,
      position,
      queueLength,
      totalJobsQueue,
    });
  } catch (error) {
    logError({
      context: "getGenerationStatus error",
      executionId: req.executionId,
      error: error,
    });

    // Handle errors
    return res.status(500).json({ error: error.message });
  }
});

app.get("/getGenerationStatusV3", authUser, async (req, res) => {
  const { task_id } = req.query;
  if (!task_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  // logInfo({
  //   context: "getGenerationStatusV3 - checking",
  //   user_id,
  //   task_id,
  // });

  addSpanAttribute("task_id", task_id);

  try {
    const { data, error } = await supabase
      .from("tasks")
      .select("*")
      .eq("id", task_id)
      .single();

    // Check for errors in database response
    if (error) {
      if (error.code === "PGRST116") {
        // if task doesn't exist, consider it failed
        logWarn({
          context: `getGenerationStatus error – no task with id: ${task_id}`,
          message: "task doesn't exist",
        });

        return res.json({
          id: task_id,
          status: "failed",
        });
      }
      logError({
        context: `getGenerationStatus error – task with id: ${task_id}`,
        error,
      });
      throw wrappedSupabaseError(error);
    }

    if (!data) {
      return res.status(404).json({ error: "task not found" });
    }

    const { post_id, profile_id, creator_id, generationType, imageMessage_id } =
      data.payload;
    if (generationType === "message" && imageMessage_id) {
      // validate if auth user requests the image generation in this message
      const { data: message } = await supabase
        .from("messages")
        .select("conversation_id")
        .eq("id", imageMessage_id)
        .single();

      const conv_id = message?.conversation_id;
      if (conv_id) {
        const cacheKey = `${user_id}_message_${task_id}`;
        const cachedData = await redisClient.get(cacheKey);
        let isValid = false;

        if (cachedData != null) {
          isValid = cachedData === "true" || cachedData === "TRUE";
        } else {
          const isOwnConversation = await validateOwnConversation(
            user_id,
            conv_id,
          );
          isValid = isOwnConversation;
          await redisClient.set(cacheKey, `${isValid}`, "EX", 30 * 60);
        }

        if (!isValid) {
          return res.status(403).json({ error: "forbidden" });
        }
      }
    }

    if (generationType === "avatar" && creator_id) {
      // validate if auth user requests the image generation for avatar
      let isProfileValid = false;
      const cacheKey = `${user_id}_avatar_${task_id}`;
      const cachedData = await redisClient.get(cacheKey);

      if (cachedData != null) {
        isProfileValid = cachedData === "true" || cachedData === "TRUE";
      } else {
        isProfileValid = await checkProfileValid(user_id, creator_id);
        await redisClient.set(cacheKey, `${isProfileValid}`, "EX", 30 * 60);
      }

      if (!isProfileValid) {
        return res.status(403).json({ error: "forbidden" });
      }
    }

    if (generationType === "post" && post_id) {
      // validate if auth user requests the image generation for post
      let post_profile_id = profile_id;
      if (!profile_id) {
        const { data: post } = await supabase
          .from("posts")
          .select("profile_id")
          .eq("id", post_id)
          .single();
        post_profile_id = post?.profile_id;
      }
      if (post_profile_id) {
        let isProfileValid = false;
        const cacheKey = `${user_id}_post_${task_id}`;
        const cachedData = await redisClient.get(cacheKey);

        if (cachedData != null) {
          isProfileValid = cachedData === "true" || cachedData === "TRUE";
        } else {
          isProfileValid = await checkProfileValid(user_id, post_profile_id);
          await redisClient.set(cacheKey, `${isProfileValid}`, "EX", 30 * 60);
        }

        if (!isProfileValid) {
          return res.status(403).json({ error: "forbidden" });
        }
      }
    }

    if (generationType === "dreams" && profile_id) {
      // validate if auth user requests the image generation for dreams
      let isProfileValid = false;
      const cacheKey = `${user_id}_dreams_${task_id}`;
      const cachedData = await redisClient.get(cacheKey);

      if (cachedData != null) {
        isProfileValid = cachedData === "true" || cachedData === "TRUE";
      } else {
        isProfileValid = await checkProfileValid(user_id, profile_id);
        await redisClient.set(cacheKey, `${isProfileValid}`, "EX", 30 * 60);
      }

      if (!isProfileValid) {
        return res.status(403).json({ error: "forbidden" });
      }
    }

    // If this is an image generation, we shim it with resized images
    // This is temporary

    if (data.status == "completed" || data.status == "failed") {
      let ret = {
        ...data,
        position: -1,
        queueLength: 0,
        totalJobsQueue: 0,
      };

      let img_uris = null;

      if (data?.result?.data?.img_uris) {
        console.log("got image urls", data.result.data.img_uris);
        img_uris = data?.result?.data?.img_uris.map((url) => {
          return {
            original: url,
            optimized: url,
          };
        });
      }

      if (img_uris) {
        ret.img_uris = img_uris;
      }

      res.json(ret);

      return;
    }

    let position = -1;
    let queueLength = 0;
    let totalJobsQueue = 0;

    const millis = Date.now() - new Date(data.created_at);

    if (millis / 1000 > 60) {
      // If the job was not completed in a minute, chances are, the completion notification was
      // somehow lost. Let's try to pull the job status.
      try {
        let status = await getTaskStatus(task_id);
        let s = JSON.stringify(status);
        // position = status.queue_position;
        // queueLength = status.queue_length;
        // totalJobsQueue = queueLength;
        logWarn({
          executionId: req.executionId,
          context: "status for delayed task",
          message: `task id: ${task_id}, status: ${s}`,
          task_id,
          status,
        });

        if (
          status.status == "TASK_STATUS_DONE" ||
          status.status == "TASK_STATUS_FAILED"
        ) {
          // The task is done, mark it as such in the database.
          await onTaskDone(task_id, status.status, status.image_id);

          logWarn({
            executionId: req.executionId,
            context: "status for delayed task",
            message: "updating delayed task",
            task_id,
            status,
          });

          return res.json({
            ...data,
            position: -1,
            queueLength: 0,
            totalJobsQueue: 0,
          });
        }
      } catch (error) {
        logError({
          executionId: req.executionId,
          context: `task status – getGenerationStatus taskId: ${task_id}`,
          error,
        });
      }
    }

    if (millis / 1000 > 3600) {
      if (data.status != "queued") {
        return res.json({
          ...data,
          position,
          queueLength,
          totalJobsQueue,
        });
      }

      // The task is too old, but still in progress. Time to mark it as failed.

      logError({
        context: "task status – task is too old",
        executionId: req.executionId,
        message: `task is too old, updating status to failed: ${task_id}`,
      });

      await supabase
        .from("tasks")
        .update({
          status: "failed",
        })
        .eq("id", task_id);
      data.status = "failed";

      // Delete user_usage if user_usage_id exists in payload
      const user_usage_id = data?.payload?.user_usage_id;
      if (user_usage_id) {
        try {
          const { error: deleteUserUsageError } = await supabase
            .from("user_usage")
            .delete()
            .eq("id", user_usage_id);
          if (deleteUserUsageError) {
            logError({
              context: `getGenerationStatusV3: failed to delete user_usage`,
              error: deleteUserUsageError,
              user_usage_id,
            });
          }
        } catch (err) {
          logError({
            context: `getGenerationStatusV3: exception deleting user_usage`,
            error: err,
            user_usage_id,
          });
        }
      }

      return res.json({
        ...data,
        position,
        queueLength,
        totalJobsQueue,
      });
    }

    try {
      if (process.env.LOCAL) {
        position = 0;
        queueLength = 0;
        totalJobsQueue = 0;
      } else {
        let status = await getTaskStatus(task_id);
        position = status.queue_position;
        queueLength = status.queue_length;
        totalJobsQueue = queueLength;
      }
    } catch (error) {
      logWarn({
        executionId: req.executionId,
        context: `task status`,
        message: `getTaskStatus failed, ${error}`,
      });
    }

    return res.json({
      ...data,
      position,
      queueLength,
      totalJobsQueue,
    });
  } catch (error) {
    logError({
      context: "getGenerationStatus error",
      executionId: req.executionId,
      error: error,
    });

    // Handle errors
    return res.status(500).json({ error: error.message });
  }
});

async function turnOffInsightFaceProfileWithTaskId(taskId) {
  const { data } = await tracer.withActiveSpan(
    "fetch tasks record",
    async (span) => {
      const result = await supabase
        .from("tasks")
        .select("payload")
        .eq("id", taskId)
        .single();

      if (result.error) {
        const error = wrappedSupabaseError(result.error);
        logError({
          context: "fetch tasks record error",
          error: error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        metrics.fetchTaskPayloadFailure.add(1);
      } else if (result.data?.payload) {
        metrics.fetchTaskPayloadSuccess.add(1);
      }

      return result;
    },
  );

  if (!data.payload?.profile_id) {
    logWarn({
      context: `turnOffInsightFaceProfileWithTaskId: taskId: ${taskId}`,
      message: `no profile_id found for task ${taskId}`,
    });
    return;
  }

  const profileId = data.payload.profile_id;
  const { error: profileUpdateError } = await supabase
    .from("profiles")
    .update({
      avatar_photo_contains_face: false,
    })
    .eq("id", profileId);

  if (profileUpdateError) {
    const error = wrappedSupabaseError(profileUpdateError);
    logError({
      context: "turnOffInsightFaceProfileWithTaskId error",
      error: error,
    });
  }
}

app.post("/taskDone", async (req, res) => {
  if (!req || !req.body || !req.body.TaskId || !req.body.Status) {
    metrics.brokerCompletionHandlerFailure.add(1);
    const error = new Error("Invalid /taskDone request body");
    logError({ context: `taskDone error taskId: ${req.body.TaskId}`, error });
    return res.sendStatus(400);
  }

  const taskId = req.body.TaskId;
  const status = req.body.Status;
  const imageIds = req.body.ImageIds;
  const imageBlurhash = req.body.BlurHash;

  addSpanAttribute("task_id", taskId);
  addSpanAttribute("status", status);

  if (req.body.StatusMessage) {
    // Log status message and status details to make sure we get getting them,
    // all the way from Comfy.

    // Some reason, the avatar in the profile no longer contains a face, or never did in the first place
    // We turn off avatar_photo_contains_face so it no longer sends "face" workflows
    if (req.body.StatusMessage.includes("No face detected")) {
      await turnOffInsightFaceProfileWithTaskId(taskId);
    }
    if (req.body.StatusDetails) {
      logInfo({
        context: "taskDone",
        executionId: 0,
        message: `got status message ${req.body.StatusMessage}, status details: ${req.body.StatusDetails}`,
      });
    } else {
      logInfo({
        context: "taskDone",
        executionId: 0,
        message: `got status message ${req.body.StatusMessage}`,
      });
    }
  }

  try {
    await onTaskDone(taskId, status, imageIds, imageBlurhash);
    metrics.brokerCompletionHandlerSuccess.add(1);
    return res.sendStatus(200);
  } catch (error) {
    metrics.brokerCompletionHandlerFailure.add(1);
    logError({ context: `taskDone error taskId: ${taskId}`, error });
    return res.sendStatus(500);
  }
});

function getPriority(priorityString, generationType) {
  if (priorityString === "low") {
    return 0;
  } else if (priorityString === "high") {
    if (generationType === "avatar") {
      return 9;
    } else if (generationType === "message") {
      return 7;
    }
    return 5;
  } else {
    let priority = parseInt(priorityString);

    if (isNaN(priority)) {
      priority = 0;
    }

    if (generationType != "avatar" && priority >= 9) {
      // Only avatars are allowed the highest priority.
      priority = 8;
    }

    return priority;
  }
}

const onTaskDone = decorateWithActiveSpanAsync(
  "comfy.js onTaskDone",
  _onTaskDone,
);
async function _onTaskDone(taskId, status, imageIds, imageBlurhash) {
  try {
    logInfo({
      context: "onTaskDone",
      executionId: 0,
      message: `task is done, task id: ${taskId}, status: ${status}, imageIds: ${imageIds}`,
    });

    const { data, error } = await tracer.withActiveSpan(
      "fetch tasks record",
      async (span) => {
        const result = await supabase
          .from("tasks")
          .select("payload")
          .eq("id", taskId)
          .single();

        if (result.error) {
          logError({
            context: "fetch tasks record error",
            error: result.error,
          });
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: result.error?.message,
          });
          metrics.fetchTaskPayloadFailure.add(1);
        } else if (result.data?.payload) {
          metrics.fetchTaskPayloadSuccess.add(1);
        }

        return result;
      },
    );

    if (error) {
      const wrappedError = wrappedSupabaseError(error);
      logError({
        context: `onTaskDone: ${taskId}`,
        executionId: 0,
        error: wrappedError,
      });
      throw wrappedError;
    }

    if (!data || !data.payload) {
      logError({
        context: `onTaskDone: ${taskId}`,
        error: {
          message: "data or payload is null",
        },
      });
      throw new Error("data or payload is null");
    }

    if (status != "TASK_STATUS_DONE" || imageIds == null) {
      const message = `image generation failed: ${status}`;
      if (isPillowGenerationType(data.payload.generationType)) {
        await onPillowImageGenerationTaskFailed({
          task_id: taskId,
          workflow_payload: data.payload,
          error_message: message,
        });
      } else if (data.payload.generationType === "vignette_scene") {
        await onVignetteImageGenerationFailed({
          taskId,
          error: {
            message: message,
          },
          payload: data.payload,
        });
      } else {
        await imageGenerationFailed({
          ...data.payload,
          error: {
            message: message,
          },
          task_id: taskId,
          executionId: 0,
        });
      }
      // return and don't throw an error here,
      // imageGenerationFailed or onPillowImageGenerationTaskFailed should've taken care of updating the db
      return;
    }

    // Convert URLs to image proxy URLs
    let imageUrls = imageIds.map(function (imageId) {
      return `https://img.butterflies.ai/w/${imageId}.webp`;
    });

    let blurhash = undefined;
    if (imageBlurhash) {
      blurhash = imageIds.map(function (imageId) {
        return imageBlurhash[imageId];
      });
    }

    if (isPillowGenerationType(data.payload.generationType)) {
      await onPillowImageGenerationTaskSucceeded({
        task_id: taskId,
        workflow_payload: data.payload,
        imageUrls,
        blurhash,
      });
    } else if (data.payload.generationType === "vignette_scene") {
      await onVignetteImageGenerationCompleted({
        taskId,
        payload: data.payload,
        imageUrls,
        blurhashes: blurhash,
      });
    } else {
      try {
        await updateImageInfo({
          ...data.payload,
          imageUrls,
          blurhash,
          task_id: taskId,
          executionId: 0,
        });
        metrics.updateImageInfoSuccess.add(1);
        if (data.payload.generationType === "post_v2") {
          await onPostRefinedTaskSucceeded(data.payload);
        }
      } catch (error) {
        metrics.updateImageInfoFailure.add(1);

        logError({
          context: `onTaskDone: ${taskId}, error in updateImageInfo`,
          error: error,
        });
        throw error;
      }
    }
  } catch (error) {
    logError({
      context: `onTaskDone: ${taskId}, caught an exception`,
      executionId: 0,
      error: error,
    });
    // We weren't able to get the image, upload it to storage and update the main db, so throw
    throw error;
  }
}

module.exports = {
  app,
  generateComfyRequest,
  generateComfyRequestForMultipleCharacters,
  generatePostImageWithPrompts,
  getRabbitMQTaskPosition,
  generateAvatarV3,
  onTaskDone,
};
