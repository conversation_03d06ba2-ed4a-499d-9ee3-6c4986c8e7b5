const express = require("express");
const usersRouter = express.Router();
const { supabase } = require("./supabaseClient");
const {
  logError,
  logWarn,
  getSpecificKeys,
  checkProfileValid,
  checkAdminValid,
  wrappedSupabaseError,
  PostgresErrorCode,
  SupabaseAuthErrorCode,
  getMD5,
  logInfo,
} = require("./utils");
const { getFollowings } = require("./root");
const {
  getBotBot,
  getByMultiplePostId,
  getProfilePostFollowing,
  getReads,
  getServedIds,
  putServedIds,
  getActiveHours,
} = require("./btClient");
const { loggingInfo } = require("./logging");
const { authUser } = require("./middleware");
const { timeDifferenceInMinutes } = require("./postsHelpers");
const { randomWordList } = require("./data/randomWords");
const { redeemInviteCode, insertWaitlist } = require("./invites");
const {
  IS_WAITLIST_ENABLED,
  SUPPORTED_CATEGORIES,
  LAUNCH_PROPOSED_POST_MODE_V1_EXPERIMENT,
} = require("./constants");
const { rateLimit } = require("express-rate-limit");
const redisClient = require("./redisClient");
const { mixpanelTrack, mixpanelTrackReq } = require("./mixpanel");
const { default: axios } = require("axios");
const { baseUrl } = require("./api");
const { sendInviteEmail, sendInviteSMS } = require("./loops");

const {
  tracer,
  decorateWithActiveSpanAsync,
  SpanStatusCode,
} = require("./instrumentation/tracer");
const opentelemetry = require("@opentelemetry/api");

require("dotenv").config();

const meter = opentelemetry.metrics.getMeter("users");

const testGauge = meter.createObservableGauge("butterfliesapi_test_gauge", {
  description: "just a test gauge",
});
let testGaugeValue = 0;
testGauge.addCallback((result) => {
  testGaugeValue += 1;
  result.observe(testGaugeValue);
});

const signupPostLimiter = rateLimit({
  windowMs: 10 * 1000, // 10 seconds
  max: 1, // Limit each combination of bot_id and post_id
  keyGenerator: async (req, _res, _next) => {
    return req.user.id;
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

const safeProfiles = [
  1455, 1451, 1446, 1428, 1386, 1384, 1368, 1362, 1311, 1309, 1174, 1118, 1091,
  1088, 1086, 1080, 1070, 995, 970, 906, 852, 834, 200, 854, 832, 830, 818, 806,
  781, 719, 699, 1082, 1084, 1145,
];

usersRouter.get("/testCache", async (req, res) => {
  const cacheKey = "feed";

  try {
    // Try to get data from cache
    const cachedData = await redisClient.get(cacheKey);

    if (cachedData != null) {
      res.send(`Cached data: ${cachedData}`);
    } else {
      // Fetch data and then set it in cache
      const newData = "..."; // Replace with your data fetching logic
      await redisClient.set(cacheKey, newData, "EX", 60);

      // No need to close the Redis client here; manage it at the application level
      res.send(`New data: ${newData}`);
    }
  } catch (err) {
    // Handle any errors here
    logError({
      context: "/testCache Error",
      error: err,
    });
    res.status(500).send("An error occurred");
  }

  // Note that you don't need to close the Redis client here since it's closed within the route handler.
});

usersRouter.get("/fetchForYouTest", async (req, res) => {
  const profileId = Number(req.query.profile_id);
  if (profileId == null || profileId == undefined) {
    return res.sendStatus(400);
  }
  const result = await getProfilePostFollowing(
    profileId,
    "for_you",
    redisClient,
  );
  return res.json(result);
});

usersRouter.get("/ping", async (req, res) => {
  return res.send("Users ping");
});

const INITIAL_FEED_LOAD_COUNT = 6;
const INITIAL_EXPLORE_LOAD_COUNT = 20;
const PAGE_LOAD_COUNT = 3;
const EXPLORE_LOAD_COUNT = 6;

// TODO(Chris): remove this later. This is just to show how to use it.
usersRouter.get("/active_hours", async (req, res) => {
  const profileId = Number(req.query.profile_id);
  if (profileId == null || profileId == undefined) {
    return res.sendStatus(400);
  }
  let data = await getActiveHours(profileId, redisClient);
  return res.json(data);
});

usersRouter.get("/fetchReelsFeed", async (req, res) => {
  let feedCount = Number(req.query.feed_count);
  const profileId = Number(req.query.profile_id);
  if (
    feedCount == null ||
    feedCount == undefined ||
    profileId == null ||
    profileId == undefined
  ) {
    return res.sendStatus(400);
  }

  if (isNaN(feedCount) || isNaN(profileId) || profileId <= 0) {
    return res.sendStatus(400);
  }

  const fetchingCount = feedCount ? PAGE_LOAD_COUNT : INITIAL_FEED_LOAD_COUNT;
  const lastPostIdsParam = req.query.last_post_ids;

  let lastPostIdsArray;
  if (typeof lastPostIdsParam === "undefined" || lastPostIdsParam === "") {
    lastPostIdsArray = [];
  } else {
    lastPostIdsArray = lastPostIdsParam
      .split(",")
      .map((id) => Number(id))
      .filter((id) => !isNaN(id));
  }

  // We use BigTable as the default for the Feed
  let posts = await fetchPostFromBT(
    profileId,
    feedCount,
    fetchingCount,
    lastPostIdsArray,
    "reels",
  );
  if (posts.length > 0) {
    return res.json(posts);
  } else {
    return res.sendStatus(204);
  }
  // End of Bigtable Lookup
  /*
  loggingInfo("feed", {
    profile_id: profileId,
    feed_count: feedCount,
    fetching_count: fetchingCount,
    feed_type: "reels",
    source: "db",
  });

  const { data, error } = await supabase.rpc(
    "func_get_reels_feed",
    {
      p_profile_id: profileId,
      feed_count: feedCount,
      fetch_count: fetchingCount,
    },
    { get: true },
  );

  if (error) {
    logError({
      executionId: req.executionId,
      context: "fetchReelsFeed: fetchFeedError",
      error: wrappedSupabaseError(error),
    });
    return res.sendStatus(500);
  }

  if (!data) {
    return res.sendStatus(204);
  }

  let mergedPosts = data;
  // apple user
  if (profileId === 711 || profileId === 201) {
    // filter out unsafe profiles
    mergedPosts = mergedPosts.filter((e) =>
      safeProfiles.includes(e.profile_id),
    );
  }

  //filtering by hide list
  const { data: filteredData, error: filterByHideListError } =
    await filterByHideList({
      profileId,
      mergedPosts,
    });

  if (filterByHideListError || !filteredData) {
    logError({
      executionId: req.executionId,
      context: "fetchReelsFeed: filterByHideListError",
      error: filterByHideListError,
    });
    return res.sendStatus(500);
  }

  return res.json(filteredData);
  */
});

usersRouter.get("/fetchFollowingFeed", async (req, res) => {
  let feedCount = Number(req.query.feed_count);
  const profileId = Number(req.query.profile_id);
  if (
    feedCount == null ||
    feedCount == undefined ||
    profileId == null ||
    profileId == undefined
  ) {
    return res.sendStatus(400);
  }

  if (isNaN(feedCount) || isNaN(profileId) || profileId <= 0) {
    return res.sendStatus(400);
  }

  const fetchingCount = feedCount ? PAGE_LOAD_COUNT : INITIAL_FEED_LOAD_COUNT;
  const lastPostIdsParam = req.query.last_post_ids;

  let lastPostIdsArray;
  if (typeof lastPostIdsParam === "undefined" || lastPostIdsParam === "") {
    lastPostIdsArray = [];
  } else {
    lastPostIdsArray = lastPostIdsParam
      .split(",")
      .map((id) => Number(id))
      .filter((id) => !isNaN(id));
  }

  // We use BigTable as the default for the Feed
  let posts = await fetchPostFromBT(
    profileId,
    feedCount,
    fetchingCount,
    lastPostIdsArray,
    "following",
  );
  if (posts.length > 0) {
    try {
      // Fetch the profiles that the current user is following
      const { data: followingData, error: followingError } = await supabase
        .from("followers")
        .select("following_id")
        .eq("follower_id", profileId);

      if (followingError) {
        logError({
          executionId: req.executionId,
          context: "fetchFollowingFeed: error fetching following list",
          error: wrappedSupabaseError(followingError),
        });
        // If we can't fetch following list, return all posts to avoid breaking the feed
        return res.json(posts);
      }

      // Create a Set of profile IDs that the user is following for efficient lookup
      const followingProfileIds = new Set(
        followingData?.map((follow) => follow.following_id) || [],
      );

      // Filter posts to only include those from profiles the user is following
      const filteredPosts = posts.filter((post) =>
        followingProfileIds.has(post.profile_id),
      );

      return res.json(filteredPosts);
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "fetchFollowingFeed: error filtering posts by following",
        error,
      });
      // If filtering fails, return all posts to avoid breaking the feed
      return res.json(posts);
    }
  }
  // End of Bigtable Lookup
  loggingInfo("feed", {
    profile_id: profileId,
    feed_count: feedCount,
    fetching_count: fetchingCount,
    feed_type: "following",
    source: "db",
  });

  const { data, error } = await supabase.rpc(
    "func_get_following_feed",
    {
      p_profile_id: profileId,
      feed_count: feedCount,
      fetch_count: fetchingCount,
    },
    { get: true },
  );

  if (error) {
    logError({
      executionId: req.executionId,
      context: "fetchFollowingFeed: fetchFeedError",
      error: wrappedSupabaseError(error),
    });
    return res.sendStatus(500);
  }

  if (!data) {
    return res.sendStatus(204);
  }

  let mergedPosts = data;
  // apple user
  if (profileId === 711 || profileId === 201) {
    // filter out unsafe profiles
    mergedPosts = mergedPosts.filter((e) =>
      safeProfiles.includes(e.profile_id),
    );
  }

  //filtering by hide list
  const { data: filteredData, error: filterByHideListError } =
    await filterByHideList({
      profileId,
      mergedPosts,
    });

  if (filterByHideListError || !filteredData) {
    logError({
      executionId: req.executionId,
      context: "fetchFollowingFeed: filterByHideListError",
      error: filterByHideListError,
    });
    return res.sendStatus(500);
  }

  return res.json(filteredData);
});

const fetchBotPostsFromCache = decorateWithActiveSpanAsync(
  "fetch botPosts_from_cache",
  _fetchBotPostsFromCache,
);
async function _fetchBotPostsFromCache() {
  try {
    // Try to get data from cache
    const cachedData = await redisClient.get(botPostsCacheKey());

    if (cachedData != null) {
      return { data: JSON.parse(cachedData) };
    } else {
      return { data: null };
    }
  } catch (error) {
    logWarn({ context: "fetchBotPostsFromCache failed", error });
    return { error };
  }
}

function followingCacheKey({ profileId }) {
  let cacheKey = `following_feed_${profileId}`;

  if (process.env.LOCAL) {
    cacheKey = `following_feed_${profileId}_local`;
  }

  return cacheKey;
}

function botPostsCacheKey() {
  let cacheKey = "feed";

  if (process.env.LOCAL) {
    cacheKey = "feed_local";
  }

  return cacheKey;
}

const fetchFollowingFromCache = decorateWithActiveSpanAsync(
  "fetch Following_from_cache",
  _fetchFollowingFromCache,
);
async function _fetchFollowingFromCache({ profileId }) {
  try {
    // Try to get data from cache
    const cachedData = await redisClient.get(followingCacheKey({ profileId }));

    if (cachedData != null) {
      return JSON.parse(cachedData);
    } else {
      return { data: null };
    }
  } catch (error) {
    return { error };
  }
}

usersRouter.post("/doesEmailExist", async (req, res) => {
  if (!req.body.email) {
    return res.sendStatus(400);
  }

  const { data, error } = await supabase.rpc("get_user_id_by_email", {
    email: req.body.email,
  });

  if (error) {
    throw wrappedSupabaseError(
      error,
      "failed to call db function 'get_user_id_by_email'",
    );
  }

  res.json({ exists: data?.length > 0 });
});

usersRouter.get("/warmFeedCache", async (req, res) => {
  try {
    await fetchBotPosts();
  } catch (error) {
    logError({
      context: "**** warmFeedCache error",
      error,
    });
    return res.json({ error: error.message });
  }
  res.sendStatus(200);
});

usersRouter.get("/warmCloudTask", async (req, res) => {
  res.sendStatus(200);
});

// This is going to be cached
const fetchBotPosts = decorateWithActiveSpanAsync(
  "fetch botPosts",
  _fetchBotPosts,
);
async function _fetchBotPosts() {
  let { data: botPostsFromCache } = await fetchBotPostsFromCache();

  if (botPostsFromCache) {
    return { data: botPostsFromCache };
  }

  // FIXME: this tries to fetch all public bots but since there is no pagination it will just fetch the first 1000 (or 2000 since we bumped up the limit)
  const { data: bots, error: botsError } = await supabase
    .from("bots")
    .select("profiles:profile_id!inner(id)")
    .eq("profiles.visibility", "public")
    .limit(100); // FIXME: saniul added this - this is a bandaid to prevent the exploding the request to supabase in fetchPostsForProfiles

  if (botsError) {
    const error = wrappedSupabaseError(
      botsError,
      "failed to fetch public bots",
    );
    throw error;
  }

  let profilesToFetch = [];

  bots?.forEach((e) => {
    if (e.profiles?.id) {
      profilesToFetch.push(e.profiles.id);
    }
  });

  const { data: botPostsFromOrigin, error: fetchPostsError } =
    await fetchPostsForProfiles({
      profilesToFetch,
    });

  if (botPostsFromOrigin) {
    await redisClient.set(
      botPostsCacheKey(),
      JSON.stringify(botPostsFromOrigin),
      {
        EX: 300,
      },
    );
  }

  if (fetchPostsError) {
    throw fetchPostsError;
  }

  return { data: botPostsFromOrigin, error: fetchPostsError };
}

const fetchFollowingPosts = decorateWithActiveSpanAsync(
  "fetchFollowingPosts",
  _fetchFollowingPosts,
);
async function _fetchFollowingPosts({ profileId, feedCount }) {
  if (!profileId) {
    return { data: [] };
  }

  let cache;
  // invalidate teh cache, always get fresh cache
  if (feedCount === 0) {
    await redisClient.del(followingCacheKey({ profileId }));
  } else {
    cache = await fetchFollowingFromCache({ profileId });

    if (cache) {
      // Todo: Vu, we're cacheing a mb, need to trim feed data
      // feed should cache pre-computed values and limit to 100 items or something
      // can also
      // can also gzip it
      return cache;
    }
  }

  function calculateTagRatios(tags) {
    const tagCounts = {};
    tags.forEach((tag) => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1;
    });

    const totalTags = tags.length;
    const tagRatios = {};
    for (const tag in tagCounts) {
      tagRatios[tag] = tagCounts[tag] / totalTags;
    }

    return tagRatios;
  }

  let following;
  let followingError;
  await tracer.withActiveSpan(
    "get followers for follwingPosts",
    async (span) => {
      // XXX: we could either parallelize it, if we don't care about the order
      //      _or_ this should happen after the updateXXX call updates the table
      const response = await supabase
        .from("followers")
        .select(
          "following_id, profiles:following_id!inner(id, bots!bots_profile_id_fkey(id, franchise, tag, source, creator_id))",
        )
        .eq("follower_id", profileId)
        .or(
          "visibility.eq.public,and(visibility.eq.private,user_id.not.is.null)",
          {
            referencedTable: "profiles",
          },
        )
        .neq("profiles.visibility", "hidden")
        .neq("profiles.visibility", "archived");

      following = response?.data;
      followingError = response?.error;

      if (followingError) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: followingError?.message,
        });
        console.warn("**** followingError", followingError);
      }
    },
  );

  // Tags of bots
  const creators = following.filter((e) => !e.profiles?.bots).filter((e) => e);
  const similarCreatorsIds = following
    .filter(
      (e) =>
        e.profiles?.bots?.creator_id &&
        e.profiles?.bots?.creator_id !== parseInt(profileId),
    )
    .map((e) => e.profiles?.bots?.creator_id);

  const tags = following
    .map((e) => e.profiles?.bots?.tag?.split(",")[0])
    .filter((e) => e);

  const tagRatios = calculateTagRatios(tags);

  let profilesToFetch = [profileId];

  following?.forEach((e) => {
    profilesToFetch.push(e.following_id);
  });

  const { data, error } = await fetchPostsForProfilesAndSelf({
    profilesToFetch,
    profileId,
  });

  // unlimited cache, but we will invalidate on first fetch, this is to promote scrolling speed
  await redisClient.set(
    followingCacheKey({ profileId }),
    JSON.stringify({
      data,
      tags,
      tagRatios,
      creators,
      similarCreatorsIds,
    }),
    { EX: 60 * 60 * 12 },
  );

  return { data, error, tags, tagRatios, creators, similarCreatorsIds };
}

async function filterByHideList({ profileId, mergedPosts }) {
  if (!profileId) {
    return { data: mergedPosts, error: null };
  }
  const { data, error } = await supabase
    .from("hide_lists")
    .select("hidden_profile_id")
    .eq("profile_id", profileId);

  if (error || !data) {
    return {
      data: null,
      error: wrappedSupabaseError(error) || "No data returned from Supabase",
    };
  }

  if (data.length > 0) {
    let hideProfileIds = [];

    data?.forEach((e) => {
      if (e.hidden_profile_id) {
        hideProfileIds.push(e.hidden_profile_id);
      }
    });

    let setHideProfileIds = new Set(hideProfileIds);
    mergedPosts = mergedPosts.filter(
      (post) => !setHideProfileIds.has(post.profile_id),
    );
  }

  return { data: mergedPosts, error: null };
}

// Confusing, VU TODO: Merge this
// The point is, when we're fetching from public cache, we should not fetch our own profile
const fetchPostsForProfilesAndSelf = decorateWithActiveSpanAsync(
  "fetch PostsForProfilesAndSelf",
  _fetchPostsForProfilesAndSelf,
);
async function _fetchPostsForProfilesAndSelf({ profilesToFetch, profileId }) {
  const { data, error } = await supabase
    .from("posts")
    .select(
      `
    id, created_at, updated_at, profile_id, media_url, location, slug, description, tags, visibility, nsfw, type,
    profiles (
      bots!bots_profile_id_fkey(id, franchise, tag, source, creator_id),
      id,
      username,
      display_name,
      avatar_url,
      visibility,
      nsfw
    ),
    post_likes (
      id,
      profile_id,
      is_like,
      is_bot
    ),
    post_comments (
      id
    ),
    post_bookmarks (*)
  `,
    )
    .or(`visibility.eq.public,profile_id.eq.${profileId}`)
    .neq("media_url", null)
    .neq("nsfw", "nsfw")
    .neq("profiles.visibility", "archived")
    .order("id", { ascending: false })
    .in("profile_id", profilesToFetch)
    .limit(100);

  logWarn({
    context: "**** fetchPostsForProfilesAndSelf: data",
    message: data?.length,
    profileId,
  });

  if (error) {
    logError({
      context: "**** fetchPostsForProfilesAndSelf: error",
      error: error,
      profileId,
    });
  }

  return { data, error };
}

const fetchPostsForProfiles = decorateWithActiveSpanAsync(
  "fetch Posts_for_Profiles",
  _fetchPostsForProfiles,
);
async function _fetchPostsForProfiles({ profilesToFetch }) {
  if (!profilesToFetch || !profilesToFetch.length) {
    throw new Error("Profiles to fetch is nil or empty");
  }

  const { data, error } = await supabase
    .from("posts")
    .select(
      `
    id, created_at, updated_at, profile_id, media_url, location, slug, description, tags, visibility, nsfw, type,
    profiles (
      bots!bots_profile_id_fkey(id, franchise, tag, source, creator_id),
      id,
      username,
      display_name,
      avatar_url,
      visibility,
      nsfw
    ),
    post_likes (
      id,
      profile_id,
      is_like,
      is_bot
    ),
    post_comments (
      id
    ),
    post_bookmarks (*)
  `,
    )
    .eq("visibility", "public")
    .neq("media_url", null)
    .neq("nsfw", "nsfw")
    .neq("profiles.visibility", "archived")
    .order("id", {
      foreignTable: "post_comments",
      ascending: true,
    })
    .order("id", { ascending: false })
    .in("profile_id", profilesToFetch)
    .limit(200);

  return { data, error };
}

async function fetchPostWithoutProfileId(
  anonymousId,
  feedCount,
  fetchingCount,
  lastPostIdsArray,
) {
  const artStyles = [-1, -2, -3];
  const artStyleId = artStyles[Math.floor(Math.random() * 3)];

  let [postIndex, servedIds] = await Promise.all([
    getProfilePostFollowing(artStyleId, "for_you", redisClient),
    getServedIds(redisClient, anonymousId),
  ]);
  const postIds = postIndex?.ids ?? [];

  let postIdsUnRead = postIds
    .slice(feedCount)
    .filter(
      (item) => !servedIds.includes(item) && !lastPostIdsArray.includes(item),
    );
  const postIdSelection = postIdsUnRead.slice(0, fetchingCount);

  let [posts] = await Promise.all([
    getByMultiplePostId(postIdSelection.map(String)),
    putServedIds(redisClient, anonymousId, postIdSelection),
  ]);

  // Reorder posts based on the ranked position
  const idToPostMap = new Map(posts.map((obj) => [obj.id, obj]));
  const sortedPosts = postIdSelection
    .map((id) => idToPostMap.get(id))
    .filter((obj) => obj !== undefined);

  loggingInfo("feed", {
    pseudo_id: String(anonymousId),
    feed_count: feedCount,
    fetching_count: fetchingCount,
    feed_type: "foryou",
    row_id: artStyleId,
    served_ids: postIdSelection,
    source: "bt-anonym",
  });

  return sortedPosts;
}

const fetchPostFromBT = decorateWithActiveSpanAsync(
  "fetch post from BT",
  _fetchPostFromBT,
);
async function _fetchPostFromBT(
  profileId,
  feedCount,
  fetchingCount,
  lastPostIdsArray,
  feedType,
) {
  let [btReads, postIndex, servedIds] = await Promise.all([
    getReads(redisClient, profileId),
    getProfilePostFollowing(profileId, feedType, redisClient),
    getServedIds(redisClient, profileId),
  ]);
  let reads = new Set(btReads);
  let rowId = profileId;
  let source = "bt";
  let postIds = postIndex?.ids ?? [];

  if (postIds.length == 0 && feedType == "for_you") {
    // ForYou: when there is no predefined index, we use the common(-1,-2,-3) index.
    rowId = await getArtStyleId(profileId);
    postIndex = await getProfilePostFollowing(rowId, feedType, redisClient);
    postIds = postIndex?.ids ?? [];
    source = "bt-style";
  }
  if (postIds.length == 0 && feedType == "reels") {
    // ForYou: when there is no predefined index, we use the common -4 index.
    rowId = -4;
    postIndex = await getProfilePostFollowing(rowId, feedType, redisClient);
    postIds = postIndex?.ids ?? [];
    source = "bt-common";
  }

  lastPostIdsArray.forEach((id) => {
    servedIds.push(id);
  });
  let postIdsUnRead = postIds
    .slice(feedCount)
    .filter((item) => !reads.has(item) && !servedIds.includes(item));
  if (postIdsUnRead.length == 0) {
    // Let's not remove the served items when we are out of posts
    postIdsUnRead = postIds
      .slice(feedCount)
      .filter((item) => !reads.has(item) && !lastPostIdsArray.includes(item));
  }
  const postIdSelection = postIdsUnRead.slice(0, fetchingCount);
  const feedMap = Object.fromEntries(
    postIndex?.feedIndex
      ?.filter((item) => postIdSelection.includes(item.id))
      .map((item) => [item.id, item]) ?? [],
  );

  let [posts] = await Promise.all([
    getByMultiplePostId(postIdSelection.map(String)),
    putServedIds(redisClient, profileId, postIdSelection),
  ]);

  // Reorder posts based on the ranked position
  const idToPostMap = new Map(posts.map((obj) => [obj.id, obj]));
  const sortedPosts = postIdSelection
    .map((id) => idToPostMap.get(id)) // Get the post object for each ID
    .map((post) => {
      if (post) {
        const label = feedMap[post.id]?.label?.trim() || "You";
        post.affordance_label = label;
        post.affordance_id = feedMap[post.id]?.type;
        switch (feedMap[post.id]?.type ?? 100) {
          case 0:
            post.affordance = "suggested";
            post.affordance_readable = "Suggested Post";
            break;
          case 1:
            post.affordance = "created_by";
            post.affordance_readable = `Created By <b>${label}</b>`;
            break;
          case 2:
            post.affordance = "followed_by";
            post.affordance_readable = `Followed By <b>${label}</b>`;
            break;
          case 3:
            post.affordance = "similar_to";
            post.affordance_readable = `Similar to <b>${label}</b>`;
            break;
          case 4:
            post.affordance = "tagged_by";
            post.affordance_readable = `Tagged By <b>${label}</b>`;
            break;
          case 5:
            post.affordance = "trending";
            post.affordance_readable = "Trending Post";
            break;
          case 6:
            post.affordance = "private_eyes";
            post.affordance_readable = "For Yours Eyes";
            break;
          case 7:
            post.affordance = "reposted";
            post.affordance_readable = `Reposted <b>${label}</b>`;
            break;
          case 8:
            post.affordance = "top_of_leaderboard";
            post.affordance_readable = "Top of Leaderboard";
            break;
          case 100:
          default:
            post.affordance = "";
            post.affordance_readable = "";
        }
      }
      return post;
    })
    .filter((post) => post !== undefined);

  const serves = sortedPosts.map((post) => ({
    post_id: post.id,
    affordance_id: post.affordance_id,
    affordance_label: post.affordance_label,
  }));

  loggingInfo("feed", {
    profile_id: profileId,
    feed_count: feedCount,
    fetching_count: fetchingCount,
    feed_type: feedType,
    row_id: rowId,
    served_ids: postIdSelection,
    serves: serves,
    source: source,
  });

  return sortedPosts;
}

async function fetchExploreFromBT(profileId, feedCount, fetchingCount) {
  const postIndex = await getProfilePostFollowing(
    profileId,
    "explore",
    redisClient,
  );
  const postIds = postIndex?.ids ?? [];
  const postIdSelection = postIds
    .slice(feedCount, feedCount + fetchingCount)
    .map(String);
  let posts = await getByMultiplePostId(postIdSelection);
  const shorterPosts = posts.map((item) => {
    const newItem = { ...item };
    newItem.like_count = item.post_likes?.length || 0;
    delete newItem.post_likes;
    delete newItem.post_comments;
    delete newItem.post_bookmarks;
    delete newItem.profiles.bots;
    return newItem;
  });

  return shorterPosts;
}

async function getArtStyleId(profileId) {
  const { data: botData, error: botError } = await supabase
    .from("bots")
    .select("art_style")
    .eq("creator_id", profileId)
    .order("id", { ascending: false })
    .limit(1);
  if (!botData || botError || botData.length == 0) {
    const ids = [-1, -2, -3];
    return ids[Math.floor(Math.random() * 3)];
  }
  if (botData[0]?.art_style?.startsWith("realistic")) {
    return -1;
  } else if (botData[0]?.art_style?.startsWith("drawing")) {
    return -2;
  } else {
    return -3;
  }
}

usersRouter.get("/fetchBNNFeed", async (req, res) => {
  let feedCount = Number(req.query.feed_count);

  if (feedCount == null || feedCount == undefined || isNaN(feedCount)) {
    return res.sendStatus(400);
  }

  const fetchingCount = feedCount ? PAGE_LOAD_COUNT : INITIAL_FEED_LOAD_COUNT;

  const profilesToFetch = [368953];

  const { data, error: supabaseError } = await supabase
    .from("posts")
    .select(
      `
    id, created_at, updated_at, profile_id, media_url, location, slug, description, tags, visibility, nsfw, type,
    profiles (
      bots!bots_profile_id_fkey(id, franchise, tag, source, creator_id),
      id,
      username,
      display_name,
      avatar_url,
      visibility,
      nsfw
    ),
    post_likes (
      id,
      profile_id,
      is_like,
      is_bot
    ),
    post_comments (
      id
    ),
    post_bookmarks (*)
      `,
    )
    .eq("visibility", "public")
    .neq("media_url", null)
    .neq("nsfw", "nsfw")
    .neq("profiles.visibility", "archived")
    .order("id", {
      foreignTable: "post_comments",
      ascending: true,
    })
    .order("id", { ascending: false })
    .in("profile_id", profilesToFetch)
    .limit(200);

  if (supabaseError) {
    const error = wrappedSupabaseError(
      supabaseError,
      "failed to fetch BNN posts",
    );
    logError({
      context: "fetchBNNFeed: supabaseError",
      error,
    });
    return res.sendStatus(500);
  }

  const mergedPosts = data.slice(feedCount, feedCount + fetchingCount);

  return res.json(mergedPosts);
});

usersRouter.get("/fetchForYouFeed", async (req, res) => {
  let feedCount = Number(req.query.feed_count);
  const profileId = Number(req.query.profile_id);
  if (feedCount == null || feedCount == undefined || isNaN(feedCount)) {
    return res.sendStatus(400);
  }

  const fetchingCount = feedCount ? PAGE_LOAD_COUNT : INITIAL_FEED_LOAD_COUNT;
  const lastPostIdsParam = req.query.last_post_ids;

  let lastPostIdsArray;
  if (typeof lastPostIdsParam === "undefined" || lastPostIdsParam === "") {
    lastPostIdsArray = [];
  } else {
    lastPostIdsArray = lastPostIdsParam
      .split(",")
      .map((id) => Number(id))
      .filter((id) => !isNaN(id));
  }

  if (
    profileId == null ||
    profileId == undefined ||
    isNaN(profileId) ||
    profileId <= 0
  ) {
    const anonymousId = req.query.anonymous_id || getMD5(req.ip); // can be replaced with null if this gets scanned!
    let posts = await fetchPostWithoutProfileId(
      anonymousId,
      feedCount,
      fetchingCount,
      lastPostIdsArray,
    );
    return res.json(posts);
  }
  let posts = await fetchPostFromBT(
    profileId,
    feedCount,
    fetchingCount,
    lastPostIdsArray,
    "for_you",
  );
  if (posts.length > 0) {
    return res.json(posts);
  }
  posts = await fetchPostWithoutProfileId(
    profileId, // using as AnonymousId to fetch top art-style content
    feedCount,
    fetchingCount,
    lastPostIdsArray,
  );
  if (posts.length > 0) {
    return res.json(posts);
  }
  // Roll back to slow supabase
  loggingInfo("feed", {
    profile_id: profileId,
    feed_count: feedCount,
    fetching_count: fetchingCount,
    feed_type: "foryou",
    source: "db",
  });
  const [botsResponse, followingResponse] = await Promise.all([
    fetchBotPosts(),
    fetchFollowingPosts({ profileId, feedCount }),
  ]);

  const {
    data: followingPosts,
    error: followingPostsError,
    tags: followingTags,
    tagRatios: followingTagRatios,
    creators,
    similarCreatorsIds,
  } = followingResponse;

  const { data: botsPosts, error: botsPostsError } = botsResponse;

  if (followingPostsError) {
    logError({
      executionId: req.executionId,
      context: "fetchForYouFeed: followingPostsError",
      error: followingPostsError,
    });

    console.warn("**** followingPostsError", followingPostsError);
  }

  logWarn({
    executionId: req.executionId,
    context: "**** fetchForYouFeed: followingPosts",
    message: followingPosts?.length,
  });

  if (botsPostsError) {
    logError({
      executionId: req.executionId,
      context: "fetchForYouFeed: botsPostsError",
      error: botsPostsError,
    });
  }

  if (!botsPosts) {
    logError({
      executionId: req.executionId,
      context: "fetchForYouFeed: botsPosts is null",
      error: new Error("botsPosts is null"),
    });
  }

  let mergedPosts = await mergeScoreAndSortPosts({
    followedPosts: followingPosts,
    otherPosts: botsPosts,
    profileId,
    followingTags,
    followingTagRatios,
    creators,
    similarCreatorsIds,
  });

  // TODO: show only posts that have 5+ comments
  mergedPosts = mergedPosts.filter(
    (post) => post.post_comments?.length > 5 || !post.profiles?.bots,
  );

  // apple user
  if (profileId === 711 || profileId === 201) {
    // safe profiles

    // filter out unsafe profiles
    mergedPosts = mergedPosts.filter((e) =>
      safeProfiles.includes(e.profile_id),
    );
  }

  //filtering by hide list
  const { data, error: filterByHideListError } = await filterByHideList({
    profileId,
    mergedPosts,
  });

  if (filterByHideListError || !data) {
    logError({
      executionId: req.executionId,
      context: "fetchForYouFeed: filterByHideListError",
      error: filterByHideListError || "Error or no data available",
    });
    return res
      .status(500)
      .send(filterByHideListError || "Error or no data available");
  }

  mergedPosts = data.slice(feedCount, feedCount + fetchingCount);

  return res.json(mergedPosts);
});

function generateRandomWords(n) {
  if (n > randomWordList.length) {
    throw new Error("Requested more words than are available in the array");
  }

  const shuffledArray = [...randomWordList].sort(() => 0.5 - Math.random());
  const selectedWords = shuffledArray.slice(0, n);

  return selectedWords.join("_");
}

function generateRandomString(length) {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

usersRouter.post(
  "/signupPost",
  authUser,
  signupPostLimiter,
  async (req, res) => {
    const { inviteData, experimentData, clientCapabilities } = req.body;
    let autoUsername = generateRandomWords(2);
    let with_random_string;

    logWarn({
      executionId: req.executionId,
      context: "**** signupPost: start",
      message: req.user.id,
    });

    mixpanelTrackReq(
      req.user.id,
      `signup_${req.user.app_metadata.provider}`,
      {
        provider: req.user.app_metadata.provider,
        invite_data: inviteData,
      },
      req,
    );

    // enable cyoa for user
    if (experimentData?.cyoaModeEnabled) {
      try {
        const { error } = await supabase
          .schema("internal")
          .from("experiments")
          .insert({
            user_id: req.user.id,
            cyoa_enabled: experimentData.cyoaModeEnabled,
          });

        if (error) {
          // throw wrappedSupabaseError(error);
          // no op, no need to throw
        }

        mixpanelTrackReq(req.user.id, "cyoa_enabled", {}, req);
      } catch (error) {
        logError({
          executionId: req.executionId,
          context: "**** signupPost: cyoa_mode update error",
          error,
        });
      }
    }

    try {
      const { data: profiles, error: profilesError } = await supabase
        .from("profiles")
        .select("username")
        .ilike("username", `${autoUsername}%`);
      if (profilesError) throw wrappedSupabaseError(profilesError);

      while (true) {
        let temp = autoUsername + "_" + generateRandomString(3);
        const isExisted = profiles.find(({ username }) => username === temp);

        if (!isExisted) {
          with_random_string = temp;
          break;
        }
      }

      //create automatic profile
      const { data: profile, error: profileError } = await supabase.rpc(
        "func_create_first_user_profile",
        {
          data: {
            username: with_random_string,
            user_id: req.user?.id,
            display_name: req.user?.user_metadata?.full_name ?? "",
            visibility: "public",
            auto_generated: true,
          },
        },
      );
      logWarn({
        executionId: req.executionId,
        context: "**** signupPost: profile",
        message: JSON.stringify(profile),
      });
      if (profileError) {
        throw wrappedSupabaseError(profileError);
      } else {
        if (
          LAUNCH_PROPOSED_POST_MODE_V1_EXPERIMENT &&
          clientCapabilities?.proposedPostMode === "v1"
        ) {
          const experimentId = "proposed_post_mode_v1";
          // salt the user id with the experiment id
          // this way we can ensure that the user won't just always be in the same group for different experiments
          const salted = `${req.user.id}-${experimentId}`;
          const hash = getMD5(salted);
          const prefix = hash.substring(0, 8); // md5 digest should be 32 characters long, we take the first 8 and treat it as a hex number
          const hexNumRepresentation = parseInt(prefix, 16) / 0xffffffff; // normalize to [0, 1]
          const threshold = 0.5; // 50% chance of being in the experiment

          const inTreatmentGroup = hexNumRepresentation > threshold;
          const variantGroup = inTreatmentGroup ? "treatment" : "control";

          logInfo({
            context: "signupPost: proposed_post_mode_v1 debugging",
            message: "debugging experiment group assignment",
            userId: req.user.id,
            salted,
            hash,
            prefix,
            hexNumRepresentation,
            threshold,
            inTreatmentGroup,
          });

          try {
            const { error } = await supabase
              .schema("internal")
              .from("experiments")
              .insert({
                user_id: req.user.id,
                proposed_post_mode_v1: inTreatmentGroup,
              });

            if (error) {
              throw wrappedSupabaseError(error);
            }

            // $experiment_started is a special event that Mixpanel uses to track experiment exposures
            mixpanelTrackReq(
              req.user.id,
              "$experiment_started",
              {
                "Experiment name": experimentId,
                "Variant name": variantGroup,
              },
              req,
            );
          } catch (error) {
            logError({
              executionId: req.executionId,
              context: "**** signupPost: proposed_post_mode update error",
              error,
            });
            // we don't want this to block the signup process
          }
        }

        if (inviteData?.inviteCode) {
          try {
            // add redem invitation
            const { error } = await redeemInviteCode({
              code: inviteData?.inviteCode,
              type: inviteData?.type || "invite",
              post_id: inviteData?.post_id,
              user_id: req.user?.id,
            });

            if (error) {
              throw wrappedSupabaseError(error);
            }
          } catch (error) {
            logError({
              context: "**** signup post redeemInviteCode error",
              error,
            });
          }
        } else {
          // add waitlist
          if (IS_WAITLIST_ENABLED) {
            const { data, error } = await insertWaitlist({
              user_id: req.user?.id,
            });

            if (!data || error) {
              logError({
                executionId: req.executionId,
                context: "**** Insert Waitlist Error",
                error: `user_id ${req.user?.id} is not added to waitlist`,
              });
            }
          }
        }

        res.send(profile);
      }
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "signupPost Error",
        error,
      });

      res.sendStatus(400);
    }
  },
);

usersRouter.get("/fetchExplore", async (req, res) => {
  let feedCount = Number(req.query.feed_count);
  const profileId = Number(req.query.profile_id);
  const category = req.query.category?.toLowerCase() || "";
  if (
    feedCount == null ||
    feedCount == undefined ||
    profileId == null ||
    profileId == undefined
  ) {
    return res.sendStatus(400);
  }

  const fetchingCount = feedCount
    ? EXPLORE_LOAD_COUNT
    : INITIAL_EXPLORE_LOAD_COUNT;

  const rowKey = SUPPORTED_CATEGORIES.includes(category) ? category : "all";

  // We use BigTable as the default for the Feed
  let posts = await fetchExploreFromBT(rowKey, feedCount, fetchingCount);
  if (posts.length > 0) {
    loggingInfo("feed", {
      profile_id: profileId,
      feed_count: feedCount,
      fetching_count: fetchingCount,
      feed_type: "explore",
      row_id: profileId,
      row_key: rowKey.toString(),
      served_ids: posts.map((post) => post.id),
      source: "bt",
    });
    return res.json(posts);
  }
  let rowId = await getArtStyleId(profileId);
  posts = await fetchExploreFromBT(rowId, feedCount, fetchingCount);
  if (posts.length > 0) {
    loggingInfo("feed", {
      profile_id: profileId,
      feed_count: feedCount,
      fetching_count: fetchingCount,
      feed_type: "explore",
      row_id: rowId,
      served_ids: posts.map((post) => post.id),
      source: "bt-style",
    });
    return res.json(posts);
  }
  // End of Bigtable Lookup
  let sqlQuery = supabase
    .from("posts")
    .select(
      `
        id,
        profile_id,
        media_url,
        slug,
        nsfw,
        type,
        profiles (
          username,
          avatar_url,
          visibility
      )
      `,
    )
    .eq("nsfw", "normal")
    .or(`visibility.eq.public,profile_id.eq.${profileId}`)
    .not("profiles", "is", null) // bots only
    .eq("profiles.visibility", "public")
    .order("id", { ascending: false });

  if (profileId === 711 || profileId === 201) {
    sqlQuery = sqlQuery.in("profile_id", safeProfiles);
  }
  sqlQuery = sqlQuery.range(feedCount, feedCount + fetchingCount);
  let { data, error: supabaseError } = await sqlQuery;

  if (supabaseError) {
    const error = wrappedSupabaseError(
      supabaseError,
      "/fetchExplore query failed",
    );
    logError({
      context: `/fetchExplore failed`,
      error,
      profileId,
    });
    throw error;
  }

  loggingInfo("feed", {
    profile_id: profileId,
    feed_count: feedCount,
    fetching_count: fetchingCount,
    feed_type: "explore",
    row_id: profileId,
    served_ids: data.map((post) => post.id),
    source: "db",
  });
  res.json(data);
});

usersRouter.get("/getSuggestedAccounts", async (req, res) => {
  const profile_id = Number(req.query?.profile_id);
  if (
    req.query.profile_id === null ||
    req.query.profile_id === undefined ||
    profile_id < 0
  ) {
    res.sendStatus(400);
    return;
  }
  let bot_id = profile_id;
  if (
    !(req.query.bot_id === null || req.query.bot_id === undefined || bot_id < 0)
  ) {
    bot_id = Number(req.query?.bot_id);
  }
  let numAccounts = 7;
  if (
    !(
      req.query.count === null ||
      req.query.count === undefined ||
      req.query.count < 0
    )
  ) {
    numAccounts = Number(req.query?.count);
  }

  try {
    const [botSuggestion, followings] = await Promise.all([
      getBotBot(bot_id, redisClient),
      getFollowings(profile_id),
    ]);

    const bots = removeCurrentFollowings(followings, botSuggestion.data);
    const visibleBots = await removeUnavailableAccounts(bots);
    let shuffled = shuffle(visibleBots, visibleBots.length).slice(
      0,
      numAccounts,
    );

    if (shuffled.length < numAccounts) {
      const topAccounts = await getTopAccounts();
      const topBots = removeCurrentFollowings(followings, topAccounts.data);
      const visibleTopBots = await removeUnavailableAccounts(topBots);
      shuffled = shuffled.concat(
        visibleTopBots.slice(0, numAccounts - shuffled.length),
      );
    }
    return res.send(shuffled);
  } catch (error) {
    logError({
      context: "/getSuggestedAccounts Error",
      error,
    });
    res.status(500).send(error);
  }
});

async function removeUnavailableAccounts(accounts) {
  const ids = accounts.map((item) => item.id);
  const { data, error } = await supabase
    .from("profiles")
    .select("id, visibility")
    .in("id", ids);

  // If there was an error or no data, return the original accounts
  if (error || !data || data.length === 0) {
    return [];
  }

  // Build a Set of IDs with allowed visibility
  const allowedIds = new Set(
    data
      .filter(
        (profile) =>
          profile.visibility === "public" || profile.visibility === "private",
      )
      .map((profile) => profile.id),
  );

  // Filter the accounts based on allowed IDs
  return accounts.filter((acc) => allowedIds.has(acc.id));
}

async function getTopAccounts() {
  // There are 8 rows in Bigtable with Top0, Top1, ... Top7 to select random top accounts.
  const rowKey = "top" + Math.floor(Math.random() * 8);
  return getBotBot(rowKey, redisClient);
}

function removeCurrentFollowings(followings, jsonArray) {
  const fixed = [];
  try {
    for (let bot of jsonArray) {
      if (!followings.has(bot.id)) {
        fixed.push(bot);
      }
    }
  } catch (error) {
    console.error("**** removeCurrentFollowings Error", error, jsonArray);
  }
  return fixed;
}

function shuffle(array, length) {
  for (let i = length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

const batchReadSelect = decorateWithActiveSpanAsync(
  "batchReadSelect",
  _batchReadSelect,
);
async function _batchReadSelect({ posts, profileId }) {
  // Create an array of composite keys
  const compositeKeys = posts.map((item) => `${profileId}_${item.id}`);

  // Perform the query using the .in method
  const { data: result, error } = await supabase
    .from("post_reads")
    .select("post_id")
    .in("profile_post_composite_key", compositeKeys);

  if (error) {
    console.error("there's an error", error);
    return [];
  }

  return result.map((e) => e.post_id);
}

async function mergeScoreAndSortPosts({
  followedPosts,
  otherPosts,
  profileId,
  followingTags,
  followingTagRatios,
  creators,
  similarCreatorsIds,
}) {
  // Merge the two arrays

  const creatorIds = (creators ?? []).map((e) => e.profiles?.id);

  let _followedPosts = (followedPosts ?? []).map((e) => ({
    ...e,
    is_following: true,
  }));
  let _otherPosts = (otherPosts ?? []).map((e) => ({
    ...e,
    is_following: false,
  }));

  // De-dupe on post.id, giving priority to followedPosts
  let mergedMap = new Map(
    [..._otherPosts, ..._followedPosts].map((post) => [post.id, post]),
  );

  let mergedPosts = Array.from(mergedMap.values());

  const readPosts = await batchReadSelect({ posts: mergedPosts, profileId });
  // read status for mergedPosts

  mergedPosts = mergedPosts.map((post) => {
    post.is_read = readPosts.includes(post.id);
    const { score } = calculateScoreForPost({
      post,
      followingTags: followingTags ?? [],
      followingTagRatios: followingTagRatios ?? [],
      creatorIds,
      similarCreatorsIds: similarCreatorsIds ?? [],
    });
    return {
      ...post,
      score,
    };
  });

  // Sort the merged array based on your sorting criteria (e.g., timestamp)
  mergedPosts = mergedPosts.sort((a, b) => {
    // Convert date strings to Date objects or timestamps
    return b.score - a.score;
  });

  return mergedPosts;
}

function calculateScoreForPost({
  post,
  followingTags,
  creatorIds,
  similarCreatorsIds,
}) {
  // Calculate the score based on likes and dislikes with time drop-off
  const postCreationTime = new Date(post.created_at);
  const currentTime = new Date();
  const postAgeMinutes = timeDifferenceInMinutes(postCreationTime, currentTime);
  const decayRate = 0.002; // Decreased decay rate for stronger boost
  const decayFactor = Math.exp(-decayRate * postAgeMinutes);

  let debug = "";

  let score = 0;

  // Check if the user is following the author
  if (post.is_following) {
    score += 12.5; // Increased initial score contribution for followers
    debug += "is following, ";
  } else {
    score += 5; // Increased initial score contribution for non-followers
    debug += "is not following, ";
    // // if it's a non follower, 1/10 chance, we boost it to the top
    // let randomNumber = Math.floor(Math.random() * 10) + 1;
    // if (randomNumber === 5) {
    //   score = 10;
    // }
  }

  // if it's read, reduce all score to 0
  if (post.is_read) {
    score = 0;
    debug += "is read, ";
    return { score, debug };
  }

  // if it's unread and following, we decay it very slowly
  if (post.is_following && !post.is_read) {
    score = 12.5;

    // decays very slowly
    const decayFactor = Math.exp(-0.002 * postAgeMinutes);
    score = score * decayFactor;

    debug += "is following and unread, ";
  }

  // if post is from creator we follow, boost
  // check that we're not already following to prevent double boost
  if (
    !post.is_following &&
    creatorIds.includes(post.profiles?.bots?.creator_id)
  ) {
    // console.log("creatorIds", creatorIds);
    // console.log("post.profiles.creator_id", post.profiles.creator_id);

    // console.log("is a following creator", post);
    score += 10 + Math.floor(Math.random() * 10) + 1;
    debug += "is a following creator, ";
  }

  // if post is from creator we follow, boost
  // check that we're not already following the creator to prevent double boost
  else if (
    !post.is_following &&
    similarCreatorsIds.includes(post.profiles?.bots?.creator_id)
  ) {
    // console.log("creatorIds", creatorIds);
    // console.log("post.profiles.creator_id", post.profiles.creator_id);

    // console.log("is a following creator", post);
    score += 7.5 + Math.floor(Math.random() * 10) + 1;
    debug += "is a following a similar creator, ";
  }

  const tags = post.profiles?.bots?.tag;

  tags?.split(",").forEach((tag) => {
    if (followingTags.includes(tag)) {
      score += 2;
      debug += "is matching tag, ";
    }
  });

  score = score * decayFactor;
  debug += "decay factor " + decayFactor;

  return { score, debug };
}

usersRouter.get("/fetchAllMediaBetweenProfiles", authUser, async (req, res) => {
  const { owner_profile_id, other_profile_id } = req.query;
  if (!owner_profile_id || !other_profile_id) {
    return res.status(400).json({
      error: "Both owner_profile_id and other_profile_id are required",
    });
  }

  const userId = req.user?.id;
  const isValid = await checkProfileValid(userId, owner_profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // Check if user is premium
  let is_premium = false;
  try {
    const { data: currentPlan, error: currentPlanError } = await supabase
      .from("user_current_plans_view")
      .select("current_plan_id, current_plan_is_active")
      .eq("user_id", userId)
      .maybeSingle();

    if (currentPlanError) {
      const error = wrappedSupabaseError(
        currentPlanError,
        "failed to fetch current plan from 'user_current_plans_view'",
      );
      logError({
        context: "fetchAllMediaBetweenProfiles - premium check error",
        error,
        user_id: userId,
      });
    }

    if (
      currentPlan &&
      currentPlan.current_plan_id === 2 &&
      currentPlan.current_plan_is_active
    ) {
      is_premium = true;
    }
  } catch (planError) {
    logError({
      context: "fetchAllMediaBetweenProfiles - premium check error",
      error: planError,
      user_id: userId,
    });
  }

  const { data, error: rpcError } = await supabase.rpc(
    "get_all_messages_with_media_url_between_profiles_v2",
    {
      profile1_id: owner_profile_id,
      profile2_id: other_profile_id,
    },
  );

  if (rpcError) {
    const error = wrappedSupabaseError(
      rpcError,
      "failed to call db function 'get_all_messages_with_media_url_between_profiles'",
    );
    logError({
      context: `/fetchAllMediaBetweenProfiles - get_all_messages_with_media_url_between_profiles`,
      error,
      owner_profile_id,
      other_profile_id,
    });
    return res.sendStatus(500);
  }

  // Create a Map to deduplicate by URL while preserving metadata
  const urlMap = new Map();

  data.forEach((item) => {
    const url = item.media_url || (item.generate_urls && item.generate_urls[0]);
    if (url && !urlMap.has(url)) {
      // Filter out NSFW content if user is not premium
      if (!is_premium && item?.metadata?.nsfw === "true") {
        return; // Skip this item
      }
      urlMap.set(url, { url, metadata: item?.metadata });
    }
  });

  const mediaUrls = Array.from(urlMap.values()).map(({ url }) => ({
    media_url: url,
  }));

  return res.json(mediaUrls);
});

usersRouter.get("/nocreator", async (req, res) => {
  const { data: bots, error: botErrors } = await supabase
    .from("bots")
    .select(
      "id, profile_id, display_name, creator_id, profile:profiles!bots_profile_id_fkey(id, visibility)",
    )
    .is("creator_id", null)
    .lt("created_at", new Date(Date.now() - 10 * 60 * 1000).toISOString()) // Less than 10 minutes ago
    .neq("profile.visibility", "hidden")
    .neq("profile.visibility", "archived")
    .neq("profile.visibility", "draft")
    .not("profile", "is", null)
    .order("id", { ascending: true })
    .limit(50);

  if (botErrors) {
    const error = wrappedSupabaseError(botErrors);
    logWarn({
      context: "/nocreator",
      error,
    });
    throw error;
  }

  const profileIds = bots.map((item) => item.profile_id);

  const response = await supabase
    .from("profiles")
    .update({ visibility: "archived" })
    .neq("visibility", "archived")
    .neq("visibility", "hidden")
    .neq("visibility", "draft")
    .in("id", profileIds);

  return res.json({ response });
});

usersRouter.post("/deleteUser", authUser, async (req, res) => {
  const user_id = req.user?.id;

  const [{ data: profileData }, { error: profileArchivedError }] =
    await Promise.all([
      // Get corresponding profile_id.
      supabase.from("profiles").select("id").eq("user_id", user_id).single(),
      // archive the profiles first
      supabase.rpc("archive_user_profiles", { p_user_id: user_id }),
    ]);

  if (profileArchivedError) {
    const error = wrappedSupabaseError(
      profileArchivedError,
      "failed to archive user profiles",
    );
    logError({
      context: "deleteuser - archiveProfiles",
      error,
      user_id,
    });
    res.sendStatus(400);
    return;
  }

  // delete user entry from users table
  const { error: deleteError } = await supabase
    .from("users")
    .delete()
    .eq("id", user_id);

  if (deleteError) {
    loggingInfo("delete", {
      user_id,
      profile_id: profileData?.id,
      status: "user-error",
      context: "user",
    });
    const error = wrappedSupabaseError(
      deleteError,
      "failed to delete user from users table",
    );
    logError({
      executionId: req.executionId,
      context: "account delete from public users",
      error,
      user_id,
    });
    res.sendStatus(400);
    return;
  }

  // delete auth user
  const { data, error: deleteFromAuthError } =
    await supabase.auth.admin.deleteUser(user_id);
  console.log("**** delete  user", user_id, data, deleteFromAuthError);
  if (deleteFromAuthError) {
    loggingInfo("delete", {
      user_id,
      profile_id: profileData?.id,
      status: "auth-error",
      context: "user",
    });
    if (deleteFromAuthError.code === SupabaseAuthErrorCode.USER_NOT_FOUND) {
      return res.sendStatus(204);
    } else {
      const error = wrappedSupabaseError(
        deleteFromAuthError,
        "failed to delete user from supabase auth",
      );
      logError({
        executionId: req.executionId,
        context: "account delete from auth users",
        error,
        user_id,
      });
      return res.sendStatus(400);
    }
  }
  loggingInfo("delete", {
    user_id,
    profile_id: profileData?.id,
    status: "success",
    context: "user",
  });
  res.sendStatus(200);
});

usersRouter.patch("/updateEmail", authUser, async (req, res) => {
  const userId = req?.user?.id;
  const { email } = req.body;

  if (!email || typeof email !== "string") {
    return res.status(400).send({ error: "Invalid email" });
  }

  try {
    const { error: authError } = await supabase.auth.admin.updateUserById(
      userId,
      { email },
    );
    if (authError)
      throw wrappedSupabaseError(authError, "Failed to update email in auth");

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update Email Failed Error",
      error,
      userId,
    });
    return res.status(500).send({ error: "Failed to update email" });
  }
});

usersRouter.patch("/me", authUser, async (req, res) => {
  const { accountDetails } = req.body;
  if (!accountDetails || Object.keys(accountDetails).length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const userId = req?.user?.id;
  const fieldsToUpdate = [
    "guidelines_agreed",
    "full_name",
    "birthday",
    "onboarding_status",
    "app_reviewed",
    "is_app_active",
    "opened_clone_announce",
    "mci_announced_ids",
    "opened_first_post_share_modal",
    "viewed_first_post",
    "opened_contacts",
    "challenge_ids",
    "viewed_vote_guide",
    "opened_nsfw_confirm_modal",
  ];
  const contentsToUpdate = getSpecificKeys(accountDetails, fieldsToUpdate);

  // check the mic_announced_ids validation
  const mciAnnouncedIds = contentsToUpdate?.mci_announced_ids;
  if (mciAnnouncedIds) {
    if (mciAnnouncedIds.length > 2) {
      delete contentsToUpdate.mci_announced_ids;
    } else {
      try {
        const { data, error } = await supabase
          .from("users")
          .select("mci_announced_ids")
          .eq("id", userId)
          .maybeSingle();

        if (error) {
          throw wrappedSupabaseError(error);
        }

        const userMciIds = data.mci_announced_ids;
        if (userMciIds) {
          const userMciIdsLength = userMciIds.length;

          if (
            userMciIdsLength > 1 ||
            (userMciIdsLength === 1 && !mciAnnouncedIds.includes(userMciIds[0]))
          ) {
            delete contentsToUpdate.mci_announced_ids;
          }
        }
      } catch (error) {
        logError({
          context: "Fetch user mci_announced_ids failed",
          error,
        });
        return res
          .status(500)
          .send({ data: null, error: "Failed to fetch user data" });
      }
    }
  }

  if (Object.keys(contentsToUpdate).length === 0) {
    return res.sendStatus(204);
  }

  // track the update event for birthday and full_name
  const updated = Object.keys(accountDetails)[0];
  const validTrack = ["birthday", "full_name"];
  if (validTrack.includes(updated)) {
    mixpanelTrack(req.user.id, `set_${updated}`, {
      updated: updated,
    });
  }

  try {
    const { error } = await supabase
      .from("users")
      .update(contentsToUpdate)
      .eq("id", userId);

    if (error) {
      const userError = wrappedSupabaseError(error);
      throw userError;
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update User Failed Error",
      error: error,
    });
    return res.status(500).send({ data: null, error: "Failed to update" });
  }
});

usersRouter.patch("/myVisit", authUser, async (req, res) => {
  const userId = req?.user?.id;

  try {
    const { data, error } = await supabase
      .from("users")
      .update({
        visited_other_bot: true,
      })
      .eq("id", userId);

    if (error) {
      const userError = wrappedSupabaseError(error);
      throw userError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update User Visit Others Failed Error",
      error: error,
    });
    return res.status(500).send({ data: null, error: "Failed to update" });
  }
});

usersRouter.patch("/banAccount", authUser, async (req, res) => {
  const { is_banned, bannedId } = req.body;
  if (is_banned === null || is_banned === undefined || !bannedId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const id = req?.user?.id;

  const { data: roleResult, error: roleError } = await supabase
    .from("users")
    .select("role")
    .eq("id", id)
    .single();

  if (!roleResult || roleResult.role === "user" || roleError) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("users")
      .update({ is_banned })
      .eq("id", bannedId)
      .select();

    if (error) {
      throw wrappedSupabaseError(error, "failed to update users.is_banned");
    }

    // Ban or unban at the auth table level
    let authTableError;
    if (is_banned === true) {
      // Ban: set a long ban duration (100 years)
      ({ error: authTableError } = await supabase.auth.admin.updateUserById(
        bannedId,
        { ban_duration: "876000h" },
      ));
    } else {
      // Unban: set ban_duration to null
      ({ error: authTableError } = await supabase.auth.admin.updateUserById(
        bannedId,
        { ban_duration: "0h" },
      ));
    }

    if (authTableError) {
      throw wrappedSupabaseError(
        authTableError,
        "failed to update ban status at the auth table level",
      );
    }
    // TO-DO: unsubscribed to Loops
    if (is_banned == true) {
      const url = `${baseUrl}/loops/userEmail`;
      await axios.get(url, {
        params: {
          user_id: bannedId,
          event_name: "UserBan",
        },
      });
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Ban/Unban User Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to ban/unban user" });
  }
});

usersRouter.patch("/role", authUser, async (req, res) => {
  const { role, roleId } = req.body;
  if (!role || !roleId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const id = req?.user?.id;

  const { data: caller, error: fetchRoleError } = await supabase
    .from("users")
    .select("role")
    .eq("id", id);

  if (fetchRoleError) {
    const error = wrappedSupabaseError(
      fetchRoleError,
      "failed to fetch role from 'users' table",
    );
    throw error;
  }

  if (caller === "user") {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error: updateRoleError } = await supabase
      .from("users")
      .update({ role: role })
      .eq("id", roleId)
      .select();

    if (updateRoleError) {
      const error = wrappedSupabaseError(
        updateRoleError,
        "failed to update 'users' table",
      );
      throw error;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Role update Failed Error",
      error: error,
    });
    return res.status(500).send({ data: null, error: "Failed to update" });
  }
});

// CHRIS: will be deprecated soon
usersRouter.post("/deleteUserProfile", authUser, async (req, res) => {
  const { profile_id } = req.body;

  // Validate profile_id
  if (!profile_id) {
    return res.sendStatus(400);
  }

  const userId = req.user?.id;
  const isValid = await checkProfileValid(userId, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { error: deleteError } = await supabase
    .from("profiles")
    .delete()
    .eq("id", profile_id);

  // Handle errors while deleting profile
  if (deleteError) {
    const error = wrappedSupabaseError(
      deleteError,
      "failed to delete from profiles",
    );
    logError({
      executionId: req.executionId,
      context: "deleteUserProfile - delete user profile error",
      error,
    });
    return res.sendStatus(500);
  }

  // Successfully deleted the profile
  return res.sendStatus(204);
});

// new api to archive the profile instead of delete
usersRouter.post("/deleteProfile", authUser, async (req, res) => {
  const { profile_id } = req.body;

  // Validate profile_id
  if (!profile_id) {
    return res.sendStatus(400);
  }

  const userId = req.user?.id;
  const isValid = await checkProfileValid(userId, profile_id);
  if (!isValid) {
    loggingInfo("delete", {
      user_id: userId,
      profile_id,
      status: "forbidden",
      context: "profile",
    });
    return res.status(403).send({ error: "Forbidden" });
  }

  // Delete the profile by updating visibility as archived
  const { error: deleteError } = await supabase
    .from("profiles")
    .update({ visibility: "archived" })
    .eq("id", profile_id);

  // Handle errors while deleting profile
  if (deleteError) {
    loggingInfo("delete", {
      user_id: userId,
      profile_id,
      status: "profile-error",
      context: "profile",
    });
    const error = wrappedSupabaseError(
      deleteError,
      "failed to delete from profiles",
    );
    logError({
      executionId: req.executionId,
      context: "deleteUserProfile - delete user profile error",
      error,
    });
    return res.sendStatus(500);
  }

  loggingInfo("delete", {
    user_id: userId,
    profile_id,
    status: "success",
    context: "profile",
  });

  // Successfully deleted the profile
  return res.sendStatus(204);
});

usersRouter.post("/report", authUser, async (req, res) => {
  const {
    profile_id,
    reported_profile_id,
    provided_reason,
    type,
    target_id,
    payload,
    reason_ids,
  } = req.body;

  if (!profile_id || !type || !payload) {
    return res.sendStatus(400);
  }

  try {
    const { error: insertReportError } = await supabase
      .from("reports")
      .insert({
        profile_id: profile_id,
        reported_profile_id: reported_profile_id,
        reason: provided_reason ? provided_reason : null,
        type: type,
        target_id: target_id,
        payload: payload,
        reason_ids: reason_ids ?? [],
      })
      .select("*")
      .single();

    if (insertReportError) {
      const error = wrappedSupabaseError(
        insertReportError,
        "failed to insert report",
      );
      throw error;
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Report Failed Error",
      error: error,
      profile_id: profile_id,
      reported_profile_id: reported_profile_id,
      target_id,
    });
    return res.sendStatus(500);
  }
});

// Function to get reports for post_comemnts
usersRouter.get("/getreports", authUser, async (req, res) => {
  const {
    report_type,
    search_type,
    search_value,
    page_size = 15,
    page_number = 1,
  } = req.query;

  if (!report_type) {
    return res.sendStatus(400);
  }

  try {
    const { data, error: fetchReportsError } = await supabase.rpc(
      "fetch_reports",
      {
        report_type,
        search_type,
        search_value,
        page_size,
        page_number,
      },
    );

    if (fetchReportsError) {
      const error = wrappedSupabaseError(
        fetchReportsError,
        "failed to call rpc 'fetch_reports'",
      );
      throw error;
    }

    return res.send({ data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Get Report Failed Error",
      error: error,
      report_type,
      search_type,
      search_value,
    });
    return res.sendStatus(500);
  }
});

// Function to get reports count for post_comments
usersRouter.get("/getreportscount", authUser, async (req, res) => {
  const { report_type, search_type, search_value } = req.query;

  if (!report_type) {
    return res.sendStatus(400);
  }

  try {
    const { data, error: fetchReportsCountError } = await supabase.rpc(
      "fetch_reports_count",
      {
        report_type,
        search_type,
        search_value,
      },
    );

    if (fetchReportsCountError) {
      const error = wrappedSupabaseError(
        fetchReportsCountError,
        "failed to call rpc 'fetch_reports_count'",
      );
      throw error;
    }

    return res.send({ data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Get Report Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

// Function to delete the report with report_id
usersRouter.post("/deletereport", authUser, async (req, res) => {
  const { id } = req.body;

  if (!id) {
    return res.sendStatus(400);
  }

  try {
    const { error: deleteReportError } = await supabase
      .from("reports")
      .delete()
      .eq("id", id);

    if (deleteReportError) {
      const error = wrappedSupabaseError(
        deleteReportError,
        "failed to delete report",
      );
      throw error;
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete Report Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

// Function to delete the reported post_comment
usersRouter.post("/deletecomment", authUser, async (req, res) => {
  const { id } = req.body;

  if (!id) {
    return res.sendStatus(400);
  }

  try {
    const { error: deleteCommentError } = await supabase.rpc("delete_comment", {
      comment_id: id,
    });

    if (deleteCommentError) {
      const error = wrappedSupabaseError(
        deleteCommentError,
        "failed to call rpc 'delete_comment'",
      );
      throw error;
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete Comment Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

/**
 * Fetch post reports with filters
 * This api should be called by Admin
 *
 * @param search_type - (string) search type
 * @param search_value - (string) search value
 * @param page_size - (number) page size
 * @param page_number - (number) page number
 *
 * @returns - post reports
 */

usersRouter.get("/getPostReports", authUser, async (req, res) => {
  const {
    search_type,
    search_value,
    page_size = 15,
    page_number = 1,
  } = req.query;

  try {
    const { data, error: fetchPostReportsError } = await supabase.rpc(
      "fetch_post_reports",
      {
        search_type,
        search_value,
        page_size,
        page_number,
      },
    );

    if (fetchPostReportsError) {
      const error = wrappedSupabaseError(
        fetchPostReportsError,
        "failed to call rpc 'fetch_post_reports'",
      );
      throw error;
    }

    return res.send({ data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Get Post Report Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

/**
 * Fetch count of post reports with filters
 * This api should be called by Admin
 *
 * @param search_type - (string) search type
 * @param search_value - (string) search value
 *
 * @returns - count of reports for post
 */

usersRouter.get("/getPostReportsCount", authUser, async (req, res) => {
  const { search_type, search_value } = req.query;

  try {
    const { data, error: fetchPostReportsCount } = await supabase.rpc(
      "fetch_post_reports_count",
      {
        search_type,
        search_value,
      },
    );

    if (fetchPostReportsCount) {
      const error = wrappedSupabaseError(
        fetchPostReportsCount,
        "failed to call rpc 'fetch_post_reports_count'",
      );
      throw error;
    }

    return res.send({ data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Get Post Report Count Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

usersRouter.post("/deleteConversations", authUser, async (req, res) => {
  const { conversationIds } = req.body;

  if (!conversationIds || conversationIds.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { error: deleteParticipantsError } = await supabase
      .from("conversation_participants")
      .delete()
      .in("conversation_id", conversationIds);
    if (deleteParticipantsError) {
      throw wrappedSupabaseError(deleteParticipantsError);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete conversations Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Delete conversations failed" });
  }
});

usersRouter.post("/userAgreements", authUser, async (req, res) => {
  const user_id = req.user.id;
  if (!user_id) {
    return res.status(400).send({ data: null, erorr: "invalid user" });
  }

  try {
    const { data, error } = await supabase
      .from("user_aggrements_history")
      .insert({
        user_id: user_id,
        type: "content_guidelines",
        status: true,
      })
      .select("*");

    if (error) throw wrappedSupabaseError(error);
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "UserAgreements Failed Error",
      error: error,
    });
    return res.status(500).send({ error: "UserAgreements Failed Error" });
  }
});

usersRouter.post("/waitlist", authUser, async (req, res) => {
  const user_id = req.user.id;
  if (!user_id) {
    return res.status(400).send({ data: null, erorr: "invalid user" });
  }

  try {
    const { data, error } = await supabase.from("waitlist").insert({
      user_id: user_id,
    });

    if (error) throw wrappedSupabaseError(error);
    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Waitlist Failed Error",
      error: error,
    });
    return res.status(500).send({ error: "Waitlist Failed Error" });
  }
});

usersRouter.post("/requestFollow", authUser, async (req, res) => {
  const { details } = req.body;

  if (!details || !details.requester_id || !details.requestee_id) {
    return res.status(400).send({ error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, details?.requester_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("follow_requests")
      .insert(details);

    if (error && error.code !== PostgresErrorCode.UNIQUE_VIOLATION) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Follow request insert Failed Error",
      error: error,
    });
    return res.status(500).send({ error: "Follow request failed Error" });
  }
});

usersRouter.post("/addExtraQuota", authUser, async (req, res) => {
  const { userId, value } = req.body;
  const user_id = req.user?.id;

  // Validate input
  if (!userId || !value) {
    return res.status(400).send({ data: null, error: "Invalid value error" });
  }

  const isAllowed = await checkAdminValid(user_id);

  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    // insert or update user_extra_quotas
    const { error: updateError } = await supabase
      .from("user_extra_quotas")
      .upsert(
        {
          user_id: userId,
          package_id: 3,
          value,
        },
        { onConflict: ["user_id", "package_id"] },
      );

    if (updateError) throw wrappedSupabaseError(updateError);

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Failed to update extra quota",
      error,
      targetUserId: userId,
    });
    return res.sendStatus(500);
  }
});

usersRouter.get("/guidelines", authUser, async (req, res) => {
  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("users")
      .select("guidelines_agreed")
      .eq("id", user_id)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch user guidelines agreed",
      error,
      user_id,
    });
    return res.sendStatus(500);
  }
});

usersRouter.get("/packageQuota", authUser, async (req, res) => {
  const { id } = req.query;
  const myUserId = req.user?.id;
  let user_id = myUserId;

  if (id) {
    const isAdmin = await checkAdminValid(myUserId);
    if (!isAdmin) {
      return res.status(403).send({ error: "Forbidden" });
    }
    user_id = id;
  }

  try {
    const { data, error } = await supabase
      .from("user_package_quotas_view")
      .select("packages")
      .eq("user_id", user_id)
      .limit(1);

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data: data[0], error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch user guidelines agreed",
      error,
      user_id,
    });
    return res.sendStatus(500);
  }
});

usersRouter.get("/followings", async (req, res) => {
  const { profileId } = req.query;
  const id = parseInt(profileId, 10);
  if (!profileId || isNaN(id) || id <= 0) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  try {
    const { data, error } = await supabase
      .from("followers")
      .select(
        "*, profiles!followers_following_id_fkey!inner(id,username,display_name,avatar_url,description,nsfw)",
      )
      .eq("follower_id", id)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived");

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch user followings",
      error,
      profileId,
    });
    return res.sendStatus(500);
  }
});

usersRouter.get("/getUserData", authUser, async (req, res) => {
  const user_id = req.user?.id;

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("users")
      .select(`is_banned, premium_months_quantity, role`)
      .eq("id", user_id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "getUserData - fetch user data error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to fetch users" });
  }
});

usersRouter.get("/getXP", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const profile_id = req.query.profile_id;

  if (!user_id || !profile_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select(`id, total_xp`)
      .eq("id", profile_id)
      .eq("user_id", user_id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send(data);
  } catch (error) {
    logError({
      context: "getUserData - fetch user data error",
      error,
      user_id,
      profile_id,
    });
    return res.status(500).send({ error: "Failed to fetch users" });
  }
});

usersRouter.get("/getUserBirthday", authUser, async (req, res) => {
  const user_id = req.user?.id;

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("users")
      .select(`birthday`)
      .eq("id", user_id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "getUserBirthday - fetch user data error",
      error,
    });
    return res.status(500).send({ data: null, error: "Failed to fetch users" });
  }
});

// delete for users tab in Admin
usersRouter.post("/deleteUserAdmin", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);
  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // archive the profiles first
  const { error: profileArchivedError } = await supabase.rpc(
    "archive_user_profiles",
    { p_user_id: id },
  );
  if (profileArchivedError) {
    const error = wrappedSupabaseError(
      profileArchivedError,
      "failed to archive user profiles from admin",
    );
    logError({
      context: "deleteuser - archiveProfiles from admin",
      error,
      id,
    });
    res.sendStatus(400);
    return;
  }

  // delete user entry from users table
  const { error: deleteError } = await supabase
    .from("users")
    .delete()
    .eq("id", id);

  if (deleteError) {
    loggingInfo("delete", {
      id,
      status: "user-error",
      context: "user",
    });
    const error = wrappedSupabaseError(
      deleteError,
      "failed to delete user from users table from admin",
    );
    logError({
      context: "account delete from public users from admin",
      error,
      id,
    });
    res.sendStatus(400);
    return;
  }

  // delete auth user
  const { error: deleteFromAuthError } =
    await supabase.auth.admin.deleteUser(id);
  if (deleteFromAuthError) {
    loggingInfo("delete", {
      id,
      status: "auth-error",
      context: "user",
    });
    if (deleteFromAuthError.code === SupabaseAuthErrorCode.USER_NOT_FOUND) {
      return res.sendStatus(204);
    } else {
      const error = wrappedSupabaseError(
        deleteFromAuthError,
        "failed to delete user from supabase auth from admin",
      );
      logError({
        context: "account delete from auth users from admin",
        error,
        id,
      });
      return res.sendStatus(400);
    }
  }
  loggingInfo("delete", {
    id,
    status: "success",
    context: "user",
  });
  res.sendStatus(200);
});

usersRouter.post("/fetchPlanHistory", authUser, async (req, res) => {
  const userId = req?.user?.id;

  try {
    const { data, error } = await supabase
      .from("user_plan_histories_view")
      .select("*")
      .eq("user_id", userId)
      .not("billing_status", "is", null);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Fetch user's plan history failed",
      error,
      userId,
    });
    return res.status(500).send({ data: null, error: "Failed to fetch" });
  }
});

usersRouter.post("/fetchCurrentPlan", authUser, async (req, res) => {
  const userId = req?.user?.id;

  try {
    const { data, error } = await supabase
      .from("user_current_plans_view")
      .select("*")
      .eq("user_id", userId);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Fetch user's current plan failed",
      error,
      userId,
    });
    return res.status(500).send({ data: null, error: "Failed to fetch" });
  }
});

// (CHRIS) TO-DO: will be removed soon
usersRouter.post("/contact", authUser, async (req, res) => {
  const { profile_id, contacts } = req.body;
  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  if (!contacts || !contacts.length) {
    return res.send({ data: { existed: [], not_existed: [] }, error: null });
  }

  const user_id = req.user?.id;
  const isProfileValid = await checkProfileValid(user_id, profile_id);
  if (!isProfileValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { data: userOnboarding } = await supabase
    .from("users")
    .select("onboarding_status")
    .eq("id", user_id)
    .single();

  const is_onboarding =
    !userOnboarding ||
    (userOnboarding.onboarding_status !== "done" &&
      userOnboarding.onboarding_status !== "skipped");

  const transformedContacts = contacts.flatMap((contact) => {
    const contactName = contact?.name?.display;
    const contactId = contact?.contactId;

    const phoneEntries = contact.phones
      ? contact.phones.map((phone) => ({
          user_id,
          contact_name: contactName,
          contact_type: "phone",
          contact_value: phone.number.replace(/\D/g, ""),
          contact_id: contactId,
        }))
      : [];

    const emailEntries = contact.emails
      ? contact.emails.map((email) => ({
          user_id,
          contact_name: contactName,
          contact_type: "email",
          contact_value: email.address.toLowerCase(),
          contact_id: contactId,
        }))
      : [];

    return [...phoneEntries, ...emailEntries];
  });

  // Deduplicate using a Map
  const uniqueContactsMap = new Map();

  transformedContacts.forEach((contact) => {
    const key = `${contact.contact_type}-${contact.contact_value}`; // Unique composite key
    if (!uniqueContactsMap.has(key)) {
      uniqueContactsMap.set(key, contact); // Add contact if key is not already present
    }
  });

  // Convert the Map back to an array
  const uniqueContacts = Array.from(uniqueContactsMap.values());

  const contact_values = uniqueContacts.map((contact) => {
    return contact.contact_value;
  });

  try {
    const { error } = await supabase.from("contacts").upsert(uniqueContacts, {
      onConflict: ["user_id", "contact_value"], // Specify the columns for conflict resolution
    });

    if (error) {
      throw wrappedSupabaseError(error);
    }

    // TODO: match on "normalized" email
    const { data: contactDetails, error: contactError } = await supabase.rpc(
      "get_user_detail_from_contact_v1",
      { selected_profile_id: profile_id, is_onboarding, contact_values },
    );

    if (contactError) {
      throw wrappedSupabaseError(contactError);
    }

    const result = contacts.reduce(
      (acc, contact) => {
        const contactName = contact?.name?.display || "Unknown"; // Default to "Unknown" if contact name is missing

        const isRegistered = contactDetails?.find(
          (reg) =>
            (reg?.email &&
              contact?.emails?.some(
                (email) => email?.address?.toLowerCase() === reg?.email,
              )) ||
            (reg?.phone &&
              contact?.phones?.some(
                (phone) => phone?.number?.replace(/\D/g, "") === reg?.phone,
              )),
        );

        if (isRegistered) {
          acc.existed.push({
            contact_name: contactName,
            profile_id: isRegistered?.profile_id,
            mutual_contacts_count: 0, // (CHRIS) TO-DO: need to return the mutual contacts count
          });
        } else {
          const contact_info = contact?.emails?.[0]?.address
            ? { type: "email", contact: contact?.emails?.[0]?.address }
            : contact?.phones?.[0]?.number
              ? { type: "phone", contact: contact?.phones?.[0]?.number }
              : null;

          if (contact_info) {
            acc.not_existed.push({
              contact_name: contactName,
              contact_info,
            });
          }
        }

        return acc;
      },
      { existed: [], not_existed: [] },
    );

    return res.send({ data: result, error: null });
  } catch (error) {
    logError({
      context: "Error to store and check the contacts",
      error,
      user_id,
    });
    return res.status(500).send({ data: null, error: "Failed to store" });
  }
});

usersRouter.post("/uploadContacts", authUser, async (req, res) => {
  const { contacts } = req.body;
  if (!contacts || !contacts.length) {
    return res.status(400).json({ error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const transformedContacts = contacts.flatMap((contact) => {
    const contactName = contact?.name?.display;
    const contactId = contact?.contactId;

    const phoneEntries = contact.phones
      ? contact.phones.map((phone) => ({
          user_id,
          contact_name: contactName,
          contact_type: "phone",
          contact_value: phone.number.replace(/\D/g, ""),
          contact_id: contactId,
        }))
      : [];

    const emailEntries = contact.emails
      ? contact.emails.map((email) => ({
          user_id,
          contact_name: contactName,
          contact_type: "email",
          contact_value: email.address.toLowerCase(),
          contact_id: contactId,
        }))
      : [];

    return [...phoneEntries, ...emailEntries];
  });

  // Deduplicate using a Map
  const uniqueContactsMap = new Map();

  transformedContacts.forEach((contact) => {
    const key = `${contact.contact_type}-${contact.contact_value}`; // Unique composite key
    if (!uniqueContactsMap.has(key)) {
      uniqueContactsMap.set(key, contact); // Add contact if key is not already present
    }
  });

  // Convert the Map back to an array
  const uniqueContacts = Array.from(uniqueContactsMap.values());

  try {
    const { error } = await supabase.from("contacts").upsert(uniqueContacts, {
      onConflict: ["user_id", "contact_value"], // Specify the columns for conflict resolution
    });

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to upload the contacts",
      error,
      user_id,
    });
    return res.status(500).json({ error: "Failed to upload contacts" });
  }
});

usersRouter.post("/getContacts", authUser, async (req, res) => {
  const { profile_id } = req.body;
  if (!profile_id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const { data: userOnboarding } = await supabase
    .from("users")
    .select("onboarding_status")
    .eq("id", user_id)
    .single();

  const is_onboarding =
    !userOnboarding ||
    (userOnboarding.onboarding_status !== "done" &&
      userOnboarding.onboarding_status !== "skipped");

  try {
    const { data, error } = await supabase
      .from("contacts")
      .select("*")
      .eq("user_id", user_id);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    if (!data || data.length === 0) {
      return res.status(204).json({ data: { existed: [], not_existed: [] } });
    }

    const contact_values = data.map((contact) => contact.contact_value);
    const { data: contactDetails, error: contactError } = await supabase.rpc(
      "get_user_detail_from_contact_v1",
      { selected_profile_id: profile_id, is_onboarding, contact_values },
    );

    if (contactError) {
      throw wrappedSupabaseError(contactError);
    }

    const result = data.reduce(
      (acc, contact) => {
        const contactName = contact?.contact_name || "Unknown"; // Default to "Unknown" if contact name is missing

        // Check if contact is registered by matching email or phone
        const isRegistered = contactDetails?.find(
          (reg) =>
            (contact?.contact_type === "email" &&
              contact?.contact_value === reg?.email) ||
            (contact?.contact_type === "phone" &&
              contact?.contact_value === reg?.phone),
        );

        if (isRegistered) {
          acc.existed.push({
            contact_name: contactName,
            profile_id: isRegistered.profile_id,
            mutual_contacts_count: 0, // (CHRIS) TO-DO: need to return the mutual contacts count
          });
        } else {
          acc.not_existed.push({
            contact_name: contactName,
            contact_info: {
              type: contact?.contact_type,
              contact: contact?.contact_value,
            },
          });
        }

        return acc;
      },
      { existed: [], not_existed: [] },
    );

    return res.status(200).json({ data: result });
  } catch (error) {
    logError({
      context: "Error to fetch and check the contacts",
      error,
      user_id,
      profile_id,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch and check the contacts" });
  }
});

usersRouter.post("/invite", authUser, async (req, res) => {
  const { to_phonenumber, to_email } = req.body;
  if (!to_phonenumber && !to_email) {
    return res.status(400).json({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select("avatar_url, username, display_name")
    .eq("user_id", user_id)
    .neq("visibility", "archived")
    .neq("visibility", "hidden")
    .limit(1)
    .single();

  if (profileError || !profile) {
    return res.status(500).json({ error: "Could not find the profile" });
  }
  const { avatar_url, username, display_name } = profile;
  try {
    if (to_phonenumber) {
      const smsContent = {
        from_name: display_name,
        to_phonenumber: to_phonenumber.replace(/[^\d+]/g, ""),
      };
      const resp = await sendInviteSMS(smsContent);
      if (resp?.status === "error") {
        throw new Error(resp.error || "SMS sending failed");
      }
    } else if (to_email) {
      const emailContent = {
        from_name: display_name,
        avatar_url,
        from_url: `https://www.butterflies.ai/users/${username}`,
        to_email,
      };
      const resp = await sendInviteEmail(emailContent);
      if (!resp?.success) {
        throw new Error(resp.message || "Email sending failed");
      }
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to invite contact",
      error,
      user_id,
      to_email,
      to_phonenumber,
    });
    return res.status(500).json({ data: null, error: "Failed to invite" });
  }
});

usersRouter.post("/getReportReasons", async (req, res) => {
  const { type, parent_id } = req.body;
  if (!type) {
    return res.status(400).json({ data: null, error: "Invalid content error" });
  }

  try {
    let query;
    if (parent_id) {
      query = supabase
        .from("report_reasons")
        .select("id, value, title")
        .eq("is_active", true)
        .eq("parent_id", 1)
        .contains("type", [type])
        .order("id");
    } else {
      query = supabase
        .from("report_reasons")
        .select("id, value, title")
        .eq("is_active", true)
        .is("parent_id", null)
        .contains("type", [type])
        .order("id");
    }
    const { data, error } = await query;

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get report reasons",
      error,
      type,
      parent_id,
    });
    return res
      .status(500)
      .json({ data: null, error: "Failed to get report reasons" });
  }
});

usersRouter.post("/getModelsLLM", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { data, error } = await supabase
      .from("llm_models")
      .select("label, value");

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get llm models",
      error,
    });
    return res
      .status(500)
      .json({ data: null, error: "Failed to get llm models" });
  }
});

usersRouter.post("/sendPokesRefreshedNotification", async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res
      .status(400)
      .send({ data: null, error: "Profile ID is required" });
  }

  // Send push notification for XP received
  const { data: receiverProfile } = await supabase
    .from("profiles")
    .select("username")
    .eq("id", profile_id)
    .single();

  try {
    const { error: insertNotificationError } = await supabase
      .from("notifications")
      .insert({
        profile_id,
        source_type: "pokes_refreshed",
        source_id: profile_id,
        title: "Pokes refreshed",
        text: "Your pokes have been refreshed!",
        path: `/users/${receiverProfile?.username}`,
      });

    if (insertNotificationError) {
      const error = wrappedSupabaseError(insertNotificationError);
      logError({
        context:
          "sendPokesRefreshedNotification: failed to insert notification",
        error,
      });
      throw error;
    }

    return res.send({
      data: "Notification created successfully",
      error: null,
    });
  } catch (error) {
    logError({
      context: "sendPokesRefreshedNotification failed",
      error,
    });
    return res.status(500).send({ data: null, error: "Internal server error" });
  }
});

module.exports = {
  usersRouter,
  getTopAccounts,
  getBotBot,
};
