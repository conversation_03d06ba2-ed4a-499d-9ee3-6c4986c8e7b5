{"last_node_id": 30, "last_link_id": 17, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [515, 672], "size": {"0": 400, "1": 200}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 6}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [3, 14], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(worst quality), (low quality), (normal quality), lowres, normal quality"]}, {"id": 8, "type": "VAEDecode", "pos": [1430, 130], "size": {"0": 210, "1": 46}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 26, "type": "UltralyticsDetectorProvider", "pos": [100, 594], "size": {"0": 315, "1": 78}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [15], "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 30, "type": "LoraLoaderModelOnly", "pos": [515, 130], "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 17}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "shape": 3}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["NSFWFilter.safetensors", -1]}, {"id": 5, "type": "EmptyLatentImage", "pos": [100, 358], "size": {"0": 315, "1": 106}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [4], "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 4]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [30, 139], "size": [399.1796875, 159.4453125], "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [10, 17], "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [5, 6, 11], "shape": 3}, {"name": "VAE", "type": "VAE", "links": [8, 12], "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["realvisxlV40_v40LightningBakedvae.safetensors"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1015, 130], "size": {"0": 315, "1": 262}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 2}, {"name": "negative", "type": "CONDITIONING", "link": 3}, {"name": "latent_image", "type": "LATENT", "link": 4}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [840897771067576, "randomize", 5, 1.5, "euler", "karras", 1]}, {"id": 24, "type": "SaveImage", "pos": [2359, -35], "size": [1015.41015625, 1110.1953125], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 16}], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 23, "type": "FaceDetailer", "pos": [1741, 136], "size": {"0": 506.4000244140625, "1": 880}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 9}, {"name": "model", "type": "MODEL", "link": 10}, {"name": "clip", "type": "CLIP", "link": 11}, {"name": "vae", "type": "VAE", "link": 12}, {"name": "positive", "type": "CONDITIONING", "link": 13}, {"name": "negative", "type": "CONDITIONING", "link": 14}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 15}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [16], "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": null, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": null, "shape": 6}, {"name": "mask", "type": "MASK", "links": null, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null, "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "links": null, "shape": 6}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 1200, 1032408465616329, "randomize", 4, 1.5, "euler", "karras", 0.5, 0, true, true, 0.5, 10, 3, "center-1", 0, 0.93, 0, 0.7000000000000001, "False", 50, "", 1, false, 10]}, {"id": 6, "type": "CLIPTextEncode", "pos": [515, 342], "size": {"0": 400, "1": 200}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2, 13], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["woman 18 years old, red head, scarlett summers, blue eyes"]}], "links": [[1, 30, 0, 3, 0, "MODEL"], [2, 6, 0, 3, 1, "CONDITIONING"], [3, 7, 0, 3, 2, "CONDITIONING"], [4, 5, 0, 3, 3, "LATENT"], [5, 4, 1, 6, 0, "CLIP"], [6, 4, 1, 7, 0, "CLIP"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [9, 8, 0, 23, 0, "IMAGE"], [10, 4, 0, 23, 1, "MODEL"], [11, 4, 1, 23, 2, "CLIP"], [12, 4, 2, 23, 3, "VAE"], [13, 6, 0, 23, 4, "CONDITIONING"], [14, 7, 0, 23, 5, "CONDITIONING"], [15, 26, 0, 23, 6, "BBOX_DETECTOR"], [16, 23, 0, 24, 0, "IMAGE"], [17, 4, 0, 30, 0, "MODEL"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}