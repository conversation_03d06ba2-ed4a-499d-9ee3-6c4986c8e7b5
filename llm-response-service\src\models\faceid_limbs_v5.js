function faceIdLimbsV5Prompt({
  prompt,
  seed,
  model = "photogasm",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 20,
        cfg: 7,
        sampler_name: "euler",
        scheduler: "karras",
        denoise: 1,
        model: ["31", 0],
        positive: ["72", 0],
        negative: ["72", 1],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${
          nsfw ? "" : ", (nudity, nsfw)"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    10: {
      inputs: {
        lora_name: "ip-adapter-faceid-plusv2_sd15_lora.safetensors",
        strength_model: 0.2,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    12: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sd15.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    13: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    25: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    27: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["25", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["10", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    31: {
      inputs: {
        weight: 0.5,
        weight_faceidv2: 3,
        weight_type: "ease in",
        combine_embeds: "concat",
        start_at: 0.2,
        end_at: 1,
        embeds_scaling: "V only",
        model: ["30", 0],
        ipadapter: ["12", 0],
        image: ["27", 0],
        clip_vision: ["13", 0],
        insightface: ["32", 0],
      },
      class_type: "IPAdapterFaceID",
      _meta: {
        title: "IPAdapter FaceID",
      },
    },
    32: {
      inputs: {
        provider: "CUDA",
      },
      class_type: "IPAdapterInsightFaceLoader",
      _meta: {
        title: "IPAdapter InsightFace Loader",
      },
    },
    34: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    36: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["34", 0],
        image: ["8", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    37: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 4,
        segs: ["36", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    39: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 700,
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 4,
        cfg: 5,
        sampler_name: "euler",
        scheduler: "karras",
        denoise: 0.5,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["8", 0],
        segs: ["37", 0],
        model: ["31", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
      },
      class_type: "DetailerForEach",
      _meta: {
        title: "Detailer (SEGS)",
      },
    },
    45: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 11,
        cfg: 5,
        sampler_name: "euler",
        scheduler: "karras",
        denoise: 1,
        model: ["31", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    47: {
      inputs: {
        samples: ["45", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    49: {
      inputs: {
        detect_hand: "disable",
        detect_body: "enable",
        detect_face: "disable",
        resolution: 512,
        image: ["47", 0],
      },
      class_type: "OpenposePreprocessor",
      _meta: {
        title: "OpenPose Pose",
      },
    },
    52: {
      inputs: {
        control_net_name: "control_v11p_sd15_openpose_fp16.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    72: {
      inputs: {
        strength: 2.4,
        start_percent: 0,
        end_percent: 0.25,
        positive: ["6", 0],
        negative: ["7", 0],
        control_net: ["52", 0],
        image: ["49", 0],
      },
      class_type: "ControlNetApplyAdvanced",
      _meta: {
        title: "Apply ControlNet (Advanced)",
      },
    },
    73: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["39", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { faceIdLimbsV5Prompt };
