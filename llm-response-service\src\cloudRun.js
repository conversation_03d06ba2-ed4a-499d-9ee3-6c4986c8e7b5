const { ServicesClient } = require("@google-cloud/run").v2;

const client = new ServicesClient();

async function updateScaling({ minScale, maxScale }) {
  const serviceName = "butterflies-api-bots";
  const project = "butterflies-ai";
  const location = "us-central1";

  const serviceFullName = client.servicePath(project, location, serviceName);

  try {
    // Fetch the current service configuration
    const [service] = await client.getService({ name: serviceFullName });

    // Ensure the template and scaling objects exist
    if (!service.template || !service.template.scaling) {
      throw new Error("Service template or scaling configuration is missing");
    }

    // Update the minInstanceCount and maxInstanceCount
    service.template.scaling.minInstanceCount = minScale; // Set your desired min instances
    service.template.scaling.maxInstanceCount = maxScale; // Set your desired max instances

    // Remove the revision name to allow Cloud Run to auto-generate a new unique name
    delete service.template.revision;

    const request = {
      service,
      updateMask:
        "template.scaling.minInstanceCount,template.scaling.maxInstanceCount",
    };

    // Send the update request
    const [operation] = await client.updateService(request);
    await operation.promise();
    console.log("Service scaling updated successfully");
  } catch (error) {
    console.error("Failed to update service scaling:", error);
  }
}

module.exports = {
  updateScaling,
};
