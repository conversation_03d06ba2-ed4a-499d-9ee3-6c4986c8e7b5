{"last_node_id": 50, "last_link_id": 113, "nodes": [{"id": 30, "type": "LoraLoaderModelOnly", "pos": [-296, 17], "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 43}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [45], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["NSFWFilter.safetensors", -1]}, {"id": 7, "type": "CLIPTextEncode", "pos": [145, 596], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6, 62], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cross-eyed,sketches, (worst quality), (low quality), (normal quality), lowres, normal quality, bad anatomy, DeepNegative, facing away, tilted head, {Multiple people}, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worstquality, low quality, normal quality, jpegartifacts, signature, watermark, username, blurry, bad feet, cropped, poorly drawn hands, poorly drawn face, mutation, deformed, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, extra fingers, fewer digits, extra limbs, extra arms,extra legs, malformed limbs, fused fingers, too many fingers, long neck, cross-eyed,mutated hands, polar lowres, bad body, bad proportions, gross proportions, text, error, missing fingers, missing arms, missing legs, extra digit, extra arms, extra leg, extra foot, (repeating hair)"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [643, 564], "size": {"0": 315, "1": 106}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 42, "type": "UltralyticsDetectorProvider", "pos": [1065, 573], "size": {"0": 315, "1": 78}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [66], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 8, "type": "VAEDecode", "pos": [1128, -123], "size": {"0": 210, "1": 46}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 30}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [58], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 31, "type": "LoraLoaderModelOnly", "pos": [79, -8], "size": {"0": 315, "1": 82}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 45}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [69, 80], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["lcm.safetensors", 1]}, {"id": 43, "type": "SaveImage", "pos": [2176, -155], "size": [736.5001562499983, 859.8430440063471], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 78}], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 35, "type": "CLIPSetLastLayer", "pos": [-273, 409], "size": {"0": 315, "1": 58}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 54}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-673, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [43], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [5, 54, 110, 113], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [30, 63], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["photogasm.safetensors"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [170, 265], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 113}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [61, 104], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["purple hair, woman, holding deck of cards, magician, wearing top hat, magic, dramatic, close up"]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [621, -117], "size": {"0": 315, "1": 262}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 69}, {"name": "positive", "type": "CONDITIONING", "link": 104}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [3731449500, "fixed", 5, 1.6, "dpmpp_2s_ancestral", "karras", 1]}, {"id": 37, "type": "FaceDetailer", "pos": [1536, -56], "size": {"0": 506.*************, "1": 880}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 58}, {"name": "model", "type": "MODEL", "link": 80, "slot_index": 1}, {"name": "clip", "type": "CLIP", "link": 110, "slot_index": 2}, {"name": "vae", "type": "VAE", "link": 63, "slot_index": 3}, {"name": "positive", "type": "CONDITIONING", "link": 61}, {"name": "negative", "type": "CONDITIONING", "link": 62, "slot_index": 5}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 66, "slot_index": 6}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "image", "type": "IMAGE", "links": [78], "shape": 3, "slot_index": 0}, {"name": "cropped_refined", "type": "IMAGE", "links": null, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": null, "shape": 6}, {"name": "mask", "type": "MASK", "links": null, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null, "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "links": null, "shape": 6}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 768, 1111880837726787, "fixed", 4, 1.6, "dpmpp_2s_ancestral", "karras", 0.5, 5, true, true, 0.5, 10, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, "", 1, false, 20]}], "links": [[2, 5, 0, 3, 3, "LATENT"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [30, 4, 2, 8, 1, "VAE"], [43, 4, 0, 30, 0, "MODEL"], [45, 30, 0, 31, 0, "MODEL"], [54, 4, 1, 35, 0, "CLIP"], [58, 8, 0, 37, 0, "IMAGE"], [61, 6, 0, 37, 4, "CONDITIONING"], [62, 7, 0, 37, 5, "CONDITIONING"], [63, 4, 2, 37, 3, "VAE"], [66, 42, 0, 37, 6, "BBOX_DETECTOR"], [69, 31, 0, 3, 0, "MODEL"], [78, 37, 0, 43, 0, "IMAGE"], [80, 31, 0, 37, 1, "MODEL"], [104, 6, 0, 3, 1, "CONDITIONING"], [110, 4, 1, 37, 2, "CLIP"], [113, 4, 1, 6, 0, "CLIP"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}