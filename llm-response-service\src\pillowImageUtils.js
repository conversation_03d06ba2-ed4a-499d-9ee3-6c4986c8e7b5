const { logError, logInfo } = require("./logUtils");
const {
  addGeneratedStoryPost,
} = require("./pillow/stories/addGeneratedStoryPost");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
// const dayjs = require("dayjs");

const PillowGenerationType = {
  SNAP: "pillowSnap",
  STORY: "pillowStory",
};

function isPillowGenerationType(generationType) {
  return (
    generationType === PillowGenerationType.SNAP ||
    generationType === PillowGenerationType.STORY
  );
}

async function onPillowImageGenerationTaskFailed({
  task_id,
  workflow_payload,
  error_message,
}) {
  const error = new Error(error_message);
  logError({
    context: "pillow image failed to generate",
    error,
    task_id,
    workflow_payload,
  });
}

async function onPillowImageGenerationTaskSucceeded({
  task_id,
  workflow_payload,
  imageUrls,
  blurhash,
}) {
  const { generationType } = workflow_payload;

  if (generationType === PillowGenerationType.SNAP) {
    const {
      pillowCaption,
      pillowWorkflowRunId,
      pillowUserProfileId,
      pillowClientId,
      pillowSenderProfileId,
      pillowInResponseTo,
      imageDescription,
    } = workflow_payload;

    await sendSnapResponseWithImage({
      taskId: task_id,
      imageUrls,
      blurhash,
      pillowCaption,
      pillowWorkflowRunId,
      pillowUserProfileId,
      pillowClientId,
      pillowSenderProfileId,
      pillowInResponseTo,
      imageDescription,
    });
    sendPushNotificationAboutSnap({
      pillowClientId,
      pillowUserProfileId,
      pillowSenderProfileId,
    });
  } else if (generationType === PillowGenerationType.STORY) {
    logInfo({
      context: "**************** pillow story image generated",
      task_id,
      workflow_payload,
      imageUrls,
      blurhash,
    });
    addGeneratedStoryPost({
      task_id,
      workflow_payload,
      imageUrls,
      blurhash,
    });
  }
}

const sendPushNotificationViaExpo = async ({
  expoNotificationPayload,
  pushToken,
}) => {
  logInfo({
    context: "sendPushNotificationViaExpo",
    message: "sending push notification",
    pushToken,
    expoNotificationPayload,
  });

  let messages = [
    {
      to: pushToken,
      ...expoNotificationPayload,
    },
  ];

  const tickets = await expo.sendPushNotificationsAsync(messages);
  const [ticket] = tickets;

  logInfo({
    context: "sendPushNotificationViaExpo",
    message: "sent push notification",
    ticket,
  });

  // TODO: we're supposed to store the tickets and check for errors after some time, see docs
  // https://github.com/expo/expo-server-sdk-node

  // This is how Expo wants us to find out about errors and remove invalid device token
};

const { Expo } = require("expo-server-sdk");

// Create a new Expo SDK client
// optionally providing an access token if you have enabled push security
const expo = new Expo({
  accessToken: "ctHOC0cvGbVnfBmNPnGGXzZzcXgEZAr6x2lpdnAJ",
});

async function sendSnapResponseWithImage({
  taskId,
  pillowUserProfileId,
  imageUrls,
  pillowCaption,
  pillowWorkflowRunId,
  blurhash,
  pillowSenderProfileId,
  pillowInResponseTo,
  imageDescription,
}) {
  if (!imageUrls.length || !imageUrls[0].length) {
    throw new Error("No image URLs provided");
  }

  if (!pillowUserProfileId) {
    throw new Error("No pillowUserProfileId provided");
  }

  if (blurhash && Array.isArray(blurhash) && blurhash[0]) {
    blurhash = blurhash[0];
  }

  const { data: newSnap, error: insertError } = await supabase
    .from("snaps")
    .insert({
      image_url: imageUrls[0],
      caption: pillowCaption,
      blurhash,
      user_profile_id: pillowUserProfileId,
      sender_profile_id: pillowSenderProfileId,
      in_response_to_snap_id: pillowInResponseTo?.snapId,
      image_description: imageDescription,
      task_id: taskId ? taskId : null,
      workflow_run_id: pillowWorkflowRunId,
    })
    .single();

  if (insertError) {
    logError({
      context: "**** inserting a 'snaps' record for bot -> user snap",
      error: wrappedSupabaseError(insertError),
      pillowUserProfileId,
      pillowSenderProfileId,
      snapId: pillowInResponseTo?.snapId,
      imageUrls,
    });
    return null;
  }

  return newSnap;
}

const sendPushNotificationAboutSnap = async ({
  pillowClientId, // either this or pillowUserProfileId is required
  pillowUserProfileId,
  pillowSenderProfileId,
}) => {
  logInfo({
    context: "**** sendPushNotificationAboutSnap called",
    pillowClientId,
    pillowUserProfileId,
    pillowSenderProfileId,
  });

  let clientIds;

  if (pillowClientId) {
    clientIds = [pillowClientId];
  } else if (pillowUserProfileId) {
    // Fetch all clients in one batch query
    const { data: clients, error: clientsError } = await supabase
      .from("pillow_clients")
      .select("*")
      .eq("butterflies_profile_id", pillowUserProfileId);

    if (clientsError) {
      logError({
        context: "**** sendPushNotificationAboutSnap",
        message: `Failed to fetch clients for user`,
        error: wrappedSupabaseError(clientsError),
        pillowUserProfileId,
        pillowSenderProfileId,
      });
      return;
    }

    if (!clients || !clients.length) {
      logError({
        context: "**** sendPushNotificationAboutSnap",
        message: `No clients found for user`,
        pillowUserProfileId,
        pillowSenderProfileId,
      });
      return;
    }

    clientIds = clients.map((client) => client.id);
  } else {
    logError({
      message:
        "No pillowClientId or pillowUserProfile - can't send push notification",
      pillowClientId,
      pillowUserProfileId,
      pillowSenderProfileId,
    });
    return;
  }

  const { data: pushTokens, error: pushTokenError } = await supabase
    .from("pillow_push_tokens")
    .select("*")
    .in("client_id", clientIds);

  if (pushTokenError) {
    logError({
      context: "**** sendPushNotificationAboutSnap",
      message: `Failed to fetch push tokens for target`,
      error: wrappedSupabaseError(pushTokenError),
      clientIds,
      pillowUserProfileId,
      pillowSenderProfileId,
    });
    return;
  }

  if (!pushTokens || !pushTokens.length) {
    logError({
      context: "**** sendPushNotificationAboutSnap",
      message: `No push tokens found for target`,
      clientIds,
      pillowUserProfileId,
      pillowSenderProfileId,
    });
    return;
  }

  logInfo({
    context: "**** sendPushNotificationAboutSnap got push tokens",
    clientIds,
    pushTokens,
    pillowSenderProfileId,
  });

  for (const pushToken of pushTokens) {
    const users = {
      372090: "tiffany",
      11433: "payton",
      371600: "saniul",
      371581: "sulley",
      39880: "leo",
      1: "vu",
    };

    console.log(
      "attached?",
      pillowSenderProfileId,
      users[pillowSenderProfileId],
    );

    let senderName = users[pillowSenderProfileId] ?? "someone";

    logInfo({
      context: "sendPushNotificationAboutSnap",
      message: `identified sender name: ${senderName}`,
      senderName,
      pillowSenderProfileId,
    });

    // Construct a message (see https://docs.expo.io/push-notifications/sending-notifications/)
    await sendPushNotificationViaExpo({
      pushToken: pushToken.push_token,
      expoNotificationPayload: {
        sound: "default",
        title: senderName,
        body: "sent you a Snap",
        data: { withSome: "data" },
      },
    });
  }
};

module.exports = {
  sendSnapResponseWithImage,
  sendPushNotificationAboutSnap,
  PillowGenerationType,
  isPillowGenerationType,
  onPillowImageGenerationTaskFailed,
  onPillowImageGenerationTaskSucceeded,
};
