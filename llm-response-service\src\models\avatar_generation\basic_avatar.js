function basicPromptWithUpscaler({
  prompt,
  seed,
  model = "photogasm",
  width = 864,
  height = 1024,
  batch_size = 1,
  nsfw = false,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 20,
        cfg: 10,
        sampler_name: "euler",
        scheduler: "karras",
        denoise: 1,
        model: ["30", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${nsfw ? "" : "nsfw"}"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    24: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["35", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    26: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    31: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 3,
        segs: ["33", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    33: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 10,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.93,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["26", 0],
        image: ["8", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    35: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 1024,
        seed: seed ?? Math.random() * 100000000000,
        steps: 6,
        cfg: 8,
        sampler_name: "euler",
        scheduler: "normal",
        denoise: 0.5,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["8", 0],
        segs: ["31", 0],
        model: ["4", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
      },
      class_type: "DetailerForEachDebug",
      _meta: {
        title: "DetailerDebug (SEGS)",
      },
    },
  };
}

module.exports = { basicPromptWithUpscaler };
