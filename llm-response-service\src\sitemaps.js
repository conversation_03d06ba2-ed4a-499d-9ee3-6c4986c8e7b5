const express = require("express");
const { getSiteMapIndex, getSiteMap } = require("./btClient");
const redisClient = require("./redisClient");

const sitemapsRouter = express.Router();

sitemapsRouter.get("/sitemap_index", async (req, res) => {
  const response = await getSiteMapIndex(redisClient);

  res.json(response);
});

sitemapsRouter.get("/sitemap", async (req, res) => {
  const { first_letter, index } = req.query;
  if (
    first_letter == null ||
    first_letter == undefined ||
    first_letter === ""
  ) {
    const alphabet = Array.from({ length: 26 }, (_, i) =>
      String.fromCharCode(97 + i),
    );
    return res.json(alphabet);
  }
  const response = await getSiteMap(first_letter, index, redisClient);

  res.json(response);
});

module.exports = {
  sitemapsRouter,
};
