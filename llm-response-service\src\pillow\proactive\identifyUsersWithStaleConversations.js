const { logInfo } = require("../../logUtils");
const { supabase, wrappedSupabaseError } = require("../../supabaseClient");

async function identifyUsersWithStaleConversations({ bot_profile_id }) {
  const NOT_STALE_TIME_INTERVAL = 4 * 60 * 60 * 1000; // 4 hours
  const CONSIDER_TIME_INTERVAL = 4 * 24 * 60 * 60 * 1000; // 4 days

  // NOTE: THIS IS CURRENTLY VERY SIMPLISTIC:
  // * we find all users who have messaged the bot in the last XXX hours
  // * subtract users who have messaged the bot (or got messaged by the bot) in the last YYY hours
  //
  // FIXME: reimplement this with stored procedures in the database, instead of these PostgREST queries using the JS client

  const now = new Date();
  const notStaleDate = new Date(now - NOT_STALE_TIME_INTERVAL);
  const considerFromDate = new Date(now - CONSIDER_TIME_INTERVAL);

  const usersWithoutStaleConversationsSet = new Set();

  // senders who messaged bot in the last "not stale" time interval
  const {
    data: messagesReceivedInTheNotStaleInterval,
    error: messagesReceivedInTheNotStaleIntervalError,
  } = await supabase
    .schema("internal")
    .from("pillow_messages")
    .select("sender_profile_id")
    .eq("receiver_profile_id", bot_profile_id)
    .gte("created_at", notStaleDate.toISOString());
  if (messagesReceivedInTheNotStaleIntervalError) {
    throw wrappedSupabaseError(
      messagesReceivedInTheNotStaleIntervalError,
      "Error fetching messagesReceivedInTheNotStaleInterval",
    );
  }
  for (const message of messagesReceivedInTheNotStaleInterval) {
    usersWithoutStaleConversationsSet.add(message.sender_profile_id);
  }

  // receivers who got messaged by the bot in the last "not stale" time interval
  const {
    data: messagesSentInTheNotStaleInterval,
    error: messagesSentInTheNotStaleIntervalError,
  } = await supabase
    .schema("internal")
    .from("pillow_messages")
    .select("receiver_profile_id")
    .eq("sender_profile_id", bot_profile_id)
    .gte("created_at", notStaleDate.toISOString());
  if (messagesSentInTheNotStaleIntervalError) {
    throw wrappedSupabaseError(
      messagesSentInTheNotStaleIntervalError,
      "Error fetching messagesSentInTheNotStaleInterval",
    );
  }
  for (const message of messagesSentInTheNotStaleInterval) {
    usersWithoutStaleConversationsSet.add(message.receiver_profile_id);
  }

  const usersWithoutStaleConversation = Array.from(
    usersWithoutStaleConversationsSet,
  );

  logInfo({
    message: `Identified users who either messaged the bot or got messaged by the bot in the last 6 hours`,
    bot_profile_id,
    usersWithoutStaleConversation,
  });

  const { data: messagesToConsider, error: messagesToConsiderError } =
    await supabase
      .schema("internal")
      .from("pillow_messages")
      .select("sender_profile_id")
      .neq("sender_profile_id", bot_profile_id)
      .gte("created_at", considerFromDate.toISOString())
      .not("sender_profile_id", "in", `(${usersWithoutStaleConversation})`);

  if (messagesToConsiderError) {
    throw wrappedSupabaseError(
      messagesToConsiderError,
      "Error fetching messagesToConsider",
    );
  }

  const usersToConsiderSet = new Set();
  for (const message of messagesToConsider) {
    usersToConsiderSet.add(message.sender_profile_id);
  }
  const usersToConsider = Array.from(usersToConsiderSet);

  logInfo({
    message: `Identified users with stale conversations`,
    bot_profile_id,
    usersToConsider,
  });

  return { usersToConsider };
}

module.exports = {
  identifyUsersWithStaleConversations,
};
