apiVersion: apps/v1
kind: Deployment
metadata:
  name: cocoon-stage-deploy
  namespace: cocoon
  labels:
    app: cocoon-stage
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cocoon-stage
  template:
    metadata:
      labels:
        app: cocoon-stage
        app.kubernetes.io/name: cocoon-stage
    spec:
      containers:
        - name: node
          image: us-central1-docker.pkg.dev/butterflies-ai/images/cocoon:latest
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: "1250m"
              memory: 2Gi
              ephemeral-storage: "10Gi"
            limits:
              cpu: "8"
              memory: 4Gi
          livenessProbe:
            httpGet:
              path: /v1/healthz
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            failureThreshold: 10
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /v1/healthz
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            failureThreshold: 10
            timeoutSeconds: 10
          volumeMounts:
            - name: "service-account"
              mountPath: "/var/run/secret/cloud.google.com"
          env:
            - name: "GOOGLE_APPLICATION_CREDENTIALS"
              value: "/var/run/secret/cloud.google.com/butterflies-ai-092f4f8147ee.json"
            - name: SERVER_ENDPOINT
              value: "https://cocoon-stage.butterflies.ai/v1"
            - name: BOT_SERVER_ENDPOINT
              value: "https://cocoon-stage.butterflies.ai/v1"
            - name: SUPABASE_URL
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: SUPABASE_URL
            - name: SUPABASE_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: SUPABASE_SECRET_KEY
            - name: GPT_TEXT_MODEL
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: GPT_TEXT_MODEL
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: OPENAI_API_KEY
            - name: CLOUD_TASK_V1
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: CLOUD_TASK_V1
            - name: CLOUD_TASK_V1_IMAGE_GENERATION
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: CLOUD_TASK_V1_IMAGE_GENERATION
            - name: CLOUD_TASK_V1_RUN_BOTS
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: CLOUD_TASK_V1_RUN_BOTS
            - name: CLOUD_TASK_V1_IMAGE_GENERATION_UPDATE_STATUS
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: CLOUD_TASK_V1_IMAGE_GENERATION_UPDATE_STATUS
            - name: CLOUD_TASK_V1_HANDLE_IMAGE
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: CLOUD_TASK_V1_HANDLE_IMAGE
            - name: TYPESENSE_HOST
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: TYPESENSE_HOST
            - name: TYPESENSE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: TYPESENSE_API_KEY
            - name: NODE_ENV
              value: "production"
            - name: COCOON_ENV
              value: "stage"
            - name: LOG_DEBUG_ENABLED
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: LOG_DEBUG_ENABLED
            - name: STRIPE_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: STRIPE_SECRET_KEY
            - name: STRIPE_WEBHOOK_ENDPOINT_SECRET
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: STRIPE_WEBHOOK_ENDPOINT_SECRET
            - name: WEBSITE_URL
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: WEBSITE_URL
            - name: BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: cocoon-secret
                  key: BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: "opentelemetry-collector.opentelemetry.svc.cluster.local:4317"
            - name: OTEL_TRACES_EXPORTER
              value: ""
            - name: OTEL_METRICS_EXPORTER
              value: ""
            - name: OTEL_EXPORTER_OTLP_INSECURE
              value: "true"
            - name: OTEL_EXPORTER_OTLP_COMPRESSION
              value: "gzip"
            - name: OTEL_SERVICE_NAME
              value: "cocoon-stage"
      volumes:
        - name: "service-account"
          secret:
            secretName: "cocoon-iam-sa-secret"
