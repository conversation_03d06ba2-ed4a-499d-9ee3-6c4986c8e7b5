function realisticAvatarGeneration({
  prompt,
  seed,
  model = "photogasm",
  width = 864,
  height = 1024,
  batch_size = 1,
  nsfw = false,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 5,
        cfg: 1.6,
        sampler_name: "dpmpp_2s_ancestral",
        scheduler: "karras",
        denoise: 1,
        model: ["31", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${nsfw ? "" : "nsfw"}"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    31: {
      inputs: {
        lora_name: "lcm.safetensors",
        strength_model: 1,
        model: ["30", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    35: {
      inputs: {
        stop_at_clip_layer: -2,
        clip: ["4", 1],
      },
      class_type: "CLIPSetLastLayer",
      _meta: {
        title: "CLIP Set Last Layer",
      },
    },
    37: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 600,
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 2,
        cfg: 1.6,
        sampler_name: "dpmpp_2s_ancestral",
        scheduler: "karras",
        denoise: 0.5,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        bbox_threshold: 0.5,
        bbox_dilation: 10,
        bbox_crop_factor: 3,
        sam_detection_hint: "center-1",
        sam_dilation: 0,
        sam_threshold: 0.93,
        sam_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        sam_mask_hint_use_negative: "False",
        drop_size: 10,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["8", 0],
        model: ["31", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
        bbox_detector: ["42", 0],
      },
      class_type: "FaceDetailer",
      _meta: {
        title: "FaceDetailer",
      },
    },
    42: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    43: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["37", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { realisticAvatarGeneration };
