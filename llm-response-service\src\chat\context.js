function now() {
  return new Date().getTime();
}

class Context {
  constructor(request_uuid, conversationId, profileId) {
    this.verbose = false;
    this.profileId = profileId;
    this.logging = {
      context: "/sendMessage",
      request_uuid,
      conversation_id: conversationId,
      profile_id: profileId,
      verbose: false,
      operations: {},
    };
    this.opStartTimes = {};
    this.startOperation("request");
  }

  setVerbose() {
    this.verbose = true;
    this.logging.verbose = {};
  }

  initExperiments(experiments) {
    this.experiments = experiments;

    this.unLimitedQuotaExperiment = experiments.includes(
      "unlimited_quota_experiment",
    );

    this.logging.experiments = {
      unlimited_quota_experiment: this.unLimitedQuotaExperiment,
    };
  }

  setProfiles(profiles) {
    this.logging.bot_id = profiles.botConfiguration.id;
    if (this.verbose) {
      this.logging.verbose.profiles = profiles;
    }
  }

  setMessageHistory(messages) {
    if (this.verbose) {
      this.logging.verbose.history = messages;
    }
  }

  setDeviceType(deviceType) {
    this.logging.device_type = deviceType;
  }

  setImageRequested(imageRequested) {
    this.logging.image_requested = imageRequested;
  }

  startOperation(name) {
    this.opStartTimes[name] = now();
  }

  endOperation(name) {
    if (name in this.logging.operations) {
      this.logging.operations[name].latency += now() - this.opStartTimes[name];
      this.logging.operations[name].count += 1;
    } else {
      this.logging.operations[name] = {
        latency: now() - this.opStartTimes[name],
        count: 1,
      };
    }
  }

  setGenerator(generator) {
    this.logging.message_generator = generator;
  }

  setComfyTask(task) {
    if (this.verbose) {
      this.logging.verbose.comfy_task = task;
    }
  }

  startMessageGeneration() {
    this.startOperation("message_generation");
    this.logging.image_generated = false;
    if (this.verbose) {
      this.logging.verbose.generated_messages = [];
    }
  }

  registerGeneratedMessage(message) {
    if (!this.logging.first_message_latency) {
      this.logging.first_message_latency =
        now() - this.opStartTimes.message_generation;
    }
    if (message.type === "image") {
      this.logging.image_generated = true;
    }
    if (this.verbose) {
      this.logging.verbose.generated_messages.push(message);
    }
  }

  endMessageGeneration() {
    this.endOperation("message_generation");
    this.logging.duration = now() - this.opStartTimes.request;
  }

  getCleanLogging() {
    return {
      ...this.logging,
      verbose: undefined,
    };
  }
}

module.exports = {
  Context,
};
