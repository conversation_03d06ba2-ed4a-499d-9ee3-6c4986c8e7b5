const axios = require("axios");

/**
 * Recraft API client for generating images from prompts
 */

class RecraftClient {
  constructor(authToken, projectId = "e6b1cd9f-bacd-4470-b4aa-6ec74f303a26") {
    this.authToken = authToken;
    this.projectId = projectId;
    this.baseUrl = "https://api.recraft.ai";
    this.headers = {
      accept: "*/*",
      "content-type": "application/json",
      authorization: `Bear<PERSON> ${authToken}`,
      "x-client-type": "web-app.recraft.ai",
      "user-agent":
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
    };
  }

  /**
   * Submit a rendering request to generate an image from a prompt
   *
   * @param {Object} options - The options for image generation
   * @param {string} options.prompt - The text prompt to generate an image from
   * @param {string} [options.imageType='any'] - The type of image to generate
   * @param {string} [options.negativePrompt=''] - Things to avoid in the generated image
   * @param {Object} [options.userControls={}] - Additional user controls
   * @param {Object} [options.layerSize={ height: 1024, width: 1024 }] - Size of the generated image
   * @param {number} [options.randomSeed] - Random seed for reproducibility (if not provided, a random one will be used)
   * @param {string} [options.styleId='b9af225b-6b98-43d0-a4b3-16343641e852'] - Style ID to use
   * @param {number} [options.numImagesPerPrompt=1] - Number of images to generate
   * @param {string} [options.transformModel='recraftv3'] - The model to use for transformation
   * @returns {Promise<Object>} - The operation ID for polling the result
   */
  async submitRenderingRequest({
    prompt,
    imageType = "any",
    negativePrompt = "",
    userControls = {},
    layerSize = { height: 1024, width: 1024 },
    randomSeed = Math.floor(Math.random() * 1000000000),
    styleId = "b9af225b-6b98-43d0-a4b3-16343641e852",
    numImagesPerPrompt = 1,
    transformModel = "recraftv3",
  }) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/queue_recraft/prompt_to_image?project_id=${this.projectId}`,
        {
          prompt,
          image_type: imageType,
          negative_prompt: negativePrompt,
          user_controls: userControls,
          layer_size: layerSize,
          random_seed: randomSeed,
          style_id: styleId,
          num_images_per_prompt: numImagesPerPrompt,
          transform_model: transformModel,
        },
        { headers: this.headers },
      );

      return response.data;
    } catch (error) {
      console.error(
        "Error submitting rendering request:",
        error.response?.data || error.message,
      );
      throw error;
    }
  }

  /**
   * Poll for the result of a rendering operation
   *
   * @param {string} operationId - The operation ID returned from submitRenderingRequest
   * @returns {Promise<Object>} - The result of the rendering operation
   */
  async pollRenderingResult(operationId) {
    try {
      const response = await axios.get(
        `${this.baseUrl}/poll_recraft?operation_id=${operationId}`,
        { headers: this.headers },
      );

      return response.data;
    } catch (error) {
      console.error(
        "Error polling rendering result:",
        error.response?.data || error.message,
      );
      throw error;
    }
  }

  /**
   * Get the URL for a generated image
   *
   * @param {string} imageId - The image ID returned from pollRenderingResult
   * @param {string} [contentType='image/webp'] - The content type of the image
   * @returns {string} - The URL of the generated image
   */
  getImageUrl(imageId, contentType = "image/webp") {
    const encodedContentType = encodeURIComponent(contentType);
    return `${this.baseUrl}/image/${imageId}?raster_image_content_type=${encodedContentType}`;
  }

  /**
   * Generate an image and wait for the result
   *
   * @param {Object} options - The options for image generation (see submitRenderingRequest)
   * @param {number} [maxAttempts=10] - Maximum number of polling attempts
   * @param {number} [pollingInterval=2000] - Interval between polling attempts in milliseconds
   * @returns {Promise<Object>} - The result including image URLs
   */
  async generateImage(options, maxAttempts = 10, pollingInterval = 2000) {
    // Submit the rendering request
    const { operationId } = await this.submitRenderingRequest(options);

    // Poll for the result
    let attempts = 0;
    let result = null;

    while (attempts < maxAttempts) {
      attempts++;

      const pollResult = await this.pollRenderingResult(operationId);

      // Check if the result contains images
      if (pollResult.images && pollResult.images.length > 0) {
        result = pollResult;
        break;
      }

      // Wait before polling again
      await new Promise((resolve) => setTimeout(resolve, pollingInterval));
    }

    if (!result) {
      throw new Error(`Failed to generate image after ${maxAttempts} attempts`);
    }

    // Add image URLs to the result
    result.imageUrls = result.images.map((img) =>
      this.getImageUrl(img.image_id),
    );

    return result;
  }
}

module.exports = RecraftClient;
