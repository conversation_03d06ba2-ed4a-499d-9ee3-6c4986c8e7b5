const express = require("express");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const { loggingInfo } = require("./logging");
const {
  logWarn,
  logError,
  getOrGenerateSignedUrl,
  wrappedSupabaseError,
} = require("./utils");
const { authUser } = require("./middleware");
const { openaiModeration } = require("./llm");
const vision = require("@google-cloud/vision");
const { default: axios } = require("axios");
const redisClient = require("./redisClient");
require("dotenv").config();

const HIVE_NSFW_KEY = process.env.HIVE_NSFW_KEY ?? null;
const HIVE_API = "https://api.thehive.ai/api/v2/task/sync";

app.post("/reviewProfiles", authUser, async (req, res) => {
  const {
    profile_id,
    user_role,
    search = "",
    limit = 15,
    offset = 0,
  } = req.body;

  if (!profile_id) {
    return res.sendStatus(400);
  }
  let [{ data: count }, { data }] = await Promise.all([
    supabase.rpc("get_admin_review_profile_moderator_count_v2", {
      search_param: search.toLowerCase(),
      user_role: user_role,
      moderator_id: profile_id,
    }),
    supabase.rpc("get_admin_review_profile_moderator_v2", {
      search_param: search.toLowerCase(),
      limit_param: limit,
      offset_param: offset,
      user_role: user_role,
      moderator_id: profile_id,
    }),
  ]);

  loggingInfo("moderation", {
    search_param: search,
    limit_param: limit,
    offset_param: offset,
    user_role: user_role,
    moderator_id: profile_id,
    count: count,
    type: "profile_review",
  });

  return res.json({ count: count, data: data });
});

app.post("/reviewPosts", authUser, async (req, res) => {
  const {
    profile_id,
    user_role,
    search = "",
    limit = 15,
    offset = 0,
  } = req.body;

  if (!profile_id) {
    return res.sendStatus(400);
  }
  const { data, error } = await supabase.rpc(
    "get_admin_review_posts_moderator_v1",
    {
      search_param: search.toLowerCase(),
      limit_param: limit,
      offset_param: offset,
      user_role: user_role,
      moderator_id: profile_id,
    },
  );
  if (error) {
    logError({
      context: "reviewPosts - posts",
      error: wrappedSupabaseError(error),
    });
    return res.sendStatus(500);
  }

  return res.json({ data });
});

app.post("/reviewPostsCount", authUser, async (req, res) => {
  const { profile_id, user_role, search = "" } = req.body;

  if (!profile_id) {
    return res.sendStatus(400);
  }
  const { data: count, error } = await supabase.rpc(
    "get_admin_review_posts_count_moderator_v1",
    {
      search_param: search.toLowerCase(),
      user_role: user_role,
      moderator_id: profile_id,
    },
  );

  if (error) {
    logError({
      context: "reviewPostsCount - count",
      error: wrappedSupabaseError(error),
    });
    return res.sendStatus(500);
  }

  return res.json({ count: count });
});

app.get("/clones_not_reviewed", async (req, res) => {
  const { data, error } = await supabase.rpc("clones_not_reviewed");
  for (let item of data) {
    item["front"] = await getOrGenerateSignedUrl(item["front"]);
    item["left"] = await getOrGenerateSignedUrl(item["left"]);
    item["right"] = await getOrGenerateSignedUrl(item["right"]);
  }
  return res.json({ data: data, error: error });
});

app.get("/test", async (req, res) => {
  let imageUrl = req.query.img;
  let id = req.query.id ?? null;
  let context = req.query.context ?? "test";
  let response = await autoModerate({ imageUrl, id, context });
  return res.json(response);
});

app.get("/openai_test", async (req, res) => {
  let imageUrl = req.query.img;
  let response = await moderateByOpenAI({ imageUrl, context: "test" });
  return res.json(response);
});

app.get("/hive_test", async (req, res) => {
  let imageUrl = req.query.img;
  let response = await autoModerate({ imageUrl, context: "test" });
  return res.json(response);
});

app.get("/vision_test", async (req, res) => {
  let imageUrl = req.query.img;
  let response = await moderateByGoogleVision({ imageUrl, context: "test" });
  return res.json(response);
});

async function skipModerate({
  imageUrl,
  id = null,
  context = null,
  ...others
}) {
  const jsonPayload = {
    image_url: imageUrl,
    id,
    context,
    skipped: true,
    ...(others ?? {}),
  };
  loggingInfo("auto_moderation", jsonPayload);
}

async function autoModerate({
  imageUrl,
  id = null,
  context = null,
  services = ["HIVE", "OpenAI", "GoogleVision"],
  ...others
}) {
  for (const service of services) {
    try {
      if (service === "HIVE") {
        return await moderateByHive({ imageUrl, id, context, ...others });
      } else if (service === "OpenAI") {
        return await moderateByOpenAI({ imageUrl, id, context, ...others });
      } else if (service === "GoogleVision") {
        return await moderateByGoogleVision({
          imageUrl,
          id,
          context,
          ...others,
        });
      }
    } catch (error) {
      logWarn({ context: `Moderation failed with ${service}:`, error });
    }
  }
  logError({
    context: "All moderation services failed.",
    imageUrl,
  });
  throw new Error("All moderation services failed.");
}

function getLikelyString(score) {
  if (score >= 0.9) {
    return "VERY_LIKELY";
  } else if (score >= 0.5) {
    return "LIKELY";
  } else if (score >= 0.1) {
    return "POSSIBLE";
  } else if (score >= 0.01) {
    return "UNLIKELY";
  } else {
    return "VERY_UNLIKELY";
  }
}

function getScoreFromString(likely) {
  if (likely === "VERY_LIKELY") {
    return 0.9;
  } else if (likely === "LIKELY") {
    return 0.5;
  } else if (likely === "POSSIBLE") {
    return 0.1;
  } else if (likely === "UNLIKELY") {
    return 0.05;
  } else {
    return 0;
  }
}

function getNsfwString(json) {
  if (json.general_nsfw >= 0.05) {
    return "nsfw";
  }

  if (
    json.general_suggestive + json.general_nsfw >= 0.98 &&
    (checkThreshold(json, ["yes_breast", "yes_butt", "yes_genitals"], 0.2) ||
      checkThreshold(json, ["yes_sex_toy"], 0.6) ||
      checkThreshold(json, ["yes_sexual_activity", "yes_sexual_intent"], 0.1))
  ) {
    return "nsfw";
  }

  return "normal";
}

function checkThreshold(jsonObj, keys, threshold = 0.1) {
  for (let key of keys) {
    if (key in jsonObj && (jsonObj[key] || 0) >= threshold) {
      return true;
    }
  }
  return false;
}

function getNsflReason(json) {
  let underAge =
    (json.yes_pre_teen || 0) + (json.yes_child_present || 0) >= 0.4 ||
    (json.yes_child_present || 0) >= 0.1;

  if (underAge && json.nsfw === "nsfw") {
    return "underage-nsfw";
  }

  let substance = [
    "illicit_injectables",
    "yes_alcohol", //.9
    "yes_drinking_alcohol",
    "yes_marijuana", //.9
    "yes_pills",
    "yes_smoking", //.5
    "medical_injectables", //check
  ];

  if (underAge && checkThreshold(json, substance, 0.9)) {
    return "underage-substance";
  }

  let harm = [
    "yes_self_harm",
    "noose",
    "hanging",
    "human_corpse", // .2
    "yes_emaciated_body", // .9
  ];

  if (underAge && checkThreshold(json, harm, 0.9)) {
    return "underage-harm";
  }

  let violence = [
    "gun_in_hand", //.2
    "knife_in_hand", //.2
    "yes_fight",
    "very_bloody", //.2
  ];

  if (underAge && checkThreshold(json, violence, 0.8)) {
    return "underage-violence";
  }

  let nudity = [
    "yes_breast", //.1
    "yes_butt", //.05
    "yes_female_nudity", // .1
    "yes_male_nudity", // .05
    "yes_genitals", //.05
    "yes_sex_toy", // .1
    "yes_sexual_activity", // .1
    "yes_sexual_intent", // .5
  ];

  if (underAge && checkThreshold(json, nudity, 0.7)) {
    return "underage-nudity";
  }

  if (underAge && checkThreshold(json, ["yes_bulge"], 0.99)) {
    return "underage-bulge";
  }

  let underwear = [
    "yes_male_underwear", // .5
    "yes_female_underwear", // .5
    "yes_bra", //.2
    "yes_negligee", // .8
    "yes_panties",
  ];

  if (underAge && checkThreshold(json, underwear, 0.8)) {
    return "underage-underwear";
  }

  if (underAge && checkThreshold(json, ["general_suggestive"], 0.95)) {
    return "underage-too-racy";
  }

  let animal = ["yes_animal_abuse"];

  if (checkThreshold(json, animal, 0.9)) {
    return "animal_abuse";
  }

  if (
    (json.yes_kkk || 0) >= 0.9 ||
    (json.yes_nazi || 0) >= 0.2 ||
    (json.yes_confederate || 0) >= 0.9 ||
    (json.yes_terrorist || 0) >= 0.9
  ) {
    return "hate";
  }

  //TODO: yes_religious_icon: too noisy
  return null;
}

async function moderateByHive({
  imageUrl,
  id = null,
  context = null,
  ...others
}) {
  const startTime = Date.now();
  const imageFile = imageUrl.split("/").pop();
  let responseData = await redisClient.get(imageFile);
  let hiveService = "hive";
  if (responseData == null) {
    let response = await axios.post(
      HIVE_API,
      {
        url: imageUrl,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: HIVE_NSFW_KEY,
        },
      },
    );
    responseData = response.data;
    await redisClient.set(imageFile, JSON.stringify(responseData), {
      EX: 30 * 60, // 30 minutes,
    });
  } else {
    responseData = JSON.parse(responseData);
    hiveService = "hive-cache";
  }

  const classes = responseData.status[0].response.output[0].classes;
  const nsfwClasses = classes.filter(
    ({ class: className, score }) =>
      (!className.startsWith("no_") &&
        !className.includes("not") &&
        score >= 0.01) ||
      className.startsWith("general_"),
  );

  const result = nsfwClasses.map(({ class: className, score }) => ({
    class: className,
    score,
  }));
  const json = result.reduce((acc, { class: className, score }) => {
    acc[className] = score;
    return acc;
  }, {});

  json.adult = getLikelyString(json.general_nsfw);
  json.racy = getLikelyString(json.general_suggestive + json.general_nsfw);
  json.nsfw = getNsfwString(json);
  json.nsfw_score = json.general_nsfw;
  let nsflReason = getNsflReason(json);
  json.nsfl = nsflReason ? true : false;
  if (json.nsfl) {
    json.nsfl_reason = nsflReason;
  }

  const jsonPayload = {
    raw_response: JSON.stringify(responseData),
    image_url: imageUrl,
    id,
    context,
    result: json,
    safe_search_detection: JSON.stringify(json),
    service: hiveService,
    duration: Date.now() - startTime,
    ...(others ?? {}),
  };
  loggingInfo("auto_moderation", jsonPayload);

  return json;
}

async function moderateByGoogleVision({
  imageUrl,
  id = null,
  context = null,
  ...others
}) {
  const startTime = Date.now();
  const clientVision = new vision.ImageAnnotatorClient();
  const [result] = await clientVision.annotateImage({
    image: { source: { imageUri: imageUrl } },
    features: [{ type: "SAFE_SEARCH_DETECTION" }],
  });
  const json = result.safeSearchAnnotation;
  json.nsfw =
    json.adult === "LIKELY" || json.adult === "VERY_LIKELY" ? "nsfw" : "normal";
  json.nsfw_score = getScoreFromString(json.adult);

  const jsonPayload = {
    raw_response: JSON.stringify(result),
    image_url: imageUrl,
    id,
    context,
    result: json,
    safe_search_detection: JSON.stringify(json),
    service: "vision",
    duration: Date.now() - startTime,
    ...(others ?? {}),
  };
  loggingInfo("auto_moderation", jsonPayload);

  return json;
}

async function moderateByOpenAI({
  imageUrl,
  id = null,
  context = null,
  ...others
}) {
  const startTime = Date.now();
  let response = await openaiModeration(null, imageUrl);
  const classes = response.results[0].category_scores;
  const json = Object.entries(classes)
    .filter(([category, score]) => score >= 0.01 || category === "sexual")
    .reduce((acc, [category, score]) => {
      acc[category] = score;
      return acc;
    }, {});
  json.adult = getLikelyString(json.sexual);
  json.nsfw =
    json.adult === "LIKELY" || json.adult === "VERY_LIKELY" ? "nsfw" : "normal";
  json.nsfw_score = json.sexual;

  const jsonPayload = {
    raw_response: JSON.stringify(response),
    image_url: imageUrl,
    id,
    context,
    result: json,
    safe_search_detection: JSON.stringify(json),
    service: "openai",
    duration: Date.now() - startTime,
    ...(others ?? {}),
  };
  loggingInfo("auto_moderation", jsonPayload);

  return json;
}

module.exports = {
  app,
  autoModerate,
  skipModerate,
};
