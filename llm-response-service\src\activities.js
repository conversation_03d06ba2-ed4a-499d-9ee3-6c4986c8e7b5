const express = require("express");
const activitiesRouter = express.Router();
const { supabase } = require("./supabaseClient");
const { authUser } = require("./middleware");

activitiesRouter.get("/ping", async (req, res) => {
  return res.send("Posts ping");
});

activitiesRouter.post("/fetchProfileActivities", authUser, async (req, res) => {
  if (!req.body) {
    return res.status(400).send("Missing body");
  }

  const { profileId, rangeStart, rangeEnd } = req.body;
  if (!profileId) {
    return res.status(400).send("Invalid profileId parameter");
  }
  if (
    rangeStart < 0 ||
    rangeEnd < 0 ||
    rangeStart > rangeEnd ||
    rangeEnd - rangeStart > 100
  ) {
    return res.status(400).send("Invalid range parameters");
  }

  const { data } = await supabase
    .from("activities")
    .select(
      `
        id, source_id, source_type, post_comment_id, profile_id, created_at,
        receiver_profile:profile_id !inner (
          username,
          avatar_url,
          user_id,
          display_name
        ),
        sender_profile:sender_profile_id (
          username
        ),
        posts(slug, media_url, visibility, nsfw),
        post_likes(
          is_like, 
          posts(slug, media_url, visibility, nsfw)
        ),
        post_comments(
          body, 
          posts(slug, media_url, visibility, nsfw, profiles(username)), 
          post_comments(body, posts(slug, media_url, visibility, nsfw)) 
        ),
        post_comment_likes(
          is_like, 
          post_comments(body, posts(slug, media_url, visibility, nsfw))
        )
      `,
    )
    .eq("sender_profile_id", profileId)
    .eq("receiver_profile.visibility", "public")
    .in("source_type", [
      "post_like",
      "post_comment",
      "post_comment_like",
      "post_comment_reply",
      "follower",
      "post",
    ])
    .order("id", { ascending: false })
    .range(rangeStart, rangeEnd);

  res.json(data);
});

module.exports = {
  activitiesRouter,
};
