const { logError } = require("../../logUtils");
const { wrappedSupabaseError, supabase } = require("../../supabaseClient");
const { generateStoryPostVideos } = require("./videos/generateStoryPostVideos");

async function addGeneratedStoryPost({
  task_id,
  imageUrls,
  blurhash,
  workflow_payload,
}) {
  const {
    imageDescription,
    pillowCaption,
    pillowWorkflowRunId,
    pillowSenderProfileId,
    pillowOtherParams,
  } = workflow_payload;

  const { decision } = pillowOtherParams;

  const original_image_description = decision.image_description;
  const intent = decision.intent;
  const image_generation_prompt = imageDescription;

  // A note on current confusing terminology:
  //
  // `imageDescription` in the image-service image generation workflows payload is what actually gets used to generate the image.
  // but `image_description` in pillow LLM workflows is actually the _verbose image description_ that the LLM spits out.
  // we then use another LLM step to refine that verbose image description into `clip_text`

  if (!imageUrls.length || !imageUrls[0].length) {
    throw new Error("No image URLs provided");
  }

  if (!pillowSenderProfileId) {
    throw new Error("No pillowSenderProfileId provided");
  }

  if (blurhash && Array.isArray(blurhash) && blurhash[0]) {
    blurhash = blurhash[0];
  }

  // For now, we do the dumbest thing and generate a video for every new story post
  // TODO: let the LLM decide whether a post should have a video or not
  const { data: newSnap, error: insertError } = await supabase
    .from("pillow_story_posts")
    .insert({
      poster_profile_id: pillowSenderProfileId,
      caption: pillowCaption,
      image_generation_prompt,
      processing_status: "image_generated",
      task_id,
      intent,
      blurhash,
      image_url: imageUrls[0],
      original_image_description: original_image_description,
      workflow_run_id: pillowWorkflowRunId,
      generate_video: true,
    })
    .single();

  if (insertError) {
    logError({
      context: "**** inserting a 'pillow_story_posts' record failed",
      error: wrappedSupabaseError(insertError),
      pillowSenderProfileId,
      imageUrls,
    });
    return null;
  }

  generateStoryPostVideos({ bot_profile_id: pillowSenderProfileId });

  return newSnap;
}

module.exports = {
  addGeneratedStoryPost,
};
