const { logInfo, logWarn } = require("../logUtils");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");
const { fetchCurrentProposedPost } = require("./fetchCurrentProposedPost");

async function fetchBotProfileOwnerProfileId({ botProfileId }) {
  const { data: bot, error: fetchBotError } = await supabase
    .from("bots")
    .select("id, creator_id, profiles:profile_id(visibility)")
    .eq("profile_id", botProfileId)
    .neq("profiles.visibility", "archived")
    .single();

  if (fetchBotError) {
    const error = wrappedSupabaseError(fetchBotError);
    throw error;
  }

  return bot.creator_id;
}

async function fetchRemindersData({ postId, userProfileId }) {
  const { data: remindersData, error: fetchRemindersDataError } = await supabase
    .schema("internal")
    .from("proposed_post_reminders")
    .select("reminder_notif_ids")
    .eq("post_id", postId)
    .eq("user_profile_id", userProfileId)
    .maybeSingle();

  if (fetchRemindersDataError) {
    const error = wrappedSupabaseError(fetchRemindersDataError);
    throw error;
  }

  return remindersData;
}

async function sendProposedPostReminderNotifIfNeeded({
  post,
  botProfile,
  nowDate,
}) {
  logInfo({
    context: "sendProposedPostReminderNotif",
    message: "checking to see if need to send proposed post reminder notif",
    post: post,
    botProfile: botProfile,
    nowDate,
  });

  const currentProposedPost = await fetchCurrentProposedPost({
    bot_profile_id: botProfile.id,
  });
  if (!currentProposedPost) {
    logWarn({
      context: "sendProposedPostReminderNotif",
      message: "there is no current proposed post",
      post: post,
      currentProposedPost: currentProposedPost,
      botProfileId: botProfile.id,
    });
    return;
  }

  if (currentProposedPost.id !== post.id) {
    logWarn({
      context: "sendProposedPostReminderNotif",
      message: "post is not the current proposed post, bailing out",
      post: post,
      currentProposedPost: currentProposedPost,
      botProfileId: botProfile.id,
    });
    return;
  }

  const botProfileId = botProfile.id;
  const creatorUserProfileId = await fetchBotProfileOwnerProfileId({
    botProfileId,
  });
  if (!creatorUserProfileId) {
    throw new Error("failed to fetch bot profile owner profile id");
  }

  const remindersData = await fetchRemindersData({
    postId: post.id,
    userProfileId: creatorUserProfileId,
  });

  // TODO: lower to logDebug
  logInfo({
    context: "sendProposedPostReminderNotif",
    message: "fetched reminders data",
    remindersData,
  });

  let reminder_notif_ids;

  if (remindersData) {
    reminder_notif_ids = remindersData.reminder_notif_ids;

    if (reminder_notif_ids.length >= 3) {
      logWarn({
        context: "sendProposedPostReminderNotif",
        message: "already sent 3 reminders, bailing out",
        postId: post.id,
      });
      return;
    }

    const {
      data: reminderNotifications,
      error: fetchReminderNotificationsError,
    } = await supabase
      .from("notifications")
      .select("id, created_at")
      .in("id", reminder_notif_ids)
      .order("created_at", { ascending: false });

    if (fetchReminderNotificationsError) {
      const error = wrappedSupabaseError(fetchReminderNotificationsError);
      throw error;
    }

    // TODO: lower to logDebug
    logInfo({
      context: "sendProposedPostReminderNotif",
      message: "fetched reminder notifications",
      reminderNotifications,
    });

    const latestSentNotification = reminderNotifications[0];
    const latestSentNotificationDate = new Date(
      latestSentNotification.created_at,
    );

    const cutoffDate = new Date(
      latestSentNotificationDate.getTime() + 6 * 60 * 60 * 1000,
    );

    if (cutoffDate > nowDate) {
      logWarn({
        context: "sendProposedPostReminderNotif",
        message:
          "latest reminder notification for this post was sent within 6 hours, bailing out",
        postId: post.id,
        cutoffDate,
        nowDate,
      });
      return;
    }
  } else {
    logInfo({
      context: "sendProposedPostReminderNotif",
      message: "no reminders have been sent yet",
    });
    reminder_notif_ids = [];
  }

  const { data: insertedNotification, error: insertNotificationError } =
    await supabase
      .from("notifications")
      .insert({
        profile_id: creatorUserProfileId,
        source_type: "proposed_post_reminder",
        source_id: post.id,
        title: "is still waiting for your opinion",
        text: post.description,
        path: `/users/${botProfile.username}`,
        sender_profile_id: botProfile.id,
        image_url: post.media_url,
      })
      .select("id")
      .single();

  if (insertNotificationError) {
    const error = wrappedSupabaseError(insertNotificationError);
    throw error;
  }

  logInfo({
    context: "sendProposedPostReminderNotif",
    message: "inserted reminder notification",
    insertedNotification,
  });

  const updatedReminderNotifIds = [
    ...reminder_notif_ids,
    insertedNotification.id,
  ];
  logInfo({
    context: "sendProposedPostReminderNotif",
    message: "updating proposed_post_reminders",
    updatedReminderNotifIds,
  });

  const { error: remindersDataInsertError } = await supabase
    .schema("internal")
    .from("proposed_post_reminders")
    .upsert(
      {
        post_id: post.id,
        user_profile_id: creatorUserProfileId,
        reminder_notif_ids: updatedReminderNotifIds,
      },
      { onConflict: ["post_id", "user_profile_id"] },
    );

  if (remindersDataInsertError) {
    const error = wrappedSupabaseError(remindersDataInsertError);
    throw error;
  }

  return { insertedNotification, updatedReminderNotifIds };
}

module.exports = {
  sendProposedPostReminderNotifIfNeeded,
};
