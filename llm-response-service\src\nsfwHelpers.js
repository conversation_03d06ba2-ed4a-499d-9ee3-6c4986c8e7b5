const { callAndLogOpenAI } = require("./llm");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { logError } = require("./utils");

async function detectCharacterWithNsflSentence({
  sentence,
  type = "unknown",
  isClone = false,
}) {
  let detectPrompt = `Determine if the content describes a character that meets these definitions:
  
${sentence}

Return a JSON object with the following:
{
  "under_18": true/false, // description must literally state that they are under 17 or high school student. "youthful appearance" / "university" is ok. 18 years old is OK
  "racism": true/false,
  "incest": true/false,
  "beastiality": true/false, // portrays explicit sexualization of animals
  "necrophilia": true/false, // portrays explicit sexualization of corpses
  "rape": true/false,
  "extreme_gore": true/false, // resident evil, zombies, chainsaw man, etc is ok
  "known_person": true/false, // content provided names a specific, real life, known person, e.g, <PERSON>, <PERSON>. Description like "celebrity", "actor" do not count
  "sexually_explicit": true/false, // content must include explicit nudity, sexual acts, bikini, lingerie, or sexually suggestive material
  "sexually_explicit_reason": "very short sentence explaining why sexually explicit if true, empty string if false",
  "sexually_suggestive_themes": true/false, // content provided, drugs, modeling, but not sexually explicit,
}`;

  try {
    const chatCompletion = await callAndLogOpenAI(
      "FireworksAI:DetectNSFL",
      {
        response_format: { type: "json_object" },
        messages: [
          {
            role: "user",
            content: detectPrompt,
          },
        ],
        model: "gpt-4o-mini",
        stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
        temperature: 0,
        seed: 111,
      },
      {
        timeout: 8 * 1000,
      },
    );

    const result = JSON.parse(chatCompletion.choices[0].message.content);

    let nsfl = false;
    let nsfl_reason = "";
    let adult = false;
    let adult_reason = "";

    if (type === "createAIPreview") {
      if (result.sexually_explicit) {
        adult = true;
        adult_reason = result.sexually_explicit_reason;
      }
    } else {
      if (result.sexually_explicit) {
        adult = true;
        adult_reason = result.sexually_explicit_reason;

        if (result.under_18) {
          nsfl = true;
          nsfl_reason =
            "Character described is under 18 and is sexually explicit";
        }

        if (result.known_person) {
          nsfl = true;
          nsfl_reason =
            "Character described is a celebrity / known person and is sexually explicit";
        }
      }

      if (result.sexually_suggestive_themes) {
        if (result.under_18) {
          nsfl = true;
          nsfl_reason =
            "Character described is under 18 and contains mature content";
        }
      }

      if (result.incest && result.sexually_explicit) {
        nsfl = true;
        nsfl_reason = "Character described involves incest";
      } else if (result.beastiality && result.sexually_explicit) {
        nsfl = true;
        nsfl_reason = "Character described involes beastiality";
      } else if (result.necrophilia && result.sexually_explicit) {
        nsfl = true;
        nsfl_reason = "Character described involes necrophilia";
      } else if (result.rape && result.sexually_explicit) {
        nsfl = true;
        nsfl_reason = "Character described involves rape";
      }

      // else if (result.extreme_gore) {
      //   nsfl = true;
      //   nsfl_reason = "Character described involves extreme gore";
      // }

      if (isClone) {
        // if (result.sexually_suggestive_themes) {
        //   nsfl = true;
        //   nsfl_reason = "Suggestive themes are not allowed for clones";
        // }
        // if (result.known_person) {
        //   nsfl = true;
        //   nsfl_reason = "Clones of celebrities are not allowed";
        // }
        if (result.sexually_explicit) {
          nsfl = true;
          nsfl_reason = "Explicit content is not allowed for clones";
        }
      }
    }

    const ret = {
      nsfl,
      nsfl_reason,
      adult,
      adult_reason,
      sexually_suggestive_themes: result.sexually_suggestive_themes,
    };

    if (ret.nsfl) {
      const { error: insertGenerationRecordError } = await supabase
        .schema("internal")
        .from("nsfl_nsfw_generations")
        .insert({
          reason: nsfl_reason,
          prompt: sentence,
          type,
          nsfl: true,
        });
      if (insertGenerationRecordError) {
        const error = wrappedSupabaseError(insertGenerationRecordError);
        logError({
          context:
            "detectCharacterWithNsflSentence: failed to write to nsfl_nsfw_generations",
          error,
          reason: nsfl_reason,
          prompt: sentence,
          type,
          nsfl: true,
        });
      }
    }

    return ret;
  } catch (error) {
    logError({
      context: "detectCharacterWithNsflSentence error",
      error,
      sentence,
    });
    throw error;
  }
}

async function detectCharacterWithChild(sentence) {
  const detectPrompt = `Does the following content describe a character who is under 18 years old? 
Look for explicit age indicators, developmental stages, or contextual clues including but not limited to:
- Direct age references (0-17 years old, infant, baby, toddler, child, minor)
- School contexts (elementary, middle school, high school student)
- Developmental descriptors (small body, childlike, infantile, infantized)
- Inappropriate terms (loli, lolicon, lolita, babygirl)
- Age-specific items (baby bib, wearing a diaper)
- Numerical ages: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17 years old

Return a JSON object with:
{
  "under_18": true/false, // true if the character is described as under 18
  "reason": "very short explanation if under_18 is true, empty string if false"
}

Content:
${sentence}
`;

  try {
    const chatCompletion = await callAndLogOpenAI(
      "FireworksAI:DetectChild",
      {
        response_format: { type: "json_object" },
        messages: [
          {
            role: "user",
            content: detectPrompt,
          },
        ],
        model: "gpt-4o-mini",
        stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
        temperature: 0,
        seed: 112,
      },
      {
        timeout: 8 * 1000,
      },
    );

    const result = JSON.parse(chatCompletion.choices[0].message.content);

    let isChild = false;
    let reason = "";

    if (result.under_18) {
      isChild = true;
      reason = result.reason || "Character described as under 18";
    }

    return {
      isChild,
      reason,
    };
  } catch (error) {
    logError({
      context: "detectCharacterWithChild error",
      error,
      sentence,
    });
    throw error;
  }
}

function removeAdultWords(sentence) {
  const adultWords = [
    "nude",
    "naked",
    "topless",
    "bottomless",
    "cock",
    "pussy",
    "vagina",
    "penis",
    "dick",
  ];

  // Loop through each word in the adultWords array
  adultWords.forEach((word) => {
    // Create a regular expression for the word, with global and case-insensitive flags
    const regex = new RegExp(`\\b${word}\\b`, "gi");
    // Replace the word in the sentence with an empty string
    sentence = sentence.replace(regex, "");
  });

  return sentence;
}

module.exports = {
  detectCharacterWithNsflSentence,
  detectCharacterWithChild,
  removeAdultWords,
};
