const express = require("express");
const { authUser } = require("./middleware");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const dayjs = require("dayjs");
const {
  generateDreamsForProfile,
  getActivePersonaAndGenerateDreamsContentsFromLLM,
} = require("./dreamsUtils");
const { logError, logInfo } = require("./logUtils");
const { baseUrl } = require("./api");
const { default: axios } = require("axios");
const { autoModerate } = require("./moderation");
const { loggingInfo } = require("./logging");
const { generateAICaptionFromImage } = require("./llm");
const {
  maybeNotifyAboutNewDreams,
  notifyAboutNewDreams,
} = require("./dreamsImageUtils");
const { checkIfUserIsActive } = require("./scenariosHelpers");

const router = express.Router();

router.post("/DEBUG_maybeNotifyAboutNewDreams", async (req, res) => {
  const { dream_batch_id, dream_id, owner_profile_id } = req.body;

  const result = await maybeNotifyAboutNewDreams({
    dream_batch_id,
    dream_id,
    owner_profile_id,
  });

  return res.json(result);
});

router.post("/DEBUG_notifyAboutNewDreams", async (req, res) => {
  const { dream_batch_id, dream_id, owner_profile_id } = req.body;

  const result = await notifyAboutNewDreams({
    dream_batch_id,
    dream_id,
    owner_profile_id,
  });

  return res.json(result);
});

// Debug endpoint to generate a new batch of dream JSON using LLM
router.post("/DEBUG_generateDreamsContentsFromLLM", async (req, res) => {
  const { owner_profile_id } = req.body;

  const ownerProfileId = owner_profile_id;

  const result = getActivePersonaAndGenerateDreamsContentsFromLLM({
    ownerProfileId,
  });

  return res.json(result);
});

// Debug endpoint to:
// * generate a new batch of dream JSON using LLM
// * insert the new dreams into the database
// * schedule generating images for the new dreams
router.post("/DEBUG_generateDreamsForProfile", async (req, res) => {
  const { owner_profile_id } = req.body;

  const ownerProfileId = owner_profile_id;

  const newDreams = generateDreamsForProfile({ ownerProfileId });

  return res.json(newDreams);
});

// Used by the frontend to fetch the dreams to display in the UI
router.post("/fetchDreams", authUser, async (req, res) => {
  const { profile_id, cursor, limit } = req.body;

  const limitInt = parseInt(limit, 10);
  if (!profile_id || !limit || isNaN(limitInt) || limitInt <= 0 || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid parameters" });
  }

  try {
    let query = supabase
      .from("dreams")
      .select("*")
      .eq("profile_id", profile_id)
      .in("status", ["image_generated", "post_published"])
      .order("created_at", { ascending: false })
      .limit(limitInt);

    if (cursor && cursor !== "init") {
      query = query.lt("created_at", cursor);
    }

    const { data: dreams, error } = await query;

    if (error) throw wrappedSupabaseError(error);

    const lastDream = dreams[dreams.length - 1];

    const next_cursor =
      dreams.length >= limitInt
        ? dayjs(lastDream.created_at).format("YYYY-MM-DDTHH:mm:ss.SSS")
        : null;

    const transformedDreams = dreams.map((dream) => {
      if (!dream.revealed) {
        // Remove the media_url if the dream is not revealed
        return {
          ...dream,
          media_url: null,
        };
      }
      return dream;
    });

    return res.send({
      data: transformedDreams,
      page_info: { next_cursor },
      error: null,
    });
  } catch (error) {
    logError({
      context: "Failed to fetch dreams",
      error,
    });
    return res.sendStatus(500);
  }
});

router.post("/revealDreams", authUser, async (req, res) => {
  const { profile_id, dream_ids } = req.body;

  let { data: ownedDreams, error: fetchDreamsError } = await supabase
    .from("dreams")
    .select("id")
    .eq("profile_id", profile_id)
    .eq("revealed", false)
    .in("id", dream_ids)
    .order("created_at", { ascending: false });

  if (fetchDreamsError) {
    throw wrappedSupabaseError(fetchDreamsError);
  }

  const nowTimestamptz = new Date().toISOString();

  const updatePayload = ownedDreams.map((dream) => ({
    id: dream.id,
    updated_at: nowTimestamptz,
    revealed: true,
  }));

  const updateResult = await supabase
    .from("dreams")
    .upsert(updatePayload)
    .select("*");

  if (updateResult.error) {
    throw wrappedSupabaseError(updateResult.error);
  }

  return res.json(updateResult);
});

router.post("/createPostFromDream", authUser, async (req, res) => {
  const { profile_id, dream_id, description } = req.body;

  try {
    const { data: dreamData, error: dreamError } = await supabase
      .from("dreams")
      .select("*")
      .eq("revealed", true)
      .eq("id", dream_id)
      .single();

    if (dreamError) {
      throw wrappedSupabaseError(dreamError);
    }

    const insertContents = {
      description,
      media_url: dreamData.media_url,
      profile_id,
      visibility: "public",
    };

    // TODO: <START> UNIFY WITH insertPost

    const moderationResult = await autoModerate({
      imageUrl: insertContents.media_url,
      id: insertContents.profile_id,
      context: "create-post-from-dream",
    });

    insertContents.nsfw = moderationResult.nsfw;
    insertContents.nsfw_score = moderationResult.nsfw_score;
    insertContents.nsfl = moderationResult.nsfl || false;
    insertContents.safe_search_detection = moderationResult;

    if (moderationResult.nsfl) {
      insertContents.visibility = "hidden";
    }

    // caption the image
    let caption = await generateAICaptionFromImage({
      imageUrl: insertContents.media_url,
      executionId: req.executionId,
      temperature: 0.1,
      prompt: "Describe what is in this image",
    });

    insertContents.ai_caption = caption;

    const { data: postData, error: postError } = await supabase
      .from("posts")
      .insert(insertContents)
      .select("*")
      .single();

    if (postError) {
      const error = wrappedSupabaseError(postError);
      throw error;
    }

    if (moderationResult.nsfl) {
      loggingInfo("visibility", {
        post_id: postData.id,
        profile_id: insertContents.profile_id,
        post_nsfw: insertContents.nsfw,
        post_nsfl: insertContents.nsfl,
        post_safe_search_detection: moderationResult,
        media_url: insertContents.media_url,
        visibility: "hidden",
      });
    } else {
      const url = `${baseUrl}/bots/generateCommentsAndLikesForPost`;
      axios.post(url, {
        post_id: postData.id,
      });
    }

    // TODO: <END> UNIFY WITH insertPost

    const { error: updateDreamError } = await supabase
      .from("dreams")
      .update({
        status: "post_published",
        post_id: postData.id,
      })
      .eq("id", dream_id)
      .select();

    if (updateDreamError) {
      logError({
        context:
          "createPostFromDream - post created but failed to update dreams record with post id",
        error: wrappedSupabaseError(updateDreamError),
      });
    }

    return res.send({ data: postData, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Create post from dream Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to create post from dream" });
  }
});

router.post("/generateDreamsTask", async (req, res) => {
  logInfo({
    context: "generateDreamsTask",
    message: `START`,
  });

  const { data: profiles, error: profilesError } = await supabase
    .from("profiles_without_pending_dreams")
    .select("*");

  if (profilesError) {
    const error = wrappedSupabaseError(profilesError);
    throw error;
  }

  logInfo({
    context: "generateDreamsTask",
    message: `fetched profiles without pending dreams`,
    profiles,
  });

  const shouldCheckActiveHours = true;

  // const internalUsersProfileIds = [
  //   1, // vu
  //   11433, // payton
  //   43151, // saniul
  //   1232, // mehrdad
  //   39880, // leo
  //   44481, // sulley
  // ];

  res.sendStatus(200);

  for (const profile of profiles) {
    const profileId = profile.id;

    try {
      if (shouldCheckActiveHours) {
        const isUserActive = await checkIfUserIsActive({
          humanProfileId: profileId,
        });

        if (!isUserActive) {
          logInfo({
            context: "generateDreamsTask",
            message: `Skipping generating dreams for user since we're out of their active hours: ${profileId}`,
            profile_id: profileId,
          });
          continue;
        }
      }
      await generateDreamsForProfile({ ownerProfileId: profileId });
    } catch (error) {
      logError({
        context: "generateDreamsTask - failed to generate dreams for profile",
        owner_profile_id: profileId,
        error,
      });
    }
  }
});

module.exports = {
  dreamsRouter: router,
};
