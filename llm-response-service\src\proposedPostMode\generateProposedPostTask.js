const { fetchBotAndBotProfile } = require("./fetchBotAndBotProfile");
const {
  generateProposedPostForBotIfNeeded,
} = require("./generateProposedPostForBotIfNeeded");

async function generateProposedPostTask({
  botProfileId,
  nowDate = new Date(),
}) {
  const botData = await fetchBotAndBotProfile({ botProfileId });
  if (!botData) {
    return;
  }
  const { bot, botProfile } = botData;
  const result = await generateProposedPostForBotIfNeeded({
    bot,
    botProfile,
    nowDate,
  });

  return result;
}

module.exports = {
  generateProposedPostTask,
};
