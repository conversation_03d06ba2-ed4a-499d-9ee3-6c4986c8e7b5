function oneFriendV2Prompt({
  prompt,
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 1016,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
  steps = 6,
  cfg = 1.5,
  faceSteps = 3,
  faceCfg = 1.5,
  faceDenoise = 0.4,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  badquality = 0,
  blurxl = 0,
  envyzoomslider = 0,
  faceInjectWeight = 1,
  faceInjectWeightFaceIDV2 = 3,
  dark = 0,
  pinterest = 0.5,
  pinterestFace = 1.0,
  selfie = 0,
  cropOffsetX = 224,
  poseImageData,
}) {
  seed = seed ?? Math.floor(Math.random() * 100000000000);

  return {
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width: 1016,
        height: 1024,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(phone), ${nsfw ? "" : ", (nudity, nsfw, naked)"}`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    11: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["64", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    33: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    64: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    66: {
      inputs: {
        lora_name: "badquality.safetensors",
        strength_model: 0,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    67: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: -0.5,
        model: ["69", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    68: {
      inputs: {
        lora_name: "envyzoomslider.safetensors",
        strength_model: 0,
        model: ["67", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    69: {
      inputs: {
        lora_name: "Instagram_Selfie_SDXL.safetensors",
        strength_model: selfie,
        model: ["79", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    74: {
      inputs: {
        lora_name: "dark.safetensors",
        strength_model: dark,
        model: ["66", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    79: {
      inputs: {
        lora_name: "Pinterest_Snap_Selfie.safetensors",
        strength_model: pinterest,
        model: ["74", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    105: {
      inputs: {
        width: 576,
        height: 1024,
        x: cropOffsetX,
        y: 0,
        image: ["111", 0],
      },
      class_type: "ImageCrop",
      _meta: {
        title: "Image Crop",
      },
    },
    108: {
      inputs: {
        strength: faceInjectWeight,
        start_at: 0,
        end_at: 1,
        face_embeds: ["115", 1],
        control_net: ["109", 0],
        image_kps: ["155", 0],
        positive: ["6", 0],
        negative: ["7", 0],
      },
      class_type: "ApplyInstantIDControlNet",
      _meta: {
        title: "InstantID Apply ControlNet",
      },
    },
    109: {
      inputs: {
        control_net_name: "diffusion_pytorch_model.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    110: {
      inputs: {
        seed,
        steps: 6,
        cfg: 1.3,
        sampler_name: "dpmpp_sde",
        scheduler: "karras",
        denoise: 1,
        model: ["115", 0],
        positive: ["108", 0],
        negative: ["108", 1],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    111: {
      inputs: {
        samples: ["110", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    115: {
      inputs: {
        weight: faceInjectWeight,
        start_at: 0,
        end_at: 1,
        noise: 0,
        instantid: ["116", 0],
        insightface: ["117", 0],
        image: ["11", 0],
        model: ["68", 0],
      },
      class_type: "InstantIDAttentionPatch",
      _meta: {
        title: "InstantID Patch Attention",
      },
    },
    116: {
      inputs: {
        instantid_file: "ip-adapter.bin",
      },
      class_type: "InstantIDModelLoader",
      _meta: {
        title: "Load InstantID Model",
      },
    },
    117: {
      inputs: {
        provider: "CPU",
      },
      class_type: "InstantIDFaceAnalysis",
      _meta: {
        title: "InstantID Face Analysis",
      },
    },
    155: {
      inputs: {
        image: "#DATA",
        image_data: poseImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    156: {
      inputs: {
        guide_size: 512,
        guide_size_for: true,
        max_size: 800,
        seed: 0,
        steps: 4,
        cfg: 1.5,
        sampler_name: "dpmpp_sde",
        scheduler: "normal",
        denoise: 0.5,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        bbox_threshold: 0.5,
        bbox_dilation: 10,
        bbox_crop_factor: 3,
        sam_detection_hint: "center-1",
        sam_dilation: 0,
        sam_threshold: 0.93,
        sam_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        sam_mask_hint_use_negative: "False",
        drop_size: 20,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["105", 0],
        model: ["66", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
        bbox_detector: ["158", 0],
      },
      class_type: "FaceDetailer",
      _meta: {
        title: "FaceDetailer",
      },
    },
    158: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    159: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["156", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { oneFriendV2Prompt };
