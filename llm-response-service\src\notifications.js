const apn = require("@parse/node-apn");
const express = require("express");
const { stageApnProvider, prodApnProvider } = require("./apnProvider");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const {
  logError,
  logWarn,
  logInfo,
  checkProfileValid,
  wrappedSupabaseError,
  PostgresErrorCode,
  logDebug,
  lazyInit,
} = require("./utils");
const { getDataWithCache } = require("./btClient");
const { loggingInfo } = require("./logging");
const { authUser } = require("./middleware");
const {
  decorateWithActiveSpanAsync,
  addSpanAttribute,
  addSpanEvent,
} = require("./instrumentation/tracer");
const redisClient = require("./redisClient");

const admin = require("firebase-admin");
const dayjs = require("dayjs");

const serviceAccount = require("./firebase-service-key.json");
const { gcloudErrorReporting } = require("./errorReporting");

// When true, include the post description in the notification payload
const BETTER_POST_NOTIFICATIONS = true;

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

app.get("/testPushNotification", async (req, res) => {
  const profileId = req.query.profile_id;
  if (!profileId) {
    return res.status(400).send("Missing profile_id");
  }
  const notificationTexts = [
    "Martin liked Trump's post",
    "Martin and 1 other liked Trump's post",
    "Martin and 2 others liked Trump's post",
    "Martin and 3 others liked Trump's post",
  ];
  const notificationId =
    req.query.notification_id ?? Math.floor(Math.random() * 1000000 + 1);
  const index =
    req.query.index ?? Math.floor(Math.random() * notificationTexts.length);

  await sendPushNotificationsForProfileId({
    profileId,
    notification: {
      id: notificationId,
      source_type: "post_like",
      image_url: "",
    },
    notificationTitle: "You've been noticed!",
    notificationText: notificationTexts[index],
    notificationLink: "",
    notificationData: "notificationData",
  });

  res.send("Done!");
});

app.get("/ping", async (req, res) => {
  const message = {
    notification: {
      title: "Hello",
      body: "This is a test notification",
    },
    token:
      "f_8NbOETQouQXZL8jeocqa:APA91bELwVMfQxXDJZlQIYK1h76aobuBlrmd9MbZBqSp6ZBMKoQc8vmscJnWgVPcdquxgarn98jZHJ93xwAhXvtiLAxYnjFhCGjyvkXVTMtGQERWFm2Ypc4Cj_QcucJV25fDEwmAPyVl",
  };

  admin
    .messaging()
    .send(message)
    .then((response) => {
      console.log("Successfully sent message:", response);
    })
    .catch((error) => {
      console.log("Error sending message:", error);
    });

  return res.send("notifications ping");
});

app.post("/registerPushToken", authUser, async (req, res) => {
  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send("Forbidden");
  }

  const { token, device_type, environment } = req.body;

  if (!token || !device_type || !environment) {
    return res.status(400).send("Missing required fields.");
  }
  try {
    const { error } = await supabase.from("device_push_tokens").upsert(
      {
        user_id,
        token,
        device_type,
        environment,
        is_active: true,
      },
      { onConflict: "token" },
    );

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "**** registerPushToken Error",
      error,
    });

    return res.sendStatus(500);
  }
});

app.post("/markUserActioned", authUser, async (req, res) => {
  const { actionedNotifications } = req.body;
  if (!actionedNotifications || actionedNotifications.length === 0) {
    return res.status(400).send("Invalid actionedNotifications error");
  }
  logInfo({
    context: "Mark notifications as user-actioned ",
    message: `***** notification data: ${JSON.stringify(actionedNotifications[0])}`,
  });

  const notificationIds = actionedNotifications.map(
    (notification) => notification?.notificationId,
  );
  const { error } = await supabase
    .from("notifications")
    .update({ user_actioned_at: new Date() })
    .in("id", notificationIds);

  if (error) {
    logError({
      executionId: req.executionId,
      context: "Error in updating user_actioned_at of notifications:",
      error: wrappedSupabaseError(
        error,
        "failed to update notifications.user_actioned_at",
      ),
    });
    return res.sendStatus(500);
  } else {
    return res.sendStatus(200);
  }
});

app.post("/markDelivered", authUser, async (req, res) => {
  const { deliveredNotifications } = req.body;
  if (!deliveredNotifications || deliveredNotifications.length === 0) {
    return res.status(400).send("Invalid deliveredNotifications error");
  }

  logInfo({
    context: "Mark notifications delivered",
    message: `***** notification data: ${JSON.stringify(deliveredNotifications[0])}`,
  });

  const notificationIds = deliveredNotifications
    .map((notification) => notification?.notificationId)
    .filter((id) => id); // Filter out any null or empty ids

  if (notificationIds.length === 0) {
    return res.status(400).send("No valid notificationIds found");
  }

  const { error } = await supabase
    .from("notifications")
    .update({ delivered_at: new Date() })
    .in("id", notificationIds);

  if (error) {
    logError({
      executionId: req.executionId,
      context: "Error in updating delivered_at of notifications:",
      error,
    });
    return res.sendStatus(500);
  } else {
    return res.sendStatus(200);
  }
});

// this function need temperally to keep current distributed apps.
async function notificationBody({ notification }) {
  let notificationType;
  let notificationText;
  let notificationImage;
  let notificationData = {};

  notificationData.username = notification?.sender_profile?.username;
  notificationData.poster =
    notification?.post_comments?.posts?.profiles?.username;
  notificationData.notifyType = notification?.source_type;
  switch (notification?.source_type) {
    case "post_like":
      notificationType = `liked your post`;
      notificationText = ``;
      notificationImage = notification?.post_likes?.posts?.media_url;
      notificationData.post_slug = notification?.post_likes?.posts?.slug;
      break;
    case "post_comment_like":
      notificationType = `liked your comment`;
      notificationText = `${notification?.post_comment_likes?.post_comments?.body}`;
      notificationImage =
        notification?.post_comment_likes?.post_comments?.posts?.media_url;
      notificationData.post_slug =
        notification?.post_comment_likes?.post_comments?.posts?.slug;
      break;
    case "post_comment":
      //console.log(notification?.post_comments?.reply_to_id ? 'REPLIED' : 'NOT REPLIED')
      notificationType = `commented on your post`;
      notificationText = `${notification?.post_comments?.body}`;
      notificationImage = notification?.post_comments?.posts?.media_url;
      notificationData.post_slug = notification?.post_comments?.posts?.slug;
      break;
    case "post_comment_reply":
      notificationType = `replied to your comment`;
      notificationText = `${notification?.post_comments?.post_comments[0]?.body}`;
      notificationImage =
        notification?.post_comments?.post_comments[0]?.posts?.media_url;
      notificationData.post_slug =
        notification?.post_comments?.post_comments[0]?.posts?.slug;
      break;
    case "follower":
      notificationType = `followed you`;
      notificationText = ``;
      notificationImage = false;
      break;
  }

  if (!notificationText || notificationText.length === 0) {
    return {
      title: "butterflies",
      text: `${notification?.sender_profile?.username} ${notificationType}`,
      data: notificationData,
      image: notificationImage,
    };
  }

  return {
    title: `${notification?.sender_profile?.username} ${notificationType}`,
    text: notificationText,
    data: notificationData,
    image: notificationImage,
  };
}

function getNotificationData(
  notification,
  { forceBetterPostNotifications, otherButterflies = 0 },
) {
  let notificationData = {};
  const sender_name =
    notification?.sender_profile?.display_name ||
    notification?.sender_profile?.username;

  const postDescription =
    notification?.posts?.description || notification?.text || "";
  // Note: this doesn't account for _grapheme cluster_ length, we just use JS string length for now.
  const MAX_JS_CHAR_LENGTH = 70;
  let trimmedPostDescription;
  if (postDescription.length >= MAX_JS_CHAR_LENGTH) {
    trimmedPostDescription = `${postDescription.substring(0, MAX_JS_CHAR_LENGTH).trim()}…`;
  } else {
    trimmedPostDescription = postDescription;
  }

  if (!sender_name) {
    notificationData.title = notification?.title;
    notificationData.text = notification?.text;
  } else if (notification?.title || notification?.text) {
    // USING NEW TABLE STRUCTURE
    switch (notification?.source_type) {
      case "post_like":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name}${otherToString(otherButterflies)} ${notification?.title}`;
        break;
      case "post_comment_like":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}: ${notification?.text}`;
        break;
      case "post_comment":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}: ${notification?.text}`;
        break;
      case "post_comment_reply":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}: ${notification?.text}`;
        break;
      case "follower":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name}${otherToString(otherButterflies)} ${notification?.title}`;
        break;
      case "post_reposted":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}`;
        break;
      case "leaderboard_vote_received":
        notificationData.title = notification?.title;
        notificationData.text = notification?.text;
        break;
      case "leaderboard_lifecycle":
        notificationData.title = notification?.title;
        notificationData.text = notification?.text;
        break;
      case "pokes_refreshed":
        notificationData.title = notification?.title;
        notificationData.text = notification?.text;
        break;
      case "post": {
        if (otherButterflies == 0) {
          // title: $SENDER_NAME just shared a post
          // text: $POST_DESCRIPTION
          notificationData.title = `${sender_name} ${notification?.title}`;
          notificationData.text = trimmedPostDescription;
        } else {
          // title: Butterflies
          // text: $SENDER_NAME just shared a post
          notificationData.title = "Butterflies";
          if (otherButterflies > 0) {
            notificationData.text = `${sender_name}${otherToString(otherButterflies)} shared new posts`;
          } else {
            notificationData.text = `${sender_name} ${notification?.title}`;
          }
        }
        break;
      }
      case "new_proposed_post": {
        notificationData.title = `Post or Pass?`;
        notificationData.text = `${sender_name} needs you to review their post!`;
        break;
      }
      case "proposed_post_reminder": {
        notificationData.title = `Post or Pass?`;
        notificationData.text = `${sender_name} is STILL waiting for you to review their post!`;
        break;
      }
      case "bot_tagged":
        notificationData.title = `${sender_name} tagged your Butterfly`;
        notificationData.text = notification?.text;
        break;
      case "mention":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}: ${notification?.text}`;
        break;
      case "message_request":
        notificationData.title = `Butterflies`;
        notificationData.text = `${sender_name} ${notification?.title}`;
        break;
      case "bot_create":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}`;
        break;
      case "message_reaction":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}`;
        break;
      case "follow_request":
        notificationData.title = "Butterflies";
        notificationData.text = `${sender_name} ${notification?.title}`;
        break;
    }
  } else {
    // I will remove this soon..
    switch (notification?.source_type) {
      case "post_like":
        notificationData.title = "butterflies";
        notificationData.text = `${notification?.sender_profile.username} liked your post`;
        notificationData.image = notification?.post_likes?.posts?.media_url;
        notificationData.link = `/users/${notification?.receiver_profile.username}/p/${notification?.post_likes?.posts?.slug}`;
        break;
      case "post_comment_like":
        notificationData.title = `${notification?.sender_profile.username} liked your comment`;
        notificationData.text = `${notification?.post_comment_likes?.post_comments?.body}`;
        notificationData.image =
          notification?.post_comment_likes?.post_comments?.posts?.media_url;
        notificationData.link = `/users/${notification?.receiver_profile.username}/p/${notification?.post_comment_likes?.post_comments?.posts?.slug}`;
        break;
      case "post_comment":
        notificationData.title = `${notification?.sender_profile.username} commented on your post`;
        notificationData.text = `${notification?.post_comments?.body}`;
        notificationData.image = notification?.post_comments?.posts?.media_url;
        notificationData.link = `/users/${notification?.receiver_profile.username}/p/${notification?.post_comments?.posts?.slug}`;
        break;
      case "post_comment_reply":
        notificationData.title = `${notification?.sender_profile.username} replied to your comment`;
        notificationData.text = `${notification?.post_comments?.post_comments[0]?.body}`;
        notificationData.image =
          notification?.post_comments?.post_comments[0]?.posts?.media_url;
        notificationData.link = `/users/${notification?.post_comments?.posts?.profiles?.username}/p/${notification?.post_comments?.post_comments[0]?.posts?.slug}`;
        break;
      case "follower":
        notificationData.title = "butterflies";
        notificationData.text = `${notification?.sender_profile.username} followed you`;
        notificationData.image = false;
        notificationData.link = `/users/${notification?.sender_profile.username}`;
        break;
      case "post":
        notificationData.title = `${notification?.sender_profile.username} just posted`;
        notificationData.text = `${notification?.posts?.description}`;
        notificationData.image = notification?.posts?.media_url;
        notificationData.link = `/users/${notification?.sender_profile.username}/p/${notification?.posts?.slug}`;
        break;
      case "mention":
        notificationData.title = `${notification?.sender_profile.username} mentioned you on this comment`;
        notificationData.text = `${notification?.post_comments?.body}`;
        notificationData.image = notification?.post_comments?.posts?.media_url;
        notificationData.link = `/users/${notification?.post_comments?.posts?.profiles?.username}/p/${notification?.post_comments?.posts?.slug}`;
        break;
      case "message_request":
        notificationData.title = `butterflies`;
        notificationData.text = `${notification?.sender_profile.username} sent you a message request`;
        notificationData.image = false;
        notificationData.link = `/requests/${notification?.conversation_requests?.id}`;
        break;
    }
  }
  return notificationData;
}

function otherToString(others) {
  if (others == 0) {
    return "";
  } else if (others == 1) {
    return " and 1 other";
  } else {
    return ` and ${others} others`;
  }
}

app.get("/manualPushNotification", async (req, res) => {
  const notificationId = req.query.notification_id;
  const profileId = req.query.profile_id;
  if (!notificationId || !profileId) {
    return res.sendStatus(400);
  }
  await processNotifications(notificationId, profileId, []);
  return res.send("Done!");
});

app.post("/processNotifications", async (req, res) => {
  if (!req.body.record) {
    return res.sendStatus(400);
  }
  const notificationId = req.body.record.id;
  const profileId = req.body.record.profile_id;
  if (!notificationId || !profileId) {
    return res.sendStatus(400);
  }
  res.sendStatus(204);
  await processNotifications(notificationId, profileId, []);
});

app.post("/processDelayedNotifications", async (req, res) => {
  const {
    profile_id = null,
    notification_id = null,
    other_notification_ids = [],
  } = req.body;

  if (!profile_id || !notification_id || !other_notification_ids) {
    return res.sendStatus(400);
  }
  res.sendStatus(204);
  await processNotifications(
    notification_id,
    profile_id,
    other_notification_ids,
  );
});

async function processNotifications(
  notificationId,
  profileId,
  otherNotificationIds,
) {
  addSpanAttribute("notificationId", notificationId);
  addSpanAttribute("profileId", profileId);
  addSpanAttribute("otherNotificationIds", otherNotificationIds);

  // Enable same behavior as the BETTER_POST_NOTIFICATIONS flag.
  const forceBetterPostNotifications = BETTER_POST_NOTIFICATIONS;

  const otherButterflies = otherNotificationIds.length;
  let startTime = new Date();
  let [
    { data: notification, error: fetchNotificationError },
    { data: notifySettings, error: fetchProfileSettingsError },
    { data: userActive, error: fetchUserError },
  ] = await Promise.all([
    supabase
      .from("notifications")
      .select(
        `
          *,
          receiver_profile:profile_id (
            username,
            avatar_url
          ),
          sender_profile:sender_profile_id (
              username,
              avatar_url,
              user_id,
              display_name
          ),
          posts(*, profiles(*)),
          post_likes(*, posts(*, profiles(*))),
          post_comments(*, posts(*, profiles(*)), post_comments(*, posts(*), profiles(*))),
          post_comment_likes(*, post_comments(*, posts(*), profiles(*))),
          conversation_requests(*)
      `,
      )
      .eq("id", notificationId)
      .in("source_type", [
        "post_like",
        "post_comment",
        "post_comment_like",
        "post_comment_reply",
        "follower",
        "post",
        "mention",
        "bot_create",
        "message_reaction",
        "follow_request",
        "bot_tagged",
        "new_dreams",
        "post_reposted",
        "new_proposed_post",
        "proposed_post_reminder",
        "leaderboard_vote_received",
        "leaderboard_lifecycle",
        "pokes_refreshed",
      ])
      .single(),
    supabase
      .from("profile_settings")
      .select(
        "notif_post_likes, notif_post_comments, notif_comment_likes, notif_comment_replies, notif_profile_follows",
      )
      .eq("profile_id", profileId)
      .single(),
    supabase
      .from("profiles")
      .select("users (is_app_active)")
      .eq("id", profileId)
      .not("user_id", "is", null)
      .single(),
  ]);

  if (fetchNotificationError) {
    addSpanEvent("fetchNotificationError");
    if (fetchNotificationError.code === "PGRST116") {
      // notification not found, means notification is deleted
      return;
    }
    throw wrappedSupabaseError(fetchNotificationError);
  }

  if (fetchProfileSettingsError) {
    addSpanEvent("fetchProfileSettingsError");
    if (fetchProfileSettingsError.code === "PGRST116") {
      // profile settings not found, means profile is deleted
      return;
    }
    throw wrappedSupabaseError(fetchProfileSettingsError);
  }

  if (!notification) {
    addSpanEvent("notificationNotFound");
    return;
  }

  const settingsData = {
    post_like: notifySettings.notif_post_likes,
    post_comment: notifySettings.notif_post_comments,
    post_comment_like: notifySettings.notif_comment_likes,
    post_comment_reply: notifySettings.notif_comment_replies,
    follower: notifySettings.notif_profile_follows,
  };

  let sourceType = notification.source_type;

  if (sourceType === "leaderboard_lifecycle") {
    if (fetchUserError) {
      addSpanEvent("fetchUserError");
      if (fetchUserError.code === "PGRST116") {
        return;
      }
      throw wrappedSupabaseError(fetchUserError);
    }

    if (userActive?.users?.is_app_active) {
      return;
    }
  }

  // TODO: remove the throttling once we confirm the other way
  // const MANY_BOTS = 5;
  // const botCount = await getDataWithCache(
  //   getBotCount,
  //   redisClient,
  //   "botcount",
  //   profileId,
  //   24 * 60 * 60, // 1 day
  // );
  const throttling = false; // botCount > MANY_BOTS && ["post"].includes(sourceType);

  let notifKey = `${profileId}_${sourceType}_notif`;
  if (throttling) {
    if (["post_like"].includes(sourceType)) {
      notifKey = `${profileId}_${sourceType}_${notification.post_id}_notif`;
    }
    const hasRecentNotification = await redisClient.get(notifKey);
    if (hasRecentNotification != null) {
      addSpanEvent("skipNotification");
      // Skip if too often
      return;
    }
  }

  if (
    Object.prototype.hasOwnProperty.call(settingsData, sourceType) &&
    !settingsData[sourceType]
  ) {
    console.log("[processNotifications]", `${sourceType} disabled`);
    await supabase
      .from("notifications")
      .update({ is_processed: false })
      .eq("id", notificationId);
    addSpanEvent("notificationDisabled");
    return;
  }

  // TODO: is this even necessary?
  const { data: notificationData } = await notificationBody({
    notification,
  });

  // Quirk:
  // This is the call that determines the push notification title and text that are going to be used.
  // TODO: Refactor this code to make it clearer how `notifications` records are mapped to push notifications payloads
  let notificationTemp = getNotificationData(notification, {
    forceBetterPostNotifications,
    otherButterflies,
  });

  let notificationTitle = notificationTemp.title;
  let notificationText = notificationTemp.text;
  let notificationLink =
    otherButterflies > 0 ? "/notifications" : notification?.path || "";

  loggingInfo("notification", {
    profile_id: profileId,
    source_type: notification?.source_type,
    notification_id: notificationId,
    notification: notification,
    notification_data: notificationData,
    notification_body: {
      title: notificationTitle,
      text: notificationText,
      link: notificationLink,
    },
    notification_others: otherNotificationIds,
    notification_others_count: otherButterflies,
    notification_setting: notifySettings,
    latency: new Date() - startTime,
    type: "process",
  });

  addSpanAttribute("source_type", notification?.source_type);
  addSpanAttribute("notificationTitle", notificationTitle);
  addSpanAttribute("notificationText", notificationText);
  addSpanAttribute("notificationLink", notificationLink);

  if (!notificationText || !notificationTitle) {
    logWarn({
      context: "processNotifications",
      message:
        "notificationText or notificationTitle is empty - a push notification will not actually be sent",
      notification,
      notificationData,
      notificationTemp,
    });
  }

  logDebug({
    context: "processNotifications",
    message: "about to send push notifications for notification record",

    notification,
    notificationData,
    notificationTemp,
    notificationTitle,
    notificationText,
    notificationLink,
  });

  await sendPushNotificationsForProfileId({
    profileId,
    notification,
    notificationTitle,
    notificationText,
    notificationLink,
    notificationData,
  });

  const finalPromises = [
    supabase
      .from("notifications")
      .update({ is_processed: true }) //true: first, false: other grouped, null: not-processed
      .eq("id", notificationId),
  ];
  /*TODO: remove
  if (throttling && ["post"].includes(sourceType)) {
    finalPromises.push(
      redisClient.set(notifKey, JSON.stringify(true), {
        EX: 29 * 60, // 29 minutes,
      }),
    );
  }
  if (throttling && ["post_like", "follower"].includes(sourceType)) {
    finalPromises.push(
      redisClient.set(notifKey, JSON.stringify(true), {
        EX: 59 * 60, // 59 minutes,
      }),
    );
  }
  */
  if (otherButterflies > 0) {
    finalPromises.push(
      supabase
        .from("notifications")
        .update({ is_processed: false })
        .in("id", otherNotificationIds),
    );
  }
  await Promise.all(finalPromises);
}

const maybeSendPushForConversation = decorateWithActiveSpanAsync(
  "maybeSendPushForConversation",
  _maybeSendPushForConversation,
);
// Expected to catch all errors
async function _maybeSendPushForConversation({
  conversation_id,
  userProfileId,
  notificationProfileId, // I'm not actually sure what this one is used for
  notificationTitle,
  notificationText,
  conversationType,
}) {
  try {
    const shouldSend = await checkEnableToSendPushNotify(
      userProfileId,
      conversation_id,
    );

    if (!shouldSend) {
      return;
    }

    const { data, error: findConversationError } = await supabase
      .from("conversations")
      .select("slug")
      .eq("id", conversation_id)
      .single();

    if (findConversationError) {
      throw wrappedSupabaseError(
        findConversationError,
        "failed to fetch conversation slug",
      );
    }
    const conversation_slug = data.slug;

    await sendPushNotificationsForProfileId({
      notification: {
        id: Math.floor(Math.random() * 1000000 + 1),
        source_type: "message",
      },
      profileId: notificationProfileId,
      notificationTitle,
      notificationText,
      notificationLink: "",
      notificationData: {
        notifyType: "messages",
        conversation_slug: conversation_slug,
        conversation_type: conversationType,
      },
    });
  } catch (error) {
    logError({
      context: "*** Failed to send push notification for conversation",
      error,
      userProfileId,
      conversation_id,
      notificationTitle,
      notificationText,
    });
    gcloudErrorReporting.report(error);
  }
}

const checkEnableToSendPushNotify = decorateWithActiveSpanAsync(
  "checkEnableToSendPushNotify",
  _checkEnableToSendPushNotify,
);
async function _checkEnableToSendPushNotify(profile_id, conversation_id) {
  try {
    let startTime = new Date();

    // check if user has notification settings enabled
    const { data: notifySettings, error: notifySettingsError } = await supabase
      .from("profile_settings")
      .select("notif_messages")
      .eq("profile_id", profile_id)
      .single();

    if (notifySettingsError) {
      throw wrappedSupabaseError(
        notifySettingsError,
        "Failed to fetch user notification settings",
      );
    }

    // check if user is in conversation now
    // const {
    //   data: conversation_participants,
    //   error: conversationParticipantsError,
    // } = await supabase
    //   .from("conversation_participants")
    //   .select("is_active")
    //   .eq("conversation_id", conversation_id)
    //   .eq("profile_id", profile_id)
    //   .single();

    // if (conversationParticipantsError) {
    //   throw wrappedSupabaseError(
    //     conversationParticipantsError,
    //     "Failed to check if user is currently active in conversation"
    //   );
    // }

    const isActive = notifySettings && notifySettings?.notif_messages;
    //  &&
    // conversation_participants?.is_active != true;

    loggingInfo("notification", {
      profile_id: profile_id,
      conversation_id: conversation_id,
      notification_setting: notifySettings,
      latency: new Date() - startTime,
      is_active: isActive,
      type: "check",
    });

    return isActive;
  } catch (error) {
    logError({
      context: "**** notification send enable fetch error",
      error,
    });
    return true;
  }
}

const sendPushNotificationsForProfileId = decorateWithActiveSpanAsync(
  "sendPushNotificationsForProfileId",
  _sendPushNotificationsForProfileId,
);
async function _sendPushNotificationsForProfileId({
  profileId,
  notification,
  notificationTitle,
  notificationText,
  notificationLink,
  notificationData,
}) {
  addSpanEvent("sendPushNotificationsForProfileId");

  // FIXME: it should be possible to fetch the device token from profile id without multiple trips to the database

  let startTime = new Date();
  let profileResponse = await supabase
    .from("profiles")
    .select("user_id")
    .eq("id", profileId)
    .neq("visibility", "archived")
    .maybeSingle();
  if (profileResponse.error) {
    addSpanEvent("fetchProfileError");
    const error = wrappedSupabaseError(
      profileResponse.error,
      "Failed to send push notification for profile - couldn't fetch profile",
    );
    throw error;
  }
  if (!profileResponse.data) {
    // profile not found or archived
    return;
  }

  const userId = profileResponse.data.user_id;
  if (!userId) {
    addSpanEvent("noUserId");
    throw new Error("Fetched profile but no user_id, somehow");
  }

  const { data: devicePushTokenRecords, error: fetchDevicePushTokensError } =
    await supabase
      .from("device_push_tokens")
      .select("device_type, token, is_active, is_active_in_staging")
      .eq("user_id", userId)
      .order("id", { ascending: false }) // prioritize most recent push tokens
      .or(
        "is_active.eq.true,and(device_type.eq.ios,is_active_in_staging.eq.true)",
      );

  console.log("devicePushTokenRecords", devicePushTokenRecords);
  if (fetchDevicePushTokensError) {
    addSpanEvent("fetchDevicePushTokensError");
    const error = wrappedSupabaseError(
      fetchDevicePushTokensError,
      "fetch device push tokens failed",
    );
    logError({
      context:
        "**** sendPushNotificationsForProfileId - fetch device push tokens failed",
      error,
      profileId,
      notificationId: notification.id,
    });
    throw error;
  }
  const separator = notificationLink.includes("?") ? "&" : "?";
  notificationLink = `${notificationLink}${separator}nid=${notification.id}`;

  const fetchBadgeCountFunc = lazyInit(async () => {
    const fetchedBadgeCount = await calculateNotificationBadgeCount({
      profileId,
      notificationId: notification.id,
    });
    return fetchedBadgeCount;
  });

  const promises = devicePushTokenRecords.map(async (devicePushTokenRecord) => {
    if (devicePushTokenRecord.device_type === "android") {
      await sendAndroidPushNotification(
        devicePushTokenRecord.token,
        notificationTitle,
        notificationText,
        notificationLink,
        notificationData,
        notification,
      );
    } else {
      await sendApplePushNotificationsForProfileId({
        devicePushTokenRecord,
        userId,
        profileId,
        notification,
        notificationTitle,
        notificationText,
        notificationLink,
        notificationData,
        fetchBadgeCountFunc,
      });
    }
    loggingInfo("notification", {
      profile_id: profileId,
      notification_id: notification.id,
      notification: notification,
      notification_body: {
        title: notificationTitle,
        text: notificationText,
        link: notificationLink,
      },
      notification_data: notificationData,
      latency: new Date() - startTime,
      device_type: devicePushTokenRecord.device_type,
      type: "send",
    });
  });

  const settledPromises = await Promise.allSettled(promises);

  const succeeded = [];
  const failed = [];
  for (let i = 0; i < settledPromises.length; i++) {
    const devicePushTokenRecord = devicePushTokenRecords[i];
    const settledPromiseResult = settledPromises[i];
    if (settledPromiseResult.status === "fulfilled") {
      succeeded.push(devicePushTokenRecord);
    } else {
      failed.push(devicePushTokenRecord);
    }
  }

  logInfo({
    context: "sendPushNotificationsForProfileId",
    message: `Push notifications sent for notification id ${notification.id} to all device tokens of profile id ${profileId}`,
    notification_id: notification.id,
    notification,
    notificationTitle,
    notificationText,
    notificationLink,
    notificationData,
    succeeded,
    failed,
  });
}

const sendAndroidPushNotification = decorateWithActiveSpanAsync(
  "sendAndroidPushNotification",
  _sendAndroidPushNotification,
);
async function _sendAndroidPushNotification(
  deviceToken,
  alertTitle,
  alertBody,
  notificationLink,
  notifyData,
  notification,
) {
  addSpanEvent("sendAndroidPushNotification");
  const notification_id = notification.id;

  const message = {
    notification: {
      title: alertTitle,
      body: alertBody,
    },
    data: {
      notifyData: JSON.stringify(notifyData),
      notification_id: `${notification_id}`,
      notificationLink: notificationLink,
    },
    token: deviceToken,
    android: {
      notification: {
        tag: getCollapseId(notification),
        channelId: "default",
      },
      // Collapse key ensures notifications in same group get updated/replaced
      collapseKey: getCollapseId(notification),
    },
  };
  try {
    const response = await admin.messaging().send(message);
    loggingInfo("notification", {
      android_notification: message,
      device_token: deviceToken,
      device_type: "android",
      environment: "prod",
      android_response: response,
      pushed: true,
      type: "push",
    });
    addSpanEvent("androidPushSent");
    return true;
  } catch (error) {
    addSpanEvent("androidPushError");
    loggingInfo("notification", {
      android_notification: message,
      device_token: deviceToken,
      device_type: "android",
      environment: "prod",
      android_error: error,
      pushed: false,
      type: "push",
    });
    return false;
  }
}

const calculateNotificationBadgeCount = decorateWithActiveSpanAsync(
  "calculateNotificationBadgeCount",
  _calculateNotificationBadgeCount,
);
async function _calculateNotificationBadgeCount({ profileId, notificationId }) {
  const result = await Promise.all([
    supabase.rpc("get_unread_notification_count", { profileid: profileId }),
    supabase
      .from("conversation_participants_with_last_message")
      .select("count", { count: "exact" })
      .eq("profile_id", profileId)
      .eq("last_message_read", false)
      .neq("last_message_sender", profileId),
  ]);

  logInfo({ context: "*** calculateNotificationBadgeCount result", result });

  let [
    { data: notifyCount, error: notifyCountError },
    { count: messageCount, error: messageCountError },
  ] = result;

  // IMO when either of those fails, we should return undefined

  let badgeCount = 0;
  if (notifyCountError) {
    const error = wrappedSupabaseError(
      notifyCountError,
      "fetch notification count failed",
    );
    logError({
      context:
        "**** sendPushNotificationsForProfileId - fetch notification count failed",
      error,
      profileId,
      notificationId,
    });
  } else {
    badgeCount += notifyCount;
  }

  if (messageCountError) {
    const error = wrappedSupabaseError(
      messageCountError,
      "fetch message count failed",
    );
    logError({
      context:
        "**** sendPushNotificationsForProfileId - fetch message count failed",
      error,
      profileId,
      notificationId,
    });
  } else {
    badgeCount += messageCount;
  }

  return badgeCount;
}

const sendApplePushNotificationsForProfileId = decorateWithActiveSpanAsync(
  "sendApplePushNotificationsForProfileId",
  _sendApplePushNotificationsForProfileId,
);
async function _sendApplePushNotificationsForProfileId({
  devicePushTokenRecord,
  userId,
  profileId,
  notification,
  notificationTitle,
  notificationText,
  notificationLink,
  notificationData,
  fetchBadgeCountFunc,
}) {
  addSpanEvent("sendApplePushNotificationsForProfileId");
  const badgeCount = await fetchBadgeCountFunc();

  console.log("GET THE DEVICE TOKEN", devicePushTokenRecord.token);

  if (devicePushTokenRecord.is_active === true) {
    let { shouldPruneDeviceToken } = await sendApplePushNotification(
      devicePushTokenRecord.token,
      notificationTitle,
      notificationText,
      badgeCount,
      notificationLink,
      notificationData,
      notification,
      "prod",
    );
    if (shouldPruneDeviceToken === true) {
      // set the device token to be inactive
      await supabase
        .from("device_push_tokens")
        .update({ is_active: false })
        .eq("token", devicePushTokenRecord.token);
    }
  }
  if (devicePushTokenRecord.is_active_in_staging === true) {
    let { shouldPruneDeviceToken } = await sendApplePushNotification(
      devicePushTokenRecord.token,
      notificationTitle,
      notificationText,
      badgeCount,
      notificationLink,
      notificationData,
      notification,
      "staging",
    );

    if (shouldPruneDeviceToken === true) {
      const { error: updateStagingActiveError } = await supabase
        .from("device_push_tokens")
        .update({ is_active_in_staging: false })
        .eq("user_id", userId);

      if (updateStagingActiveError) {
        const error = wrappedSupabaseError(updateStagingActiveError);
        logError({
          context:
            "**** sendApplePushNotificationsForProfileId - update is_active_in_staging failed",
          error,
          profileId,
          notificationId: notification.id,
        });
      }
    }
  }
}

function getCollapseId(notification) {
  const sourceType = notification.source_type || "butterflies";
  if (["post", "post_like", "follower"].includes(sourceType)) {
    const bucketizedTime = Math.floor(Date.now() / (15 * 60 * 1000)); // 15 min bucket
    return `${sourceType}_${bucketizedTime}`;
  } else {
    const notificationId =
      notification.id || Math.floor(Math.random() * 1000000);
    return `${sourceType}_${notificationId}`;
  }
}

const sendApplePushNotification = decorateWithActiveSpanAsync(
  "sendApplePushNotification",
  _sendApplePushNotification,
);
async function _sendApplePushNotification(
  deviceToken,
  alertTitle,
  alertBody,
  badgeCount,
  notificationLink,
  notifyData,
  notification,
  environment,
) {
  const notification_id = notification.id;
  const notificationImageUrl = notification.image_url;
  let apnNotification = new apn.Notification({
    alert: {
      title: alertTitle,
      body: alertBody,
    },
    badge: badgeCount,
    sound: "notification.aiff",
    topic: "ai.butterflies.ios",
    mutableContent: 1,
    collapseId: getCollapseId(notification),
    payload: {
      notifyData: notifyData,
      notification_id: notification_id,
      notificationLink: notificationLink,
      media_url: notificationImageUrl, // URL of your image
    },
  });

  const apnProvider =
    environment === "staging" ? stageApnProvider : prodApnProvider;
  let shouldPruneDeviceToken = false;

  try {
    let pushed = true;
    const response = await apnProvider.send(apnNotification, deviceToken);
    if (response.failed?.length > 0) {
      addSpanEvent("apnPushFailed");
      const failedReason = response.failed[0].response.reason;
      if (
        failedReason === "BadDeviceToken" ||
        failedReason === "Unregistered"
      ) {
        pushed = false;
        shouldPruneDeviceToken = true;
      }
    } else {
      addSpanEvent("apnPushSent");
    }
    loggingInfo("notification", {
      ios_notification: apnNotification,
      device_token: deviceToken,
      device_type: "ios",
      environment: environment,
      ios_response: response,
      pushed: pushed,
      type: "push",
    });
    return { shouldPruneDeviceToken };
  } catch (error) {
    console.log("ERROR");
    loggingInfo("notification", {
      ios_notification: apnNotification,
      device_token: deviceToken,
      device_type: "ios",
      environment: environment,
      ios_error: error,
      pushed: true, //TODO(Mehrdad): to go over exceptions
      type: "push",
    });
    return { shouldPruneDeviceToken }; //TODO(Mehrdad): to go over exceptions
  }
}

app.post("/deleteNotification", authUser, async (req, res) => {
  const { notificationId } = req.body;

  if (!notificationId) {
    return res.status(400).send({
      data: null,
      error: "Invalid input error: notificationId is required",
    });
  }

  try {
    const { data, error } = await supabase
      .from("notifications")
      .delete()
      .eq("id", notificationId);

    if (error) {
      const notificationError = wrappedSupabaseError(error);
      throw notificationError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete Notification Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to delete notification" });
  }
});

// accept follow reqeust notification
app.post("/deleteFRNotification", authUser, async (req, res) => {
  const { profile_id, sender_profile_id } = req.body;

  if (!profile_id || !sender_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("notifications")
      .delete()
      .eq("profile_id", profile_id)
      .eq("sender_profile_id", sender_profile_id)
      .eq("additional_info->>status", "requested");

    if (error) {
      const notificationError = wrappedSupabaseError(error);
      throw notificationError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete Follow Request Notification Failed Error",
      error: error,
    });
    return res.status(500).send({
      data: null,
      error: "Failed to delete follow request notification",
    });
  }
});

app.post("/updateNotification", authUser, async (req, res) => {
  const { updateContents, queryContents } = req.body;

  if (
    !updateContents ||
    Object.keys(updateContents).length === 0 ||
    !queryContents ||
    Object.keys(queryContents).length === 0
  ) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  let query = supabase.from("notifications").update(updateContents);
  Object.keys(queryContents).map((key) => {
    query = query.eq(key, queryContents[key]);
  });

  try {
    const { data, error } = await query;

    if (error) {
      const notificationError = wrappedSupabaseError(error);
      throw notificationError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update Notification Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to update notifications" });
  }
});

app.post("/insertNotification", authUser, async (req, res) => {
  // TODO: Secure this. Currently anyone (who is authenticated) can send any notification to any user.

  const { insertContents } = req.body;

  if (!insertContents || Object.keys(insertContents).length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("notifications")
      .insert(insertContents);

    if (error) {
      const notificationError = wrappedSupabaseError(error);
      throw notificationError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert Notification Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add notifications" });
  }
});

app.post("/upsertNotification", authUser, async (req, res) => {
  // TODO: Secure this. Currently anyone (who is authenticated) can send any notification to any user.

  const { upsertContents } = req.body;

  if (!upsertContents || upsertContents.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid  error" });
  }

  try {
    const { data, error } = await supabase
      .from("notifications")
      .upsert(upsertContents);

    if (error) {
      const notificationError = wrappedSupabaseError(error);
      throw notificationError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Upsert Notification Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to upsert notifications" });
  }
});

app.post("/subscribeNotifications", authUser, async (req, res) => {
  const { details } = req.body;
  if (!details || !details.subscriber_id || !details.subscribing_id) {
    return res.status(400).send({ data: null, error: "Invalid  error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, details?.subscriber_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("subscribers")
      .insert(details)
      .select("*");

    if (error) throw wrappedSupabaseError(error);
    return res.send({ data, erorr: null });
  } catch (error) {
    if (error.code === PostgresErrorCode.UNIQUE_VIOLATION) {
      logWarn({
        executionId: req.executionId,
        context: "Insert subscribe notification Duplicate",
        message: `Duplicate subscribe notification - ${details.subscriber_id}, ${details.subscribing_id}`,
      });
      return res.sendStatus(204);
    }
    logError({
      executionId: req.executionId,
      context: "Subscribe Notification Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to subscribe notifications" });
  }
});

app.post("/unsubscribeNotifications", authUser, async (req, res) => {
  const { details } = req.body;
  if (!details || !details.subscriber_id || !details.subscribing_id) {
    return res.status(400).send({ data: null, error: "Invalid  error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, details?.subscriber_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("subscribers")
      .delete()
      .eq("subscriber_id", details.subscriber_id)
      .eq("subscribing_id", details.subscribing_id);

    if (error) throw wrappedSupabaseError(error);
    return res.send({ data, erorr: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Unsubscribe Notification Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to unsubscribe notifications" });
  }
});

app.get("/bot_count_test", async (req, res) => {
  const botCount = await getDataWithCache(
    getBotCount,
    redisClient,
    "botcount",
    req.query.profile_id,
    24 * 60 * 60, // 1 day
  );
  res.json({ botCount });
});

async function getBotCount(profile_id) {
  const { count: botCount, error: botCountError } = await supabase
    .from("bots")
    .select("count", { count: "exact" })
    .eq("creator_id", profile_id);

  return botCountError ? 0 : botCount;
}

app.get("/unreadNotificationsCount", authUser, async (req, res) => {
  const { profileId } = req.query;
  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data: count, error } = await supabase.rpc(
      "get_unread_notification_count",
      { profileid: profileId },
    );

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data: { count }, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch the unread notifications count",
      error,
    });
    return res.sendStatus(500);
  }
});

app.get("/getNotifications", authUser, async (req, res) => {
  const { profileId, cursor, limit } = req.query;
  const limitInt = parseInt(limit, 10);
  if (!profileId || !limit || isNaN(limitInt) || limitInt <= 0 || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase.rpc("get_notifications", {
      profileid: profileId,
      cursor: cursor === "init" ? null : cursor,
      limit_int: limitInt,
    });

    if (error) throw wrappedSupabaseError(error);

    const next_cursor =
      data.length === limitInt
        ? dayjs(data[data.length - 1].created_at).format(
            "YYYY-MM-DDTHH:mm:ss.SSS",
          )
        : null;
    return res.send({ data, page_info: { next_cursor }, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch the notifications",
      error,
    });
    return res.sendStatus(500);
  }
});

app.post("/getNotification", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .rpc("get_notification_with_id", { noti_id: id })
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch the notifications",
      error,
    });
    return res.sendStatus(500);
  }
});

module.exports = {
  app,
  sendPushNotificationsForProfileId,
  checkEnableToSendPushNotify,
  maybeSendPushForConversation,
};
