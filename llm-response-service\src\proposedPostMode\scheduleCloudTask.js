const { CloudTasksClient } = require("@google-cloud/tasks");

const { logWarn, logInfo } = require("../logUtils");

async function createAndScheduleCloudTask({
  queueName,
  taskId, // can be undefined
  taskURL,
  taskPayload,
  scheduleDate,
}) {
  const client = new CloudTasksClient();
  const parent = client.queuePath("butterflies-ai", "us-central1", queueName);

  const url = taskURL;

  const payload = taskPayload;

  const taskName = client.taskPath(
    "butterflies-ai",
    "us-central1",
    "v1-proposed-posts-reminders",
    taskId,
  );

  const task = {
    name: taskName,
    httpRequest: {
      httpMethod: "POST",
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN}`,
      },
    },

    scheduleTime: {
      seconds: scheduleDate.getTime() / 1000,
    },
  };
  const request = { parent: parent, task: task };
  try {
    logInfo({
      context: "createAndScheduleCloudTask",
      message: "Creating cloud task...",
      queueName,
      taskId,
      scheduleDate,
      payload,
    });
    const task = await client.createTask(request);
    return task;
  } catch (error) {
    // ALREADY_EXISTS
    if (error.code === 6) {
      logWarn({
        context: "createAndScheduleCloudTask",
        message: `task already exists`,
        error: error,
        queueName,
        taskId,
        scheduleDate,
        payload,
      });
    } else {
      throw error;
    }
  }
}

module.exports = {
  createAndScheduleCloudTask,
};
