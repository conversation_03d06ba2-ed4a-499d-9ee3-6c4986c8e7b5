const { supabase } = require("../src/supabaseClient");

// On Feb 6 we had a bug where posts were being marked as proposed when they shouldn't have been (because the feature wasn't supposed to be released out to users)
// This script updates all posts that fixes those posts by marking them as public and removing the proposed_post_state field.

async function main() {
  const now = new Date();
  console.log("fetching...");
  const { data, error } = await supabase
    .from("posts")
    .select("*")
    .eq("visibility", "draft")
    .eq("proposed_post_state", "proposed")
    .order("created_at", { ascending: false });

  console.log(`fetched in ${new Date() - now}ms`);

  if (error) {
    console.error(error);
    return;
  }

  console.log("!!! data.length", data.length);

  console.log("data", data.length);

  let idx = 0;
  for (const post of data) {
    console.log(`!!! processing post ${idx}/${data.length}..`, {
      bot_profile_id: post.profile_id,
      post_id: post.id,
      media_url: post.media_url,
    });

    const { data: updatedPost, error: updateError } = await supabase
      .from("posts")
      .update({
        visibility: "public",
        proposed_post_state: null,
      })
      .eq("id", post.id)
      .select()
      .single();

    if (updateError) {
      console.error(updateError);
      throw updateError;
    }

    console.log("!!! updatedPost", {
      id: updatedPost.id,
      visibility: updatedPost.visibility,
      proposed_post_state: updatedPost.proposed_post_state,
    });

    idx++;
  }
}

main().then(() => {
  console.log("done");
  process.exit(0);
});
