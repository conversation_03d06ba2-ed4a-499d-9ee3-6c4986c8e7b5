const IS_MEMORIES_ENABLED = true;
const IS_MESSAGE_REQUEST_ENABLED = true;
const IS_MENTIONED_POSTS_ENABLED = true;
const IS_PRO_ACTIVE_ENABLED = true;
const IS_SEND_IMAGE_ENABLED = false;
const IS_STRIPE_INSTANT_REPLY_ENABLED = false;
const IS_STRIPE_POKES_ENABLED = process.env.LOCAL ? false : true;
const IS_WAITLIST_ENABLED = true;

const LAUNCH_PROPOSED_POST_MODE_V1_EXPERIMENT = true;

const INIT_FOLLOW_COUNT = 30; // Define your initial count
const FOLLOW_COUNT = 10; // Define subsequent fetch count

const SUBMISSION_QUOTA = 3;

const IMAGE_WIDTH_LIST = [64, 128, 240, 640, 1280, 1800];

const IMITATION_TYPE = {
  NORMAL: "normal",
  FICTIONAL: "fictional",
  CELEBRITY: "celebrity",
};

const AGE_TYPE = {
  CHILD: "child",
  ADULT: "adult",
};

const PLAN_TYPE = {
  FREE: "free",
  PLUS: "plus",
};

const DEFAULT_TEMPERATURES = {
  REALISM: 0.7,
  ROLEPLAY: 0.7,
};

const REDEMPTION_COUNT_LIMIT = 2;

const REDEMPTION_TYPE = {
  INVITE: "invite",
  POST_SHARE: "post_share",
  INVITER: "inviter",
  INVITEE: "invitee",
  POST_SHARER: "post_sharer",
  POST_SHAREE: "post_sharee",
};

const REDEMPTION_VALUE_PREMIUM_DAYS = {
  invite: 14,
  post_share: 14,
};

const REDEMPTION_VALUE_EXTRA_QUOTA = {
  invite: 50,
  post_share: 50,
};

const PACKAGE_TYPE = {
  IMAGE_IN_CHAT: 1,
  POKES: 3,
  POST_IMAGE_REGENERATION: 8,
  ENTIRE_POST_REGENERATION: 9,
};

const BOT_CREATE_LIMIT = 25;

const TOGETHERAI_MODELS = [
  "zero-one-ai/Yi-34B-Chat",
  "mistralai/Mistral-7B-Instruct-v0.2",
  "mistralai/Mixtral-8x7B-Instruct-v0.1",
  "mistralai/Mixtral-8x22B-Instruct-v0.1",
  "mistralai/Mixtral-8x7B-v0.1",
  "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO",
  "NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT",
  "teknium/OpenHermes-2p5-Mistral-7B",
  "Qwen/Qwen1.5-14B-Chat",
  "NousResearch/Nous-Hermes-2-Yi-34B",
  "mistralai/Mixtral-8x7B-Instruct-v0.1",
  "meta-llama/Llama-3-8b-chat-hf",
  "meta-llama/Llama-3-70b-chat-hf",
  "meta-llama/Meta-Llama-3-8B",
  "meta-llama/Meta-Llama-3-8B-Instruct",
  "meta-llama/Meta-Llama-3-70B-Instruct",
];

const OPENROUTHER_MODELS = [
  "neversleep/noromaid-mixtral-8x7b-instruct",
  "koboldai/psyfighter-13b-2",
  "pygmalionai/mythalion-13b",
  "cognitivecomputations/dolphin-mixtral-8x7b",
  "cognitivecomputations/dolphin-2.5-mixtral-8x7b",
  "cognitivecomputations/dolphin-2.9-llama3-8b",
  "lynn/soliloquy-l3",
  "neversleep/llama-3-lumimaid-8b",
  "openchat/openchat-8b",
];

const FIREWORKS_MODELS = ["accounts/leo-262e34/models/l3-rp-v33"];

const LLM_SERVICE_MODELS = [
  "/data/l3-rp-v33",
  "realism-chat-llm",
  "roleplay-llm",
  "post-reply-comment-llm",
];

const SUPPORTED_NOTIFICATION_SOURCE_TYPES = [
  "post_like",
  "post_comment",
  "post_comment_like",
  "post_comment_reply",
  "follower",
  "post",
  "mention",
  "message_request",
  "bot_create",
  "message_reaction",
  "invite_accepted",
  "redemption",
  "subscription",
  "bot_tagged",
];

const SUPPORTED_CATEGORIES = [
  "animal",
  "anime",
  "art",
  "artist",
  "beachlife",
  "beauty",
  "cartoon",
  //"celebrity",
  "cheerleader",
  "cooking",
  //"culture",
  "dancing",
  "fiction",
  "fitness",
  "foodie",
  "friendship",
  "gardening",
  "hiking",
  "international",
  "medieval",
  "mermaid",
  "movies",
  "music",
  "outdoor",
  "party",
  "pet",
  "politics",
  "school",
  "sports",
  "superhero",
  "yoga",
];

const SAFE_PROFILES = [
  1455, 1451, 1446, 1428, 1386, 1384, 1368, 1362, 1311, 1309, 1174, 1118, 1091,
  1088, 1086, 1080, 1070, 995, 970, 906, 852, 834, 200, 854, 832, 830, 818, 806,
  781, 719, 699, 1082, 1084, 1145,
];

module.exports = {
  IS_MEMORIES_ENABLED,
  IS_MESSAGE_REQUEST_ENABLED,
  IS_MENTIONED_POSTS_ENABLED,
  IS_PRO_ACTIVE_ENABLED,
  IS_SEND_IMAGE_ENABLED,
  IS_STRIPE_INSTANT_REPLY_ENABLED,
  IS_STRIPE_POKES_ENABLED,
  IS_WAITLIST_ENABLED,
  IMAGE_WIDTH_LIST,
  IMITATION_TYPE,
  AGE_TYPE,
  BOT_CREATE_LIMIT,
  PLAN_TYPE,
  PACKAGE_TYPE,
  TOGETHERAI_MODELS,
  OPENROUTHER_MODELS,
  FIREWORKS_MODELS,
  LLM_SERVICE_MODELS,
  REDEMPTION_COUNT_LIMIT,
  REDEMPTION_VALUE_PREMIUM_DAYS,
  REDEMPTION_VALUE_EXTRA_QUOTA,
  REDEMPTION_TYPE,
  SUPPORTED_NOTIFICATION_SOURCE_TYPES,
  SUPPORTED_CATEGORIES,
  SAFE_PROFILES,
  DEFAULT_TEMPERATURES,
  INIT_FOLLOW_COUNT,
  FOLLOW_COUNT,
  LAUNCH_PROPOSED_POST_MODE_V1_EXPERIMENT,
  SUBMISSION_QUOTA,
};
