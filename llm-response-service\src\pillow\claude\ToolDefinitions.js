const ToolDefinitions = {
  lookup_self_facts: {
    name: "lookup_self_facts",
    description:
      "Look up facts about yourself, e.g. where you were born, where you live, where you go to school etc.",
    input_schema: {
      type: "object",
      required: ["query"],
      properties: {
        query: {
          type: "string",
          description:
            "short text that can be used to generate an embedding to query factual memories with",
        },
      },
    },
  },
  focus_to_recall_more_memories: {
    name: "focus_to_recall_more_memories",
    description:
      "Focus hard to recall more memories based on previous conversations with the user or other users",
    input_schema: {
      type: "object",
      required: ["query"],
      properties: {
        query: {
          type: "string",
          description:
            "short text that can be used to generate an embedding to query factual memories with. If recalling things about a particular user make sure to include their name. e.g. '<PERSON><PERSON>'s hobbies'",
        },
      },
    },
  },
};

module.exports = {
  ToolDefinitions,
};
