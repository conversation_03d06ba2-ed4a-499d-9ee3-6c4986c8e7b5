const { Bigtable } = require("@google-cloud/bigtable");
const protobuf = require("protobufjs");
const projectId = "butterflies-ai";
const instanceId = "profiles";
const profileTable = "profiles";
const postTable = "posts";
const followersTable = "followers";
const columnFamilyGraph = "graph";
const columnFamilyPostIds = "postids";
const columnFamilyPost = "post";
const columnFamilySitemap = "sitemap";
// const columnFamilyReads = "reads";
const columnFamilyScores = "scores";
const columnActiveHours = "utc";
const columnBotBot = "bb";
const columnFollowers = "f";
const columnExploreProtoIndex = "xpp";
const columnFollowingProtoIndex = "fwp";
const columnForYouProtoIndex = "4up";
const columnReelsProtoIndex = "rlp";
const columnProfileScoreProto = "sp";
const columnPost = "p";
const columnSitemapIndex = "i";
const trueByte = booleanToByte(true);
const { logError } = require("./logUtils");
const { tracer } = require("./instrumentation/tracer");
const { default: PQueue } = require("p-queue");

const CACHE_READS_SUFFIX = "reads";
const CACHE_READS_TTL = 7 * 24 * 60 * 60; // 1 week
const CACHE_SERVED_SUFFIX = "servedIds";
const CACHE_SERVED_TTL = 60 * 60; // 1 hour

const PROTO_PATH = __dirname + "/proto/feed.proto";

// eslint-disable-next-line no-unused-vars
function toUnderScoredKeys(obj) {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(toUnderScoredKeys);
  }

  const newObj = {};
  for (const key in obj) {
    const newKey = key.replace(
      /([A-Z])/g,
      (match) => `_${match.toLowerCase()}`,
    );
    newObj[newKey] = toUnderScoredKeys(obj[key]);
  }
  return newObj;
}

const proto = protobuf.loadSync([PROTO_PATH]);

async function readLatestValueWithCache(
  rowKey,
  columnFamily,
  column,
  tableId,
  redisClient,
  redisSuffix,
  ttl = 1800,
) {
  if (redisClient && redisSuffix) {
    const cachedData = await redisClient.get(getCacheKey(redisSuffix, rowKey));
    if (cachedData != null) {
      return cachedData;
    }
  }
  let data = await readLatestValue(rowKey, columnFamily, column, tableId);
  if (redisClient && redisSuffix) {
    if (data == null) {
      const IdArray = proto.lookupType("IdArray");
      const emptyProto = IdArray.create();
      data = Buffer.from(IdArray.encode(emptyProto).finish()).toString(
        "base64",
      );
    }
    if (data != null) {
      await redisClient.set(getCacheKey(redisSuffix, rowKey), data, {
        EX: ttl,
      });
    }
  }
  return data;
}

async function readLatestValue(rowKey, columnFamily, column, tableId) {
  const bigtable = new Bigtable({ projectId: projectId });
  const instance = bigtable.instance(instanceId);
  const table = instance.table(tableId);
  // console.debug(`\tBT (${tableId}): row=${rowKey}, family=${columnFamily}, column=${column}`);
  // only read the latest cell of the needed column qualifier!
  const filter = [
    {
      row: rowKey,
    },
    {
      family: columnFamily,
    },
    {
      column: column,
    },
    {
      column: {
        cellLimit: 1,
      },
    },
  ];

  try {
    const key = Buffer.from(rowKey);
    const [row] = await table.row(key).get({
      filter: filter,
    });

    if (row) {
      const cells = row.data[columnFamily][column];
      if (cells && cells.length > 0) {
        const latestCell = cells[0];
        return latestCell.value;
      } else {
        console.log(`No cells found for ${columnFamily}:${column}`);
      }
    } else {
      console.log(`Row with key ${rowKey} not found.`);
    }
  } catch (err) {
    console.log(`Row with key ${rowKey} not found`, err);
  }

  return null;
}

async function readLatestCell(rowKey, columnFamily, column, tableId) {
  let data = await readLatestValue(rowKey, columnFamily, column, tableId);
  if (data == null) {
    return { data: [] };
  } else {
    return JSON.parse(data);
  }
}

// async function readLatestIds(rowKey, columnFamily, column, tableId) {
//   let data = await readLatestValue(rowKey, columnFamily, column, tableId);
//   try {
//     if (data != null) {
//       const IdArray = proto.lookupType("IdArray");
//       const buffer = Buffer.from(data, "base64");
//       const idArrayMessage = IdArray.decode(buffer);
//       return { data: idArrayMessage.ids };
//     }
//   } catch (err) {
//     console.log(`Row with key ${rowKey} not suitable`, err);
//   }

//   return { data: [] };
// }

async function readLatestProfileScore(profileId) {
  let data = await readLatestValue(
    profileId.toString(),
    columnFamilyScores,
    columnProfileScoreProto,
    profileTable,
  );
  try {
    if (data != null) {
      const ProfileScore = proto.lookupType("ProfileScore");
      const buffer = Buffer.from(data, "base64");
      const profileScoreMessage = ProfileScore.decode(buffer);
      return { data: profileScoreMessage.likes };
    }
  } catch (err) {
    console.log(`Row with key ${profileId} not suitable`, err);
  }
  return { data: 0 };
}

async function readMultipleRows(rowKeys, columnFamily, column, tableId) {
  const data = await readMultipleRowValues(
    rowKeys,
    columnFamily,
    column,
    tableId,
  );
  let result = [];
  data.forEach((e) => {
    result.push(JSON.parse(e));
  });
  return result;
}

async function readMultipleRowValues(rowKeys, columnFamily, column, tableId) {
  const bigtable = new Bigtable({ projectId: projectId });
  const instance = bigtable.instance(instanceId);
  const table = instance.table(tableId);
  // console.debug(`\tBT (${tableId}): rows=${rowKeys}, family=${columnFamily}, column=${column}`);
  // only read the latest cell of the needed column qualifier!
  const filter = [
    {
      row: rowKeys,
    },
    {
      family: columnFamily,
    },
    {
      column: column,
    },
    {
      column: {
        cellLimit: 1,
      },
    },
  ];

  let data = [];
  if (rowKeys.length > 0) {
    try {
      const keys = rowKeys.map((rowKey) => Buffer.from(rowKey));
      const [rows] = await table.getRows({ keys: keys, filter: filter });

      rows.forEach((row) => {
        if (
          Object.prototype.hasOwnProperty.call(row.data, columnFamily) &&
          Object.prototype.hasOwnProperty.call(row.data[columnFamily], column)
        ) {
          const cells = row.data[columnFamily][column];
          if (cells && cells.length > 0) {
            const latestCell = cells[0];
            data.push(latestCell.value);
          }
        }
      });
    } catch (error) {
      console.error("Error reading rows:", error);
    }
  }
  return data;
}

// useful debugging function, when needed
// eslint-disable-next-line no-unused-vars
function printRow(rowkey, rowData) {
  console.log(`Reading data for ${rowkey}:`);

  for (const columnFamily of Object.keys(rowData)) {
    const columnFamilyData = rowData[columnFamily];
    console.log(`Column Family ${columnFamily}`);

    for (const columnQualifier of Object.keys(columnFamilyData)) {
      const col = columnFamilyData[columnQualifier];

      for (const cell of col) {
        const labels = cell.labels.length ? ` [${cell.labels.join(",")}]` : "";
        let datasize = cell.value.length;
        console.log(
          `\t${columnQualifier}: ${datasize} @${cell.timestamp}${labels}`,
        );
      }
    }
  }
  console.log();
}

async function writeToBigtable(tableId, rowKey, columnFamily, column, value) {
  const data = {};
  data[columnFamily] = {};
  data[columnFamily][column] = value;
  return putData(tableId, rowKey, data);
}

async function putData(tableId, rowKey, data) {
  const bigtable = new Bigtable({ projectId: projectId });
  const instance = bigtable.instance(instanceId);
  const table = instance.table(tableId);
  const row = table.row(rowKey);

  try {
    await row.save(data);
    return true;
  } catch (err) {
    console.error("BT Write Error", err);
    return false;
  }
}

// async function getIntegerColumns(tableId, rowKey, columnFamily) {
//   const bigtable = new Bigtable({ projectId: projectId });
//   const instance = bigtable.instance(instanceId);
//   const table = instance.table(tableId);
//   const filter = [
//     {
//       row: rowKey,
//     },
//     {
//       family: columnFamily,
//     },
//     {
//       column: {
//         cellLimit: 1,
//       },
//     },
//   ];
//   let columns = new Set();
//   try {
//     const [row] = await table.row(rowKey).get({
//       filter: filter,
//     });

//     if (row) {
//       Object.entries(row.data[columnFamily]).forEach(([columnName]) => {
//         columns.add(Number(columnName));
//       });
//     }
//   } catch (err) {
//     console.log(`Row with key ${rowKey} not found for IntegerColumn.`, err);
//   }
//   return columns;
// }

const sitemapCrawlerQueue = new PQueue({ concurrency: 7 });

// TODO: add a special "letter" for characters that use non-latin characters
// TODO: add 0 to 9
const allSitemapLetters = Array.from({ length: 26 }, (_, i) =>
  String.fromCharCode(97 + i),
);

async function getSiteMapIndex(redisClient) {
  if (redisClient) {
    let data = await fetchFromCache(redisClient, "sitemap_index", "index");
    if (data != null) {
      return data;
    }
  }

  const task = async (letter) => {
    const letterData = await getSiteMap(letter, undefined, redisClient);
    return {
      letter,
      letterData,
    };
  };

  const allResults = await sitemapCrawlerQueue.addAll(
    allSitemapLetters.map((letter) => {
      return () => task(letter);
    }),
  );

  const data = {};
  for (const result of allResults) {
    data[result.letter] = result.letterData;
  }

  if (redisClient) {
    putToCache(redisClient, "sitemap_index", "index", data, 6 * 60 * 60); // 6 hours
  }

  return data;
}

async function getSiteMap(first_letter, index, redisClient) {
  const key = index
    ? `${first_letter.toLowerCase()}_${index}`
    : first_letter.toLowerCase();
  if (redisClient) {
    let data = await fetchFromCache(redisClient, "sitemap", key);
    if (data != null) {
      return data;
    }
  }
  let data = await readLatestCell(
    key,
    columnFamilySitemap,
    columnSitemapIndex,
    profileTable,
  );
  if (redisClient) {
    putToCache(redisClient, "sitemap", key, data, 6 * 60 * 60); // 6 hours
  }
  return data;
}

async function getActiveHours(profileId, redisClient) {
  if (redisClient) {
    let data = await fetchFromCache(redisClient, "activets", profileId);
    if (data != null) {
      return data;
    }
  }
  let data = await readLatestCell(
    profileId.toString(),
    columnFamilyGraph,
    columnActiveHours,
    profileTable,
  );
  if (redisClient) {
    putToCache(redisClient, "activets", profileId, data, 24 * 60 * 60);
  }
  return data;
}

async function getBotBot(profileId, redisClient) {
  if (redisClient) {
    let data = await fetchFromCache(redisClient, "botbot", profileId);
    if (data != null) {
      // Cache hit!
      return data;
    }
  }
  let data = await readLatestCell(
    profileId.toString(),
    columnFamilyGraph,
    columnBotBot,
    profileTable,
  );
  if (redisClient) {
    putToCache(redisClient, "botbot", profileId, data, 3600);
  }
  return data;
}

async function getProfilePostFollowing(profileId, feedType, redisClient) {
  let suffix, family;

  switch (feedType) {
    case "following":
      suffix = "fwp";
      family = columnFollowingProtoIndex;
      break;
    case "for_you":
      suffix = "4up";
      family = columnForYouProtoIndex;
      break;
    case "explore":
      suffix = "xpp";
      family = columnExploreProtoIndex;
      break;
    case "reels":
      suffix = "rlp";
      family = columnReelsProtoIndex;
      break;
    default:
      throw new Error(`Unknown feed: ${feedType}`);
  }
  let data = await readLatestValueWithCache(
    profileId.toString(),
    columnFamilyPostIds,
    family,
    profileTable,
    redisClient,
    suffix,
    5 * 60, // 5 min
  );
  if (data != null) {
    const IdArray = proto.lookupType("IdArray");
    const buffer = Buffer.from(data, "base64");
    const idArrayMessage = IdArray.decode(buffer);
    return idArrayMessage;
  } else {
    return null;
  }
}

// async function getProfilePostExplore(profileId, redisClient) {
//   let data;
//   const suffix = "xp";
//   if (redisClient) {
//     let data = await fetchFromCache(redisClient, suffix, profileId);
//     if (data != null) {
//       return data;
//     }
//   }
//   if (FEED_POST_ID_PROTO_ENABLED) {
//     data = await readLatestIds(
//       profileId.toString(),
//       columnFamilyPostIds,
//       columnExploreProtoIndex,
//       profileTable,
//     );
//   } else {
//     // Deprecating to read json from BT
//     data = await readLatestCell(
//       profileId.toString(),
//       columnFamilyPostIds,
//       columnExploreIndex,
//       profileTable,
//     );
//   }
//   if (redisClient) {
//     await putToCache(redisClient, suffix, profileId, data, 5 * 60); // 5 min
//   }
//   return data;
// }

async function getByMultiplePostId(postIds) {
  return readMultipleRows(postIds, columnFamilyPost, columnPost, postTable);
  //return readMultiplePosts(postIds, columnFamilyPost, "pp", postTable);
}

async function getReads(redisClient, profileId) {
  return await fetchArrayFromCache(
    redisClient,
    CACHE_READS_SUFFIX,
    profileId,
    CACHE_READS_TTL,
  ); // 1 day
  // return getIntegerColumns(
  //   profileTable,
  //   profileId.toString(),
  //   columnFamilyReads,
  // );
}

async function putReads(redisClient, profileId, postIds) {
  // loggingInfo("post-reads", {profile_id:profileId, post_ids: postIds,});
  appendToCache(
    redisClient,
    CACHE_READS_SUFFIX,
    profileId,
    postIds,
    CACHE_READS_TTL,
    true,
  );
  // const data = {};
  // data[columnFamilyReads] = {};
  // postIds?.forEach((postId) => {
  //   data[columnFamilyReads][postId] = trueByte;
  // });
  // return putData(profileTable, profileId.toString(), data);
}

async function putFollowers(followerId, followingId, isFollowing) {
  let rowKey = `${followerId}:${followingId}`;
  return writeToBigtable(
    followersTable,
    rowKey,
    columnFamilyGraph,
    columnFollowers,
    booleanToByte(isFollowing),
  );
}

async function isFollowingBT(followerId, followingId) {
  let rowKey = `${followerId}:${followingId}`;
  const bigtable = new Bigtable({ projectId: projectId });
  const instance = bigtable.instance(instanceId);
  const table = instance.table(followersTable);
  const filter = [
    {
      row: rowKey,
    },
    {
      family: columnFamilyGraph,
    },
    {
      column: columnFollowers,
    },
    {
      column: {
        cellLimit: 1,
      },
    },
  ];

  try {
    const [row] = await table.row(rowKey).get({
      filter: filter,
    });

    if (row) {
      const cells = row.data[columnFamilyGraph][columnFollowers];
      if (cells && cells.length > 0) {
        const latestCell = cells[0];
        return latestCell.value == trueByte;
      } else {
        return false;
      }
    } else {
      return false;
    }
  } catch (err) {
    console.log(`Row with key ${rowKey} not found for isFollowingBT.`, err);
    return false;
  }
}

async function getFollowingsBT(followerId) {
  let rowPrefix = `${followerId}:`;
  const bigtable = new Bigtable({ projectId: projectId });
  const instance = bigtable.instance(instanceId);
  const table = instance.table(followersTable);
  const columnFamily = columnFamilyGraph;
  const column = columnFollowers;

  const filter = [
    {
      row: `^${rowPrefix}.*`,
    },
    {
      family: columnFamily,
    },
    {
      column: column,
    },
    {
      column: {
        cellLimit: 1,
      },
    },
  ];

  let data = new Set();
  try {
    const [rows] = await table.getRows({ filter: filter });

    rows.forEach((row) => {
      if (
        Object.prototype.hasOwnProperty.call(row.data, columnFamily) &&
        Object.prototype.hasOwnProperty.call(row.data[columnFamily], column)
      ) {
        const cells = row.data[columnFamily][column];
        if (cells && cells.length > 0) {
          const latestCell = cells[0];
          if (latestCell.value == trueByte) {
            let key = row.id; // key format: {follower_id:following_id}
            data.add(parseInt(key.split(":")[1], 10));
          }
        }
      }
    });
  } catch (error) {
    console.error("Error reading rows:", error);
  }
  return data;
}

function booleanToByte(boolValue) {
  const buffer = Buffer.alloc(1);
  buffer.writeUInt8(boolValue ? 1 : 0, 0);
  return buffer;
}

function getCacheKey(suffix, id) {
  return `${id}_${suffix}`;
}

async function fetchFromCache(client, suffix, id) {
  return await tracer.withActiveSpan(
    `fetchFromCache ${suffix}`,
    async (_span) => {
      try {
        const cachedData = await client.get(getCacheKey(suffix, id));
        if (cachedData != null) {
          const parsed = JSON.parse(cachedData);
          return parsed;
        }
      } catch (error) {
        // span.setException(error);
        // span.setStatus({ code: SpanStatusCode.ERROR, message: error.message });

        logError({
          context: "**** btClient.js: cache read exception",
          error: error,
          suffix,
          id,
        });
      }
      return null;
    },
  );
}

async function putToCache(client, suffix, id, data, ttl = 1800) {
  return await tracer.withActiveSpan(`putToCache ${suffix}`, async (_span) => {
    if (!client) {
      return;
    }
    if (!data) {
      return;
    }

    try {
      const cacheKey = getCacheKey(suffix, id);
      const stringified = JSON.stringify(data);
      await client.set(cacheKey, stringified, {
        EX: ttl,
      });
    } catch (error) {
      logError({
        context: "**** btClient.js: cache write exception",
        error: error,
        suffix,
        id,
      });
      throw error;
    }
  });
}

async function appendToCache(
  client,
  suffix,
  id,
  values,
  ttl = 1800,
  add = true,
) {
  return await tracer.withActiveSpan(
    `appendToCache ${suffix}`,
    async (_span) => {
      if (client && id && values) {
        try {
          const currentTime = Date.now();
          const cacheKey = getCacheKey(suffix, id);
          const cachedData = await client.get(cacheKey);
          let items = cachedData ? JSON.parse(cachedData) : [];
          if (add) {
            values.forEach((newValue) => {
              const existingItemIndex = items.findIndex(
                (entry) => entry.value === newValue,
              );
              if (existingItemIndex !== -1) {
                items[existingItemIndex].timestamp = currentTime;
              } else {
                items.push({ value: newValue, timestamp: currentTime });
              }
            });
          } else {
            items = items.filter((entry) => !values.includes(entry.value));
          }
          const expiredMillis = currentTime - ttl * 1000;
          items = items.filter((entry) => entry.timestamp > expiredMillis);
          const stringified = JSON.stringify(items);
          await client.set(cacheKey, stringified, {
            EX: ttl,
          });
          return items;
        } catch (error) {
          logError({
            context: "**** btClient.js: cache write exception",
            error: error,
            suffix,
            id,
          });
          throw error;
        }
      }
      return [];
    },
  );
}

async function fetchArrayFromCache(client, suffix, id, ttl = 1800) {
  return await tracer.withActiveSpan(
    `fetchArrayFromCache ${suffix}`,
    async (_span) => {
      if (client && id) {
        try {
          const cachedData = await client.get(getCacheKey(suffix, id));
          if (cachedData != null) {
            const parsed = JSON.parse(cachedData);
            const expiredMillis = Date.now() - ttl * 1000;
            const values = parsed
              .filter((entry) => entry.timestamp > expiredMillis)
              .map((entry) => entry.value);
            return values;
          }
        } catch (error) {
          logError({
            context: "**** btClient.js: cache read exception",
            error: error,
            suffix,
            id,
          });
        }
      }
      return [];
    },
  );
}

async function getDataWithCache(func, redisClient, suffix, id, ttl = 1800) {
  return await tracer.withActiveSpan(
    `getDataWithCache ${suffix}`,
    async (span) => {
      if (redisClient) {
        let data = await fetchFromCache(redisClient, suffix, id);
        if (data != null) {
          return data;
        }
      }
      let data = await func(id);
      if (redisClient) {
        putToCache(redisClient, suffix, id, data, ttl).catch((_error) => {
          // We catch it to ensure it's not considered uncaught,
          // but then we intentionally ignore the error here since there
          // is nothing useful we can do at this level.
          //
          // The error has already been logged inside putToCache.
        });
      }
      return data;
    },
  );
}

async function getServedIds(redisClient, id) {
  // let data = await fetchFromCache(redisClient, "served", id); if (data == null) { return []; }
  let data = await fetchArrayFromCache(
    redisClient,
    CACHE_SERVED_SUFFIX,
    id,
    CACHE_SERVED_TTL,
  );
  return data;
}

async function putServedIds(redisClient, id, newData) {
  await appendToCache(
    redisClient,
    CACHE_SERVED_SUFFIX,
    id,
    newData,
    CACHE_SERVED_TTL,
    true,
  );
  // await putToCache(redisClient, "served", id, data, 60 * 60); // 1 hour
}

module.exports = {
  getActiveHours,
  getBotBot,
  getByMultiplePostId,
  getProfilePostFollowing,
  getSiteMap,
  getSiteMapIndex,
  putReads,
  getReads,
  getServedIds,
  putServedIds,
  putFollowers,
  isFollowingBT,
  getFollowingsBT,
  getDataWithCache,
  putToCache,
  readLatestProfileScore,
  appendToCache,
  fetchArrayFromCache,
};
