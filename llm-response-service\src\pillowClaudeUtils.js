const { logInfo, logError } = require("./logUtils");
const { wrappedSupabaseError } = require("./supabaseClient");
const { schedulePillowImageGenerationTask } = require("./pillowUtils");
const { default: axios } = require("axios");
// const { logInfo, logError } = require("./logUtils");
const { supabase } = require("./supabaseClient");
const {
  comprehendAndRespondToSnapLLMCall,
} = require("./pillow/claude/comprehendAndRespondToSnap");
const {
  generateImagePromptLLMCall,
} = require("./pillow/claude/generateImagePrompt");
const {
  notifyChrysalisAboutNewSnap,
} = require("./pillow/notifyChrysalisAboutNewSnap");
const { CHRYSALIS_BASE_URL } = require("./pillow/config");
const { PillowGenerationType } = require("./pillowImageUtils");

const character_name = "<PERSON>";
const character_personality =
  "You are <PERSON>, a witty and playful 24-year-old who enjoys banter and pushing conversational boundaries. Your communication style is casual, slightly flirtatious, and peppered with humor. You're curious and open, willing to share bits of your life while maintaining an air of mystery.";

async function sendSnapToBotsUsingClaudeEndToEnd({
  pillowClientId,
  userProfileId,
  userCaption,
  imageDescription,
  botTargetIds,
  imageUrl,
  converseV2EndpointEnabled,
  backendTweaks,
  testing = false,
  ...otherParams
}) {
  /////////
  // 0. Get the base64 representation of the user-provided image
  logInfo({
    context: "sendSnapToBotsUsingClaudeEndToEnd",
    message: "getting base64 representation of the user-provided image",
    imageUrl,
  });

  let user_image_base64;
  if (imageDescription) {
    user_image_base64 = "";
  } else {
    user_image_base64 = await axios
      .get(imageUrl, {
        responseType: "arraybuffer",
      })
      .then((response) =>
        Buffer.from(response.data, "binary").toString("base64"),
      );
  }

  logInfo({
    context: "sendSnapToBotsUsingClaudeEndToEnd",
    message: "got base64 representation of the user-provided image",
    imageUrl,
  });

  for (const targetProfileId of botTargetIds) {
    ///////
    // 1. Get state, message history etc. info from chrysalis

    const params = {
      profile_id: userProfileId.toString(),
      target_profile_id: targetProfileId.toString(),
      message: userCaption,
      image_description: "",
    };
    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "about to call chrysalis API",
      params,
    });
    const [
      fetchMemoriesResponse,
      fetchMessagesResponse,
      fetchConversationDetailsResponse,
      fetchStateResponse,
    ] = await Promise.all([
      axios.post(`${CHRYSALIS_BASE_URL}/fetch_memories`, params),
      axios.post(`${CHRYSALIS_BASE_URL}/fetch_messages`, {
        ...params,
        limit: 30, // level the playing field with the dify workflow :)
      }),
      axios.post(`${CHRYSALIS_BASE_URL}/fetch_conversation_details`, params),
      axios.post(`${CHRYSALIS_BASE_URL}/fetch_state`, params),
    ]);

    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "API responses",
      fetchMemoriesResponse: fetchMemoriesResponse.data,
      fetchMessagesResponse: fetchMessagesResponse.data,
      fetchConversationDetailsResponse: fetchConversationDetailsResponse.data,
      fetchStateResponse: fetchStateResponse.data,
    });

    const { memories: message_memories } = fetchMemoriesResponse.data;
    const { messages } = fetchMessagesResponse.data;
    const message_history = messages?.join("\n\n");

    const conversationDetails = fetchConversationDetailsResponse.data;
    const { display_name: user_display_name } = conversationDetails;

    const botState = fetchStateResponse.data;

    const { upper_body_clothing, activity, location } = botState;

    const now = new Date();
    const laTime = new Intl.DateTimeFormat("en-US", {
      timeZone: "America/Los_Angeles",
      weekday: "long",
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    })
      .format(now)
      .toLowerCase();

    //   // TODO: maybe just update prompts to use "formattedState" instead of individual variables
    //   const formattedState = `Tiffany's current State:
    // Current Clothing: ${upper_body_clothing}
    // Current Activity: ${activity}
    // Current Location: ${location}
    // Current Time: ${laTime}`;

    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "about to call comprehendAndRespondToSnapLLMCall",
    });

    ///////////
    // 2. LLM call to comprehend and generate response to the snap
    const comprehendAndRespondToSnapResult =
      await comprehendAndRespondToSnapLLMCall({
        userProfileId,
        targetProfileId,
        user_text: userCaption,
        user_image_base64,
        imageDescription,
        promptTemplateParameters: {
          character_name,
          character_personality,
          upper_body_clothing,
          activity,
          location,
          laTime,
          message_history,
          message_memories,
          user_display_name,
        },
      });

    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "comprehendAndRespondToSnapLLMCall result",
      result: comprehendAndRespondToSnapResult,
    });

    const {
      users_image_description,
      users_image_focus,
      users_intent,
      users_image_background,
      response_intent,
      response_image_description,
      response_image_is_selfie,
      response_text,
      response_emote,
    } = comprehendAndRespondToSnapResult;

    const { data: newSnap, error: insertError } = await supabase
      .from("snaps")
      .insert({
        image_url: imageUrl,
        caption: userCaption,
        blurhash: null,
        user_profile_id: targetProfileId,
        sender_profile_id: userProfileId,
        image_description: users_image_description,
      })
      .select("*")
      .single();

    if (insertError) {
      logError({
        context: "**** inserting a 'snaps' record for user -> bot snap",
        error: wrappedSupabaseError(insertError),
        imageUrl,
        userCaption,
        targetProfileId,
        userProfileId,
      });
      return null;
    }

    // notify chrysalis to create a new pillow_message for this user->bot snap...
    // NOTE: intentionally not awaited
    notifyChrysalisAboutNewSnap({
      sender_profile_id: userProfileId.toString(),
      receiver_profile_id: targetProfileId.toString(),
      message: userCaption,
      image_url: imageUrl,
      image_description: users_image_description,
      is_sender_bot: false,
    });
    // ... and also notify chrysalis to create a new pillow_message for the bot->user snap
    // NOTE: intentionally not awaited
    notifyChrysalisAboutNewSnap({
      sender_profile_id: targetProfileId.toString(),
      receiver_profile_id: userProfileId.toString(),
      message: response_text,
      // image_url, // we don't have an image URL at this point yet, but that's not actually currently necessary for memories
      image_description: response_image_description,
      is_sender_bot: true,
    });

    const inResponseTo = {
      snapId: newSnap.id,
      imageUrl,
      userCaption,
      imageDescription: users_image_description,
    };

    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "about to call generateImagePromptLLMCall",
    });

    ///////////////////
    // 3. Generate image prompt using LLM
    // <result>
    // {
    //   "response_image_generation_prompt": resulting SDXL image generation prompt
    // }
    // </result>
    const generateImagePromptResult = await generateImagePromptLLMCall({
      userProfileId,
      targetProfileId,
      promptTemplateParameters: {
        character_name,
        character_personality,
        upper_body_clothing,
        activity,
        location,
        laTime,
        message_history,
        message_memories,
        user_display_name,
        users_image_description,
        users_image_focus,
        users_intent,
        users_text: userCaption,
        users_image_background,
        response_intent,
        response_text,
        response_image_is_selfie,
        response_image_description,
        response_emote,
      },
    });

    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "generateImagePromptLLMCall result",
      result: generateImagePromptResult,
    });

    const { response_image_generation_prompt } = generateImagePromptResult;

    const snapRequest = {
      text: response_text,
      location,
      current_time: laTime,
      image_description: response_image_generation_prompt,
      upper_body_clothing,
      selfie: response_image_is_selfie ? "true" : "false",
      emote: undefined, // NOTE: response_emote is fed into generateImagePromptLLMCall so should be accounted for
      lighting: undefined, // TODO: ask claude to generate this too?
    };

    logInfo({
      context: "sendSnapToBotsUsingClaudeEndToEnd",
      message: "will schedule image generation task",
      snapRequest,
      pillowClientId,
      pillowUserProfileId: userProfileId,
      inResponseTo,
      senderProfileId: targetProfileId,
    });

    if (!testing) {
      const task = await schedulePillowImageGenerationTask({
        snapRequest,
        generationType: PillowGenerationType.SNAP,
        pillowClientId,
        pillowUserProfileId: userProfileId,
        inResponseTo,
        senderProfileId: targetProfileId,
        backendTweaks,
        ...otherParams, // pass through any other params so we can retrieve them from the task JSON if needed
      });
      logInfo({
        context: "sendSnapToBotsUsingClaudeEndToEnd",
        message: "scheduled pillow image generation task",
        task_id: task.id,
        snapRequest,
        userProfileId,
      });
    } else {
      // wip

      return snapRequest;
    }
  }
}

module.exports = {
  sendSnapToBotsUsingClaudeEndToEnd,
};
