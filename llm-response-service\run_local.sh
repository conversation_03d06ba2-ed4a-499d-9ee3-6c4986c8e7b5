#!/bin/bash
set -ex

source ~/util/setenv.sh

docker run  -it \
 -v ~/work/google-cloud-functions/llm-response-service:/src \
 -e SERVER_ENDPOINT \
 -e BOT_SERVER_ENDPOINT \
 -e SUPABASE_URL \
 -e SUPABASE_SECRET_KEY \
 -e GPT_TEXT_MODEL \
 -e OPENAI_API_KEY \
 -e CLOUD_TASK_V1 \
 -e CLOUD_TASK_V1_IMAGE_GENERATION \
 -e CLOUD_TASK_V1_RUN_BOTS \
 -e CLOUD_TASK_V1_IMAGE_GENERATION_UPDATE_STATUS \
 -e CLOUD_TASK_V1_HANDLE_IMAGE \
 -e TYPESENSE_HOST \
 -e TYPESENSE_API_KEY \
 -e NODE_ENV \
 -e LOG_DEBUG_ENABLED \
 -e STRIPE_SECRET_KEY \
 -e STRIPE_WEBHOOK_ENDPOINT_SECRET \
 -e WEBSITE_URL \
 -e BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN \
 -e GOOGLE_APPLICATION_CREDENTIALS=/src/google.json \
 -p 8080:8080 \
 -p 9464:9464 \
 us-central1-docker.pkg.dev/butterflies-ai/images/cocoon \
 /bin/bash /src/src/run.sh