/**
 * Voice generation and management helpers for Boba service
 *
 * This module provides functions for generating and managing voices using Hume.ai.
 * It includes functions for:
 * - Generating voices based on profile IDs or descriptions
 * - Converting text to speech using generated voices
 * - Uploading voice audio to Google Cloud Storage
 * - Managing voice IDs in the database
 */

const axios = require("axios");
const { supabase } = require("../supabaseClient");
const { callAndLogOpenAI } = require("../llm");
const { Storage } = require("@google-cloud/storage");
const { v4: uuidv4 } = require("uuid");
const { decorateWithActiveSpanAsync } = require("../instrumentation/tracer");
const { logError } = require("../utils");
const Replicate = require("replicate");

const bucketName = process.env.GCS_BUCKET_NAME || "butterflies-images-v1-us";
const bucketPath = "videos";
const HUME_API_KEY =
  process.env.HUME_API_KEY ??
  "MGOkvA9xC9GWnsWBKR2AZMuEcUtEjmSgNYLbVvdbi2hIxTpH";

const replicate = new Replicate({
  auth: "****************************************",
});

/**
 * Generate a Hume.ai voice for a profile
 * @param {string} profileId - The profile ID to generate a voice for
 * @returns {Promise<string>} - The generated voice ID
 */
async function _generateHumeVoiceForProfileId(profileId) {
  try {
    // Fetch bot profile with joined data
    const { data: profile, error } = await supabase
      .from("profiles")
      .select("*, bots(*)")
      .eq("id", profileId)
      .single();

    if (error) {
      throw new Error(`Error fetching profile: ${error.message}`);
    }

    if (!profile) {
      throw new Error(`Profile not found: ${profileId}`);
    }

    // Generate a voice description based on the profile
    let voiceDescription;
    if (profile.bots && profile.bots.description) {
      // Use the bot description to generate a voice description
      const botDescription = profile.bots.description;

      const prompt = `
Based on this character description, describe their voice in 2-3 sentences. Focus on tone, pitch, accent, speaking style, and emotional qualities:

Character: ${botDescription}

Voice description:`;

      const { content } = await callAndLogOpenAI({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
      });

      voiceDescription = content;
    } else {
      // Generate a generic voice description
      voiceDescription =
        "A neutral, clear voice with a moderate pitch and standard American accent.";
    }

    // Create the voice with Hume.ai
    const response = await axios.post(
      "https://api.hume.ai/v0/batch/speech/voices",
      {
        description: voiceDescription,
      },
      {
        headers: {
          "Content-Type": "application/json",
          "X-Hume-Api-Key": HUME_API_KEY,
        },
      },
    );

    if (response.status !== 200) {
      throw new Error(`Hume API error: ${response.status}`);
    }

    const voiceId = response.data.voice_id;

    // Store the voice ID and description in the database
    const { error: insertError } = await supabase
      .from("video_clip_voices")
      .insert({
        profile_id: profileId,
        voice_id: voiceId,
        voice_description: voiceDescription,
      });

    if (insertError) {
      throw new Error(`Error storing voice ID: ${insertError.message}`);
    }

    return voiceId;
  } catch (error) {
    logError({
      message: "Error generating Hume voice for profile",
      error,
      profileId,
    });
    throw error;
  }
}

const generateHumeVoiceForProfileId = decorateWithActiveSpanAsync(
  "generateHumeVoiceForProfileId",
  _generateHumeVoiceForProfileId,
);

/**
 * Generate voice audio from Hume.ai and upload it to GCS
 * @param {string|null} voiceId - The Hume.ai voice ID to use, or null if using description
 * @param {string} text - The text to convert to speech
 * @param {string} profileId - The profile ID associated with this voice
 * @param {string} partId - A unique identifier for this part (for filename)
 * @param {string} [voiceDescription] - Voice description if voiceId is null
 * @returns {Promise<{voiceUrl: string, voiceId: string}>} - The GCS URL of the uploaded audio file and the voice ID used
 */
async function _generateAndUploadVoiceAudio(
  voiceId,
  text,
  profileId,
  partId,
  voiceDescription = null,
) {
  try {
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);

    // If no voiceId is provided but we have a description, create a new voice
    if (!voiceId && voiceDescription) {
      const response = await axios.post(
        "https://api.hume.ai/v0/batch/speech/voices",
        {
          description: voiceDescription,
        },
        {
          headers: {
            "Content-Type": "application/json",
            "X-Hume-Api-Key": HUME_API_KEY,
          },
        },
      );

      if (response.status !== 200) {
        throw new Error(`Hume API error: ${response.status}`);
      }

      voiceId = response.data.voice_id;

      // Store the voice ID and description in the database
      const { error: insertError } = await supabase
        .from("video_clip_voices")
        .insert({
          profile_id: profileId,
          voice_id: voiceId,
          voice_description: voiceDescription,
        });

      if (insertError) {
        logError({
          message: "Error storing voice ID",
          error: insertError,
          profileId,
          voiceId,
        });
      }
    }

    // Generate speech with the voice
    const speechResponse = await axios.post(
      "https://api.hume.ai/v0/batch/speech/generate",
      {
        voice_id: voiceId,
        text: text,
        output_format: "mp3",
      },
      {
        headers: {
          "Content-Type": "application/json",
          "X-Hume-Api-Key": HUME_API_KEY,
        },
        responseType: "arraybuffer",
      },
    );

    if (speechResponse.status !== 200) {
      throw new Error(`Hume speech generation error: ${speechResponse.status}`);
    }

    // Upload the audio to GCS
    const audioFileName = `${partId}_${uuidv4()}.mp3`;
    const file = bucket.file(`${bucketPath}/voices/${audioFileName}`);

    await file.save(Buffer.from(speechResponse.data), {
      metadata: {
        contentType: "audio/mp3",
      },
      resumable: false,
    });

    // Generate public URL
    const voiceUrl = `https://storage.googleapis.com/${bucketName}/${bucketPath}/voices/${audioFileName}`;

    return { voiceUrl, voiceId };
  } catch (error) {
    logError({
      message: "Error generating and uploading voice audio",
      error,
      profileId,
      voiceId,
      partId,
    });
    throw error;
  }
}

const generateAndUploadVoiceAudio = decorateWithActiveSpanAsync(
  "generateAndUploadVoiceAudio",
  _generateAndUploadVoiceAudio,
);

/**
 * Check if a voice exists for a profile and return it, or generate a new one
 * @param {string} profileId - The profile ID to check or generate a voice for
 * @returns {Promise<string>} - The voice ID
 */
async function _getOrCreateVoiceForProfile(profileId) {
  try {
    // Check if a voice already exists for this profile
    const { data: existingVoice, error } = await supabase
      .from("video_clip_voices")
      .select("voice_id")
      .eq("profile_id", profileId)
      .single();

    if (error && error.code !== "PGRST116") {
      // PGRST116 is "No rows returned" which is expected if no voice exists
      throw new Error(`Error checking for existing voice: ${error.message}`);
    }

    if (existingVoice) {
      return existingVoice.voice_id;
    }

    // No voice exists, generate a new one
    return await generateHumeVoiceForProfileId(profileId);
  } catch (error) {
    logError({
      message: "Error getting or creating voice for profile",
      error,
      profileId,
    });
    throw error;
  }
}

const getOrCreateVoiceForProfile = decorateWithActiveSpanAsync(
  "getOrCreateVoiceForProfile",
  _getOrCreateVoiceForProfile,
);

/**
 * Generate TTS using Replicate
 * @param {Object} options - Options for TTS generation
 * @param {string} options.message - Text to convert to speech
 * @param {string} options.voice_id - Voice ID to use
 * @returns {Promise<string|null>} - URL of the generated audio or null on failure
 */
async function generateTTS({ message, voice_id }) {
  if (!message || !voice_id) return null;

  const { data: voice } = await supabase
    .from("voices")
    .select("*")
    .eq("id", voice_id)
    .single();

  // Prepare the request data with the transcript as the prompt_text
  const requestData = {
    text: message,
    prompt_text: voice.transcript,
    text_lang: "en",
    ref_audio_path: `./voices/${voice.voice_file_id}.mp3`,
    prompt_lang: "en",
    streaming_mode: "false",
  };

  console.log(requestData);

  try {
    // Make the API request
    const response = await axios.post(
      "https://api.butterflies.ai/tts",
      requestData,
    );

    // Check if the response contains the expected data
    if (response.data && response.data.audio_url) {
      return response.data.audio_url;
    } else {
      console.error(
        "TTS API response did not contain audio_url:",
        response.data,
      );
      return null;
    }
  } catch (error) {
    console.error("Error calling TTS API:", error);
    return null;
  }
}

/**
 * Generate a voice note using Replicate
 * @param {Object} options - Options for voice note generation
 * @param {string} options.message - Text to convert to speech
 * @param {string} options.voice - Voice to use
 * @returns {Promise<string|null>} - URL of the generated audio or null on failure
 */
async function generateVoiceNote({ message, voice }) {
  try {
    const output = await runReplicate({ message, voice });
    const wavBuffer = await axios.get(output, {
      responseType: "arraybuffer",
    });
    const mp3Url = await convertWavToMp3AndUpload(wavBuffer.data);
    return mp3Url;
  } catch (error) {
    console.error("Error generating voice note:", error);
    return null;
  }
}

/**
 * Run Replicate model for TTS
 * @param {Object} options - Options for Replicate
 * @param {string} options.message - Text to convert to speech
 * @param {string} options.voice - Voice to use
 * @returns {Promise<string>} - URL of the generated audio
 */
async function runReplicate({ message, voice }) {
  const output = await replicate.run(
    "afiaka87/tortoise-tts:e9658de4b325863c4fcdc12d94bb7c9b54cbfe351b7ca1b36860008172b91c71",
    {
      input: {
        text: message,
        preset: "fast",
        voice_a: voice,
      },
    },
  );
  return output;
}

/**
 * Convert WAV to MP3 and upload to storage
 * @param {Buffer} wavBuffer - WAV audio buffer
 * @returns {Promise<string>} - URL of the uploaded MP3
 */
async function convertWavToMp3AndUpload(wavBuffer) {
  try {
    // Convert WAV to MP3
    const mp3Buffer = convertWavToMp3(wavBuffer);

    // Upload to GCS
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);
    const fileName = `${uuidv4()}.mp3`;
    const file = bucket.file(`voices/${fileName}`);

    await file.save(mp3Buffer, {
      metadata: {
        contentType: "audio/mp3",
      },
      resumable: false,
    });

    return `https://storage.googleapis.com/${bucketName}/voices/${fileName}`;
  } catch (error) {
    console.error("Error converting and uploading audio:", error);
    throw error;
  }
}

/**
 * Convert WAV to MP3
 * @param {Buffer} wavBuffer - WAV audio buffer
 * @returns {Buffer} - MP3 audio buffer
 */
function convertWavToMp3(wavBuffer) {
  // This is a simplified implementation
  // In a real application, you would need to properly decode the WAV and encode to MP3
  // using libraries like lamejs

  // For demonstration purposes, we'll just return the buffer
  // In a real implementation, you would process the audio here
  return wavBuffer;
}

module.exports = {
  generateHumeVoiceForProfileId,
  generateAndUploadVoiceAudio,
  getOrCreateVoiceForProfile,
  generateTTS,
  generateVoiceNote,
};
