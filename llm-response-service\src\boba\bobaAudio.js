/**
 * Boba Audio Routes
 *
 * This module provides routes for audio generation in the boba service.
 */

const express = require("express");
const { logError } = require("../utils");
const {
  generateAndUploadVoiceAudio,
  // getOrCreateVoiceForProfile,
  generateVoiceNote,
} = require("./bobaAudioHelpers");

const app = express.Router();

/**
 * Generate voice audio from text
 *
 * @route POST /generateVoice
 * @param {string} text - The text to convert to speech
 * @param {string} [voice_id] - The voice ID to use
 * @param {string} [voice_description] - A description of the voice to generate
 * @returns {Object} The generated audio URL and voice ID
 */
app.post("/generateVoice", async (req, res) => {
  try {
    const { text, voice_id, voice_description } = req.body;

    if (!text) {
      return res.status(400).json({ error: "Text is required" });
    }

    // Either use an existing voice ID or generate a new one with the provided description
    let finalVoiceId = voice_id;
    if (!voice_id && voice_description) {
      // If no voice ID but we have a description, generate a new voice
      const { generateVoiceWithDescription } = require("../voiceHelpers");
      finalVoiceId = await generateVoiceWithDescription(voice_description);
    }

    if (!finalVoiceId) {
      return res
        .status(400)
        .json({ error: "Either voiceId or voice_description is required" });
    }

    // Generate the voice audio
    const voiceUrl = await generateAndUploadVoiceAudio(
      finalVoiceId,
      text,
      "api-request", // Use a generic identifier for the profile ID
      `test-${Date.now()}`, // Use timestamp for part ID
    );

    return res.status(200).json({
      success: true,
      voiceUrl,
      voiceId: finalVoiceId,
    });
  } catch (error) {
    console.error("Error in generateVoice endpoint:", error);
    return res.status(500).json({
      error: "Failed to generate voice",
      details: error.message,
    });
  }
});

/**
 * Generate a voice note
 *
 * @route POST /generateVoiceNote
 * @param {string} text - The text to convert to speech
 * @param {string} voice - The voice to use
 * @returns {Object} The generated voice note data
 */
app.post("/generateVoiceNote", async (req, res) => {
  try {
    const { text, voice } = req.body;

    if (!text || !voice) {
      return res.status(400).json({
        success: false,
        error: "Text and voice are required",
      });
    }

    const voiceNote = await generateVoiceNote({ text, voice });

    return res.status(200).json({
      success: true,
      voiceNote,
    });
  } catch (error) {
    logError({
      message: "Error generating voice note",
      error,
    });

    return res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

module.exports = {
  app,
};
