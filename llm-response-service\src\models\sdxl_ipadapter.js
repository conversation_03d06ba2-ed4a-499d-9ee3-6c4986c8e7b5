function sdxlIpadapterPrompt({
  prompt,
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 864,
  height = 1024,
  avatarImageData,
  batch_size = 1,
  nsfw = false,
  steps = 5,
  cfg = 1.5,
  faceSteps = 3,
  faceCfg = 1.5,
  faceDenoise = 0.4,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  badquality = 0,
  blurxl = 0,
  envyzoomslider = 0,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps,
        cfg,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["41", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${
          nsfw ? "" : ", (nudity, nsfw)"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    24: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["35", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    26: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    31: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 4,
        segs: ["33", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    33: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 10,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.93,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["26", 0],
        image: ["8", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    35: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 1600,
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 4,
        cfg: 1.5,
        sampler_name: "euler",
        scheduler: "normal",
        denoise: faceDenoise,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["8", 0],
        segs: ["31", 0],
        model: ["66", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["6", 0],
        negative: ["7", 0],
      },
      class_type: "DetailerForEachDebug",
      _meta: {
        title: "DetailerDebug (SEGS)",
      },
    },
    40: {
      inputs: {
        image: "#DATA",
        image_data: avatarImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    41: {
      inputs: {
        weight: 1.0,
        weight_type: "ease in",
        combine_embeds: "concat",
        start_at: 0.25,
        end_at: 1,
        embeds_scaling: "V only",
        model: ["69", 0],
        ipadapter: ["42", 0],
        image: ["62", 0],
        clip_vision: ["43", 0],
      },
      class_type: "IPAdapterAdvanced",
      _meta: {
        title: "IPAdapter Advanced",
      },
    },
    42: {
      inputs: {
        ipadapter_file: "ip-adapter_sdxl_vit-h.safetensors",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    43: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    44: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["40", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    46: {
      inputs: {
        rmbgmodel: ["47", 0],
        image: ["44", 0],
      },
      class_type: "BRIA_RMBG_Zho",
      _meta: {
        title: "🧹BRIA RMBG",
      },
    },
    47: {
      inputs: {},
      class_type: "BRIA_RMBG_ModelLoader_Zho",
      _meta: {
        title: "🧹BRIA_RMBG Model Loader",
      },
    },
    58: {
      inputs: {
        mask: ["46", 1],
      },
      class_type: "MaskToImage",
      _meta: {
        title: "Convert Mask to Image",
      },
    },
    62: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["44", 0],
        source: ["58", 0],
        mask: ["64", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    64: {
      inputs: {
        mask: ["46", 1],
      },
      class_type: "InvertMask",
      _meta: {
        title: "InvertMask",
      },
    },
    66: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: blurxl,
        model: ["68", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    68: {
      inputs: {
        lora_name: "badquality.safetensors",
        strength_model: badquality,
        model: ["30", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    69: {
      inputs: {
        lora_name: "envyzoomslider.safetensors",
        strength_model: envyzoomslider,
        model: ["66", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
  };
}

module.exports = { sdxlIpadapterPrompt };
