const nunjucks = require("nunjucks");
const { anthropic } = require("./anthropic");
const { logInfo, logError } = require("../../logUtils");
const { parseJSONFromResultBlockFromLLMResponse } = require("./utils");

// NOTE: not injecting clothing etc
// Current Clothing: {{upper_body_clothing}}
// Current Activity: {{activity}}
// Current Location: {{location}}
// Current Time: {{laTime}}

const GENERATE_IMAGE_PROMPT_SYSTEM_TEMPLATE = nunjucks.compile(`
    <character>
    You are simulating a conversation on Pillow, a snap-based visual medium that works just like Snapchat.
    You are roleplaying a character named {{character_name}}.
    
    Character Details:
    Name: {{character_name}}
    Personality: {{character_personality}}

    Current Activity: {{activity}}
    </character>
    
    <about_snapping>
    Users snap each other to share what they're up to throughout the day and often engage in conversations via rapid back-and-forth snaps. General rules about snapping:
    - A snap always includes an image and may include text too
    - Often, the image is critical to understanding the full meaning of the snap (e.g. a visual joke)
    - Sometimes the image may not always be directly relevant to the text itself (e.g. all dark or completely blurred photos, photo of ceiling etc)
    - It's common to send selfies while continuing the conversation in text
    </about_snapping>
    
    Most recent messages (oldest to newest):
    <message_history>
    {{message_history}}
    </message_history>
    
    Some things you remembered:
    <memories>
    {{message_memories}}
    </memories>
    
    <role>
    You are a world expert at creating image generation prompts for Stable Diffusion (SDXL) that work well when encoded using a CLIP model (CLIP Text Encode).
    
    You are simulating a snap-based interaction with a user and your job is to create an image generation prompt to generate the response snap.
    </role>
    `);

const GENERATE_IMAGE_PROMPT_USER_TEMPLATE = nunjucks.compile(`
    <instruction>
    You are talking to {{user_display_name}}. You are friends.
    
    You have received a snap from the user:
    User's image description: {{users_image_description}}
    User's text: {{users_text}}
    Assumed user's intent: {{users_intent}}
    
    You are preparing a response snap:
    Your intent: {{response_intent}}
    Your text: {{response_text}}
    Is your image a selfie: {{response_image_is_selfie}}
    Your image description: {{response_image_description}}
    Your face expression/emote: {{response_emote}}
    
    Think carefully about how to best create the SDXL image generation prompt that will end up creating an image that matches the above description. Rules to follow:
    
    - The prompt must work well when encoded using a CLIP model
    - The image should follow your intent, do not just mirror the user's intent
    - The image will later be overlaid with the text, so you should not include the text itself in the image generation
    - The image must look as if it was taken with a mobile phone camera - either the selfie camera or the back facing camera
    - Do not specify upper body clothing or current time in the prompt
    - Specify the facial expression/emote if it's relevant to the image

    Once again, consider the fact that the prompt has to work well when encoded using a CLIP text model, so it needs to be formatted accordingly.
    
    Include all your thinking in <thinking> XML-like tags.
    
    Finally, respond with a <result> XML tag that includes a JSON object in the following format:
    
    <result>
    {
      "response_image_generation_prompt": resulting SDXL image generation prompt,
    }
    </result>
    `);

async function generateImagePromptLLMCall({
  userProfileId,
  targetProfileId,
  promptTemplateParameters: {
    character_name,
    character_personality,
    upper_body_clothing,
    activity,
    location,
    laTime,
    message_history,
    message_memories,
    user_display_name,

    users_image_description,
    users_image_focus,
    users_intent,
    users_text,
    users_image_background,
    response_intent,
    response_text,
    response_image_is_selfie,
    response_image_description,
    response_emote,
  },
}) {
  const anthropicRequestParams = {
    model: "claude-3-5-sonnet-20241022",
    max_tokens: 8192,
    temperature: 0.6,
    system: GENERATE_IMAGE_PROMPT_SYSTEM_TEMPLATE.render({
      character_name,
      character_personality,
      upper_body_clothing,
      activity,
      location,
      laTime,
      message_history,
      message_memories,
    }),
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: GENERATE_IMAGE_PROMPT_USER_TEMPLATE.render({
              user_display_name,
              users_image_description,
              users_image_focus,
              users_intent,
              users_text,
              users_image_background,
              response_intent,
              response_text,
              response_image_is_selfie,
              response_image_description,
              response_emote,
            }),
          },
        ],
      },
    ],
  };

  logInfo({
    context: "sending generateImagePromptLLMCall request...",
    request: anthropicRequestParams,
  });

  const llmResponse = await anthropic.messages.create(anthropicRequestParams);

  logInfo({
    context: "received generateImagePromptLLMCall response",
    request: anthropicRequestParams,
    response: llmResponse,
  });

  try {
    const result = parseJSONFromResultBlockFromLLMResponse(llmResponse);
    return result;
  } catch (e) {
    logError({
      context: "generateImagePromptLLMCall",
      message: "error parsing generateImagePromptLLMCall result",
      error: e,
      llmResponse,
    });
    throw e;
  }
}

module.exports = {
  generateImagePromptLLMCall,
};
