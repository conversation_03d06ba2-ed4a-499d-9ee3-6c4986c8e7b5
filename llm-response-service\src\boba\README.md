# Boba Service

The Boba service provides functionality for generating images, videos, and voices. It is designed to be a standalone service that can be used by other services in the Butterflies ecosystem.

## Endpoints

### Image Generation

The Boba service provides an endpoint for generating images from text prompts. The service uses either Fireworks AI or Runware for image generation.

#### `/v1/boba/models`

Get a list of available models for image generation.

**Response:**

```json
{
  "models": ["flux", "realvisxl", "pony", "autism"]
}
```

#### `/v1/boba/generateImage` or `/v1/boba/image/generateImage`

Generate an image from a text prompt.

**Request:**

```json
{
  "prompt": "A beautiful sunset over the ocean",
  "aspect_ratio": "9:16",
  "guidance_scale": 3.5,
  "num_inference_steps": 30,
  "num_generations": 1,
  "seed": 123456,
  "optimize": true,
  "model": "flux",
  "provider": "runware",
  "rendering_style": "photo realistic"
}
```

| Parameter             | Type    | Description                                                | Default          |
| --------------------- | ------- | ---------------------------------------------------------- | ---------------- |
| `prompt`              | string  | The text prompt to generate an image from                  | (required)       |
| `aspect_ratio`        | string  | The aspect ratio of the generated image                    | `"1:1"`          |
| `guidance_scale`      | number  | The guidance scale for image generation                    | (model-specific) |
| `num_inference_steps` | number  | The number of inference steps                              | (model-specific) |
| `num_generations`     | number  | The number of images to generate                           | `1`              |
| `seed`                | number  | The seed for image generation                              | (random)         |
| `optimize`            | boolean | Whether to optimize the prompt using Claude                | `true`           |
| `model`               | string  | The model to use for image generation (when using Runware) | `"flux"`         |
| `provider`            | string  | The provider to use for image generation                   | `"runware"`      |
| `rendering_style`     | string  | The rendering style to use for the image                   | (model-specific) |

#### Available Models

The following models are available for image generation:

| Model       | Description                                  | Default CFG Scale | Default Steps | Max Dimensions | Scheduler                       | Additional Settings                                                                                                                                                         |
| ----------- | -------------------------------------------- | ----------------- | ------------- | -------------- | ------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `flux`      | Runware Flux model (runware:101@1)           | 3.5               | 28            | 1024x1024      | FlowMatchEulerDiscreteScheduler | -                                                                                                                                                                           |
| `realvisxl` | RealVisXL V5.0 model (civitai:139562@361593) | 1.5               | 5             | 1024x1024      | default                         | -                                                                                                                                                                           |
| `pony`      | Pony model (civitai:257749@290640)           | 8.5               | 30            | 1024x1024      | EulerAncestralDiscreteScheduler | clipSkip: -2<br>Adds "source_anime" prefix with anime rendering style                                                                                                       |
| `autism`    | Autism model (civitai:288584@379262)         | 2.5               | 8             | 1024x1024      | default                         | Adds prefix: "score_9, score_8_up, score_8, "<br>Adds negative prompt: "score_5, score_4, score_3, monochrome, 3d"<br>Adds "source_anime" prefix with anime rendering style |

#### Model-Specific Dimensions

Each model has specific dimensions for different aspect ratios:

**All models (flux, realvisxl, pony, autism)**:

- 1:1: 1024x1024
- 16:9: 1024x576
- 9:16: 576x1024

#### Prompt Optimization

When the `optimize` parameter is set to `true`, the service uses Anthropic's Claude API to enhance the user's prompt. The optimization strategy varies depending on the selected model:

#### Flux Model

For the Flux model, the prompt is optimized to include detailed descriptions of:

- Lighting conditions
- Scenery elements
- Character details (clothing, pose, items they're holding)
- Rendering style (art style, photo-realistic, 3D rendering, etc.)

The optimized prompt uses complete sentences with detailed language.

#### RealVisXL and Pony Models

For RealVisXL and Pony models, the prompt is optimized differently:

- Short descriptions separated by commas
- No periods
- Still includes details about lighting, scenery, characters, and rendering style
- More concise format that works better with these models

This model-specific prompt optimization helps achieve the best results for each model's unique characteristics and requirements.

#### Default Rendering Styles

Each model has a default rendering style if none is provided:

| Model       | Default Rendering Style |
| ----------- | ----------------------- |
| `flux`      | photo realistic         |
| `realvisxl` | photo realistic         |
| `pony`      | anime                   |
| `autism`    | anime                   |

When using `anime` rendering style with `pony` or `autism` models, "source_anime" will be automatically added to the prompt prefix.

You can override the default rendering style by providing a `rendering_style` parameter in your request.

#### Response

```json
{
  "images": [
    {
      "url": "https://storage.googleapis.com/butterflies-images-v1-us/boba/images/123456.png",
      "seed": 123456,
      "index": 0
    }
  ],
  "prompt": "A breathtaking sunset over the vast ocean, with vibrant orange and pink hues reflecting off the gentle waves. The sky is painted with streaks of purple and gold as the sun dips below the horizon, casting a warm glow over the entire scene.",
  "optimize": true,
  "model": "flux",
  "provider": "runware",
  "guidance_scale": 3.5,
  "num_inference_steps": 28,
  "dimensions": {
    "width": 1024,
    "height": 1024
  },
  "scheduler": "FlowMatchEulerDiscreteScheduler",
  "availableModels": ["flux", "realvisxl", "pony", "autism"]
}
```

### Audio Generation

#### `/v1/boba/generateVoice` or `/v1/boba/audio/generateVoice`

Generates audio from text using a specified voice or voice description.

**Request:**

```json
{
  "text": "Hello, world!",
  "voice_id": "voice_id",
  "voice_description": "A deep male voice with a British accent"
}
```

**Response:**

```json
{
  "success": true,
  "audioUrl": "https://storage.googleapis.com/butterflies-images-v1-us/videos/voices/uuid.mp3"
}
```

#### `/v1/boba/generateVoiceNote` or `/v1/boba/audio/generateVoiceNote`

Generates a voice note using a specified voice.

**Request:**

```json
{
  "text": "Hello, world!",
  "voice": "voice_id"
}
```

**Response:**

```json
{
  "success": true,
  "voiceNote": {
    "id": "uuid",
    "url": "https://storage.googleapis.com/butterflies-images-v1-us/videos/voices/uuid.mp3"
  }
}
```

### Video Generation

#### `/v1/boba/generateVideoFromPrompt` or `/v1/boba/video/generateVideoFromPrompt`

Generates a video from a text prompt.

**Request:**

```json
{
  "prompt": "A person walking through a forest"
}
```

**Response:**

```json
{
  "success": true,
  "requestId": "uuid"
}
```

#### `/v1/boba/getVideoStatus` or `/v1/boba/video/getVideoStatus`

Gets the status of a video generation request.

**Request:**

```
GET /v1/boba/getVideoStatus?requestId=uuid
```

**Response:**

```json
{
  "success": true,
  "status": {
    "id": "uuid",
    "status": "pending|completed|failed",
    "image_url": "https://storage.googleapis.com/butterflies-images-v1-us/orig/uuid.png",
    "video_url": "https://storage.googleapis.com/butterflies-images-v1-us/videos/uuid.mp4",
    "created_at": "2023-01-01T00:00:00.000Z",
    "updated_at": "2023-01-01T00:00:00.000Z"
  }
}
```

#### `/v1/boba/generateVideoFromAudio` or `/v1/boba/video/generateVideoFromAudio`

Generates a video from an audio file.

**Request:**

```json
{
  "audioUrl": "https://storage.googleapis.com/butterflies-images-v1-us/videos/voices/uuid.mp3"
}
```

**Response:**

```json
{
  "success": true,
  "video_url": "https://storage.googleapis.com/butterflies-images-v1-us/videos/uuid.mp4",
  "duration": 10
}
```

## Implementation Details

The Boba service is implemented using a modular structure with the following components:

- **boba.js**: Main file that defines the Express router and mounts the route modules.
- **bobaImage.js**: Routes for image generation.
- **bobaAudio.js**: Routes for audio generation.
- **bobaVideo.js**: Routes for video generation.
- **bobaImageHelpers.js**: Helper functions for image generation.
- **bobaAudioHelpers.js**: Helper functions for audio generation.
- **bobaVideoHelpers.js**: Helper functions for video generation.

The service uses the following external APIs:

- **Fireworks AI**: For image generation (alternative provider).
- **Runware**: For image generation (default provider).
- **Hume.ai**: For voice generation. Hume.ai is used to create unique voices for each profile, which are stored in the video_clip_voices table for future use.
- **Anthropic Claude**: For generating video descriptions and optimizing image prompts.

## Model Manager

The Boba service uses a Model Manager to manage the different models used for image generation. The Model Manager is responsible for:

1. Mapping model names to their respective IDs
2. Providing default settings for each model (scheduler, steps, CFG scale)
3. Determining appropriate dimensions for different aspect ratios per model
4. Registering new models with custom settings

### Model Settings

Each model has its own default settings:

| Model       | Scheduler                       | Steps | CFG Scale | Max Dimensions | Additional Settings                                                                                                                                                         |
| ----------- | ------------------------------- | ----- | --------- | -------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `flux`      | FlowMatchEulerDiscreteScheduler | 28    | 3.5       | 1024x1024      | -                                                                                                                                                                           |
| `realvisxl` | default                         | 5     | 1.5       | 1024x1024      | -                                                                                                                                                                           |
| `pony`      | EulerAncestralDiscreteScheduler | 30    | 8.5       | 1024x1024      | clipSkip: -2<br>Adds "source_anime" prefix with anime rendering style                                                                                                       |
| `autism`    | default                         | 8     | 2.5       | 1024x1024      | Adds prefix: "score_9, score_8_up, score_8, "<br>Adds negative prompt: "score_5, score_4, score_3, monochrome, 3d"<br>Adds "source_anime" prefix with anime rendering style |

## Usage

To use the Boba service, make HTTP requests to the endpoints described above. The service is designed to be used by other services in the Butterflies ecosystem, but can also be used directly by clients.

## Development

To add new functionality to the Boba service, create new functions in the appropriate helper files and expose them through the corresponding route files.

## Deployment

The Boba service is deployed as part of the main llm-response-service. No additional deployment steps are required.
