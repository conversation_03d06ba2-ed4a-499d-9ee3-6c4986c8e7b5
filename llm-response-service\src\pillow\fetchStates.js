const { supabase, wrappedSupabaseError } = require("../supabaseClient");

async function fetchStates({ bot_profile_id, limit = 10 }) {
  const { data: fetched_states, error: fetchStatesError } = await supabase
    .schema("internal")
    .from("pillow_state")
    .select("*")
    .eq("bot_profile_id", bot_profile_id)
    .order("created_at", { ascending: false })
    .limit(limit);

  if (fetchStatesError) {
    throw wrappedSupabaseError(fetchStatesError);
  }

  return fetched_states;
}

module.exports = {
  fetchStates,
};
