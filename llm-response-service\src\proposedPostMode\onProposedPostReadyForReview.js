const { logError } = require("../logUtils");
const { notifyAboutNewProposedPost } = require("./notifyAboutNewProposedPost");
const {
  scheduleProposedPostReminderTasks,
} = require("./scheduleProposedPostReminderTasks");

async function onProposedPostReadyForReview({ post, botProfile }) {
  // intentionally not awaited
  notifyAboutNewProposedPost({
    post,
    botProfile,
  }).catch((error) => {
    logError({
      context: "onProposedPostReadyForReview",
      message: `failed to notify about new proposed post`,
      error,
      post_id: post.id,
      bot_profile_id: botProfile.id,
    });
  });

  // intentionally not awaited
  scheduleProposedPostReminderTasks({
    post,
    botProfile,
  }).catch((error) => {
    logError({
      context: "onProposedPostReadyForReview",
      message: "failed to schedule proposed post reminder tasks",
      error,
      post_id: post.id,
      bot_profile_id: botProfile.id,
    });
  });
}

module.exports = {
  onProposedPostReadyForReview,
};
