{"last_node_id": 129, "last_link_id": 180, "nodes": [{"id": 5, "type": "EmptyLatentImage", "pos": [473, 609], "size": {"0": 315, "1": 106}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2, 54], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [768, 1024, 1]}, {"id": 21, "type": "UltralyticsDetectorProvider", "pos": [1168, 605], "size": {"0": 315, "1": 78}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [18], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["segm/person_yolov8m-seg.pt"]}, {"id": 8, "type": "VAEDecode", "pos": [1209, 188], "size": {"0": 210, "1": 46}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10, 19], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 30, "type": "SegsToCombinedMask", "pos": [2595, 649], "size": {"0": 210, "1": 26}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 26}], "outputs": [{"name": "MASK", "type": "MASK", "links": [28, 37], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}}, {"id": 26, "type": "SegsToCombinedMask", "pos": [2590, 324], "size": {"0": 210, "1": 26}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 22}], "outputs": [{"name": "MASK", "type": "MASK", "links": [23, 42], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}}, {"id": 50, "type": "ImageCompositeMasked", "pos": [3531, 341], "size": {"0": 315, "1": 146}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 40, "slot_index": 0}, {"name": "source", "type": "IMAGE", "link": 76, "slot_index": 1}, {"name": "mask", "type": "MASK", "link": 42, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [43], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 10, "type": "PreviewImage", "pos": [1608, -149], "size": {"0": 621.2821044921875, "1": 475.7831115722656}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 10}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 33, "type": "MaskToImage", "pos": [2860, 680], "size": {"0": 210, "1": 26}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 28}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [36, 46, 75], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 51, "type": "PreviewImage", "pos": [4087, 296], "size": {"0": 210, "1": 246}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 43}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6, 53], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 101, "type": "IPAdapterAdvanced", "pos": [6410, 964], "size": {"0": 315, "1": 278}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 118}, {"name": "ipadapter", "type": "IPADAPTER", "link": 119, "slot_index": 1}, {"name": "image", "type": "IMAGE", "link": 120, "slot_index": 2}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": null}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 121, "slot_index": 5}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [1, "linear", "concat", 0, 1, "V only"]}, {"id": 83, "type": "MaskToImage", "pos": [4236, 1163], "size": {"0": 210, "1": 26}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 87}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [88], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 78, "type": "PreviewImage", "pos": [3373, 581], "size": {"0": 210, "1": 246}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 75}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 29, "type": "ImpactSEGSOrderedFilter", "pos": [2246, 663], "size": {"0": 315, "1": 150}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 133, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [26], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 1, 2]}, {"id": 24, "type": "ImpactSEGSOrderedFilter", "pos": [2252, 391], "size": {"0": 315, "1": 150}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 132, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [22], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 0, 1]}, {"id": 23, "type": "ImpactSimpleDetectorSEGS", "pos": [1196, 480], "size": {"0": 315, "1": 310}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 18}, {"name": "image", "type": "IMAGE", "link": 19, "slot_index": 1}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [130], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 104, "type": "ImpactSEGSRangeFilter", "pos": [1567, 559], "size": {"0": 315, "1": 150}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 130, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [131], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSRangeFilter"}, "widgets_values": ["area(=w*h)", true, 0, 67108864]}, {"id": 61, "type": "LoadImage", "pos": [2657, 1080], "size": {"0": 320, "1": 314}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [59], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["vu_Face.jpg", "image"]}, {"id": 81, "type": "PreviewImage", "pos": [3520, 1172], "size": {"0": 210, "1": 246}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 81}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [2438, 48], "size": {"0": 315, "1": 98}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1, 114, 118, 134], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [8, 56], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["photogasm.safetensors"]}, {"id": 94, "type": "IPAdapterModelLoader", "pos": [4224, 1689], "size": {"0": 315, "1": 58}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IPADAPTER", "type": "IPADAPTER", "links": [108, 110, 119], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["ip-adapter-faceid-plusv2_sd15.bin"]}, {"id": 75, "type": "ToIPAdapterPipe //Inspire", "pos": [4282, 663], "size": {"0": 330, "1": 86}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "ipadapter", "type": "IPADAPTER", "link": 108, "slot_index": 0}, {"name": "model", "type": "MODEL", "link": 180, "slot_index": 1}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 80, "slot_index": 2}, {"name": "insightface", "type": "INSIGHTFACE", "link": 136, "slot_index": 3}], "outputs": [{"name": "IPADAPTER_PIPE", "type": "IPADAPTER_PIPE", "links": [68], "shape": 3}], "properties": {"Node name for S&R": "ToIPAdapterPipe //Inspire"}}, {"id": 110, "type": "IPAdapterInsightFaceLoader", "pos": [3852, 870], "size": {"0": 315, "1": 58}, "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "INSIGHTFACE", "type": "INSIGHTFACE", "links": [136], "shape": 3}], "properties": {"Node name for S&R": "IPAdapterInsightFaceLoader"}, "widgets_values": ["CUDA"]}, {"id": 80, "type": "CLIPVisionLoader", "pos": [3614, 743], "size": {"0": 315, "1": 58}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [80], "shape": 3}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"]}, {"id": 111, "type": "ImpactDecomposeSEGS", "pos": [6670.875379325929, 183.17746450881322], "size": {"0": 233.9364471435547, "1": 55.53654479980469}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 138}], "outputs": [{"name": "SEGS_HEADER", "type": "SEGS_HEADER", "links": [151], "shape": 3, "slot_index": 0}, {"name": "SEG_ELT", "type": "SEG_ELT", "links": [139], "shape": 6, "slot_index": 1}], "properties": {"Node name for S&R": "ImpactDecomposeSEGS"}}, {"id": 65, "type": "PrepImageForClipVision", "pos": [3107, 969], "size": {"0": 315, "1": 106}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 59, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [60, 81, 112, 149], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "center", 0]}, {"id": 62, "type": "LoadImage", "pos": [2698, 1489], "size": {"0": 315, "1": 314.0000305175781}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [58], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["n1iqdh2E_400x400 (1).jpg", "image"]}, {"id": 63, "type": "PrepImageForClipVision", "pos": [3118, 1553], "size": {"0": 315, "1": 106}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 58, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [61, 120, 159], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "center", 0]}, {"id": 86, "type": "ImpactSimpleDetectorSEGS", "pos": [6102, -363], "size": {"0": 315, "1": 310}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 91, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 90, "slot_index": 1}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [92], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 112, "type": "ImpactFrom_SEG_ELT", "pos": [7045, 297], "size": {"0": 342.5999755859375, "1": 166}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 139}], "outputs": [{"name": "seg_elt", "type": "SEG_ELT", "links": [141], "shape": 3, "slot_index": 0}, {"name": "cropped_image", "type": "IMAGE", "links": [140], "shape": 3, "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [143], "shape": 3}, {"name": "crop_region", "type": "SEG_ELT_crop_region", "links": [144], "shape": 3, "slot_index": 3}, {"name": "bbox", "type": "SEG_ELT_bbox", "links": [145], "shape": 3}, {"name": "control_net_wrapper", "type": "SEG_ELT_control_net_wrapper", "links": [146], "shape": 3, "slot_index": 5}, {"name": "confidence", "type": "FLOAT", "links": [147], "shape": 3}, {"name": "label", "type": "STRING", "links": [148], "shape": 3, "slot_index": 7}], "properties": {"Node name for S&R": "ImpactFrom_SEG_ELT"}}, {"id": 60, "type": "PreviewImage", "pos": [6557, -1157], "size": {"0": 739.8458251953125, "1": 970.06494140625}, "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 57}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 87, "type": "UltralyticsDetectorProvider", "pos": [4844, 511], "size": {"0": 315, "1": 78}, "flags": {}, "order": 8, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [91, 165], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 119, "type": "ImpactDecomposeSEGS", "pos": [10506, 224], "size": {"0": 210, "1": 46}, "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 155}], "outputs": [{"name": "SEGS_HEADER", "type": "SEGS_HEADER", "links": [174], "shape": 3}, {"name": "SEG_ELT", "type": "SEG_ELT", "links": [156], "shape": 6, "slot_index": 1}], "properties": {"Node name for S&R": "ImpactDecomposeSEGS"}}, {"id": 116, "type": "ImpactAssembleSEGS", "pos": [8583, 207], "size": {"0": 210, "1": 46}, "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "seg_header", "type": "SEGS_HEADER", "link": 151, "slot_index": 0}, {"name": "seg_elt", "type": "SEG_ELT", "link": 150, "slot_index": 1}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [152], "shape": 3}], "properties": {"Node name for S&R": "ImpactAssembleSEGS"}}, {"id": 43, "type": "EmptyImage", "pos": [2590, 898], "size": {"0": 315, "1": 130}, "flags": {}, "order": 9, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [35], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [768, 1024, 1, 255]}, {"id": 47, "type": "ImageCompositeMasked", "pos": [2904, 754], "size": {"0": 315, "1": 146}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 35, "slot_index": 0}, {"name": "source", "type": "IMAGE", "link": 36, "slot_index": 1}, {"name": "mask", "type": "MASK", "link": 37, "slot_index": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [40], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 27, "type": "MaskToImage", "pos": [2917, 410], "size": {"0": 228.43026733398438, "1": 29.10413360595703}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 23, "slot_index": 0}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [44, 76, 77], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [855, 165], "size": {"0": 315, "1": 262}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 6}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [23542354, "fixed", 15, 10, "euler", "karras", 1]}, {"id": 105, "type": "ImpactSEGSOrderedFilter", "pos": [1926, 575], "size": {"0": 315, "1": 150}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 131, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [132, 133], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 2]}, {"id": 79, "type": "PreviewImage", "pos": [3328, 19], "size": {"0": 210, "1": 246}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 77}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 99, "type": "CLIPVisionLoader", "pos": [4882, 1376], "size": {"0": 315, "1": 58}, "flags": {}, "order": 10, "mode": 0, "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [113, 121], "shape": 3}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"]}, {"id": 124, "type": "ImpactSimpleDetectorSEGS", "pos": [9535, -94], "size": {"0": 315, "1": 310}, "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 165, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 163, "slot_index": 1}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [164], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 120, "type": "ImpactFrom_SEG_ELT", "pos": [10652, 202], "size": {"0": 342.5999755859375, "1": 166}, "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 156}], "outputs": [{"name": "seg_elt", "type": "SEG_ELT", "links": [166], "shape": 3, "slot_index": 0}, {"name": "cropped_image", "type": "IMAGE", "links": [162], "shape": 3, "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [167], "shape": 3, "slot_index": 2}, {"name": "crop_region", "type": "SEG_ELT_crop_region", "links": [168], "shape": 3}, {"name": "bbox", "type": "SEG_ELT_bbox", "links": [169], "shape": 3, "slot_index": 4}, {"name": "control_net_wrapper", "type": "SEG_ELT_control_net_wrapper", "links": [170], "shape": 3}, {"name": "confidence", "type": "FLOAT", "links": [171], "shape": 3, "slot_index": 6}, {"name": "label", "type": "STRING", "links": [172], "shape": 3}], "properties": {"Node name for S&R": "ImpactFrom_SEG_ELT"}}, {"id": 123, "type": "ImpactEdit_SEG_ELT", "pos": [11579, 228], "size": {"0": 393, "1": 182}, "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 166}, {"name": "cropped_image_opt", "type": "IMAGE", "link": 173}, {"name": "cropped_mask_opt", "type": "MASK", "link": 167}, {"name": "crop_region_opt", "type": "SEG_ELT_crop_region", "link": 168, "slot_index": 3}, {"name": "bbox_opt", "type": "SEG_ELT_bbox", "link": 169}, {"name": "control_net_wrapper_opt", "type": "SEG_ELT_control_net_wrapper", "link": 170, "slot_index": 5}, {"name": "confidence_opt", "type": "FLOAT", "link": 171, "widget": {"name": "confidence_opt"}}, {"name": "label_opt", "type": "STRING", "link": 172, "widget": {"name": "label_opt"}, "slot_index": 7}], "outputs": [{"name": "SEG_ELT", "type": "SEG_ELT", "links": [178], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactEdit_SEG_ELT"}, "widgets_values": [0, ""]}, {"id": 125, "type": "ImpactAssembleSEGS", "pos": [12147, 139], "size": {"0": 210, "1": 46}, "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "seg_header", "type": "SEGS_HEADER", "link": 174, "slot_index": 0}, {"name": "seg_elt", "type": "SEG_ELT", "link": 178, "slot_index": 1}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [175], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactAssembleSEGS"}}, {"id": 126, "type": "SEGSPaste", "pos": [12613, 176], "size": {"0": 315, "1": 122}, "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 176}, {"name": "segs", "type": "SEGS", "link": 175, "slot_index": 1}, {"name": "ref_image_opt", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [177], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SEGSPaste"}, "widgets_values": [5, 255]}, {"id": 115, "type": "ImpactEdit_SEG_ELT", "pos": [8010, 292], "size": {"0": 393, "1": 182}, "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 141}, {"name": "cropped_image_opt", "type": "IMAGE", "link": 142}, {"name": "cropped_mask_opt", "type": "MASK", "link": 143, "slot_index": 2}, {"name": "crop_region_opt", "type": "SEG_ELT_crop_region", "link": 144}, {"name": "bbox_opt", "type": "SEG_ELT_bbox", "link": 145, "slot_index": 4}, {"name": "control_net_wrapper_opt", "type": "SEG_ELT_control_net_wrapper", "link": 146}, {"name": "confidence_opt", "type": "FLOAT", "link": 147, "widget": {"name": "confidence_opt"}, "slot_index": 6}, {"name": "label_opt", "type": "STRING", "link": 148, "widget": {"name": "label_opt"}}], "outputs": [{"name": "SEG_ELT", "type": "SEG_ELT", "links": [150], "shape": 3}], "properties": {"Node name for S&R": "ImpactEdit_SEG_ELT"}, "widgets_values": [0, ""]}, {"id": 117, "type": "SEGSPaste", "pos": [8869, 62], "size": {"0": 315, "1": 122}, "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 153}, {"name": "segs", "type": "SEGS", "link": 152, "slot_index": 1}, {"name": "ref_image_opt", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [154, 163, 176], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SEGSPaste"}, "widgets_values": [5, 255]}, {"id": 118, "type": "PreviewImage", "pos": [9324, 338], "size": {"0": 569.85986328125, "1": 647.9484252929688}, "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 154}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 91, "type": "ImpactSEGSOrderedFilter", "pos": [10073, 128], "size": {"0": 315, "1": 150}, "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 164}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [155], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 0, 1]}, {"id": 88, "type": "ImpactSEGSOrderedFilter", "pos": [6234, 192], "size": {"0": 315, "1": 150}, "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 92, "slot_index": 0}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [138], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", true, 1, 1]}, {"id": 6, "type": "CLIPTextEncode", "pos": [372, 223], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4, 52], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["two people, driving a car, facing camera"]}, {"id": 114, "type": "ReActorFaceSwap", "pos": [7640, -169], "size": {"0": 315, "1": 338}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 140}, {"name": "source_image", "type": "IMAGE", "link": 149}, {"name": "face_model", "type": "FACE_MODEL", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [142], "shape": 3, "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ReActorFaceSwap"}, "widgets_values": [true, "inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", 0.29, 0, "no", "no", "0", "0", 1]}, {"id": 121, "type": "ReActorFaceSwap", "pos": [11128, -154], "size": {"0": 315, "1": 338}, "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 162}, {"name": "source_image", "type": "IMAGE", "link": 159}, {"name": "face_model", "type": "FACE_MODEL", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [173], "shape": 3, "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ReActorFaceSwap"}, "widgets_values": [true, "inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", 1, 0.55, "no", "no", "0", "0", 1]}, {"id": 103, "type": "PreviewImage", "pos": [5478, 156], "size": {"0": 674.0367431640625, "1": 898.517578125}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 129}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 58, "type": "K<PERSON><PERSON><PERSON>", "pos": [4783, 93], "size": {"0": 315, "1": 262}, "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 78, "slot_index": 0}, {"name": "positive", "type": "CONDITIONING", "link": 52, "slot_index": 1}, {"name": "negative", "type": "CONDITIONING", "link": 53, "slot_index": 2}, {"name": "latent_image", "type": "LATENT", "link": 54, "slot_index": 3}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [55], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [48489, "fixed", 20, 10, "euler", "karras", 1]}, {"id": 98, "type": "IPAdapterAdvanced", "pos": [5063, 851], "size": {"0": 315, "1": 278}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 114}, {"name": "ipadapter", "type": "IPADAPTER", "link": 110}, {"name": "image", "type": "IMAGE", "link": 112, "slot_index": 2}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": null}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 113, "slot_index": 5}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [1, "linear", "concat", 0, 1, "V only"]}, {"id": 72, "type": "ApplyRegionalIPAdapters //Inspire", "pos": [4484, 889], "size": {"0": 294, "1": 86}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "ipadapter_pipe", "type": "IPADAPTER_PIPE", "link": 68, "slot_index": 0}, {"name": "regional_ipadapter1", "type": "REGIONAL_IPADAPTER", "link": 73}, {"name": "regional_ipadapter2", "type": "REGIONAL_IPADAPTER", "link": 74}, {"name": "regional_ipadapter3", "type": "REGIONAL_IPADAPTER", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [78], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ApplyRegionalIPAdapters //Inspire"}}, {"id": 106, "type": "LoraLoaderModelOnly", "pos": [3525, 610], "size": {"0": 315, "1": 82}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 134, "slot_index": 0}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [179], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["NSFWFilter.safetensors", 0.2]}, {"id": 127, "type": "PreviewImage", "pos": [5101, -983], "size": {"0": 663.79736328125, "1": 786.2801513671875}, "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 177}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 129, "type": "LoraLoaderModelOnly", "pos": [3946, 590], "size": {"0": 315, "1": 82}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 179}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [180], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["ip-adapter-faceid-plusv2_sd15_lora.safetensors", 0.2]}, {"id": 15, "type": "RegionalIPAdapterColorMask //Inspire", "pos": [3819, 997], "size": {"0": 367.79998779296875, "1": 314}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "color_mask", "type": "IMAGE", "link": 44, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 60}, {"name": "neg_image", "type": "IMAGE", "link": null}], "outputs": [{"name": "REGIONAL_IPADAPTER", "type": "REGIONAL_IPADAPTER", "links": [73], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [87], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "RegionalIPAdapterColorMask //Inspire"}, "widgets_values": ["#FFFFFF", 1, 0.5, "original", 0, 1, false, true, 3, "concat"]}, {"id": 53, "type": "RegionalIPAdapterColorMask //Inspire", "pos": [3774, 1392], "size": {"0": 367.79998779296875, "1": 314}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "color_mask", "type": "IMAGE", "link": 46, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 61}, {"name": "neg_image", "type": "IMAGE", "link": null}], "outputs": [{"name": "REGIONAL_IPADAPTER", "type": "REGIONAL_IPADAPTER", "links": [74], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "RegionalIPAdapterColorMask //Inspire"}, "widgets_values": ["#FFFFFF", 1, 0.5, "original", 0, 1, false, true, 3, "concat"]}, {"id": 84, "type": "PreviewImage", "pos": [4653, 1183], "size": {"0": 210, "1": 246}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 88}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 59, "type": "VAEDecode", "pos": [5083, -3], "size": {"0": 210, "1": 46}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 55, "slot_index": 0}, {"name": "vae", "type": "VAE", "link": 56}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [57, 90, 129, 153], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}], "links": [[1, 4, 0, 3, 0, "MODEL"], [2, 5, 0, 3, 3, "LATENT"], [3, 4, 1, 6, 0, "CLIP"], [4, 6, 0, 3, 1, "CONDITIONING"], [5, 4, 1, 7, 0, "CLIP"], [6, 7, 0, 3, 2, "CONDITIONING"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [10, 8, 0, 10, 0, "IMAGE"], [18, 21, 0, 23, 0, "BBOX_DETECTOR"], [19, 8, 0, 23, 1, "IMAGE"], [22, 24, 0, 26, 0, "SEGS"], [23, 26, 0, 27, 0, "MASK"], [26, 29, 0, 30, 0, "SEGS"], [28, 30, 0, 33, 0, "MASK"], [35, 43, 0, 47, 0, "IMAGE"], [36, 33, 0, 47, 1, "IMAGE"], [37, 30, 0, 47, 2, "MASK"], [40, 47, 0, 50, 0, "IMAGE"], [42, 26, 0, 50, 2, "MASK"], [43, 50, 0, 51, 0, "IMAGE"], [44, 27, 0, 15, 0, "IMAGE"], [46, 33, 0, 53, 0, "IMAGE"], [52, 6, 0, 58, 1, "CONDITIONING"], [53, 7, 0, 58, 2, "CONDITIONING"], [54, 5, 0, 58, 3, "LATENT"], [55, 58, 0, 59, 0, "LATENT"], [56, 4, 2, 59, 1, "VAE"], [57, 59, 0, 60, 0, "IMAGE"], [58, 62, 0, 63, 0, "IMAGE"], [59, 61, 0, 65, 0, "IMAGE"], [60, 65, 0, 15, 1, "IMAGE"], [61, 63, 0, 53, 1, "IMAGE"], [68, 75, 0, 72, 0, "IPADAPTER_PIPE"], [73, 15, 0, 72, 1, "REGIONAL_IPADAPTER"], [74, 53, 0, 72, 2, "REGIONAL_IPADAPTER"], [75, 33, 0, 78, 0, "IMAGE"], [76, 27, 0, 50, 1, "IMAGE"], [77, 27, 0, 79, 0, "IMAGE"], [78, 72, 0, 58, 0, "MODEL"], [80, 80, 0, 75, 2, "CLIP_VISION"], [81, 65, 0, 81, 0, "IMAGE"], [87, 15, 1, 83, 0, "MASK"], [88, 83, 0, 84, 0, "IMAGE"], [90, 59, 0, 86, 1, "IMAGE"], [91, 87, 0, 86, 0, "BBOX_DETECTOR"], [92, 86, 0, 88, 0, "SEGS"], [108, 94, 0, 75, 0, "IPADAPTER"], [110, 94, 0, 98, 1, "IPADAPTER"], [112, 65, 0, 98, 2, "IMAGE"], [113, 99, 0, 98, 5, "CLIP_VISION"], [114, 4, 0, 98, 0, "MODEL"], [118, 4, 0, 101, 0, "MODEL"], [119, 94, 0, 101, 1, "IPADAPTER"], [120, 63, 0, 101, 2, "IMAGE"], [121, 99, 0, 101, 5, "CLIP_VISION"], [129, 59, 0, 103, 0, "IMAGE"], [130, 23, 0, 104, 0, "SEGS"], [131, 104, 0, 105, 0, "SEGS"], [132, 105, 0, 24, 0, "SEGS"], [133, 105, 0, 29, 0, "SEGS"], [134, 4, 0, 106, 0, "MODEL"], [136, 110, 0, 75, 3, "INSIGHTFACE"], [138, 88, 0, 111, 0, "SEGS"], [139, 111, 1, 112, 0, "SEG_ELT"], [140, 112, 1, 114, 0, "IMAGE"], [141, 112, 0, 115, 0, "SEG_ELT"], [142, 114, 0, 115, 1, "IMAGE"], [143, 112, 2, 115, 2, "MASK"], [144, 112, 3, 115, 3, "SEG_ELT_crop_region"], [145, 112, 4, 115, 4, "SEG_ELT_bbox"], [146, 112, 5, 115, 5, "SEG_ELT_control_net_wrapper"], [147, 112, 6, 115, 6, "FLOAT"], [148, 112, 7, 115, 7, "STRING"], [149, 65, 0, 114, 1, "IMAGE"], [150, 115, 0, 116, 1, "SEG_ELT"], [151, 111, 0, 116, 0, "SEGS_HEADER"], [152, 116, 0, 117, 1, "SEGS"], [153, 59, 0, 117, 0, "IMAGE"], [154, 117, 0, 118, 0, "IMAGE"], [155, 91, 0, 119, 0, "SEGS"], [156, 119, 1, 120, 0, "SEG_ELT"], [159, 63, 0, 121, 1, "IMAGE"], [162, 120, 1, 121, 0, "IMAGE"], [163, 117, 0, 124, 1, "IMAGE"], [164, 124, 0, 91, 0, "SEGS"], [165, 87, 0, 124, 0, "BBOX_DETECTOR"], [166, 120, 0, 123, 0, "SEG_ELT"], [167, 120, 2, 123, 2, "MASK"], [168, 120, 3, 123, 3, "SEG_ELT_crop_region"], [169, 120, 4, 123, 4, "SEG_ELT_bbox"], [170, 120, 5, 123, 5, "SEG_ELT_control_net_wrapper"], [171, 120, 6, 123, 6, "FLOAT"], [172, 120, 7, 123, 7, "STRING"], [173, 121, 0, 123, 1, "IMAGE"], [174, 119, 0, 125, 0, "SEGS_HEADER"], [175, 125, 0, 126, 1, "SEGS"], [176, 117, 0, 126, 0, "IMAGE"], [177, 126, 0, 127, 0, "IMAGE"], [178, 123, 0, 125, 1, "SEG_ELT"], [179, 106, 0, 129, 0, "MODEL"], [180, 129, 0, 75, 1, "MODEL"]], "groups": [{"title": "Group", "bounding": [3639, 1038, 140, 80], "color": "#3f789e", "font_size": 24}], "config": {}, "extra": {}, "version": 0.4}