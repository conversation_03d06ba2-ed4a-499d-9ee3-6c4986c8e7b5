function sdxlTwoPeople({
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 864,
  height = 1024,
  batch_size = 1,
  nsfw = false,
  steps = 5,
  cfg = 1.5,
  faceSteps = 3,
  faceCfg = 1.5,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  faceDenoise = 0.6,
  face_image_data_1,
  face_image_data_2,
  bot_description_1,
  bot_description_2,
  image_prompt,
  background,
}) {
  seed = seed ?? Math.floor(Math.random() * 100000000000);

  return {
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    11: {
      inputs: {
        instantid_file: "ip-adapter.bin",
      },
      class_type: "InstantIDModelLoader",
      _meta: {
        title: "Load InstantID Model",
      },
    },
    16: {
      inputs: {
        control_net_name: "diffusion_pytorch_model.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    38: {
      inputs: {
        provider: "CPU",
      },
      class_type: "InstantIDFaceAnalysis",
      _meta: {
        title: "InstantID Face Analysis",
      },
    },
    39: {
      inputs: {
        text: bot_description_1,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    40: {
      inputs: {
        text: "",
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    60: {
      inputs: {
        weight: 1,
        start_at: 0,
        end_at: 1,
        instantid: ["11", 0],
        insightface: ["38", 0],
        control_net: ["16", 0],
        image: ["132", 0],
        model: ["4", 0],
        positive: ["39", 0],
        negative: ["40", 0],
        image_kps: ["157", 0],
        mask: ["311", 0],
      },
      class_type: "ApplyInstantID",
      _meta: {
        title: "Apply InstantID",
      },
    },
    77: {
      inputs: {
        weight: 1,
        start_at: 0,
        end_at: 1,
        instantid: ["11", 0],
        insightface: ["38", 0],
        control_net: ["16", 0],
        image: ["133", 0],
        model: ["60", 0],
        positive: ["89", 0],
        negative: ["40", 0],
        image_kps: ["161", 0],
        mask: ["308", 0],
      },
      class_type: "ApplyInstantID",
      _meta: {
        title: "Apply InstantID",
      },
    },
    89: {
      inputs: {
        text: bot_description_2,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    116: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    117: {
      inputs: {
        enabled: true,
        swap_model: "inswapper_128.onnx",
        facedetection: "YOLOv5n",
        face_restore_model: "GPEN-BFR-512.onnx",
        face_restore_visibility: 0.7,
        codeformer_weight: 0.5,
        detect_gender_input: "no",
        detect_gender_source: "no",
        input_faces_index: "0",
        source_faces_index: "0",
        console_log_level: 1,
        input_image: ["165", 0],
        source_image: ["133", 0],
      },
      class_type: "ReActorFaceSwap",
      _meta: {
        title: "ReActor 🌌 Fast Face Swap",
      },
    },
    118: {
      inputs: {
        enabled: true,
        swap_model: "inswapper_128.onnx",
        facedetection: "YOLOv5n",
        face_restore_model: "GPEN-BFR-512.onnx",
        face_restore_visibility: 0.7,
        codeformer_weight: 0.5,
        detect_gender_input: "no",
        detect_gender_source: "no",
        input_faces_index: "0",
        source_faces_index: "0",
        console_log_level: 1,
        input_image: ["195", 0],
        source_image: ["132", 0],
      },
      class_type: "ReActorFaceSwap",
      _meta: {
        title: "ReActor 🌌 Fast Face Swap",
      },
    },
    123: {
      inputs: {
        text: image_prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    131: {
      inputs: {
        image: "#DATA",
        image_data: face_image_data_1,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    132: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["131", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    133: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["134", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    134: {
      inputs: {
        image: "#DATA",
        image_data: face_image_data_2,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    135: {
      inputs: {
        seed,
        steps: 6,
        cfg: 1.5,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["4", 0],
        positive: ["123", 0],
        negative: ["40", 0],
        latent_image: ["116", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    136: {
      inputs: {
        samples: ["135", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    138: {
      inputs: {
        model_name: "segm/person_yolov8m-seg.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    146: {
      inputs: {
        mask: ["151", 0],
      },
      class_type: "MaskToImage",
      _meta: {
        title: "Convert Mask to Image",
      },
    },
    150: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 30,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7000000000000001,
        post_dilation: 0,
        bbox_detector: ["138", 0],
        image: ["136", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    151: {
      inputs: {
        segs: ["152", 0],
      },
      class_type: "SegsToCombinedMask",
      _meta: {
        title: "SEGS to MASK (combined)",
      },
    },
    152: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 1,
        take_count: 1,
        segs: ["150", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    155: {
      inputs: {
        mask: ["188", 0],
      },
      class_type: "MaskToImage",
      _meta: {
        title: "Convert Mask to Image",
      },
    },
    157: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["136", 0],
        source: ["155", 0],
        mask: ["160", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    160: {
      inputs: {
        mask: ["188", 0],
      },
      class_type: "InvertMask",
      _meta: {
        title: "InvertMask",
      },
    },
    161: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["136", 0],
        source: ["146", 0],
        mask: ["162", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    162: {
      inputs: {
        mask: ["151", 0],
      },
      class_type: "InvertMask",
      _meta: {
        title: "InvertMask",
      },
    },
    165: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["315", 0],
        source: ["146", 0],
        mask: ["162", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    187: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["150", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    188: {
      inputs: {
        segs: ["187", 0],
      },
      class_type: "SegsToCombinedMask",
      _meta: {
        title: "SEGS to MASK (combined)",
      },
    },
    190: {
      inputs: {
        x: 0,
        y: 0,
        operation: "multiply",
        destination: ["160", 0],
        source: ["162", 0],
      },
      class_type: "MaskComposite",
      _meta: {
        title: "MaskComposite",
      },
    },
    193: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["117", 0],
        source: ["315", 0],
        mask: ["162", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    195: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["193", 0],
        source: ["155", 0],
        mask: ["160", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    197: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["118", 0],
        source: ["193", 0],
        mask: ["160", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    218: {
      inputs: {
        detection_hint: "center-1",
        dilation: 0,
        threshold: 0.93,
        bbox_expansion: 0,
        mask_hint_threshold: 0.7,
        mask_hint_use_negative: "False",
        sam_model: ["219", 0],
        segs: ["187", 0],
        image: ["136", 0],
      },
      class_type: "SAMDetectorSegmented",
      _meta: {
        title: "SAMDetector (segmented)",
      },
    },
    219: {
      inputs: {
        model_name: "sam_vit_b_01ec64.pth",
        device_mode: "AUTO",
      },
      class_type: "SAMLoader",
      _meta: {
        title: "SAMLoader (Impact)",
      },
    },
    227: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["228", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    228: {
      inputs: {
        combined: false,
        crop_factor: 3,
        bbox_fill: false,
        drop_size: 50,
        contour_fill: false,
        mask: ["218", 0],
      },
      class_type: "MaskToSEGS",
      _meta: {
        title: "MASK to SEGS",
      },
    },
    229: {
      inputs: {
        segs: ["227", 0],
      },
      class_type: "SegsToCombinedMask",
      _meta: {
        title: "SEGS to MASK (combined)",
      },
    },
    246: {
      inputs: {
        detection_hint: "center-1",
        dilation: 0,
        threshold: 0.93,
        bbox_expansion: 0,
        mask_hint_threshold: 0.7,
        mask_hint_use_negative: "False",
        sam_model: ["219", 0],
        segs: ["152", 0],
        image: ["136", 0],
      },
      class_type: "SAMDetectorSegmented",
      _meta: {
        title: "SAMDetector (segmented)",
      },
    },
    247: {
      inputs: {
        combined: false,
        crop_factor: 3,
        bbox_fill: false,
        drop_size: 50,
        contour_fill: false,
        mask: ["246", 0],
      },
      class_type: "MaskToSEGS",
      _meta: {
        title: "MASK to SEGS",
      },
    },
    248: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["247", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    249: {
      inputs: {
        segs: ["248", 0],
      },
      class_type: "SegsToCombinedMask",
      _meta: {
        title: "SEGS to MASK (combined)",
      },
    },
    254: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["197", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    287: {
      inputs: {
        mask: ["249", 0],
      },
      class_type: "MaskToImage",
      _meta: {
        title: "Convert Mask to Image",
      },
    },
    288: {
      inputs: {
        detect_hand: "enable",
        detect_body: "enable",
        detect_face: "disable",
        resolution: 512,
        image: ["291", 0],
      },
      class_type: "OpenposePreprocessor",
      _meta: {
        title: "OpenPose Pose",
      },
    },
    291: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["287", 0],
        source: ["136", 0],
        mask: ["249", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    293: {
      inputs: {
        strength: 0.3,
        conditioning: ["77", 1],
        control_net: ["294", 0],
        image: ["288", 0],
      },
      class_type: "ControlNetApply",
      _meta: {
        title: "Apply ControlNet",
      },
    },
    294: {
      inputs: {
        control_net_name: "OpenPoseXL2.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    295: {
      inputs: {
        mask: ["229", 0],
      },
      class_type: "MaskToImage",
      _meta: {
        title: "Convert Mask to Image",
      },
    },
    297: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["295", 0],
        source: ["136", 0],
        mask: ["229", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    299: {
      inputs: {
        detect_hand: "enable",
        detect_body: "enable",
        detect_face: "disable",
        resolution: 512,
        image: ["297", 0],
      },
      class_type: "OpenposePreprocessor",
      _meta: {
        title: "OpenPose Pose",
      },
    },
    301: {
      inputs: {
        strength: 0.3,
        conditioning: ["60", 1],
        control_net: ["294", 0],
        image: ["299", 0],
      },
      class_type: "ControlNetApply",
      _meta: {
        title: "Apply ControlNet",
      },
    },
    307: {
      inputs: {
        expand: 32,
        tapered_corners: false,
        mask: ["249", 0],
      },
      class_type: "GrowMask",
      _meta: {
        title: "GrowMask",
      },
    },
    308: {
      inputs: {
        amount: 16,
        device: "auto",
        mask: ["307", 0],
      },
      class_type: "MaskBlur+",
      _meta: {
        title: "🔧 Mask Blur",
      },
    },
    309: {
      inputs: {
        expand: 32,
        tapered_corners: false,
        mask: ["229", 0],
      },
      class_type: "GrowMask",
      _meta: {
        title: "GrowMask",
      },
    },
    311: {
      inputs: {
        amount: 16,
        device: "auto",
        mask: ["309", 0],
      },
      class_type: "MaskBlur+",
      _meta: {
        title: "🔧 Mask Blur",
      },
    },
    312: {
      inputs: {
        conditioning_1: ["301", 0],
        conditioning_2: ["293", 0],
      },
      class_type: "ConditioningCombine",
      _meta: {
        title: "Conditioning (Combine)",
      },
    },
    313: {
      inputs: {
        conditioning_1: ["60", 2],
        conditioning_2: ["77", 2],
      },
      class_type: "ConditioningCombine",
      _meta: {
        title: "Conditioning (Combine)",
      },
    },
    314: {
      inputs: {
        seed,
        steps,
        cfg: 1.5,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["77", 0],
        positive: ["321", 0],
        negative: ["313", 0],
        latent_image: ["116", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    315: {
      inputs: {
        samples: ["314", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    317: {
      inputs: {
        x: 0,
        y: 0,
        operation: "add",
        destination: ["311", 0],
        source: ["308", 0],
      },
      class_type: "MaskComposite",
      _meta: {
        title: "MaskComposite",
      },
    },
    318: {
      inputs: {
        mask: ["317", 0],
      },
      class_type: "InvertMask",
      _meta: {
        title: "InvertMask",
      },
    },
    320: {
      inputs: {
        strength: 1,
        set_cond_area: "mask bounds",
        conditioning: ["322", 0],
        mask: ["318", 0],
      },
      class_type: "ConditioningSetMask",
      _meta: {
        title: "Conditioning (Set Mask)",
      },
    },
    321: {
      inputs: {
        conditioning_1: ["312", 0],
        conditioning_2: ["320", 0],
      },
      class_type: "ConditioningCombine",
      _meta: {
        title: "Conditioning (Combine)",
      },
    },
    322: {
      inputs: {
        text: background,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
  };
}

module.exports = {
  sdxlTwoPeople,
};
