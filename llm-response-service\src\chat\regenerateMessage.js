const { v4: uuidv4 } = require("uuid");
const { Context } = require("./context");
const { logError, logInfo } = require("../logUtils");
const { getExperiments } = require("../ab_expriments");
const {
  authorizeUserForConversation,
} = require("./authorizeUserForConversation");
const { readProfiles } = require("./readProfiles");
const { updateIsTyping } = require("./updateIsTyping");
const { getDeviceType, wrappedSupabaseError } = require("../utils");
const { generateBotResponseStreaming } = require("./generateBotResponse");
const {
  respondWithTextMessage,
} = require("./responders/respondWithTextMessage");
const {
  respondWithImageMessage,
} = require("./responders/respondWithImageMessage");
const { loggingInfo } = require("../logging");
const { supabase } = require("../supabaseClient");

async function regenerateMessage(req, res) {
  if (!req.body.record) {
    return res.sendStatus(200);
  }

  if (
    req.body.record.pre_processed ||
    req.body.record.is_system_message ||
    req.body.record.is_bot ||
    !req.body.record.body?.length
  ) {
    return res.sendStatus(200);
  }

  const senderId = req.body.record.sender_id;
  const conversationId = req.body.record.conversation_id;
  const userId = req.user?.id;
  const branchIndex = req.body.record.branch_index;

  if (!branchIndex) {
    logError({
      context: "regenerateMessage called without branch_index",
    });
    throw new Error("regenerateMessage called without branch_index");
  }

  const ctx = new Context(uuidv4(), conversationId, senderId);

  if (senderId == 411382) {
    ctx.setVerbose();
  }

  try {
    ctx.initExperiments(await getExperiments(senderId));
  } catch (error) {
    logError({
      context: "regenerateMessage - failed to fetch experiments",
      error,
    });
    return res.status(500).send({ error: "Internal server error" });
  }

  logInfo({
    ...ctx.logging,
    request_body: req.body,
    msg: "handling regenerateMessage request",
  });

  // authorize the request.
  if (!req.isAdmin) {
    if (
      !(await authorizeUserForConversation(conversationId, senderId, userId))
    ) {
      logInfo({
        ...ctx.logging,
        msg: "request not authorized.",
      });
      return res.status(403).json({ error: "Forbidden" });
    }
  }

  const {
    userParticipant,
    botParticipant,
    userProfile,
    botProfile,
    botConfiguration,
  } = await readProfiles(ctx, { conversationId, senderId });

  if (!botConfiguration) {
    logError({
      context: "renegerateMessage",
      error: "user-to-user message configuration",
    });
    return res.sendStatus(200);
  }

  ctx.setProfiles({
    userParticipant,
    botParticipant,
    userProfile,
    botProfile,
    botConfiguration,
  });

  updateIsTyping(conversationId, botProfile.id, true);

  const deviceType = getDeviceType(req.get("User-Agent"));
  ctx.setDeviceType(deviceType);

  ctx.startMessageGeneration();

  const { data: history, error: historyError } = await supabase
    .from("messages")
    .select("*")
    .eq("conversation_id", conversationId)
    .order("id", { ascending: false })
    .limit(100);
  if (historyError) {
    const error = wrappedSupabaseError(
      historyError,
      "Failed to fetch messages",
    );
    logError({
      ...ctx.logging,
      msg: "failed to fetch messages",
      error,
    });
    throw error;
  }

  history.reverse();

  // Filter out latest bot messages.
  while (history.length > 0 && history[history.length - 1].is_bot) {
    history.pop();
  }

  if (history.length == 0) {
    logError({
      ...ctx.logging,
      context: "regenerateMessage - called on empty history",
    });
    return res.sendStatus(200);
  }

  const body = history[history.length - 1].body;

  if (history.length > 40) {
    history.splice(0, history.length - 40);
  }

  ctx.setMessageHistory(history);

  try {
    const botMessages = generateBotResponseStreaming(
      ctx,
      { conversationConfiguration: botParticipant, botConfiguration },
      { userProfile, botProfile },
      { body, history, deviceType },
    );

    for await (const message of botMessages) {
      ctx.registerGeneratedMessage(message);
      if (message.type === "text") {
        await respondWithTextMessage(
          ctx,
          { conversationId, messageGroupingId: null },
          { botProfile },
          { branchIndex },
          message,
        );
      } else if (message.type === "image") {
        await respondWithImageMessage(
          ctx,
          { conversationId },
          { botConfiguration, botProfile, userProfile },
          { branchIndex, deviceType },
          message,
        );
      }
    }

    ctx.endMessageGeneration();

    logInfo({
      ...ctx.logging,
      msg: "regeneration done",
      success: true,
    });
    // Send the logs to bigquery as well
    loggingInfo("chat_backend", {
      type: "REGENERATION_DONE",
      result: "SUCCESS",
      info: JSON.stringify({
        ...ctx.getCleanLogging(),
      }),
    });

    res.sendStatus(204);
  } catch (error) {
    logError({
      ...ctx.logging,
      msg: "regeneration done",
      success: false,
      error,
    });
    // Send the logs to bigquery as well
    loggingInfo("chat_backend", {
      type: "REGENERATION_DONE",
      result: "FAILURE",
      info: JSON.stringify({
        ...ctx.getCleanLogging(),
      }),
    });

    updateIsTyping(conversationId, botProfile.id, false);

    throw error;
  }

  updateIsTyping(conversationId, botProfile.id, false);
}

module.exports = {
  regenerateMessage,
};
