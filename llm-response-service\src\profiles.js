const express = require("express");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const {
  logError,
  checkProfileValid,
  checkAdminValid,
  getSpecificKeys,
  wrappedSupabaseError,
  logWarn,
  checkMediaUrlValid,
} = require("./utils");
const { readLatestProfileScore } = require("./btClient");
const {
  generateEmbeddingForProfile,
  deleteFollowRequests,
} = require("./profilesHelpers");
const { doesImageContainFace } = require("./imageHelpers");
const { authUser } = require("./middleware");
const { doFollow } = require("./root");
const dayjs = require("dayjs");
const { INIT_FOLLOW_COUNT, FOLLOW_COUNT } = require("./constants");

require("dotenv").config();

app.get("/ping", async (req, res) => {
  return res.send("Profiles ping");
});

app.get("/getPendingPostTasks", async (req, res) => {
  const { profile_id } = req.query;
  if (!profile_id) {
    return res.sendStatus(400);
  }

  let postsAndTasks = [];

  // get all posts that are drafts within the past 5 minutes
  const { data: posts, error: postsError } = await supabase
    .from("posts")
    .select("id, slug, ai_caption, description, visibility, created_at")
    .eq("profile_id", profile_id)
    .eq("visibility", "draft")
    .not("proposed_post_state", "in", `(proposed,generating)`)
    .gte("created_at", new Date(Date.now() - 5 * 60 * 1000).toISOString());

  if (postsError) {
    const error = wrappedSupabaseError(postsError, "Failed to get posts");
    logError({ context: "/getPendingPostTasks failed", error, profile_id });
    return res.status(500).send({ data: null, error: error.message });
  }

  for (const post of posts) {
    // VU: TODO optimize by batching
    const { data: taskData, error: taskError } = await supabase.rpc(
      "get_tasks_by_post_id",
      {
        post_id_param: post.id,
      },
    );

    if (taskError || !taskData || taskData.length === 0) {
      logError({
        executionId: req.executionId,
        context: "getPendingPostTasks - Get tasks by post id failed",
        error: taskError,
        post_id: post.id,
      });
      continue;
    }
    const payload = {
      post,
      task: taskData[0],
    };

    postsAndTasks.push(payload);
  }

  res.json(postsAndTasks);
});

app.get("/getProfileLikeScore", async (req, res) => {
  const { profile_id } = req.query;

  const { data } = await readLatestProfileScore(profile_id);

  /* Deprecated: to read profile_score from supabase
  const { data, error: rpcError } = await supabase.rpc(
    "func_get_profiles_score",
    {
      p_profile_id: profile_id,
    },
  );

  if (rpcError) {
    const error = wrappedSupabaseError(
      rpcError,
      "failed to get profile like score",
    );
    logError({
      context: "getProfileLikeScore",
      error: error,
    });
    return res.status(500).send({ data: null, error: error });
  }
  */

  return res.send({ data });
});

app.get("/faceDetectForAllProfiles", async (req, res) => {
  async function processInBatches(profiles, batchSize) {
    for (let i = 0; i < profiles.length; i += batchSize) {
      const batch = profiles.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (profile) => {
          if (
            profile.avatar_photo_contains_face !== null &&
            profile.avatar_photo_contains_face !== undefined &&
            !req.query.force
          ) {
            console.log("Skipping avatar_photo_contains_face for:", profile.id);
            return Promise.resolve(); // Resolve immediately for skipped profiles
          }
          console.log("Generating avatar_photo_contains_face for:", profile.id);
          const avatar_photo_contains_face = await doesImageContainFace({
            imageUrl: profile.avatar_url,
          });

          const { error: updateProfileError } = await supabase
            .from("profiles")
            .update({
              avatar_photo_contains_face,
            })
            .eq("id", profile.id);

          if (updateProfileError) {
            const error = wrappedSupabaseError(updateProfileError);
            logError({
              executionId: req.executionId,
              context: "/faceDetectForAllProfiles: failed to update profile",
              profile_id: profile.id,
              error: error,
            });
          }
        }),
      );
    }
  }

  const totalBatchSize = 200;
  const concurrentBatchSize = 40;
  let startIndex = 0;

  while (true) {
    const { data: profiles, error: profilesErrors } = await supabase
      .from("profiles")
      .select("id, avatar_photo_contains_face, avatar_url")
      .neq("visibility", "archived")
      .order("id", { ascending: false })
      .range(startIndex, startIndex + totalBatchSize - 1);

    if (profilesErrors) {
      const error = wrappedSupabaseError(profilesErrors);
      logError({
        executionId: req.executionId,
        context: "/faceDetectForAllProfiles: failed to fetch profiles",
        error: error,
      });
      throw error;
    }

    if (!profiles || profiles.length === 0) {
      break;
    }

    await processInBatches(profiles, concurrentBatchSize);
    startIndex += totalBatchSize;
  }
  res.sendStatus(200);
});

app.get("/generateEmbeddingForAllProfiles", async (req, res) => {
  async function processInBatches(profiles, batchSize) {
    for (let i = 0; i < profiles.length; i += batchSize) {
      const batch = profiles.slice(i, i + batchSize);
      await Promise.all(
        batch.map((profile) => {
          if (profile.embedding && !req.query.force) {
            console.log("Skipping embedding for:", profile.id);
            return Promise.resolve(); // Resolve immediately for skipped profiles
          }
          console.log("Generating embedding for:", profile.id);
          return generateEmbeddingForProfile({
            profile,
          });
        }),
      );
    }
  }

  const totalBatchSize = 200;
  const concurrentBatchSize = 5;
  let startIndex = 0;

  while (true) {
    const { data: profiles, error: profilesErrors } = await supabase
      .from("profiles")
      .select(
        "id, username, description, location, display_name, nsfw, bots!bots_profile_id_fkey(*), embedding",
      )
      .neq("visibility", "archived")
      .order("id", { ascending: false })
      .range(startIndex, startIndex + totalBatchSize - 1);

    if (profilesErrors) {
      const error = wrappedSupabaseError(profilesErrors);
      logError({
        executionId: req.executionId,
        context: "/generateEmbeddingForAllProfiles: failed to fetch profiles",
        error: error,
      });
      throw error;
    }

    if (!profiles || profiles.length === 0) {
      break;
    }

    await processInBatches(profiles, concurrentBatchSize);
    startIndex += totalBatchSize;
  }
  res.sendStatus(200);
});

app.get("/generateEmbeddingForProfileId", async (req, res) => {
  if (!req.query.profile_id) {
    return res.sendStatus(400);
  }

  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .select(
      "id, username, description, location, display_name, nsfw, bots!bots_profile_id_fkey(*)",
    )
    .neq("visibility", "archived")
    .eq("id", req.query.profile_id)
    .single();
  if (profileError) {
    logWarn({
      executionId: req.executionId,
      context: "/generateEmbeddingForProfileId: failed to fetch profile",
      error: wrappedSupabaseError(profileError),
    });
    return res.sendStatus(400);
  }

  let skipIfExists = true;
  const embedding = await generateEmbeddingForProfile({
    profile,
    skipIfExists,
  });

  res.json(embedding);
});

/**
 * Fetch profile with filters
 * This api should be called by Admin
 *
 * @param search_type - search type
 * @param search_value - search value
 * @param nsfw_type - nsfw type
 * @param nsfl_type - nsfl type
 * @param profile_type - profile type
 * @param page_size - page size
 * @param page_number - page number
 * @param strict - strict search mode
 *
 * @returns
 */
app.get("/fetchProfilesWithFilters", authUser, async (req, res) => {
  const {
    search_type,
    search_value = "",
    nsfw_type,
    nsfl_type,
    profile_type,
    page_size = 15,
    page_number = 1,
    strict = false,
  } = req.query;

  if (!search_type) {
    return res.sendStatus(400);
  }

  try {
    const parameters = {
      search_type,
      search_value,
      nsfw_type: nsfw_type || null,
      nsfl_type: nsfl_type || null,
      profile_type: profile_type || null,
      page_size,
      page_number,
      strict: strict === "true" ? true : false,
    };
    const { data, error } = await supabase.rpc(
      "fetch_profiles_admin_v2",
      parameters,
    );

    if (error) {
      logError({
        executionId: req.executionId,
        context: "Get Profiles Error",
        error: error,
      });
      return res.sendStatus(400);
    }

    return res.send({ data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Get Profiles Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

/**
 * Fetch profiles count with filters
 * This api should be called by Admin
 *
 * @param search_type - search type
 * @param search_value - search value
 * @param nsfw_type - nsfw type
 * @param nsfl_type - nsfl type
 * @param profile_type - profile type
 * @param strict - strict search mode
 *
 * @returns - (number) profile_count
 */
app.get("/fetchProfilesCountWithFilters", authUser, async (req, res) => {
  const {
    search_type,
    search_value = "",
    nsfw_type,
    nsfl_type,
    profile_type,
    strict = false,
  } = req.query;

  if (!search_type) {
    return res.sendStatus(400);
  }
  try {
    const parameters = {
      search_type,
      search_value,
      nsfw_type: nsfw_type || null,
      nsfl_type: nsfl_type || null,
      profile_type: profile_type || null,
      strict: strict === "true" ? true : false,
    };
    const { data, error } = await supabase.rpc(
      "fetch_profiles_count_admin_v2",
      parameters,
    );

    if (error) {
      logError({
        executionId: req.executionId,
        context: "Get Profiles Count Error",
        error: error,
      });
      return res.sendStatus(400);
    }

    return res.send({ data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Get Profiles Count Failed Error",
      error: error,
    });
    return res.sendStatus(500);
  }
});

app.post("/updateProfile", authUser, async (req, res) => {
  const { updateContents, profileId } = req.body;
  const user_id = req.user?.id;

  if (
    !updateContents ||
    Object.keys(updateContents).length === 0 ||
    !profileId
  ) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    const isValid = await checkProfileValid(user_id, profileId);
    if (!isValid) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  const keysForNormal = [
    "visibility",
    "username",
    "avatar_url",
    "display_name",
    "description",
    "location",
    "gender",
    "old_usernames",
    "auto_generated",
    "bio",
  ];
  let condition;

  if (isAdmin) {
    condition = updateContents;
  } else {
    condition = getSpecificKeys(updateContents, keysForNormal);
  }

  if (condition?.username) {
    const usernameRegex = /^[a-zA-Z0-9_.-]{2,60}$/; //no whitespace, emojis and Zalgo text, min:2 max:60 text length
    const isValid = usernameRegex.test(condition.username);
    if (!isValid) {
      delete condition.username;
    }
  }

  if (JSON.stringify(condition) === "{}") {
    return res.sendStatus(204);
  }

  condition.updated_at = new Date().toISOString();

  if (condition.visibility) {
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("age")
      .eq("id", profileId)
      .single();

    if (profileError) {
      logError({
        context: "profile age error",
        error: wrappedSupabaseError(profileError),
        profileId,
      });
      return res.status(500).send({ error: "Failed to get profile data" });
    }

    const profileAge = profileData?.age;
    if (profileAge === "child" && condition.visibility) {
      condition.visibility = "private";
    }
  }

  if (condition.avatar_url) {
    const isValid = checkMediaUrlValid(condition.avatar_url);
    if (!isValid) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  try {
    const { error } = await supabase
      .from("profiles")
      .update(condition)
      .eq("id", profileId);

    if (error) {
      const profileError = wrappedSupabaseError(error);
      throw profileError;
    }

    return res.send({ data: "success", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update Profile Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to update profile on profiles" });
  }
});

app.post("/deleteProfile", authUser, async (req, res) => {
  const { profileId } = req.body;
  const user_id = req.user?.id;

  if (!profileId) {
    return res.status(400).send({
      data: null,
      error: "Invalid input error: profileId is required",
    });
  }

  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("profiles")
      .update({ visibility: "archived" })
      .eq("id", profileId);

    if (error) {
      const profileError = wrappedSupabaseError(error);
      throw profileError;
    }

    return res.send({ data: "Successfully Deleted", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete Profile Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to delete profile from profiles" });
  }
});

app.post("/insertProfile", authUser, async (req, res) => {
  const { insertContents, fieldText } = req.body;

  if (!insertContents || !fieldText) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .insert(insertContents)
      .select(fieldText)
      .single();

    if (error) {
      const profileError = wrappedSupabaseError(error);
      throw profileError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert Profile Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add profile to profiles" });
  }
});

app.post("/addProfileToBlockList", authUser, async (req, res) => {
  const { profile_id, blocked_profile_id } = req.body;

  if (!profile_id || !blocked_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase.from("blocked_lists").upsert(
      {
        profile_id: profile_id,
        blocked_profile_id: blocked_profile_id,
      },
      { onConflict: ["profile_id", "blocked_profile_id"] },
    );

    if (error) {
      const blockError = wrappedSupabaseError(error);
      throw blockError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert profile to blockedlist Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add profile to blocklist" });
  }
});

app.post("/removeProfileFromBlockList", authUser, async (req, res) => {
  const { profile_id, blocked_profile_id } = req.body;

  if (!profile_id || !blocked_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("blocked_lists")
      .delete()
      .eq("profile_id", profile_id)
      .eq("blocked_profile_id", blocked_profile_id)
      .select();

    if (error) {
      const blockError = wrappedSupabaseError(error);
      throw blockError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Remove profile from blockedlist Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to remove profile from blocklist" });
  }
});

app.post("/addProfileToHideList", authUser, async (req, res) => {
  const { profile_id, hidden_profile_id } = req.body;

  if (!profile_id || !hidden_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase.from("hide_lists").upsert(
      {
        profile_id: profile_id,
        hidden_profile_id: hidden_profile_id,
      },
      { onConflict: ["profile_id", "hidden_profile_id"] },
    );

    if (error) {
      const hideError = wrappedSupabaseError(error);
      throw hideError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert profile to HideList Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to add profile to Hidelist" });
  }
});

app.post("/showProfileToHideList", authUser, async (req, res) => {
  const { profile_id, hidden_profile_id } = req.body;

  if (!profile_id || !hidden_profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("hide_lists")
      .delete()
      .eq("profile_id", profile_id)
      .eq("hidden_profile_id", hidden_profile_id)
      .select();

    if (error) {
      const hideError = wrappedSupabaseError(error);
      throw hideError;
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Remove profile to HideList Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to remove profile from Hidelist" });
  }
});

app.post("/addMultiProfilesToBlockList", authUser, async (req, res) => {
  const { profile_id, blocked_profile_ids } = req.body;

  if (
    !profile_id ||
    !blocked_profile_ids ||
    !blocked_profile_ids.length === 0
  ) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data: existingBlockedProfiles, error: existingError } =
      await supabase
        .from("blocked_lists")
        .select("blocked_profile_id")
        .eq("profile_id", profile_id);

    if (existingError) {
      throw existingError;
    }

    const recordsToInsert = blocked_profile_ids
      .filter(
        (id) =>
          !existingBlockedProfiles.some(
            (profile) => profile.blocked_profile_id === id,
          ),
      )
      .map((blocked_profile_id) => ({ profile_id, blocked_profile_id }));

    if (recordsToInsert.length === 0) {
      return res.status(204).send({ data: "No new record", error: null });
    }

    const { error } = await supabase
      .from("blocked_lists")
      .insert(recordsToInsert);

    if (error) {
      const blockError = wrappedSupabaseError(error);
      throw blockError;
    }

    return res.send({ data: "Done", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Insert multiple profiles to blockedlist Failed Error",
      error: error,
    });
    return res.status(500).send({
      data: null,
      error: "Failed to add multiple profiles to blocklist",
    });
  }
});

app.post("/acceptFollowRequest", authUser, async (req, res) => {
  const { profile_id, requester_id } = req.body;

  if (!profile_id || !requester_id) {
    return res.status(400).send({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;

  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    // Update follow request status
    const { error: updateFollowRequestError } = await supabase
      .from("follow_requests")
      .update({ status: "accepted", updated_at: new Date() })
      .eq("requester_id", requester_id)
      .eq("requestee_id", profile_id);

    if (updateFollowRequestError) {
      const error = wrappedSupabaseError(updateFollowRequestError);
      logError({
        context: "acceptFollowRequest - update follow request status error",
        error,
      });
      throw error;
    }

    // do follow
    const { error: followError } = await doFollow({
      following_id: profile_id,
      follower_id: requester_id,
    });

    if (followError) {
      const error = wrappedSupabaseError(followError);
      logError({
        context: "acceptFollowRequest - doFollow error",
        error,
      });
      throw error;
    }

    // send notification
    await supabase
      .from("notifications")
      .update({
        additional_info: { status: "accepted" },
        is_read: true,
      })
      .eq("profile_id", profile_id)
      .eq("sender_profile_id", requester_id)
      .eq("additional_info->>status", "requested");

    return res.send({ data: "done", error: null });
  } catch (error) {
    logError({
      context: "Accept follow request failed Error",
      error,
    });
    return res.status(500).send({ error: "Accept follow request failed" });
  }
});

app.post("/deFollowRequests", authUser, async (req, res) => {
  const { details } = req.body;
  if (!details || Object.keys(details).length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const keysToDelete = ["id", "requester_id", "requestee_id"];
  const condition = getSpecificKeys(details, keysToDelete);

  if (Object.keys(condition).length === 0) {
    return res.send({ data: null, error: null });
  }

  let query = supabase.from("follow_requests").delete();
  Object.keys(condition).forEach(
    (key) => (query = query.eq(key, condition[key])),
  );

  try {
    const { error } = await query;
    if (error) {
      const followRequestError = wrappedSupabaseError(error);
      throw followRequestError;
    }
    return res.send({ data: "done", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete Follow Requests Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to follow requests" });
  }
});

app.patch("/myProfileSettings", authUser, async (req, res) => {
  const { key, value, profile_id } = req.body;

  if (!key || value === null || value === undefined) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profile_settings")
      .update({ [key]: value })
      .eq("profile_id", profile_id)
      .select("*")
      .single();

    if (error) {
      const profileSettingError = wrappedSupabaseError(error);
      throw profileSettingError;
    }
    return res.send({ data: data, error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Update my profile Settings Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to update my profile settings" });
  }
});

app.put("/profileSettings/:profile_id", authUser, async (req, res) => {
  const profileId = req.params.profile_id;
  const { details } = req.body;

  details.profile_id = parseInt(profileId, 10);

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, details.profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profile_settings")
      .upsert(details, { onConflict: ["profile_id"] })
      .select("*")
      .single();

    if (error) {
      const profileSettingError = wrappedSupabaseError(error);
      throw profileSettingError;
    }
    return res.send({ data: data, error: null });
  } catch (error) {
    logError({
      context: "profileSettings: upsert Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Profile settings failed" });
  }
});

app.get("/userProfiles", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const PROFILE_FIELDS_TEXT =
    "id, user_id, avatar_url, username, description, display_name, location, visibility, gender, auto_generated, age, total_xp, users:user_id (is_banned, premium_months_quantity, role, onboarding_status, birthday, app_reviewed, visited_other_bot, opened_clone_announce, mci_announced_ids, opened_first_post_share_modal, viewed_first_post, opened_contacts, challenge_ids, viewed_vote_guide, opened_nsfw_confirm_modal)";

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data: profiles, error } = await supabase
      .from("profiles")
      .select(PROFILE_FIELDS_TEXT)
      .eq("user_id", user_id)
      .neq("visibility", "archived")
      .order("id", { ascending: true });
    if (error) {
      throw wrappedSupabaseError(error);
    }

    let is_premium = false;

    try {
      const { data: currentPlan, error: currentPlanError } = await supabase
        .from("user_current_plans_view")
        .select("current_plan_id, current_plan_is_active")
        .eq("user_id", user_id)
        .maybeSingle();

      if (currentPlanError) {
        const error = wrappedSupabaseError(
          currentPlanError,
          "Failed to fetch user current plan",
        );
        logError({
          context: "userProfiles - failed to fetch user current plan",
          error,
          user_id: user_id,
        });
      }

      if (
        currentPlan &&
        currentPlan.current_plan_id === 2 &&
        currentPlan.current_plan_is_active
      ) {
        is_premium = true;
      }
    } catch (planError) {
      logError({
        context: "userProfiles - premium check error",
        error: planError,
        user_id: user_id,
      });
    }

    // Add premium status to each profile
    const profilesWithPremium = profiles.map((profile) => ({
      ...profile,
      is_premium,
    }));

    return res.send({ data: profilesWithPremium, error: null });
  } catch (error) {
    logError({
      context: "profile - fetch profile error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to fetch profile" });
  }
});

app.get("/profileByName", async (req, res) => {
  const { username, id, isOwnerProfile } = req.query;
  if (!username) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select(
        `
          id, avatar_url, username, description, display_name, location, visibility, nsfw, user_id, avatar_photo_contains_face, cyoa_mode, proposed_post_mode, proposed_post_next_generation_date
        `,
      )
      .eq("username", username)
      .neq("visibility", "hidden")
      .neq("visibility", "archived");

    if (profileError) {
      // Check if username is changed
      const { data, error } = await supabase
        .from("profiles")
        .select("username")
        .not("old_usernames", "is", null)
        .contains("old_usernames", `"${username}"`)
        .single();

      const isUsernameChanged = !error && data && data?.username;

      if (isUsernameChanged) {
        return res.send({
          data: { usernameChanged: true, newUsername: data.username },
          error: null,
        });
      }

      return res.status(404).send({ error: "Profile not found" });
    }

    if (!profile || profile.length === 0) {
      return res.status(404).send({ error: "Profile not found" });
    }
    const [profileData] = profile;

    const botQuery = profileData?.user_id
      ? Promise.resolve({ data: null })
      : supabase
          .from("bots")
          .select(
            `
              id, show_creator, profile_id, description, display_name, life, bio, art_style, clone_id,
              creator:profiles!bots_creator_id_fkey(id, visibility, username, display_name, avatar_url)
            `,
          )
          .eq("profile_id", profileData.id)
          .single();

    const { data: botsData, error: botsError } = await botQuery;
    if (botsError) {
      const error = wrappedSupabaseError(botsError, "failed to fetch bot data");
      throw error;
    }

    const { id: botId, creator } = botsData || {};
    const isMyBot = creator?.id == id;
    // Check if the profile is private and if the bot is not owned by the user
    if (profileData?.visibility === "private" && botId && !isMyBot) {
      return res.status(403).send({ error: "Forbidden" });
    }

    const follwerCountQuery = supabase
      .from("followers")
      .select(
        "follower_id, profile:followers_follower_id_fkey!inner(visibility)",
        {
          count: "exact",
        },
      )
      .eq("following_id", profileData.id)
      .neq("profile.visibility", "archived")
      .neq("profile.visibility", "hidden");
    const followingCountQuery = supabase
      .from("followers")
      .select(
        "following_id, profile:followers_following_id_fkey!inner(visibility)",
        {
          count: "exact",
        },
      )
      .eq("follower_id", profileData.id)
      .neq("profile.visibility", "archived")
      .neq("profile.visibility", "hidden");
    const isFollowingQuery =
      !id || id == profileData?.id
        ? Promise.resolve({ data: [] })
        : supabase
            .from("followers")
            .select("id")
            .eq("follower_id", id ?? 0)
            .eq("following_id", profileData.id);
    const isFollowedQuery =
      !id || id == profileData?.id
        ? Promise.resolve({ data: [] })
        : supabase
            .from("followers")
            .select("id")
            .eq("follower_id", profileData.id)
            .eq("following_id", id ?? 0);
    const isSubscribingQuery =
      !id || id == profileData?.id
        ? Promise.resolve({ data: [] })
        : supabase
            .from("subscribers")
            .select("*")
            .eq("subscriber_id", id ?? 0)
            .eq("subscribing_id", profileData.id);

    const [
      { count: followerCount, error: followerCountError },
      { count: followingCount, error: followingCountError },
      { data: isFollowingData, error: isFollowingError },
      { data: isFollowedData, error: isFollowedError },
      { data: isSubscribingData, error: isSubscribingError },
    ] = await Promise.all([
      follwerCountQuery,
      followingCountQuery,
      isFollowingQuery,
      isFollowedQuery,
      isSubscribingQuery,
    ]);

    const completeProfile = {
      ...profileData,
      bots: botsData,
      follower_count: [{ count: followerCount }],
      following_count: [{ count: followingCount }],
      is_following: isFollowingData,
      is_followed: isFollowedData,
      is_subscribing: isSubscribingData,
    };

    if (followerCountError) {
      const error = wrappedSupabaseError(
        followerCountError,
        "failed to fetch follower count",
      );
      throw error;
    }
    if (followingCountError) {
      const error = wrappedSupabaseError(
        followingCountError,
        "failed to fetch following count",
      );
      throw error;
    }
    if (isFollowingError) {
      const error = wrappedSupabaseError(
        isFollowingError,
        "failed to fetch isfollowing data",
      );
      throw error;
    }
    if (isFollowedError) {
      const error = wrappedSupabaseError(
        isFollowedError,
        "failed to fetch isFollowed data",
      );
      throw error;
    }
    if (isSubscribingError) {
      const error = wrappedSupabaseError(
        isSubscribingError,
        "failed to fetch subscribing",
      );
      throw error;
    }

    if (completeProfile) {
      // Fetch bot count for the profile
      let botCount = 0;
      if (isOwnerProfile === "false" && completeProfile?.user_id) {
        const { count } = await supabase
          .from("bots")
          .select("profiles:profile_id!inner(visibility)", { count: "exact" })
          .eq("creator_id", completeProfile?.id)
          .is("show_creator", true)
          .eq("profiles.visibility", "public");

        botCount = count;
      }

      return res.send({
        data: {
          profile: completeProfile,
          botCount,
        },
        error: null,
      });
    }
  } catch (error) {
    logError({
      context: "profileByName - error",
      error,
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
});

app.get("/profilesStories", async (req, res) => {
  const { id } = req.query;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles_with_stories")
      .select("*")
      .eq("user_profile_id", id)
      .or(`profile_visibility.eq.public,creator_id.eq.${id}`)
      .order("max_unread_post_id", { ascending: false, nullsFirst: false })
      .range(0, 9);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "profilesStories - fetch profile error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to fetch profile" });
  }
});

app.get("/blockedStatus", async (req, res) => {
  const { selectedId, targetId, option } = req.query;

  if (!selectedId || !targetId) {
    return res.status(400).json({ data: null, error: "Invalid input error" });
  }

  try {
    const queries = [
      supabase
        .from("blocked_lists")
        .select("count", { count: "exact" })
        .eq("profile_id", selectedId)
        .eq("blocked_profile_id", targetId)
        .single(),
    ];

    // Add the second query if not in "single" mode
    if (option !== "single") {
      queries.push(
        supabase
          .from("blocked_lists")
          .select("count", { count: "exact" })
          .eq("profile_id", targetId)
          .eq("blocked_profile_id", selectedId)
          .single(),
      );
    }

    const results = await Promise.all(queries);

    // Map results to extract data or handle errors
    const data = results.map((result) => {
      if (result.error) {
        throw wrappedSupabaseError(result.error);
      }
      return result.data;
    });

    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "blockedStatus - fetch blocked status error",
      error,
    });
    return res
      .status(500)
      .json({ data: null, error: "Failed to fetch blocked status" });
  }
});

app.get("/storyWithMemories", async (req, res) => {
  const { profileId, selectedProfileId, isMyBot } = req.query;
  if (!profileId || !selectedProfileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    let query = supabase
      .from("stories")
      .select(
        "*, memories(*, posts(id, media_url, visibility, nsfw, created_at))",
      )
      .eq("profile_id", profileId)
      .eq("memories.posts.visibility", "public")
      .not("memories.posts", "is", null)
      .order("id", { ascending: false });

    // if (profileId !== selectedProfileId) {
    //   query = query.neq("memories.posts.nsfw", "nsfw");
    // }

    const { data, error } = await query;

    if (error) {
      throw wrappedSupabaseError(error);
    }

    const safeData = Array.isArray(data) ? data : [];
    const filteredData = safeData.filter(
      (item) => isMyBot || item.memories.length > 0,
    );

    return res.json({ data: filteredData, error: null });
  } catch (error) {
    logError({
      context: "storyWithMemories - fetch story error",
      error,
      profileId,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to fetch storyWithMemories" });
  }
});

app.get("/isRegenerating", async (req, res) => {
  const { id } = req.query;
  if (!id || isNaN(Number(id))) {
    return res
      .status(400)
      .send({ data: null, error: "Invalid input: id must be a number" });
  }

  const twoMinutesAgo = dayjs().subtract(2, "minute").toISOString();

  const { data } = await supabase
    .from("tasks")
    .select("status, payload")
    .eq("payload->profile_id", id)
    .eq("status", "queued")
    .gte("created_at", twoMinutesAgo)
    .order("id", { ascending: false })
    .single();

  const isRegenerating = !!data;
  return res.status(200).send({ data: isRegenerating, error: null });
});

app.post("/getFollowRequests", authUser, async (req, res) => {
  const { selectedProfileId } = req.body;
  if (!selectedProfileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, selectedProfileId);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data: requestData, error: requestError } = await supabase
      .from("follow_requests")
      .select(
        `
          id, 
          created_at, 
          profiles:requester_id(
            id, 
            avatar_url, 
            username, 
            display_name
          )
        `,
      )
      .eq("requestee_id", selectedProfileId)
      .eq("status", "requested")
      .neq("profiles.visibility", "archived")
      .neq("profiles.visibility", "hidden")
      .not("profiles", "is", null);
    if (requestError) {
      throw wrappedSupabaseError(requestError);
    }
    if (!requestData || requestData.length === 0) {
      return res.json({ data: [], error: null });
    }

    const requesterIds = requestData.map((item) => item.profiles.id);

    const [
      { data: followData, error: followError },
      { data: visibilityData, error: visibilityError },
    ] = await Promise.all([
      supabase
        .from("followers")
        .select("follower_id")
        .eq("following_id", selectedProfileId)
        .in("follower_id", requesterIds),
      supabase
        .from("profiles")
        .select("visibility")
        .eq("id", selectedProfileId)
        .single(),
    ]);
    if (followError) {
      const error = wrappedSupabaseError(followError);
      logWarn({
        context: "failed to fetch followers",
        error,
      });
      return res.json({ data: requestData, error: null });
    }

    if (visibilityError) {
      const error = wrappedSupabaseError(visibilityError);
      logWarn({
        context: "failed to profile visibility",
        error,
      });
      return res.json({ data: requestData, error: null });
    }

    // If profile visibility is public, return empty array and delete the old follow requests
    if (visibilityData?.visibility === "public") {
      res.json({ data: [], error: null });
      await deleteFollowRequests(selectedProfileId, requesterIds);
    } else {
      const followerIds = (followData || []).map(
        (follower) => follower.follower_id,
      );
      const filteredRequestData = requestData.filter(
        (request) => !followerIds.includes(request.profiles.id),
      );

      res.json({ data: filteredRequestData, error: null });

      // Delete redundant follow requests
      if (followerIds.length > 0) {
        await deleteFollowRequests(selectedProfileId, followerIds);
      }
    }
  } catch (error) {
    logError({
      context: "getFollowRequests failed",
      error,
      selectedProfileId,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getFollowRequests" });
  }
});

// profiles for users tab in Admin
app.post("/getUserProfilesAdmin", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);
  if (!isAllowed) {
    return res.status(403).send({ data: null, error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("id, avatar_url, display_name, username")
      .eq("user_id", id);
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getUserProfile from Admin failed",
      error,
      id,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getUserProfile" });
  }
});

app.post("/fetchBlockedProfiles", authUser, async (req, res) => {
  const { profile_id } = req.body;
  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("blocked_lists")
      .select(
        "id, created_at, profiles:blocked_profile_id(id, avatar_url, username, display_name)",
      )
      .eq("profile_id", profile_id)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .not("profiles", "is", null);
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchBlockedProfiles failed",
      error,
      profile_id,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to fetch blocked accounts" });
  }
});

app.post("/fetchFollowingsDetail", async (req, res) => {
  const { profileId, selectedProfileId, cursor } = req.body;

  if (!profileId || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const limit = cursor === "init" ? INIT_FOLLOW_COUNT : FOLLOW_COUNT;

  try {
    let query = supabase
      .from("followers")
      .select(
        "id, following_id, profiles:profiles_with_bots!followers_following_id_fkey(id, display_name, username, avatar_url, visibility, user_id, creator_id, nsfw)",
      )
      .eq("follower_id", profileId)
      .in("profiles.visibility", ["public", "private"])
      .not("profiles", "is", null)
      .or(
        `user_id.not.is.null,visibility.eq.public,creator_id.eq.${selectedProfileId || 0}`,
        {
          foreignTable: "profiles",
        },
      )
      .order("id", { ascending: false })
      .limit(limit);

    if (cursor !== "init") {
      query = query.lt("id", cursor);
    }

    const { data: followingData, error } = await query;

    if (error) {
      const followingError = wrappedSupabaseError(error);
      logError({
        context: "fetchFollowingsDetail - fetch following",
        error: followingError,
      });
      throw followingError;
    }

    if (!followingData) {
      return res
        .status(200)
        .json({ data: [], page_info: { next_cursor: null }, error: null });
    }

    // Fetch additional following details if selectedProfileId is provided
    let isFollowingSet = new Set();
    if (selectedProfileId) {
      const followingIDs = followingData.map(
        (follower) => follower.following_id,
      );

      const { data } = await supabase
        .from("followers")
        .select("following_id")
        .eq("follower_id", selectedProfileId)
        .in("following_id", followingIDs);

      if (data) {
        isFollowingSet = new Set(data.map((d) => d.following_id));
      }
    }

    // Update following data with isFollowing attribute
    const updatedFollowingData = followingData.map((following) => ({
      ...following,
      isfollowing: isFollowingSet.has(following.following_id),
    }));

    // Determine the next cursor for pagination
    const lastFollowing = updatedFollowingData[updatedFollowingData.length - 1];
    const next_cursor =
      updatedFollowingData.length >= limit ? lastFollowing.id : null;

    return res.status(200).json({
      data: updatedFollowingData,
      page_info: { next_cursor },
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchFollowingsDetail failed",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch followings" });
  }
});

app.post("/fetchFollowersDetail", async (req, res) => {
  const { profileId, selectedProfileId, cursor } = req.body;

  if (!profileId || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const limit = cursor === "init" ? INIT_FOLLOW_COUNT : FOLLOW_COUNT;

  try {
    let query = supabase
      .from("followers")
      .select(
        "id, follower_id, profiles:profiles_with_bots!followers_follower_id_fkey(id, display_name, username, user_id, avatar_url, visibility, creator_id, nsfw)",
      )
      .eq("following_id", profileId)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .not("profiles", "is", null)
      .or(
        `user_id.not.is.null,visibility.eq.public,creator_id.eq.${
          selectedProfileId || 0
        }`,
        {
          foreignTable: "profiles",
        },
      )
      .order("id", { ascending: false })
      .limit(limit);

    if (cursor !== "init") {
      query = query.lt("id", cursor);
    }

    const { data: followersData, error } = await query;

    if (error) {
      const followerError = wrappedSupabaseError(error);
      logError({
        context: "fetchFollowersDetail - fetch followers",
        error: followerError,
      });
      throw followerError;
    }

    if (!followersData) {
      return res
        .status(200)
        .json({ data: [], page_info: { next_cursor: null }, error: null });
    }

    // Fetch additional follower details if selectedProfileId is provided
    let isFollowerSet = new Set();
    if (selectedProfileId) {
      const followerIDs = followersData.map((follower) => follower.follower_id);

      const { data } = await supabase
        .from("followers")
        .select("following_id")
        .eq("follower_id", selectedProfileId)
        .in("following_id", followerIDs);

      if (data) {
        isFollowerSet = new Set(data.map((d) => d.following_id));
      }
    }

    // Update follower data with isFollowing attribute
    const updatedFollowersData = followersData.map((following) => ({
      ...following,
      isfollowing: isFollowerSet.has(following.follower_id),
    }));

    // Determine the next cursor for pagination
    const lastFollowing = updatedFollowersData[updatedFollowersData.length - 1];
    const next_cursor =
      updatedFollowersData.length >= limit ? lastFollowing.id : null;

    return res.status(200).json({
      data: updatedFollowersData,
      page_info: { next_cursor },
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchFollowersDetail failed",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch followers" });
  }
});

app.post("/fetchFollowingIds", async (req, res) => {
  const { selectedProfileId, profileIds } = req.body;

  if (!selectedProfileId || !profileIds || profileIds.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data: followData, error } = await supabase
      .from("followers")
      .select("following_id")
      .eq("follower_id", selectedProfileId)
      .in("following_id", profileIds);
    if (error) {
      const followerError = wrappedSupabaseError(error);
      logError({
        context: "fetchFollowingIds",
        error: followerError,
      });
      throw followerError;
    }

    return res.status(200).json({ data: followData, error: null });
  } catch (error) {
    logError({
      context: "fetchFollowingIds failed",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch followingids" });
  }
});

app.post("/getIsFollowing", async (req, res) => {
  const { follower_id, following_id } = req.body;
  if (!follower_id || !following_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("followers")
      .select("id")
      .eq("follower_id", follower_id)
      .eq("following_id", following_id)
      .limit(1);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    const isFollowing = data.length > 0;

    return res.send({ data: { isFollowing }, error: null });
  } catch (error) {
    logError({
      context: "getIsFollowing - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getIsFollowing fetch failed" });
  }
});

app.post("/fetchFollowerProfilesForAdmin", async (req, res) => {
  const { profileId } = req.body;

  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data: followers, error } = await supabase
      .from("followers")
      .select(
        "profiles!followers_follower_id_fkey(id, display_name, username, avatar_url)",
      )
      .eq("following_id", profileId)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .not("profiles", "is", null)
      .order("id", { ascending: false });

    if (error) {
      const followerError = wrappedSupabaseError(error);
      throw followerError;
    }

    if (!followers) {
      return res.status(200).json({ data: [], error: null });
    }

    const followerProfiles = followers?.map((follower) => {
      return follower.profiles;
    });

    return res.status(200).json({
      data: followerProfiles,
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchFollowerProfilesForAdmin failed",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch followers for admin" });
  }
});

app.post("/fetchFollowingProfilesForAdmin", async (req, res) => {
  const { profileId } = req.body;

  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data: followings, error } = await supabase
      .from("followers")
      .select(
        "profiles!followers_follower_id_fkey(id, display_name, username, avatar_url)",
      )
      .eq("follower_id", profileId)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .not("profiles", "is", null)
      .order("id", { ascending: false });

    if (error) {
      const followerError = wrappedSupabaseError(error);
      throw followerError;
    }

    if (!followings) {
      return res.status(200).json({ data: [], error: null });
    }

    const followingProfiles = followings?.map((following) => {
      return following.profiles;
    });

    return res.status(200).json({
      data: followingProfiles,
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchFollowingProfilesForAdmin failed",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch followings for admin" });
  }
});

app.post("/fetchProfileForNewConversation", authUser, async (req, res) => {
  const { slug, selectedProfileId } = req.body;

  if (!slug || !selectedProfileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data: profile, error } = await supabase
      .from("profiles_with_bots")
      .select(
        "id, user_id, avatar_url, username, description, display_name, location, visibility, nsfw, status, creator_id, cyoa_mode, clone_id",
      )
      .neq("visibility", "hidden")
      .neq("visibility", "archived")
      .eq("username", slug) // when create conversation, conversationSlug is username
      .or(
        `visibility.eq.public,creator_id.eq.${selectedProfileId},user_id.not.is.null`,
      );

    if (error) {
      const wrappedError = wrappedSupabaseError(error);
      throw wrappedError;
    }

    if (profile.length === 0) {
      return res.status(404).send({ data: null, error: "Profile not found" });
    }

    return res.status(200).json({
      data: profile[0],
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchProfileForNewConversation failed",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch profile" });
  }
});

app.post("/fetchBotVisibility", async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("visibility")
      .eq("id", profile_id)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "fetchBotVisibility error",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch bot visibility" });
  }
});

app.post("/checkUsernameExisted", async (req, res) => {
  const { username } = req.body;

  if (!username) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("username")
      .ilike("username", username);

    if (error) throw wrappedSupabaseError(error);

    const isUsernameExisted = data?.length ? "existed" : "new";

    return res.status(200).json({ data: isUsernameExisted, error: null });
  } catch (error) {
    logError({
      context: "checkUsernameExisted error",
      error,
    });
    return res.status(500).json({ error: "Failed to check username existed" });
  }
});

app.post("/fetchProfileInEdit", async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select(
        "id, username, display_name, location, description, avatar_url, gender",
      )
      .eq("id", profile_id)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchProfileInEdit error",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch profile in edit" });
  }
});

app.post("/fetchProfileBotInAdmin", authUser, async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isAdminValid = await checkAdminValid(user_id);
  if (!isAdminValid) {
    return res.status(403).json({ error: "forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select(
        "display_name, avatar_url, nsfw, bots!bots_profile_id_fkey(art_style)",
      )
      .eq("id", profile_id)
      .limit(1)
      .single();
    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchProfileBotInAdmin error",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch profile & bot in admin" });
  }
});

app.post("/fetchProfilePostsInAdmin", authUser, async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isAdminValid = await checkAdminValid(user_id);
  if (!isAdminValid) {
    return res.status(403).json({ error: "forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*, posts(*)")
      .eq("id", profile_id)
      .order("created_at", { ascending: false, foreignTable: "posts" })
      .single();
    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchProfilePostsInAdmin error",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch profile with posts in admin" });
  }
});

app.post("/fetchProfileBotDetailInAdmin", authUser, async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }
  const user_id = req.user?.id;
  const isAdminValid = await checkAdminValid(user_id);
  if (!isAdminValid) {
    return res.status(403).json({ error: "forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*,bots!bots_profile_id_fkey(*)")
      .eq("id", profile_id)
      .single();
    if (error) throw wrappedSupabaseError(error);

    return res.status(200).json({ data, error: null });
  } catch (error) {
    logError({
      context: "fetchProfileBotDetailInAdmin error",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch profile & bot in details in admin" });
  }
});

module.exports = {
  app,
};
