const { default: OpenAI } = require("openai");

class SensitiveTopicDetector {
  constructor({ message }) {
    this.message = message;
  }

  async run() {
    // TODO: why is this client without a baseurl??
    const client = new OpenAI({
      timeout: 30 * 1000, // 30 seconds (default is 10 minutes)
    });

    const response = await client.moderations.create({
      input: this.message,
    });

    return response.results[0].flagged;
  }
}

module.exports = {
  SensitiveTopicDetector,
};
