const { getConversationEmbeddings } = require("../../memoryHelper");
const dayjs = require("dayjs");
const { getSafeTimezone } = require("../../timeUtils");
const { LLMServiceBase } = require("./LLMServiceBase");

class TextMessageModelBase extends LLMServiceBase {
  constructor({
    message,
    messages,
    botProfile,
    userProfile,
    botConfiguration,
    conversationConfiguration,
  }) {
    super();
    this.message = message;
    this.messages = messages;
    this.botProfile = botProfile;
    this.userProfile = userProfile;
    this.botConfiguration = botConfiguration;
    this.conversationConfiguration = conversationConfiguration;
  }

  /* OVerride */
  async getMessages() {
    const embeddings = await getConversationEmbeddings({
      bot_profile_id: this.botProfile.id,
      user_profile_id: this.userProfile.id,
      message: this.message,
    });

    let rag_embedding = null;
    if (embeddings && embeddings.length > 0) {
      if (embeddings[0].summary) {
        const createdAt = dayjs(embeddings[0].created_at)
          .tz(getSafeTimezone(this.botProfile.timezone))
          .format("dddd, MMMM D, YYYY HH:mm A");
        rag_embedding = {
          created_at: createdAt,
          summary: embeddings[0].summary,
        };
      }
    }

    const getText = (text) => (text ? text.slice(0, 800) + "\n\n" : "");

    const bioSections = [
      { label: "", value: this.botConfiguration.bio },
      { label: "Background:\n", value: this.botConfiguration.background },
      {
        label: "Characteristics:\n",
        value: this.botConfiguration.characteristics,
      },
      { label: "Personality:\n", value: this.botConfiguration.personality },
      { label: "Description:\n", value: this.botConfiguration.description },
    ];

    const bio = bioSections
      .map(({ label, value }) => (value ? label + getText(value) : ""))
      .join("");

    const conversation = [
      {
        role: "system",
        content: this.generatePrompt({
          character: this.botProfile.display_name,
          bio,
          rag_embedding,
          user: this.userProfile.display_name ?? "User",
          chat_len: this.conversationConfiguration.chat_length ?? "balanced",
        }),
      },
    ];

    addDatabaseMessagesToConversation(this.messages, conversation);

    shrinkConversationSize(conversation, 2000);

    return conversation;
  }

  generatePrompt(/*{ character, bio, rag_embedding: { created_at, summary }, user }*/) {
    throw new Error("Unimplemented method generatePrompt");
  }

  splitMessage(/*content*/) {
    throw new Error("Unimplemented splitMessage");
  }

  /* Override */
  isStreaming() {
    return true;
  }

  /* Override */
  async *parseStreamingResponse(ctx, response) {
    var content = "";
    if (ctx.logging.verbose) {
      ctx.logging.verbose.models[this.constructor.name].chunks = [];
    }
    for await (const chunk of response) {
      if (ctx.logging.verbose) {
        ctx.logging.verbose.models[this.constructor.name].chunks.push(chunk);
      }
      if (chunk.choices.length == 0) continue;
      const delta = chunk.choices[0].delta?.content;
      if (!delta) continue;
      content += delta;
      const parts = this.splitMessage(content);
      if (parts.length <= 1) continue;
      for (var i = 0; i < parts.length - 1; i++) {
        yield parts[i];
      }
      content = parts[parts.length - 1];
    }
    yield content;
  }

  /* Override */
  getTimeout() {
    return 15 * 1000;
  }

  /* Override */
  getStreaming() {
    return true;
  }
}

function addDatabaseMessagesToConversation(messages, conversation) {
  for (const msg of messages.filter((m) => m.body)) {
    let content;
    if (msg.body.includes("Description: ")) {
      content = msg.body.split("Description: ")[1].trim();
      if (!content) continue;
    } else {
      content = msg.body.trim();
      if (!content || /^Sent an image by\b/.test(content)) continue;
    }
    if (msg.is_bot) {
      if (
        content.includes("can't fulfill that request") ||
        content.includes("cannot create explicit content")
      ) {
        continue;
      }
      const terminatedMsg = /[.!?。！？…*]$/.test(content)
        ? content
        : content + ".";
      if (
        conversation.length > 0 &&
        conversation[conversation.length - 1].role == "assistant"
      ) {
        conversation[conversation.length - 1].content += " " + terminatedMsg;
      } else {
        conversation.push({
          role: "assistant",
          content: terminatedMsg,
        });
      }
    } else {
      conversation.push({
        role: "user",
        content: content.includes("/imagine")
          ? `Requesting you send a photo of ${content.split("/imagine")[1].trim()}.`
          : content,
      });
    }
  }
}

function shrinkConversationSize(conversation, maxTokens) {
  while (
    conversation.length > 2 &&
    conversation.reduce((c, msg) => c + msg.content.length, 0) > 4 * maxTokens
  ) {
    conversation.splice(1, 1);
  }
}

module.exports = {
  TextMessageModelBase,
};
