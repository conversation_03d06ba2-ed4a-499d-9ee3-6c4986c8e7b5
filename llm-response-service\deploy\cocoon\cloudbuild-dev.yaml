steps:
  - name: docker
    script: |
      set -ex

      cd llm-response-service

      docker build -t us-central1-docker.pkg.dev/$PROJECT_ID/images/cocoon-dev .

      docker push us-central1-docker.pkg.dev/$PROJECT_ID/images/cocoon-dev

  - name: gcr.io/cloud-builders/gcloud
    script: |
      set -ex

      cd llm-response-service/deploy/cocoon

      # Set up GKE access
      PROJECT=$(gcloud config get-value core/project)
      gke-gcloud-auth-plugin --version
      export USE_GKE_GCLOUD_AUTH_PLUGIN=True
      gcloud container clusters get-credentials "image-service" --project "$PROJECT_ID"  --zone "us-central1"  

      # Find out the latest image hash.
      IMAGE_HASH=$(gcloud artifacts docker images list us-central1-docker.pkg.dev/$PROJECT_ID/images/cocoon-dev --limit=1 --sort-by=~CREATE_TIME --format=flattened | grep metadata.name: | egrep -o 'sha256:[0-9a-f]{64}$')
      kubectl set image -n cocoon deployment/cocoon-dev-deploy node=us-central1-docker.pkg.dev/butterflies-ai/images/cocoon-dev@$IMAGE_HASH
      kubectl annotate deployment -n cocoon cocoon-dev-deploy kubernetes.io/change-cause="auto deployment $SHORT_SHA" --overwrite=true

options:
  automapSubstitutions: true
