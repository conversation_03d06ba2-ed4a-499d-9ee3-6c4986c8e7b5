{"last_node_id": 181, "last_link_id": 377, "nodes": [{"id": 47, "type": "CLIPSetLastLayer", "pos": [12, 251], "size": {"0": 315, "1": 58}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 113}], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [126], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPSetLastLayer"}, "widgets_values": [-2]}, {"id": 54, "type": "CR LoRA Stack", "pos": [15, 472], "size": {"0": 315, "1": 342}, "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "lora_stack", "type": "LORA_STACK", "link": null}], "outputs": [{"name": "LORA_STACK", "type": "LORA_STACK", "links": [125], "shape": 3, "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR LoRA Stack"}, "widgets_values": ["Off", "None", 1, 1, "Off", "None", 1, 1, "Off", "None", 1, 1]}, {"id": 65, "type": "MaskPreview+", "pos": [1546, 1266], "size": {"0": 210, "1": 246}, "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 144}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#323", "bgcolor": "#535"}, {"id": 63, "type": "MaskPreview+", "pos": [1550, 894], "size": {"0": 210, "1": 246}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 142}], "properties": {"Node name for S&R": "MaskPreview+"}}, {"id": 58, "type": "CR Color Bars", "pos": [837, 869], "size": {"0": 315, "1": 294}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 138, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 139, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [141], "shape": 3, "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Color Bars"}, "widgets_values": ["2-color", 512, 512, "black", "white", "vertical", 2, 0, "#000000", "#000000"]}, {"id": 89, "type": "UltralyticsDetectorProvider", "pos": [3704, 98], "size": {"0": 315, "1": 78}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [219], "shape": 3, "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 98, "type": "SAMLoader", "pos": [3713, 218], "size": {"0": 303.04522705078125, "1": 82}, "flags": {"collapsed": false}, "order": 2, "mode": 0, "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [220], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"], "color": "#323"}, {"id": 97, "type": "ImpactSimpleDetectorSEGS", "pos": [3723, 387], "size": {"0": 280, "1": 310}, "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 219, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 218}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 220, "slot_index": 2}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [221, 228], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 50, 0.5, 0, 0, 0.7, 0], "color": "#323"}, {"id": 95, "type": "SEGSToImageList", "pos": [4269, 880], "size": {"0": 203.1999969482422, "1": 46}, "flags": {"collapsed": false}, "order": 71, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 222}, {"name": "fallback_image_opt", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [217], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "SEGSToImageList"}, "color": "#323", "bgcolor": "#535"}, {"id": 93, "type": "SEGSToImageList", "pos": [4275, 498], "size": {"0": 203.1999969482422, "1": 46}, "flags": {"collapsed": false}, "order": 68, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 214}, {"name": "fallback_image_opt", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [215], "shape": 6, "slot_index": 0}], "properties": {"Node name for S&R": "SEGSToImageList"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 103, "type": "MaskPreview+", "pos": [4562, 118], "size": {"0": 210, "1": 246}, "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 227}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 102, "type": "MaskPreview+", "pos": [4541, 1260], "size": {"0": 210, "1": 246}, "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 226}], "properties": {"Node name for S&R": "MaskPreview+"}, "color": "#323", "bgcolor": "#535"}, {"id": 110, "type": "PreviewImage", "pos": [1839, 345], "size": {"0": 210, "1": 246}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 250}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 111, "type": "PreviewImage", "pos": [1822, 1221], "size": {"0": 210, "1": 246}, "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 251}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#323", "bgcolor": "#535"}, {"id": 52, "type": "CLIPTextEncodeSDXL", "pos": [1346.8000244140626, 126], "size": {"0": 400, "1": 270}, "flags": {"collapsed": false}, "order": 54, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 127}, {"name": "text_g", "type": "STRING", "link": 260, "widget": {"name": "text_g"}}, {"name": "text_l", "type": "STRING", "link": 261, "widget": {"name": "text_l"}}, {"name": "width", "type": "INT", "link": 133, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 136, "widget": {"name": "height"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [145], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 4096, 4096, "CLIP_G", "CLIP_L"], "color": "#232", "bgcolor": "#353"}, {"id": 118, "type": "CLIPTextEncodeSDXL", "pos": [7902.5139806511215, 259.2838510098602], "size": {"0": 400, "1": 270}, "flags": {"collapsed": false}, "order": 56, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 262}, {"name": "text_g", "type": "STRING", "link": 272, "widget": {"name": "text_g"}}, {"name": "text_l", "type": "STRING", "link": 273, "widget": {"name": "text_l"}}, {"name": "width", "type": "INT", "link": 265, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 266, "widget": {"name": "height"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [275], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 4096, 4096, "CLIP_G", "CLIP_L"], "color": "#332922", "bgcolor": "#593930"}, {"id": 119, "type": "CLIPTextEncodeSDXL", "pos": [7867.5139806511215, 908.2838510098601], "size": {"0": 400, "1": 270}, "flags": {"collapsed": false}, "order": 57, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 267}, {"name": "text_g", "type": "STRING", "link": 282, "widget": {"name": "text_g"}}, {"name": "text_l", "type": "STRING", "link": 283, "widget": {"name": "text_l"}}, {"name": "width", "type": "INT", "link": 270, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 271, "widget": {"name": "height"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [284], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 4096, 4096, "CLIP_G", "CLIP_L"], "color": "#323", "bgcolor": "#535"}, {"id": 53, "type": "CLIPTextEncodeSDXL", "pos": [1354.8000244140626, 464], "size": {"0": 400, "1": 270}, "flags": {"collapsed": false}, "order": 55, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 128}, {"name": "text_g", "type": "STRING", "link": 131, "widget": {"name": "text_g"}}, {"name": "text_l", "type": "STRING", "link": 132, "widget": {"name": "text_l"}}, {"name": "width", "type": "INT", "link": 134, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 135, "widget": {"name": "height"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [146, 274, 285], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncodeSDXL"}, "widgets_values": [1024, 1024, 0, 0, 4096, 4096, "CLIP_G", "CLIP_L"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "VAELoader", "pos": [12, 361], "size": {"0": 306.5513610839844, "1": 58}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [120, 276, 286], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl_vae.safetensors"]}, {"id": 120, "type": "PreviewImage", "pos": [8846.51398065111, 161.28385100986026], "size": {"0": 491.10498046875, "1": 582.0070190429688}, "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 291}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 121, "type": "PreviewImage", "pos": [8827.51398065111, 918.2838510098601], "size": {"0": 491.10498046875, "1": 582.0070190429688}, "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 293}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#323", "bgcolor": "#535"}, {"id": 122, "type": "ImageUpscaleWithModel", "pos": [9487.876120667874, 197.65608853748626], "size": {"0": 241.79998779296875, "1": 46}, "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "upscale_model", "type": "UPSCALE_MODEL", "link": 294, "slot_index": 0}, {"name": "image", "type": "IMAGE", "link": 295}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [296], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageUpscaleWithModel"}}, {"id": 124, "type": "ImageScaleBy", "pos": [9791.876120667874, 92.6560885374862], "size": {"0": 315, "1": 82}, "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 296}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [297], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.5]}, {"id": 46, "type": "SaveImage", "pos": [9406.876120667874, 293.65608853748637], "size": {"0": 1209.6844482421875, "1": 1222.8536376953125}, "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 297}], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 57, "type": "CR SDXL Aspect Ratio", "pos": [15, 874], "size": {"0": 315, "1": 278}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "width", "type": "INT", "links": [133, 134, 138, 265, 270], "shape": 3, "slot_index": 0}, {"name": "height", "type": "INT", "links": [135, 136, 139, 266, 271], "shape": 3, "slot_index": 1}, {"name": "upscale_factor", "type": "FLOAT", "links": null, "shape": 3}, {"name": "batch_size", "type": "INT", "links": null, "shape": 3}, {"name": "empty_latent", "type": "LATENT", "links": [137], "shape": 3, "slot_index": 4}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR SDXL Aspect Ratio"}, "widgets_values": [1024, 1024, "16:9 landscape 1344x768", "Off", 1, 1], "color": "#ffffff"}, {"id": 101, "type": "SegsToCombinedMask", "pos": [4319, 83], "size": {"0": 210, "1": 26}, "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 223}], "outputs": [{"name": "MASK", "type": "MASK", "links": [227], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 100, "type": "SegsToCombinedMask", "pos": [4269, 1309], "size": {"0": 210, "1": 26}, "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 224}], "outputs": [{"name": "MASK", "type": "MASK", "links": [226], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "color": "#323", "bgcolor": "#535"}, {"id": 147, "type": "Note", "pos": [-1350, 150], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 5, "mode": 0, "properties": {"text": ""}, "widgets_values": [". cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 148, "type": "Note", "pos": [-740, 150], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 6, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 146, "type": "Note", "pos": [-1560, 340], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 7, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 145, "type": "Note", "pos": [-1350, 340], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 8, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 144, "type": "Note", "pos": [-1140, 340], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 9, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 143, "type": "Note", "pos": [-940, 340], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 10, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 142, "type": "Note", "pos": [-740, 340], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 11, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 141, "type": "Note", "pos": [-530, 340], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 12, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 140, "type": "Note", "pos": [-330, 530], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 13, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 139, "type": "Note", "pos": [-740, 530], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 14, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 138, "type": "Note", "pos": [-940, 530], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 15, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 137, "type": "Note", "pos": [-1140, 530], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 16, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 136, "type": "Note", "pos": [-1350, 530], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 17, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 135, "type": "Note", "pos": [-1770, 530], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 18, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff8714", "bgcolor": "#ff7300"}, {"id": 127, "type": "Note", "pos": [-1560, 720], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 19, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 128, "type": "Note", "pos": [-1350, 720], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 20, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 131, "type": "Note", "pos": [-740, 720], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 21, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 132, "type": "Note", "pos": [-530, 720], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 22, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 133, "type": "Note", "pos": [-330, 920], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 23, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 134, "type": "Note", "pos": [-540, 1090], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 24, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 130, "type": "Note", "pos": [-940, 920], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 25, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 129, "type": "Note", "pos": [-1140, 920], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 26, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 126, "type": "Note", "pos": [-1560, 1090], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 27, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 125, "type": "Note", "pos": [-1770, 890], "size": {"0": 210, "1": 158.2581329345703}, "flags": {}, "order": 28, "mode": 0, "properties": {"text": ""}, "widgets_values": [".. cerdits:\n<PERSON>\nhttps://www.linkedin.com/feed/update/urn:li:activity:7142596509602086912/\n\nhttps://civitai.com/user/denrakeiw\n\nhttps://openart.ai/workflows/profile/denrakeiw?tab=workflows&sort=latest"], "color": "#ff1d1d", "bgcolor": "#fb0909"}, {"id": 116, "type": "ttN text3BOX_3WAYconcat", "pos": [855, 415], "size": {"0": 400, "1": 320}, "flags": {"collapsed": false}, "order": 45, "mode": 0, "inputs": [{"name": "text1", "type": "STRING", "link": 256, "widget": {"name": "text1"}}, {"name": "text2", "type": "STRING", "link": 257, "widget": {"name": "text2"}}, {"name": "text3", "type": "STRING", "link": 259, "widget": {"name": "text3"}}], "outputs": [{"name": "text1", "type": "STRING", "links": null, "shape": 3}, {"name": "text2", "type": "STRING", "links": null, "shape": 3}, {"name": "text3", "type": "STRING", "links": null, "shape": 3}, {"name": "1 & 2", "type": "STRING", "links": null, "shape": 3}, {"name": "1 & 3", "type": "STRING", "links": [272, 273], "shape": 3, "slot_index": 4}, {"name": "2 & 3", "type": "STRING", "links": [282, 283], "shape": 3, "slot_index": 5}, {"name": "concat", "type": "STRING", "links": [260, 261], "shape": 3, "slot_index": 6}], "properties": {"Node name for S&R": "ttN text3BOX_3WAYconcat", "ttNnodeVersion": "1.0.0"}, "widgets_values": ["", "", "", ","]}, {"id": 152, "type": "ImpactDecomposeSEGS", "pos": [4933.225441650391, 117.54936041259765], "size": {"0": 210, "1": 46}, "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 305}], "outputs": [{"name": "SEGS_HEADER", "type": "SEGS_HEADER", "links": [302], "shape": 3}, {"name": "SEG_ELT", "type": "SEG_ELT", "links": [303], "shape": 6, "slot_index": 1}], "properties": {"Node name for S&R": "ImpactDecomposeSEGS"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 155, "type": "ReActorFaceSwap", "pos": [5538.225441650391, 207.54936041259765], "size": {"0": 315, "1": 338}, "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 307}, {"name": "source_image", "type": "IMAGE", "link": 306}, {"name": "face_model", "type": "FACE_MODEL", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [308, 309], "shape": 3, "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ReActorFaceSwap"}, "widgets_values": [true, "inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", 1, 0.5, "no", "no", "0", "0", 1], "color": "#332922", "bgcolor": "#593930"}, {"id": 154, "type": "ImpactEdit_SEG_ELT", "pos": [5886.225441650391, 187.54936041259765], "size": {"0": 393, "1": 182}, "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 310}, {"name": "cropped_image_opt", "type": "IMAGE", "link": 309}, {"name": "cropped_mask_opt", "type": "MASK", "link": 311}, {"name": "crop_region_opt", "type": "SEG_ELT_crop_region", "link": 312}, {"name": "bbox_opt", "type": "SEG_ELT_bbox", "link": 313}, {"name": "control_net_wrapper_opt", "type": "SEG_ELT_control_net_wrapper", "link": 314}, {"name": "confidence_opt", "type": "FLOAT", "link": 315, "widget": {"name": "confidence_opt"}}, {"name": "label_opt", "type": "STRING", "link": 316, "widget": {"name": "label_opt"}}], "outputs": [{"name": "SEG_ELT", "type": "SEG_ELT", "links": [304], "shape": 3}], "properties": {"Node name for S&R": "ImpactEdit_SEG_ELT"}, "widgets_values": [0, ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 153, "type": "ImpactFrom_SEG_ELT", "pos": [5174.225441650391, 186.54936041259765], "size": {"0": 342.5999755859375, "1": 166}, "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 303}], "outputs": [{"name": "seg_elt", "type": "SEG_ELT", "links": [310], "shape": 3, "slot_index": 0}, {"name": "cropped_image", "type": "IMAGE", "links": [307], "shape": 3, "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [311], "shape": 3, "slot_index": 2}, {"name": "crop_region", "type": "SEG_ELT_crop_region", "links": [312], "shape": 3, "slot_index": 3}, {"name": "bbox", "type": "SEG_ELT_bbox", "links": [313], "shape": 3, "slot_index": 4}, {"name": "control_net_wrapper", "type": "SEG_ELT_control_net_wrapper", "links": [314], "shape": 3, "slot_index": 5}, {"name": "confidence", "type": "FLOAT", "links": [315], "shape": 3, "slot_index": 6}, {"name": "label", "type": "STRING", "links": [316], "shape": 3, "slot_index": 7}], "properties": {"Node name for S&R": "ImpactFrom_SEG_ELT"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 156, "type": "PreviewImage", "pos": [5889.225441650391, 416.5493604125977], "size": {"0": 210, "1": 246}, "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 308}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 160, "type": "ImpactEdit_SEG_ELT", "pos": [5868.225441650391, 993.5493604125977], "size": {"0": 393, "1": 182}, "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 320}, {"name": "cropped_image_opt", "type": "IMAGE", "link": 321}, {"name": "cropped_mask_opt", "type": "MASK", "link": 322}, {"name": "crop_region_opt", "type": "SEG_ELT_crop_region", "link": 323}, {"name": "bbox_opt", "type": "SEG_ELT_bbox", "link": 324}, {"name": "control_net_wrapper_opt", "type": "SEG_ELT_control_net_wrapper", "link": 325}, {"name": "confidence_opt", "type": "FLOAT", "link": 326, "widget": {"name": "confidence_opt"}}, {"name": "label_opt", "type": "STRING", "link": 327, "widget": {"name": "label_opt"}}], "outputs": [{"name": "SEG_ELT", "type": "SEG_ELT", "links": [318], "shape": 3}], "properties": {"Node name for S&R": "ImpactEdit_SEG_ELT"}, "widgets_values": [0, ""], "color": "#323", "bgcolor": "#535"}, {"id": 161, "type": "ReActorFaceSwap", "pos": [5518.225441650391, 1013.5493604125977], "size": {"0": 315, "1": 338}, "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 328}, {"name": "source_image", "type": "IMAGE", "link": 331}, {"name": "face_model", "type": "FACE_MODEL", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [321, 329], "shape": 3, "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ReActorFaceSwap"}, "widgets_values": [true, "inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", 1, 0.5, "no", "no", "0", "0", 1], "color": "#323", "bgcolor": "#535"}, {"id": 159, "type": "ImpactFrom_SEG_ELT", "pos": [5158.225441650391, 993.5493604125977], "size": {"0": 342.5999755859375, "1": 166}, "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "seg_elt", "type": "SEG_ELT", "link": 319}], "outputs": [{"name": "seg_elt", "type": "SEG_ELT", "links": [320], "shape": 3, "slot_index": 0}, {"name": "cropped_image", "type": "IMAGE", "links": [328], "shape": 3, "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [322], "shape": 3, "slot_index": 2}, {"name": "crop_region", "type": "SEG_ELT_crop_region", "links": [323], "shape": 3, "slot_index": 3}, {"name": "bbox", "type": "SEG_ELT_bbox", "links": [324], "shape": 3, "slot_index": 4}, {"name": "control_net_wrapper", "type": "SEG_ELT_control_net_wrapper", "links": [325], "shape": 3, "slot_index": 5}, {"name": "confidence", "type": "FLOAT", "links": [326], "shape": 3, "slot_index": 6}, {"name": "label", "type": "STRING", "links": [327], "shape": 3, "slot_index": 7}], "properties": {"Node name for S&R": "ImpactFrom_SEG_ELT"}, "color": "#323", "bgcolor": "#535"}, {"id": 158, "type": "ImpactDecomposeSEGS", "pos": [4910.225441650391, 942.5493604125977], "size": {"0": 210, "1": 46}, "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 330}], "outputs": [{"name": "SEGS_HEADER", "type": "SEGS_HEADER", "links": [317], "shape": 3}, {"name": "SEG_ELT", "type": "SEG_ELT", "links": [319], "shape": 6, "slot_index": 1}], "properties": {"Node name for S&R": "ImpactDecomposeSEGS"}, "color": "#323", "bgcolor": "#535"}, {"id": 151, "type": "ImpactAssembleSEGS", "pos": [6356.225441650391, 146.54936041259765], "size": {"0": 210, "1": 46}, "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "seg_header", "type": "SEGS_HEADER", "link": 302, "slot_index": 0}, {"name": "seg_elt", "type": "SEG_ELT", "link": 304, "slot_index": 1}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [334], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactAssembleSEGS"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 164, "type": "MiDaS_DepthMap_Preprocessor_Provider_for_SEGS //Inspire", "pos": [7535.13508360487, 724.8512896345727], "size": {"0": 369.6000061035156, "1": 82}, "flags": {}, "order": 29, "mode": 0, "outputs": [{"name": "SEGS_PREPROCESSOR", "type": "SEGS_PREPROCESSOR", "links": [335], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MiDaS_DepthMap_Preprocessor_Provider_for_SEGS //Inspire"}, "widgets_values": [6.283185307179586, 0.1], "color": "#332922", "bgcolor": "#593930"}, {"id": 165, "type": "ImpactControlNetApplySEGS", "pos": [7937.13508360487, 594.8512896345727], "size": {"0": 315, "1": 118}, "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 334}, {"name": "control_net", "type": "CONTROL_NET", "link": 336, "slot_index": 1}, {"name": "segs_preprocessor", "type": "SEGS_PREPROCESSOR", "link": 335}, {"name": "control_image", "type": "IMAGE", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [337], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactControlNetApplySEGS"}, "widgets_values": [0.6], "color": "#332922", "bgcolor": "#593930"}, {"id": 168, "type": "ImpactControlNetApplySEGS", "pos": [7928.13508360487, 1271.8512896345726], "size": {"0": 315, "1": 118}, "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 341}, {"name": "control_net", "type": "CONTROL_NET", "link": 342, "slot_index": 1}, {"name": "segs_preprocessor", "type": "SEGS_PREPROCESSOR", "link": 339}, {"name": "control_image", "type": "IMAGE", "link": null}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [340], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactControlNetApplySEGS"}, "widgets_values": [0.6], "color": "#323", "bgcolor": "#535"}, {"id": 157, "type": "ImpactAssembleSEGS", "pos": [6390, 955], "size": {"0": 210, "1": 46}, "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "seg_header", "type": "SEGS_HEADER", "link": 317, "slot_index": 0}, {"name": "seg_elt", "type": "SEG_ELT", "link": 318, "slot_index": 1}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [341], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImpactAssembleSEGS"}, "color": "#323", "bgcolor": "#535"}, {"id": 96, "type": "PreviewImage", "pos": [4544, 883], "size": {"0": 210, "1": 246}, "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 217}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#323", "bgcolor": "#535"}, {"id": 162, "type": "PreviewImage", "pos": [5877, 1226], "size": {"0": 210, "1": 246}, "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 329}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#323", "bgcolor": "#535"}, {"id": 94, "type": "PreviewImage", "pos": [4561, 500], "size": {"0": 210, "1": 246}, "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 215}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#332922", "bgcolor": "#593930"}, {"id": 166, "type": "ControlNetLoader", "pos": [7537.13508360487, 609.8512896345727], "size": {"0": 315, "1": 58}, "flags": {}, "order": 30, "mode": 0, "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [336, 342], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["control-lora-depth-rank256.safetensors"], "color": "#332922", "bgcolor": "#593930"}, {"id": 167, "type": "MiDaS_DepthMap_Preprocessor_Provider_for_SEGS //Inspire", "pos": [7542, 1314], "size": {"0": 369.6000061035156, "1": 82}, "flags": {}, "order": 31, "mode": 0, "outputs": [{"name": "SEGS_PREPROCESSOR", "type": "SEGS_PREPROCESSOR", "links": [339], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MiDaS_DepthMap_Preprocessor_Provider_for_SEGS //Inspire"}, "widgets_values": [6.283185307179586, 0.1], "color": "#323", "bgcolor": "#535"}, {"id": 43, "type": "PreviewImage", "pos": [3115, 194], "size": {"0": 524.0672607421875, "1": 570.2116088867188}, "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 121}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 10, "type": "LoadImage", "pos": [404, 74], "size": {"0": 336.8447570800781, "1": 361.460205078125}, "flags": {}, "order": 32, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [193], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "title": "Load Image Person 1", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["OIP (61).jpg", "image"], "color": "#332922", "bgcolor": "#593930"}, {"id": 56, "type": "LoadImage", "pos": [400, 729], "size": {"0": 336.8447570800781, "1": 361.460205078125}, "flags": {"collapsed": false}, "order": 33, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [194], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "title": "Load Image Person 2", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["OIP (62).jpg", "image"], "color": "#323", "bgcolor": "#535"}, {"id": 48, "type": "KSampler (Efficient)", "pos": [2761, 200], "size": {"0": 325, "1": 562}, "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 364}, {"name": "positive", "type": "CONDITIONING", "link": 145}, {"name": "negative", "type": "CONDITIONING", "link": 146}, {"name": "latent_image", "type": "LATENT", "link": 137}, {"name": "optional_vae", "type": "VAE", "link": 120}, {"name": "script", "type": "SCRIPT", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": null, "shape": 3}, {"name": "CONDITIONING+", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "CONDITIONING-", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "LATENT", "type": "LATENT", "links": null, "shape": 3}, {"name": "VAE", "type": "VAE", "links": null, "shape": 3}, {"name": "IMAGE", "type": "IMAGE", "links": [121, 218, 280], "shape": 3, "slot_index": 5}], "properties": {"Node name for S&R": "KSampler (Efficient)"}, "widgets_values": [1670641162, null, 25, 7, "dpmpp_2m", "karras", 1, "auto", "true"], "color": "#332222", "bgcolor": "#553333", "shape": 1}, {"id": 55, "type": "CR Apply LoRA Stack", "pos": [414, 1437], "size": {"0": 254.40000915527344, "1": 66}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 165}, {"name": "clip", "type": "CLIP", "link": 126}, {"name": "lora_stack", "type": "LORA_STACK", "link": 125}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [354, 359], "shape": 3, "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [127, 128, 262, 267, 278, 287], "shape": 3, "slot_index": 1}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Apply LoRA Stack"}}, {"id": 64, "type": "InvertMask", "pos": [1222, 1274], "size": {"0": 210, "1": 26}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 143}], "outputs": [{"name": "MASK", "type": "MASK", "links": [144, 360], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "InvertMask"}, "color": "#323", "bgcolor": "#535"}, {"id": 61, "type": "Image To Mask", "pos": [1192, 868], "size": {"0": 315, "1": 58}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 141}], "outputs": [{"name": "MASK", "type": "MASK", "links": [142, 143, 361], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Image To Mask"}, "widgets_values": ["intensity"]}, {"id": 176, "type": "IPAdapterUnifiedLoaderFaceID", "pos": [1881, 690], "size": {"0": 315, "1": 126}, "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 359}, {"name": "ipadapter", "type": "IPADAPTER", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [362], "shape": 3, "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [355, 367], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoaderFaceID"}, "widgets_values": ["FACEID", 0.6, "CPU"]}, {"id": 180, "type": "IPAdapterInsightFaceLoader", "pos": [1885, 912], "size": {"0": 315, "1": 58}, "flags": {}, "order": 34, "mode": 0, "outputs": [{"name": "INSIGHTFACE", "type": "INSIGHTFACE", "links": [372, 373], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterInsightFaceLoader"}, "widgets_values": ["CPU"]}, {"id": 83, "type": "PrepImageForClipVision", "pos": [1813, 1063], "size": {"0": 315, "1": 106}, "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 194}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [251, 331, 366, 374], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "top", 0], "color": "#323", "bgcolor": "#535"}, {"id": 82, "type": "PrepImageForClipVision", "pos": [1845, 178], "size": {"0": 315, "1": 106}, "flags": {"collapsed": false}, "order": 42, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 193}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [250, 306, 365, 375], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "PrepImageForClipVision"}, "widgets_values": ["LANCZOS", "top", 0], "color": "#332922", "bgcolor": "#593930"}, {"id": 123, "type": "UpscaleModelLoader", "pos": [9407.876120667874, 91.6560885374862], "size": {"0": 315, "1": 58}, "flags": {}, "order": 35, "mode": 0, "outputs": [{"name": "UPSCALE_MODEL", "type": "UPSCALE_MODEL", "links": [294], "shape": 3}], "properties": {"Node name for S&R": "UpscaleModelLoader"}, "widgets_values": ["2xESRGAN.pth"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [16, 96], "size": {"0": 290, "1": 100}, "flags": {}, "order": 36, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [165], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [113], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["SDXL\\juggernautXL_v7Rundiffusion.safetensors"]}, {"id": 178, "type": "IPAdapterAdvanced", "pos": [7162, 207], "size": {"0": 315, "1": 278}, "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 368}, {"name": "ipadapter", "type": "IPADAPTER", "link": 370}, {"name": "image", "type": "IMAGE", "link": 375}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": null}, {"name": "clip_vision", "type": "CLIP_VISION", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [376], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [0.75, "linear", "concat", 0, 1, "V only"], "color": "#332922", "bgcolor": "#593930"}, {"id": 179, "type": "IPAdapterAdvanced", "pos": [7157, 928], "size": {"0": 315, "1": 278}, "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 369}, {"name": "ipadapter", "type": "IPADAPTER", "link": 371}, {"name": "image", "type": "IMAGE", "link": 374}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": null}, {"name": "clip_vision", "type": "CLIP_VISION", "link": null}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [377], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [0.75, "linear", "concat", 0, 1, "V only"], "color": "#323", "bgcolor": "#535"}, {"id": 173, "type": "IPAdapterUnifiedLoader", "pos": [6732, 704], "size": {"0": 315, "1": 78}, "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 354}, {"name": "ipadapter", "type": "IPADAPTER", "link": null}], "outputs": [{"name": "model", "type": "MODEL", "links": [368, 369], "shape": 3, "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [370, 371], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoader"}, "widgets_values": ["PLUS FACE (portraits)"]}, {"id": 86, "type": "Detailer<PERSON>or<PERSON>ach", "pos": [8337.513980651118, 918.2838510098601], "size": {"0": 400, "1": 600}, "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 289}, {"name": "segs", "type": "SEGS", "link": 340}, {"name": "model", "type": "MODEL", "link": 377}, {"name": "clip", "type": "CLIP", "link": 287}, {"name": "vae", "type": "VAE", "link": 286}, {"name": "positive", "type": "CONDITIONING", "link": 284}, {"name": "negative", "type": "CONDITIONING", "link": 285}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [293, 295], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Detailer<PERSON>or<PERSON>ach"}, "widgets_values": [256, true, 768, 1670641162, "fixed", 22, 3.5, "dpmpp_2m", "karras", 0.33, 5, true, true, "", 1, false, 20], "color": "#323", "bgcolor": "#535"}, {"id": 87, "type": "Detailer<PERSON>or<PERSON>ach", "pos": [8367.513980651116, 159.28385100986026], "size": {"0": 400, "1": 600}, "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 280}, {"name": "segs", "type": "SEGS", "link": 337}, {"name": "model", "type": "MODEL", "link": 376}, {"name": "clip", "type": "CLIP", "link": 278}, {"name": "vae", "type": "VAE", "link": 276}, {"name": "positive", "type": "CONDITIONING", "link": 275}, {"name": "negative", "type": "CONDITIONING", "link": 274}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [289, 291], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Detailer<PERSON>or<PERSON>ach"}, "widgets_values": [256, true, 768, 1670641162, "fixed", 22, 3.5, "dpmpp_2m", "karras", 0.33, 5, true, true, "", 1, false, 0], "color": "#332922", "bgcolor": "#593930"}, {"id": 115, "type": "Text _O", "pos": [387, 484], "size": {"0": 400, "1": 200}, "flags": {}, "order": 37, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [256], "shape": 3, "slot_index": 0}], "title": "Prompt_Person_2", "properties": {"Node name for S&R": "Text _O"}, "widgets_values": ["one person, a man, chuck norris"], "color": "#332922", "bgcolor": "#593930"}, {"id": 51, "type": "SDXLPromptStylerbyMileHigh", "pos": [836, 118], "size": {"0": 422.3548889160156, "1": 229.5388641357422}, "flags": {}, "order": 38, "mode": 0, "outputs": [{"name": "positive_prompt_text_g", "type": "STRING", "links": [259], "shape": 3, "slot_index": 0}, {"name": "negative_prompt_text_g", "type": "STRING", "links": [131, 132], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "SDXLPromptStylerbyMileHigh"}, "widgets_values": ["(2person:1.15), at a  alien movie,alien planet, explosion, hollywood 60s, \n\nhigh quality, diffuse light, highly detailed, 4k", "(looking at the viewer:1.3), blurry, malformed, distorted, naked", "Sci-Fi Films", "No"], "color": "#223", "bgcolor": "#335"}, {"id": 175, "type": "IPAdapterFaceID", "pos": [2285, 245], "size": {"0": 315, "1": 322}, "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 362}, {"name": "ipadapter", "type": "IPADAPTER", "link": 355, "slot_index": 1}, {"name": "image", "type": "IMAGE", "link": 365}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": 361}, {"name": "clip_vision", "type": "CLIP_VISION", "link": null}, {"name": "insightface", "type": "INSIGHTFACE", "link": 372}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [363], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterFaceID"}, "widgets_values": [0.6, 1, "linear", "concat", 0, 1, "V only"], "color": "#332922", "bgcolor": "#593930"}, {"id": 177, "type": "IPAdapterFaceID", "pos": [2262, 1082], "size": {"0": 315, "1": 322}, "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 363}, {"name": "ipadapter", "type": "IPADAPTER", "link": 367, "slot_index": 1}, {"name": "image", "type": "IMAGE", "link": 366}, {"name": "image_negative", "type": "IMAGE", "link": null}, {"name": "attn_mask", "type": "MASK", "link": 360}, {"name": "clip_vision", "type": "CLIP_VISION", "link": null}, {"name": "insightface", "type": "INSIGHTFACE", "link": 373, "slot_index": 6}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [364], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterFaceID"}, "widgets_values": [0.6, 1, "linear", "concat", 0, 1, "V only"], "color": "#323", "bgcolor": "#535"}, {"id": 114, "type": "Text _O", "pos": [379, 1156], "size": {"0": 400, "1": 200}, "flags": {}, "order": 39, "mode": 0, "outputs": [{"name": "STRING", "type": "STRING", "links": [257], "shape": 3, "slot_index": 0}], "title": "Prompt_Person_2", "properties": {"Node name for S&R": "Text _O"}, "widgets_values": ["one person, (a woman, emma whatson:1.15), spacesuite, red hair,"], "color": "#323", "bgcolor": "#535"}, {"id": 181, "type": "GlobalSeed //Inspire", "pos": [20, 1210], "size": {"0": 315, "1": 130}, "flags": {}, "order": 40, "mode": 0, "properties": {"Node name for S&R": "GlobalSeed //Inspire"}, "widgets_values": [1670641162, true, "fixed", 1670641162]}, {"id": 91, "type": "ImpactSEGSOrderedFilter", "pos": [4093, 202], "size": {"0": 210, "1": 150}, "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 221}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [214, 223, 305], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", false, 1, 1], "color": "#332922", "bgcolor": "#593930"}, {"id": 99, "type": "ImpactSEGSOrderedFilter", "pos": [4018, 1311], "size": {"0": 210, "1": 150}, "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 228}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [222, 224, 330], "shape": 3, "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["x1", false, 0, 1], "color": "#323", "bgcolor": "#535"}], "links": [[113, 2, 1, 47, 0, "CLIP"], [120, 42, 0, 48, 4, "VAE"], [121, 48, 5, 43, 0, "IMAGE"], [125, 54, 0, 55, 2, "LORA_STACK"], [126, 47, 0, 55, 1, "CLIP"], [127, 55, 1, 52, 0, "CLIP"], [128, 55, 1, 53, 0, "CLIP"], [131, 51, 1, 53, 1, "STRING"], [132, 51, 1, 53, 2, "STRING"], [133, 57, 0, 52, 3, "INT"], [134, 57, 0, 53, 3, "INT"], [135, 57, 1, 53, 4, "INT"], [136, 57, 1, 52, 4, "INT"], [137, 57, 4, 48, 3, "LATENT"], [138, 57, 0, 58, 0, "INT"], [139, 57, 1, 58, 1, "INT"], [141, 58, 0, 61, 0, "IMAGE"], [142, 61, 0, 63, 0, "MASK"], [143, 61, 0, 64, 0, "MASK"], [144, 64, 0, 65, 0, "MASK"], [145, 52, 0, 48, 1, "CONDITIONING"], [146, 53, 0, 48, 2, "CONDITIONING"], [165, 2, 0, 55, 0, "MODEL"], [193, 10, 0, 82, 0, "IMAGE"], [194, 56, 0, 83, 0, "IMAGE"], [214, 91, 0, 93, 0, "SEGS"], [215, 93, 0, 94, 0, "IMAGE"], [217, 95, 0, 96, 0, "IMAGE"], [218, 48, 5, 97, 1, "IMAGE"], [219, 89, 0, 97, 0, "BBOX_DETECTOR"], [220, 98, 0, 97, 2, "SAM_MODEL"], [221, 97, 0, 91, 0, "SEGS"], [222, 99, 0, 95, 0, "SEGS"], [223, 91, 0, 101, 0, "SEGS"], [224, 99, 0, 100, 0, "SEGS"], [226, 100, 0, 102, 0, "MASK"], [227, 101, 0, 103, 0, "MASK"], [228, 97, 0, 99, 0, "SEGS"], [250, 82, 0, 110, 0, "IMAGE"], [251, 83, 0, 111, 0, "IMAGE"], [256, 115, 0, 116, 0, "STRING"], [257, 114, 0, 116, 1, "STRING"], [259, 51, 0, 116, 2, "STRING"], [260, 116, 6, 52, 1, "STRING"], [261, 116, 6, 52, 2, "STRING"], [262, 55, 1, 118, 0, "CLIP"], [265, 57, 0, 118, 3, "INT"], [266, 57, 1, 118, 4, "INT"], [267, 55, 1, 119, 0, "CLIP"], [270, 57, 0, 119, 3, "INT"], [271, 57, 1, 119, 4, "INT"], [272, 116, 4, 118, 1, "STRING"], [273, 116, 4, 118, 2, "STRING"], [274, 53, 0, 87, 6, "CONDITIONING"], [275, 118, 0, 87, 5, "CONDITIONING"], [276, 42, 0, 87, 4, "VAE"], [278, 55, 1, 87, 3, "CLIP"], [280, 48, 5, 87, 0, "IMAGE"], [282, 116, 5, 119, 1, "STRING"], [283, 116, 5, 119, 2, "STRING"], [284, 119, 0, 86, 5, "CONDITIONING"], [285, 53, 0, 86, 6, "CONDITIONING"], [286, 42, 0, 86, 4, "VAE"], [287, 55, 1, 86, 3, "CLIP"], [289, 87, 0, 86, 0, "IMAGE"], [291, 87, 0, 120, 0, "IMAGE"], [293, 86, 0, 121, 0, "IMAGE"], [294, 123, 0, 122, 0, "UPSCALE_MODEL"], [295, 86, 0, 122, 1, "IMAGE"], [296, 122, 0, 124, 0, "IMAGE"], [297, 124, 0, 46, 0, "IMAGE"], [302, 152, 0, 151, 0, "SEGS_HEADER"], [303, 152, 1, 153, 0, "SEG_ELT"], [304, 154, 0, 151, 1, "SEG_ELT"], [305, 91, 0, 152, 0, "SEGS"], [306, 82, 0, 155, 1, "IMAGE"], [307, 153, 1, 155, 0, "IMAGE"], [308, 155, 0, 156, 0, "IMAGE"], [309, 155, 0, 154, 1, "IMAGE"], [310, 153, 0, 154, 0, "SEG_ELT"], [311, 153, 2, 154, 2, "MASK"], [312, 153, 3, 154, 3, "SEG_ELT_crop_region"], [313, 153, 4, 154, 4, "SEG_ELT_bbox"], [314, 153, 5, 154, 5, "SEG_ELT_control_net_wrapper"], [315, 153, 6, 154, 6, "FLOAT"], [316, 153, 7, 154, 7, "STRING"], [317, 158, 0, 157, 0, "SEGS_HEADER"], [318, 160, 0, 157, 1, "SEG_ELT"], [319, 158, 1, 159, 0, "SEG_ELT"], [320, 159, 0, 160, 0, "SEG_ELT"], [321, 161, 0, 160, 1, "IMAGE"], [322, 159, 2, 160, 2, "MASK"], [323, 159, 3, 160, 3, "SEG_ELT_crop_region"], [324, 159, 4, 160, 4, "SEG_ELT_bbox"], [325, 159, 5, 160, 5, "SEG_ELT_control_net_wrapper"], [326, 159, 6, 160, 6, "FLOAT"], [327, 159, 7, 160, 7, "STRING"], [328, 159, 1, 161, 0, "IMAGE"], [329, 161, 0, 162, 0, "IMAGE"], [330, 99, 0, 158, 0, "SEGS"], [331, 83, 0, 161, 1, "IMAGE"], [334, 151, 0, 165, 0, "SEGS"], [335, 164, 0, 165, 2, "SEGS_PREPROCESSOR"], [336, 166, 0, 165, 1, "CONTROL_NET"], [337, 165, 0, 87, 1, "SEGS"], [339, 167, 0, 168, 2, "SEGS_PREPROCESSOR"], [340, 168, 0, 86, 1, "SEGS"], [341, 157, 0, 168, 0, "SEGS"], [342, 166, 0, 168, 1, "CONTROL_NET"], [354, 55, 0, 173, 0, "MODEL"], [355, 176, 1, 175, 1, "IPADAPTER"], [359, 55, 0, 176, 0, "MODEL"], [360, 64, 0, 177, 4, "MASK"], [361, 61, 0, 175, 4, "MASK"], [362, 176, 0, 175, 0, "MODEL"], [363, 175, 0, 177, 0, "MODEL"], [364, 177, 0, 48, 0, "MODEL"], [365, 82, 0, 175, 2, "IMAGE"], [366, 83, 0, 177, 2, "IMAGE"], [367, 176, 1, 177, 1, "IPADAPTER"], [368, 173, 0, 178, 0, "MODEL"], [369, 173, 0, 179, 0, "MODEL"], [370, 173, 1, 178, 1, "IPADAPTER"], [371, 173, 1, 179, 1, "IPADAPTER"], [372, 180, 0, 175, 6, "INSIGHTFACE"], [373, 180, 0, 177, 6, "INSIGHTFACE"], [374, 83, 0, 179, 2, "IMAGE"], [375, 82, 0, 178, 2, "IMAGE"], [376, 178, 0, 87, 2, "MODEL"], [377, 179, 0, 86, 2, "MODEL"]], "groups": [{"title": "Loader", "bounding": [0, 1, 808, 1537], "color": "#88A", "font_size": 24, "locked": false}, {"title": "Style Selector + Background Prompt", "bounding": [813, -1, 971, 765], "color": "#b58b2a", "font_size": 24, "locked": false}, {"title": "Ip_Adapter_Base_Image", "bounding": [1792, 1, 836, 1528], "color": "#3f789e", "font_size": 24, "locked": false}, {"title": "Mask Generation", "bounding": [815, 770, 964, 764], "color": "#ffffff", "font_size": 24, "locked": false}, {"title": "Base Sampler", "bounding": [2634, 3, 1032, 1528], "color": "#8A8", "font_size": 24, "locked": false}, {"title": "Face_Detector", "bounding": [3679, 6, 1150, 1528], "color": "#3f789e", "font_size": 24, "locked": false}, {"title": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Face_Detailer", "bounding": [6635, 7, 2741, 1523], "color": "#8A8", "font_size": 24, "locked": false}, {"title": "Upscaler", "bounding": [9384, 10, 1258, 1523], "color": "#3f789e", "font_size": 24, "locked": false}, {"title": "Person_1", "bounding": [-2, -184, 140, 80], "color": "#b06634", "font_size": 24, "locked": false}, {"title": "Person_2", "bounding": [-3, -95, 140, 80], "color": "#a1309b", "font_size": 24, "locked": false}, {"title": "<PERSON><PERSON>", "bounding": [4835, 8, 1792, 1526], "color": "#3f789e", "font_size": 24, "locked": false}], "config": {}, "extra": {"0246.VERSION": [0, 0, 4]}, "version": 0.4}