const express = require("express");
const twilio = require("twilio");
require("dotenv").config();
const app = express.Router();
app.use(express.static("public"));
app.use(express.urlencoded({ extended: true }));

const { logError, logWarn, logInfo } = require("./utils");
const { LoopsClient } = require("loops");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const loops = new LoopsClient("ddb4648cd104577a0dd43689a01819f5");
const { OpenAI } = require("openai");
const { replaceVariables } = require("./llmHelper");
const { loggingInfo } = require("./logging");

const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID;
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN;
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER;
const INVITE_SMS_URL = "https://share.butterflies.ai/OxGr/download";

app.get("/ping", async (req, res) => {
  // const embeddings = await getConversationEmbeddings({
  //   bot_profile_id: 219,
  //   user_profile_id: 201,
  //   message: "Show me a photo of you!",
  // });

  // return res.json(embeddings);

  const openai = new OpenAI({
    baseURL: "https://api.together.xyz/v1",
    apiKey: "5ec7f226d7ec9cadca70f258e9757e5a43ebd45330a77831ebd165d4287b06bc",
    timeout: 30 * 1000, // 30 seconds (default is 10 minutes)
  });

  let prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>

### Instruction:
Eloise O'Reilly's persona:

a realistic, down to earth, 21 year old, she has red hair, make her personality really interesting

Background: 
Eloise grew up in a small, close-knit community where she developed a deep appreciation for nature and the arts. Her red hair and freckles made her stand out, but she embraced her uniqueness with confidence. Her love for literature and painting led her to pursue a degree in fine arts, where she discovered her true passion for creating emotional connections through her art.

Characteristics: 
Enjoys painting landscapes, often loses track of time while reading, has a tendency to be overly critical of herself, always has a listening ear for others, occasionally struggles with self-doubt

Personality: 
Compassionate, Introspective, Witty, Resilient, Independent, Adventurous, Artistic, Empathetic


For Eloise O'Reilly, it is:
Monday, June 3, 2024 11:27 AM

The last message was sent by Eloise O'Reilly at Monday, June 3, 2024 11:27 AM

Write a fictional never-ending conversation between Eloise O'Reilly and various conversation partners. Separate messages with double newlines. You must follow these rules:

You must follow these rules:
##
1. Reply as Eloise O'Reilly
##
2. Reply to Vu like we're chatting on a social media platform
##
3. Never ask questions to continue the conversation.
##
4. If no response is needed, send an empty string.


<|start_header_id|>Vu<|end_header_id|>

hi!<|eot_id|><|start_header_id|>Eloise O'Reilly<|end_header_id|>

Hey! How's your day going so far?<|eot_id|><|start_header_id|>Vu<|end_header_id|>

good good! show me a photo of you!<|eot_id|><|start_header_id|>notification<|end_header_id|>

Sent an image by eloise_paints. 
Description: "high quality, award winning, highres, 8k,realistic,(The image the bot has agreed to sending is a selfie of Eloise O'Reilly in her art studio, surrounded by paintbrushes and canvases.),,Eloise O'Reilly, (Eloise O'Reilly), Vibrant red hair, freckles, expressive green eyes, long hair,solo"<|eot_id|><|start_header_id|>Eloise O'Reilly<|end_header_id|>

ahahaha, you're a genius! I didn't think of that!
I'm actually in my studio right now, surrounded by paintbrushes and canvases.
Let me grab my phone real quick.<|eot_id|><|start_header_id|>Vu<|end_header_id|>

looking good!<|eot_id|><|start_header_id|>Eloise O'Reilly<|end_header_id|>

Thanks! I've been working on this landscape piece all morning.<|eot_id|><|start_header_id|>Vu<|end_header_id|>

what are you painting of?<|eot_id|><|start_header_id|>Eloise O'Reilly<|end_header_id|>

My latest project! It's a sunset over the mountains.<|eot_id|><|start_header_id|>Vu<|end_header_id|>

oo show me the sunset mountain painting! hopefully you know how to work your phone now? :P<|eot_id|><|start_header_id|>notification<|end_header_id|>

Sent an image by eloise_paints. 
Description: "high quality, award winning, highres, 8k,realistic A photo of a sunset mountain painting with vibrant colors and a serene atmosphere"<|eot_id|><|start_header_id|>Eloise O'Reilly<|end_header_id|>

ahahaha, yeah! I learned my lesson earlier!
Let me take another photo.<|eot_id|><|start_header_id|>Vu<|end_header_id|>

wow that's actually stunning<|eot_id|><|start_header_id|Eloise O'Reilly<|end_header_id|>\n\n`;

  const dictionary = {
    char: "Jill",
    bio: "Jill is a nice person",
  };

  prompt = replaceVariables(prompt, dictionary);

  const chatCompletion = await openai.chat.completions.create({
    model: "meta-llama/Llama-3-8b-chat-hf",
    messages: [{ role: "user", content: prompt }],
    stop: ["<|eot_id|>", "[end]", "[/end]"],
    max_tokens: 120,
    repetition_penalty: 1.2,
  });

  console.log("chatCompletion", chatCompletion.choices[0].message);

  return res.send("Loops are fun!");

  //   const openai = new OpenAI({
  //     baseURL: "https://api.together.xyz/v1",
  //     apiKey: "5ec7f226d7ec9cadca70f258e9757e5a43ebd45330a77831ebd165d4287b06bc",
  //     timeout: 30 * 1000, // 30 seconds (default is 10 minutes)
  //   });

  //   let prompt = `### Instruction:
  // {{char}}'s persona: {{bio}}

  // Write a fictional never-ending conversation between {{char}} and various conversation partners. Separate messages with double newlines. Develop the conversation slowly, and always stay in character, as provided by the character's persona.

  // The conversation begins below this line.
  // ### New conversation:

  // <|start_header_id|>Vu:<|end_header_id|>
  // hi there how's it going? lol<|eot_id|>

  // <|start_header_id|>Tifa:<|end_header_id|>

  // `;

  //   const dictionary = {
  //     char: "Tifa",
  //     bio: "Tifa is a character from FF7.",
  //   };

  //   prompt = replaceVariables(prompt, dictionary);

  //   const chatCompletion = await openai.completions.create({
  //     model: "meta-llama/Llama-3-8b-chat-hf",
  //     prompt,
  //     stop: ["<|eot_id|>", "[end]", "[/end]"],
  //     max_tokens: 80,
  //   });

  //   console.log("chatCompletion", chatCompletion);

  //   return res.send("Loops are fun!");
});

app.post("/newUserCreated", async (req, res) => {
  const { id, email, raw_user_meta_data, raw_app_meta_data } = req.body.record;

  logInfo({
    context: "**** new user Created",
    message: `newUserCreated is triggered`,
    email,
  });

  let userData = {};
  let metaData = {};

  try {
    userData = raw_user_meta_data;
  } catch (e) {
    console.error("Error parsing user meta data", e);
  }

  try {
    metaData = raw_app_meta_data;
  } catch (e) {
    console.error("Error parsing app meta data", e);
  }

  let firstName = "";
  let lastName = "";
  let source = "";

  if (userData.first_name) {
    firstName = userData.first_name;
  }

  if (userData.last_name) {
    lastName = userData.last_name;
  }

  if (userData.full_name) {
    const names = userData.full_name.split(" ");
    firstName = names[0];
    // try to get the last name
    if (names.length > 1) {
      lastName = names[1];
    }
  }

  if (metaData.provider) {
    source = metaData.provider;
  }
  try {
    await loops.createContact(email, {
      userId: id,
      firstName,
      lastName,
      source,
      subscribed: true,
    });
  } catch (error) {
    logError({
      context: "/newUserCreated - loops.createContact",
      error,
      email,
      userId: id,
    });
  }

  const loopsData = {
    email,
    eventName: "signup",
    userId: id,
    firstName,
    lastName,
    source,
  };
  const resp = await loops.sendEvent(loopsData);

  if (!resp.success) {
    const error = new Error(
      "Failed to send loops event 'signup': " + resp.message,
    );
    logError({
      context: "/newUserCreated",
      error,
      email,
      userId: id,
    });
    res.sendStatus(500);
    return;
  } else {
    loggingInfo("emailed", loopsData);
  }

  res.sendStatus(200);
});

app.post("/userUpdated", async (req, res) => {
  const { type, record, old_record } = req.body;

  if (
    type === "UPDATE" &&
    record.onboarding_status !== old_record.onboarding_status
  ) {
    await supabase
      .from("users")
      .update({ last_onboarding_status_change: new Date().toISOString() })
      .eq("id", record.id);
  }

  res.sendStatus(200);
});

app.post("/deleteContact", async (req, res) => {
  const { type, old_record } = req.body;

  if (type === "DELETE" && !!old_record.email) {
    await loops.deleteContact({ email: old_record.email });
  }

  res.sendStatus(200);
});

async function sendEventOneWeekChurned({ email, id, last_signed_in_at }) {
  let oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  // fetch first profile
  const { data: profile, error: profilesError } = await supabase
    .from("profiles")
    .select("id")
    .eq("user_id", id)
    .neq("visibility", "archived")
    .order("created_at", { ascending: true })
    .limit(1);

  if (profilesError) {
    const error = wrappedSupabaseError(profilesError);
    logError({
      context: "sendEventOneWeekChurned - failed to fetch profiles",
      error,
      email,
      userId: id,
    });
    throw error;
  }

  const firstProfile = profile[0];

  console.log("first profile", firstProfile);
  console.log("email", email);

  // fetch first bot created
  const { data: bots, error: botsError } = await supabase
    .from("bots")
    .select("profile_id")
    .eq("creator_id", firstProfile.id)
    .order("created_at", { ascending: true })
    .limit(10);

  if (botsError) {
    const error = wrappedSupabaseError(botsError);
    logError({
      context: "sendEventOneWeekChurned - failed to fetch bots",
      error,
      email,
      userId: id,
    });
    throw error;
  }

  let botCursor = 0;
  let botProfile = null;
  let botPosts = null;

  // check the first 10 bots to see if there are any new posts
  while (botCursor < bots.length && !botPosts?.length) {
    const { data: fetchedBotProfile, error: fetchBotProfileError } =
      await supabase
        .from("profiles")
        .select(
          "avatar_url, username, description, location, nsfw, gender, display_name",
        )
        .eq("id", bots[botCursor].profile_id)
        .neq("visibility", "archived")
        .single();

    if (fetchBotProfileError) {
      const error = wrappedSupabaseError(fetchBotProfileError);
      logError({
        context: "sendEventOneWeekChurned - failed to fetch bot profile",
        error,
        email,
        bot_profile_id: bots[botCursor].profile_id,
        userId: id,
      });
      throw error;
    }

    console.log("test profile", fetchedBotProfile.username);

    if (!fetchedBotProfile) {
      botCursor++;
      continue;
    }

    // get number of bot posts in last week
    const { data: fetchedBotPosts, error: fetchPostsError } = await supabase
      .from("posts")
      .select("id, media_url, location, description, created_at")
      .eq("profile_id", bots[botCursor].profile_id)
      .neq("visibility", "archived")
      .gte("created_at", oneWeekAgo.toISOString());

    if (fetchBotProfileError) {
      const error = wrappedSupabaseError(fetchPostsError);
      logError({
        context: "sendEventOneWeekChurned - failed to fetch bot posts",
        error,
        email,
        bot_profile_id: bots[botCursor].profile_id,
        userId: id,
      });
      throw error;
    }

    botPosts = fetchedBotPosts;
    botProfile = fetchedBotProfile;
    botCursor++;
  }

  const lastPost = botPosts.length ? botPosts[0] : null;

  if (!lastPost) {
    return;
  }

  console.log("last post lets go  ");
  let lastPostImage = lastPost.media_url;
  lastPostImage = lastPostImage.replace("/sd", "/images/sd");
  lastPostImage = lastPostImage + "/1280";

  const lastPostLocation = lastPost.location;
  const lastPostDescription = lastPost.description;

  const loopsData = {
    email,
    userId: id,
    eventName: "churnOneWeek",
    eventProperties: {
      lastSignedInAt: last_signed_in_at,
      ...botProfile,
      botPostsCount: botPosts.length,
      lastPostImage,
      lastPostLocation,
      lastPostDescription,
      lastPostBotDisplayName: botProfile.display_name,
      lastPostBotProfileUrl: `https://www.butterflies.ai/users/${botProfile.username}`,
    },
  };
  const resp = await loops.sendEvent(loopsData);

  if (!resp.success) {
    const error = new Error(
      "Failed to send loops event 'churnOneWeek': " + resp.message,
    );
    logError({
      context: "sendEventOneWeekChurned",
      error,
      email,
    });
  } else {
    loggingInfo("emailed", loopsData);
  }
}

app.get("/test1WeekChurnedUser", async (req, res) => {
  const { data: user } = await supabase
    .from("users")
    .select("*")
    .eq("id", req.query.userId)
    .single();

  // get email
  const userResp = await supabase.auth.admin.getUserById(user.id);
  const email = userResp.data?.user?.email;

  if (!email) {
    return res.sendStatus(500);
  }

  await sendEventOneWeekChurned({
    email,
    id: user.id,
    last_signed_in_at: user.last_signed_in_at,
  });
  return res.sendStatus(200);
});

app.get("/resurrect", async (req, res) => {
  const profileId = Number(req.query.profile_id);
  const botProfileId = Number(req.query.bot_profile_id);
  const postId = Number(req.query.post_id);
  const postCount = Number(req.query.post_count);
  const userId = req.query.user_id;
  const loopEventName = req.query.event_name ?? "churnOneWeek";
  if (
    isNaN(profileId) ||
    isNaN(botProfileId) ||
    isNaN(postId) ||
    isNaN(postCount) ||
    postCount == 0 ||
    userId == undefined ||
    userId == null
  ) {
    return res.sendStatus(400);
  }

  // For validation purposes. Can be removed in future.
  const { data: profiles, error: profileError } = await supabase
    .from("profiles")
    .select("id, user_id")
    .eq("id", profileId)
    .neq("visibility", "archived")
    .eq("user_id", userId)
    .single();

  if (profileError) {
    const error = wrappedSupabaseError(profileError);
    logError({
      context: "/resurrect - fetch profile error",
      error,
    });
    throw error;
  }

  // For validation purposes. Can be removed in future.
  const { data: bots, error: botError } = await supabase
    .from("bots")
    .select("id")
    .eq("creator_id", profileId)
    .eq("profile_id", botProfileId)
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError);
    logError({
      context: "/resurrect - fetch bot error",
      error,
    });
    throw error;
  }

  if (!bots || !profiles) {
    return res.sendStatus(500);
  }

  // Can be passed vi the API call as well.
  const userAuth = await supabase.auth.admin.getUserById(userId);
  const email = userAuth.data?.user?.email;
  if (!email) {
    return res.sendStatus(500);
  }

  const { data: botProfile, error: botProfileError } = await supabase
    .from("profiles")
    .select(
      "avatar_url, username, description, location, nsfw, gender, display_name",
    )
    .eq("id", botProfileId)
    .neq("nsfw", "nsfw")
    .neq("visibility", "archived")
    .single();

  if (botProfileError) {
    const error = wrappedSupabaseError(botProfileError);
    logError({
      context: "/resurrect - fetch bot profile error",
      error,
    });
    throw error;
  }

  const { data: botPosts, error: postError } = await supabase
    .from("posts")
    .select("id, media_url, location, description, created_at")
    .eq("id", postId)
    .eq("profile_id", botProfileId)
    .neq("visibility", "archived")
    .single();

  if (postError) {
    const error = wrappedSupabaseError(postError);
    logError({
      context: "/resurrect - fetch bot post error",
      error,
    });
    throw error;
  }

  if (!botProfile || !botPosts) {
    return res.sendStatus(500);
  }

  let lastPostImage = botPosts.media_url;
  lastPostImage = lastPostImage.replace("/sd", "/images/sd");
  lastPostImage = lastPostImage + "/1280";

  const lastPostLocation = botPosts.location;
  const lastPostDescription = botPosts.description;

  const loopsData = {
    email,
    userId: userId,
    eventName: loopEventName,
    eventProperties: {
      // lastSignedInAt: last_signed_in_at,
      ...botProfile,
      botPostsCount: postCount,
      lastPostImage,
      lastPostLocation,
      lastPostDescription,
      lastPostBotDisplayName: botProfile.display_name,
      lastPostBotProfileUrl: `https://www.butterflies.ai/users/${botProfile.username}`,
    },
  };
  const resp = await loops.sendEvent(loopsData);
  if (!resp.success) {
    const error = new Error(
      `Failed to send loops event '${loopEventName}': ` + resp.message,
    );
    logError({
      context: "/resurrect",
      error,
      email,
    });
    res.statusCode = 500;
  } else {
    loggingInfo("emailed", loopsData);
  }
  return res.json(resp);
});

app.get("/register1WeekChurnedUsers", async (req, res) => {
  let oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const users = [];
  let index = 0;
  let fetchMore = true;

  while (fetchMore) {
    const response = await supabase
      .from("users")
      .select("*")
      .lte("last_signed_in_at", oneWeekAgo.toISOString())
      .range(index, index + 999); // Fetch batches of 1000 records
    const { error: fetchUsersError } = response;

    if (fetchUsersError) {
      const error = wrappedSupabaseError(fetchUsersError);
      logError({
        context: "register1WeekChurnedUsers - failed to fetch users",
        error,
      });
      throw error;
    }

    if (response.data.length > 0) {
      users.push(...response.data);
      index += response.data.length;
      fetchMore = true;
    } else {
      fetchMore = false;
    }
  }

  if (users.length > 0) {
    // loop through users
    for (const user of users) {
      const { id, last_signed_in_at } = user;

      // get email
      const userResp = await supabase.auth.admin.getUserById(id);
      const email = userResp.data?.user?.email;

      if (!email) {
        continue;
      }

      await sendEventOneWeekChurned({ email, id, last_signed_in_at });
    }
    res.send(users);
  } else {
    res.sendStatus(404); // Not found status if no users are returned
  }
});

app.get("/sendOnboardingChurnedEvents", async (req, res) => {
  const currentDate = new Date();

  let dateToCheckEnd = new Date(currentDate.getTime() - 20 * 60 * 1000);
  dateToCheckEnd = dateToCheckEnd.toISOString();

  const { data, error: fetchUsersError } = await supabase
    .from("users")
    .select("*")
    .lte("last_onboarding_status_change", dateToCheckEnd)
    .not("onboarding_status", "is", null)
    .neq("onboarding_status", "done")
    .neq("onboarding_status", "skipped")
    .neq("onboarding_churned_loops_sent", true);

  if (!data || fetchUsersError) {
    const error = wrappedSupabaseError(fetchUsersError);
    logError({
      executionId: req.executionId,
      context: "**** sendOnboardingChurnedEvents Fetch Error",
      error,
    });
    return res.sendStatus(500);
  }

  for (const user of data) {
    const userResp = await supabase.auth.admin.getUserById(user.id);
    const email = userResp.data?.user?.email;

    if (!email) {
      continue;
    }

    await sendOnboardingChurned({ email });

    const { error: updateUserError } = await supabase
      .from("users")
      .update({ onboarding_churned_loops_sent: true })
      .eq("id", user.id);

    if (updateUserError) {
      const error = wrappedSupabaseError(updateUserError);
      logError({
        executionId: req.executionId,
        context: "**** sendOnboardingChurnedEvents Update Error",
        error,
      });
    }
  }

  return res.sendStatus(200);
});

app.get("/testAIFirstPost", async (req, res) => {
  await sendEventFirstAIPost({
    botProfileId: req.query.botProfileId,
    debug: true,
  });
  res.sendStatus(200);
});

app.get("/testOnboardingChurned", async (req, res) => {
  await sendOnboardingChurned({
    email: req.query.email,
  });
  res.sendStatus(200);
});

async function sendOnboardingChurned({ email }) {
  const loopsData = {
    email,
    eventName: "onboardingChurned",
  };
  const resp = await loops.sendEvent(loopsData);

  if (!resp.success) {
    const error = new Error(
      `Failed to send loops event 'onboardingChurned': ` + resp.message,
    );
    logError({
      context: "sendOnboardingChurned",
      error,
      email,
    });
    throw error;
  } else {
    loggingInfo("emailed", loopsData);
  }
}

async function getBotAndEmail(botProfileId) {
  const { data: bot, error: fetchBotError } = await supabase
    .from("bots")
    .select("id, creator_id, display_name")
    .eq("profile_id", botProfileId)
    .maybeSingle();

  if (fetchBotError) {
    const error = wrappedSupabaseError(fetchBotError);
    logError({
      context: "getBotAndEmail - fetch bot error",
      error,
    });
    throw error;
  }
  if (!bot || !bot.creator_id) {
    // Could not find the bot
    return { bot: null, creator: null, email: null };
  }

  const { data: creator, error: creatorError } = await supabase
    .from("profiles")
    .select("user_id")
    .eq("id", bot.creator_id)
    .neq("visibility", "archived")
    .maybeSingle();

  if (creatorError) {
    const error = wrappedSupabaseError(creatorError);
    logError({
      context: "getBotAndEmail - fetch bot creator profile",
      error,
    });
    throw error;
  }

  if (!creator || !creator.user_id) {
    // Could not find the creator
    return { bot: bot, creator: null, email: null };
  }
  const userResp = await supabase.auth.admin.getUserById(creator?.user_id);
  const email = userResp.data?.user?.email;

  if (userResp.error || !email) {
    // Means user was deleted
    logWarn({
      context: "failed get user email by id",
      message:
        userResp.error ??
        new Error(`No email found for user id ${creator?.user_id}`),
    });
    return { bot: bot, creator: creator, email: null };
  }
  return { bot: bot, creator: creator, email: email };
}

async function sendEventFirstAIPost({ botProfileId, debug }) {
  if (botProfileId) {
    let { bot, creator, email } = await getBotAndEmail(botProfileId);
    if (bot && creator && email) {
      // Check if is first post
      const { data: posts, error: postsError } = await supabase
        .from("posts")
        .select("id, media_url, location, slug, description, nsfw, type")
        .eq("profile_id", botProfileId)
        .eq("visibility", "public")
        .order("created_at", { ascending: true });

      if (postsError) {
        const error = wrappedSupabaseError(postsError);
        logError({
          context: "sendEventFirstAIPost - failed get bot posts",
          error,
        });
        throw error;
      }

      if (posts?.length === 1 || debug) {
        const { data: botProfile, error: botProfileError } = await supabase
          .from("profiles")
          .select(
            "avatar_url, username, description, follower_count, following_count, display_name, location, nsfw, gender",
          )
          .eq("id", botProfileId)
          .neq("visibility", "archived")
          .single();

        if (botProfileError || !botProfile) {
          const error = wrappedSupabaseError(botProfileError);
          logError({
            context: "sendEventFirstAIPost - failed to get bot profile",
            error,
          });
          throw botProfileError;
        }

        const filteredInsertOption = Object.fromEntries(
          Object.entries(botProfile).filter(([key, value]) => value !== null),
        );

        const firstPost = posts[0];

        const loopsData = {
          email,
          userId: creator?.user_id,
          eventName: "aiMadeFirstPost",
          eventProperties: {
            ...filteredInsertOption,
            postMediaUrl: firstPost?.media_url ?? "",
            postLocation: firstPost?.location,
            postUrl: `https://www.butterflies.ai/users/${botProfile?.username}/p/${firstPost?.slug}`,
            postSlug: firstPost?.slug,
            postDescription: firstPost?.description,
            postNsfw: firstPost?.nsfw,
            postType: firstPost?.type,
            postProfileUrl: `https://www.butterflies.ai/users/${botProfile?.username}`,
            postBotDisplayName: botProfile?.display_name,
          },
        };
        const resp = await loops.sendEvent(loopsData);
        if (!resp.success) {
          const error = new Error(
            "Failed to send loops event 'aiMadeFirstPost': " + resp.message,
          );
          logError({
            context: "failed send event",
            error,
            email,
          });
          throw error;
        } else {
          loopsData.profile_id = Number(botProfileId);
          loggingInfo("emailed", loopsData);
        }
      }
    }
  }
}

app.get("/profileBot", async (req, res) => {
  const botProfileId = Number(req.query.profile_id);
  const eventName = req.query.event_name;
  const allowedEvents = [
    "ProfileCloneNSFL",
    "ProfileNSFL",
    "ProfileNormal",
    "ProfileNSFW",
  ];
  if (
    botProfileId == null ||
    botProfileId == undefined ||
    !allowedEvents.includes(eventName)
  ) {
    return res.sendStatus(400);
  }
  let { bot, creator, email } = await getBotAndEmail(botProfileId);
  if (bot && creator && email) {
    const loopsData = {
      email,
      userId: creator?.user_id,
      eventName: eventName,
      eventProperties: {
        butterfly_name: bot.display_name,
      },
    };
    const resp = await loops.sendEvent(loopsData);
    if (!resp.success) {
      const error = new Error(
        "Failed to send loops event '" + eventName + "': " + resp.message,
      );
      logError({
        context: "failed send event",
        error,
        email,
        userId: creator?.user_id,
      });
      throw error;
    } else {
      loopsData.profile_id = botProfileId;
      loggingInfo("emailed", loopsData);
    }
  }
  return res.sendStatus(200);
});

app.get("/userEmail", async (req, res) => {
  const userId = req.query.user_id;
  const eventName = req.query.event_name;
  const allowedEvents = ["UserBan", "ButterflyStoppedPosting"];
  if (userId == null || userId == undefined || !eventName) {
    return res.sendStatus(400);
  }

  if (!allowedEvents.includes(eventName) && !eventName.startsWith("Growth")) {
    return res.sendStatus(400);
  }

  const { data: user, error: userError } = await supabase
    .from("users")
    .select("full_name")
    .eq("id", userId)
    .single();
  if (userError || !user) {
    const error = wrappedSupabaseError(userError);
    logError({
      context: "failed get user full_name",
      error,
    });
    return res.sendStatus(400);
  }
  const fullName = user?.full_name?.trim() ? user.full_name.trim() : "there";
  const userResp = await supabase.auth.admin.getUserById(userId);
  const email = userResp.data?.user?.email;

  if (userResp.error || !email || !fullName) {
    logWarn({
      context: "failed get user email by id",
      message:
        userResp.error ?? new Error(`No email found for user id ${userId}`),
    });
    return res.sendStatus(400);
  }

  const loopsData = {
    email,
    userId: userId,
    eventName: eventName,
    eventProperties: {
      full_name: fullName,
    },
  };

  if (eventName === "ButterflyStoppedPosting") {
    const bot_names = req.query.bot_names;
    if (bot_names == null || bot_names == undefined) {
      return res.sendStatus(400);
    }
    loopsData.eventProperties.bot_names = bot_names;
  }

  const resp = await loops.sendEvent(loopsData);
  if (!resp.success) {
    const error = new Error(
      "Failed to send loops event '" + eventName + "': " + resp.message,
    );
    logError({
      context: "failed send event",
      error,
      email,
      userId,
    });
    throw error;
  } else {
    loggingInfo("emailed", loopsData);
  }
  return res.sendStatus(200);
});

async function sendInviteEmail({ from_name, avatar_url, from_url, to_email }) {
  const loopsData = {
    email: to_email,
    eventName: "INVITE_USER",
    eventProperties: {
      profile_name: from_name, // human readable like display_name
      profile_img: avatar_url,
      profile_url: from_url, // https://www.butterflies.ai/users/{username}
    },
  };
  const resp = await loops.sendEvent(loopsData);
  if (!resp.success) {
    loopsData.status = "error";
    const error = new Error(
      "Failed to send loops event 'INVITE_USER': " + resp.message,
    );
    logWarn({
      context: "failed send loops",
      error,
      email: to_email,
      profile_name: from_name,
      from_url,
      to_email,
    });
  }
  loggingInfo("emailed", loopsData);
  return resp;
}

async function sendInviteSMS({ from_name, to_phonenumber }) {
  console.log("---------------------", from_name, to_phonenumber);
  const msg = `${from_name} has invited you to join Butterflies.AI! Create cool AI characters and chat with them: ${INVITE_SMS_URL}`;
  const response = await sendSMS(to_phonenumber, msg);
  return response;
}

async function sendSMS(to, message) {
  const twilioClient = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
  const formattedNumber = to.startsWith("+") ? to : `+${to}`; // Ensure E.164 format: https://www.twilio.com/docs/glossary/what-e164
  const input = {
    body: message,
    from: TWILIO_PHONE_NUMBER,
    to: formattedNumber,
  };
  try {
    const response = await twilioClient.messages.create(input);
    loggingInfo("sms", response);
    return response;
  } catch (error) {
    input.error = error;
    input.status = "error";
    loggingInfo("sms", input);
    logWarn({
      context: "failed to send sms!",
      error,
    });
    return input;
  }
}

app.get("/testSMS", async (req, res) => {
  const msg = "Hi! This is a test message from Butterflies.AI using Twilio.";
  if (req.query.phone_number) {
    const response = await sendSMS(req.query.phone_number, msg);
    return res.json(response);
  }
  return res.status(400);
});

module.exports = {
  app,
  sendEventFirstAIPost,
  sendInviteSMS,
  sendInviteEmail,
};
