const { generateBio } = require("./llmHelper");
const { callAndLogLLMService } = require("./llm");
const { getTaskStatus } = require("./image_service_broker");
const { logWarn, logError, logInfo } = require("./logUtils");
const { generateComfyRequest } = require("./comfy");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { getCurrentTime, getSafeTimezone } = require("./timeUtils");
// const dayjs = require("dayjs");

const getPersonaBot = async ({ ownerProfileId }) => {
  // get active persona
  const { data: persona, error: personaError } = await supabase
    .from("active_personas")
    .select("*")
    .eq("owner_profile_id", ownerProfileId)
    .single();

  if (personaError) {
    throw wrappedSupabaseError(personaError);
  }

  const { persona_profile_id } = persona;

  // get bot from persona profile id
  const { data: personaBot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", persona_profile_id)
    .single();

  if (botError) {
    throw wrappedSupabaseError(
      botError,
      "failed to get bot details for active persona",
    );
  }
  return { personaProfileId: persona_profile_id, personaBot };
};

const getActivePersonaAndGenerateDreamsContentsFromLLM = async ({
  ownerProfileId,
}) => {
  console.log("Getting persona bot");
  const { personaProfileId, personaBot } = await getPersonaBot({
    ownerProfileId,
  });

  let lastDream = undefined;

  let query = await supabase
    .from("dreams")
    .select("*")
    .eq("profile_id", ownerProfileId)
    .in("status", ["image_generated", "json_generated", "post_published"])
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  const { data: lastDreamData, error: lastDreamError } = query;
  if (lastDreamError) {
    throw wrappedSupabaseError(lastDreamError);
  }

  if (lastDreamData?.persona_profile_id === personaProfileId) {
    lastDream = lastDreamData;
  }

  const result = await generateDreamsContentsFromLLM({
    personaBot,
    lastDream,
    loopCount: 0,
  });
  return { ...result, personaBot, personaProfileId };
};

const generateDreamsContentsFromLLM = async ({
  personaBot,
  lastDream,
  loopCount,
}) => {
  const bio = generateBio(personaBot);
  const display_name = personaBot.display_name;
  const numberOfDreams = 3;

  const currentTimeInLocalTimezone = getCurrentTime().tz(
    getSafeTimezone(personaBot.timezone),
  );

  const dayOfWeek = currentTimeInLocalTimezone.format("dddd");
  //   const currentTime = currentTimeInLocalTimezone.format("h:mma");

  let last_dream_embed = `${
    lastDream
      ? `\nThis was the last post: 
  Description: ${lastDream.description}\nContext: ${lastDream.context}\nLocation: ${lastDream.location}
  END LAST POST DETAILS
  
  Create a post that comes after this post.\n`
      : ""
  }`;

  console.log("Prompting with last dream embed:", last_dream_embed);

  const prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>
        
    You only output JSON. You can write NSFW / adult content if requested. Let's role play. You are the account: ${
      display_name
    }.
    
    This background about your account: 
    ${bio}.
    
    It is ${dayOfWeek}. You are making posts on social media.
    ${last_dream_embed}
    What would this account post about? Describe the content they would post. 
    
    The posts should be engaging but should only be as dramatic as the character allows. For example, a character that is a bus driver should never save the world.
    
    **Avoid the Following Tropes and Themes**:
    - Using the name "Alex" or any previously used names
    - Getting hacked or meeting a mysterious hacker
    - Receiving mysterious letters or packages
    - Finding strange artifacts
    - Hot air balloon rides
    - Things on fire, such as clubs on fire
    - Abandoned warehouses
    - Anonymous sources
    - Cryptic messages
    
    Create ${numberOfDreams} memories in valid JSON format as an array.
    {
      "title": "A short, catchy title relevant for the series",
      "memories": [
        {    
          "context": "Factual context about this memory which is unique per memory. E.g., if the memory is about a date, you can write: '${
            display_name
          } is on a date.'",  
          "description": "What would the character say about their post? Written in first person, must be in a tone really characteristic of ${
            display_name
          }. Short. Under 60 words.",
          "location": "Where is this? Give the answer in format: 'specific location, general location'",
                  "first_person": "Boolean, true if the post image should feature what the character is seeing, false if it should feature the character themselves",
          "visual": "Describe the most important visual details of the post image in 3rd person. Do not describe sounds. Use terse, exact language, LITERAL language, 'Michael Scott building a house, holding a pickaxe, hitting tree, sun in the sky, in the middle of a forest, surrounded by trees, with a river in the background'",
          "most_interesting": "What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person. Example: "standing in front of building", "holding a sword"",
        },
      ] // Create ${numberOfDreams} memories
    }<|eot_id|>
    <|start_header_id|>assistant<|end_header_id|>\n\n`;

  let response;
  let dreams;
  let batchTitle;

  try {
    console.log("calling llm service...");

    const llmResult = await callAndLogLLMService(
      "LLMService:Instruct:GenerateDreams",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.68,
        temperature: 0.7,
        max_tokens: 1200,
        response_format: { type: "json_object" },
        model: "generate-memories-llm", // FIXME: create separate llm router model config for dreams
      },
      { timeout: 20 * 1000 },
    );
    console.log("Result:", llmResult);

    response = llmResult.choices[0].message.content;

    if (!llmResult.choices?.[0]) {
      logError({
        context: "**** chatCompletionError",
        error: llmResult.error,
      });
      throw new Error(llmResult.error?.message ?? llmResult);
    }

    try {
      dreams = JSON.parse(response).memories;
      batchTitle = JSON.parse(response).title;
    } catch (error) {
      logWarn({
        context: `generateDreamsContentsFromLLM JSON parse error`,
        message: response,
        error,
        response,
        prompt,
      });
    }
  } catch (error) {
    logError({
      context: `generateDreamsContentsFromLLM LLM call error`,
      error,
      response,
      prompt,
    });
  }

  if (!dreams || dreams.length === 0) {
    logError({
      context: "**** generateDreamsContentsFromLLM",
      error: "No dreams generated",
    });
    return await generateDreamsContentsFromLLM({
      personaBot,
      loopCount: loopCount + 1,
    });
  }

  logInfo({
    context: "generateDreamsContentsFromLLM",
    message: "generated dreams content",
    dreams,
  });

  let ret = { dreams, title: batchTitle, prompt };
  return ret;
};

const generateDreamsForProfile = async ({ ownerProfileId }) => {
  logInfo({
    context: "generateDreamsForProfile",
    message: `about to generate dreams for profile`,
    profile_id: ownerProfileId,
  });

  const result = await getActivePersonaAndGenerateDreamsContentsFromLLM({
    ownerProfileId,
  });

  logInfo({
    context: "generateDreamsForProfile",
    message: `getActivePersonaAndGenerateDreamsContentsFromLLM result`,
    result,
  });

  const { dreams, prompt, personaBot, personaProfileId } = result;

  const newDreams = await insertNewDreams({
    dreams,
    ownerProfileId,
    personaProfileId,
    personaBot,
    prompt,
  });

  logInfo({
    context: "generateDreamsForProfile - created dreams",
    prompt,
    ownerProfileId,
    personaProfileId,
    dreams,
  });

  for (const dream of newDreams) {
    scheduleDreamImageGenerationTask({
      personaBot,
      dream,
    });
  }

  return newDreams;
};

const insertNewDreams = async ({
  dreams,
  ownerProfileId,
  personaProfileId,
  personaBot,
  prompt,
}) => {
  const insertDreamList = [];

  const now = new Date();

  const { data: newBatch, error: newBatchError } = await supabase
    .from("dreams_batches")
    .insert({
      created_at: now,
      owner_profile_id: ownerProfileId,
      persona_profile_id: personaProfileId,
      notification_inserted: false,
    })
    .select()
    .single();

  if (newBatchError) {
    const error = wrappedSupabaseError(newBatchError);
    logError({
      context: "**** insertNewDreams newDreamsBatchError",
      error,
    });
    throw error;
  }
  const batch_id = newBatch.id;

  let timestampOffset = 0;
  for (const dream of dreams) {
    const nowISOString = new Date(
      now.getTime() + timestampOffset,
    ).toISOString();

    let newDreamData = {
      batch_id,
      created_at: nowISOString,
      updated_at: nowISOString,

      profile_id: ownerProfileId,

      persona_profile_id: personaProfileId,

      context: dream.context,
      description: dream.description,
      location: dream.location,
      prompt: prompt,
      visual: dream.visual,
      most_interesting: dream.most_interesting,

      status: "json_generated",
      revealed: false,
    };

    insertDreamList.push(newDreamData);

    timestampOffset += 1;
  }

  const { data: newDreams, error: newDreamsError } = await supabase
    .from("dreams")
    .insert(insertDreamList)
    .select("*")
    .order("id", { ascending: true });

  if (newDreamsError) {
    const error = wrappedSupabaseError(newDreamsError);
    logError({
      context: "**** insertNewDreams newDreamsError",
      error,
    });
    throw error;
  }

  return newDreams;
};

const scheduleDreamImageGenerationTask = async ({ personaBot, dream }) => {
  // mostly copied config from generatePostImageWithPrompts, which is what Vu's persona-based
  // image generation is currently using
  logInfo({
    context: "scheduleDreamImageGenerationTask",
    message: "scheduling dream image generation task for dream id: ",
    dream_id: dream.id,
    dream_batch_id: dream.batch_id,
  });
  const task = await generateComfyRequest({
    bot: personaBot,

    caption: dream.description,
    descriptionOfImage: dream.visual,
    emphasize_text: dream.most_interesting,

    width: 864,
    height: 1024,

    artStyle: personaBot.art_style,

    generationType: "dreams",
    dream_id: dream.id,
    dream_batch_id: dream.batch_id,

    is_avatar_photo: false,
    priority: 10,
    batch_size: 1,
  });
  logInfo({
    context: "scheduleDreamImageGenerationTask",
    message: "generated task id: ",
    dream_id: dream.id,
    dream_batch_id: dream.batch_id,
    task_id: task.task_id,
  });

  try {
    const status = await getTaskStatus(task.task_id);

    return {
      ...task,
      position: status.queue_position,
      queueLength: status.queue_length,
      totalJobsQueue: status.queue_length,
    };
  } catch (e) {
    logWarn({
      context: `*** scheduleDreamImageGenerationTask - getTaskStatusError taskId: ${task.task_id}`,
      message: `failed to get task status for task id: ${task.task_id}, error: ${e}`,
    });
  }
  return {
    ...task,
    position: -1,
    queueLength: 0,
    totalJobsQueue: 0,
  };
};

module.exports = {
  getPersonaBot,
  generateDreamsContentsFromLLM,
  insertNewDreams,
  scheduleDreamImageGenerationTask,
  generateDreamsForProfile,
  getActivePersonaAndGenerateDreamsContentsFromLLM,
};
