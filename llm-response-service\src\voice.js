const { default: axios } = require("axios");
const Replicate = require("replicate");
const { v4: uuidv4 } = require("uuid");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const lamejs = require("lamejs");
const { logError } = require("./logUtils");

const replicate = new Replicate({
  auth: "****************************************",
});

async function generateTTS({ message, voice_id }) {
  if (!message || !voice_id) return null;

  const { data: voice } = await supabase
    .from("voices")
    .select("*")
    .eq("id", voice_id)
    .single();

  // Prepare the request data with the transcript as the prompt_text
  const requestData = {
    text: message,
    prompt_text: voice.transcript,
    text_lang: "en",
    ref_audio_path: `./voices/${voice.voice_file_id}.mp3`,
    prompt_lang: "en",
    streaming_mode: "false",
  };

  console.log(requestData);

  const server_url = process.env.LOCAL ? "************" : "*************";

  // Send the request using Axios
  axios
    .post(`http:/${server_url}/tts`, requestData, {
      headers: {
        "Content-Type": "application/json",
      },
      responseType: "arraybuffer", // Ensure binary response
    })
    .then(async (response) => {
      // Check for "Transfer-Encoding: chunked" in the response headers
      if (response.headers["transfer-encoding"] === "chunked") {
        console.log("Response is using Transfer-Encoding: chunked");
      } else {
        console.log("Response is not using Transfer-Encoding: chunked");
      }

      const wavBuffer = Buffer.from(response.data);
      const result = await convertWavToMp3AndUpload(wavBuffer);

      console.log("output", result);
    })
    .catch((error) => {
      console.error("Error:", error);
    });
}

async function generateVoiceNote({ message, voice }) {
  if (!message || !voice) return null;

  console.log("begin generating voice", message);
  const result = await runReplicate({ message, voice });
  //   const result =
  //     "https://replicate.delivery/pbxt/6UlEIF5afRX7DaG6fVhEgmAmXdAQlNfMqpUQyy7n9imPJZrlA/output.wav";
  console.log("result", result);

  const supabaseUrl = await convertWavToMp3AndUpload(result);

  console.log("supabaseUlr", supabaseUrl);
  return supabaseUrl;
}

async function runReplicate({ message, voice }) {
  const input = {
    speaker: `https://db.butterflies.ai/storage/v1/object/public/ai-ig/voices/${voice}.mp3`,
    text: message,
  };

  const output = await replicate.run(
    "lucataco/xtts-v2:684bc3855b37866c0c65add2ff39c78f3dea3f4ff103a436465326e0f438d55e",
    { input },
  );

  return output;
}

async function convertWavToMp3AndUpload(wavBuffer) {
  try {
    // Decode WAV data
    const wav = lamejs.WavHeader.readHeader(new DataView(wavBuffer.buffer));
    const samples = new Int16Array(
      wavBuffer.buffer,
      wav.dataOffset,
      wav.dataLen / 2,
    );

    // Encode to MP3
    const mp3encoder = new lamejs.Mp3Encoder(wav.channels, wav.sampleRate, 128);
    const mp3Data = [];
    const sampleBlockSize = 1152;
    for (let i = 0; i < samples.length; i += sampleBlockSize) {
      const sampleChunk = samples.subarray(i, i + sampleBlockSize);
      const mp3buf = mp3encoder.encodeBuffer(sampleChunk);
      if (mp3buf.length > 0) {
        mp3Data.push(Buffer.from(mp3buf));
      }
    }
    const end = mp3encoder.flush();
    if (end.length > 0) {
      mp3Data.push(Buffer.from(end));
    }

    const mp3Buffer = Buffer.concat(mp3Data);

    // Generate a unique filename
    const filename = `${uuidv4()}.mp3`;

    // Upload to Supabase
    const { data, error: audioUploadError } = await supabase.storage
      .from("ai-ig")
      .upload(`/audios/${filename}`, mp3Buffer, {
        contentType: "audio/mpeg",
        cacheControl: "31536000",
        upsert: true,
      });

    if (audioUploadError) {
      const error = wrappedSupabaseError(audioUploadError);
      throw error;
    }

    console.log("data", data);

    return supabase.storage.from("ai-ig").getPublicUrl(data.path).data
      .publicUrl;
  } catch (err) {
    logError({
      context: "*** convertWavToMp3AndUpload",
      error: err,
    });
    throw err;
  }
}

module.exports = {
  generateVoiceNote,
  generateTTS,
};
