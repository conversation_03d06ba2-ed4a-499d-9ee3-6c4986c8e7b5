const { botServerUrl } = require("../api");
const { getMD5, logInfo } = require("../utils");
const { createAndScheduleCloudTask } = require("./scheduleCloudTask");

async function scheduleProposedPostReminderTasks({ post, botProfile }) {
  logInfo({
    context: "scheduleProposedPostReminderTasks",
    message: "scheduling proposed post reminder tasks...",
    post_id: post.id,
    bot_profile_id: botProfile.id,
  });

  const postCreatedAtDate = new Date(post.created_at);

  const reminderDates = [24, 48, 72].map((offsetHours) => {
    return new Date(postCreatedAtDate.getTime() + offsetHours * 60 * 60 * 1000);
  });
  const tasks = [];
  let idx = 0;
  for (const scheduleDate of reminderDates) {
    // "Using hashed strings for the task id or for the prefix of the task id is recommended"
    const taskIdWithoutHash = `${post.id}-${idx}-${scheduleDate.getTime()}`;
    const hash = getMD5(taskIdWithoutHash);
    const taskId = `${hash}-${taskIdWithoutHash}`;
    const task = await createAndScheduleCloudTask({
      queueName: "v1-proposed-posts-reminders",
      taskId: taskId,
      taskURL: `${botServerUrl}/proposedPosts/sendProposedPostReminderTask`,
      taskPayload: {
        post,
        botProfile,
      },
      scheduleDate: scheduleDate,
    });
    tasks.push(task);
    idx += 1;
  }
  return tasks;
}

module.exports = {
  scheduleProposedPostReminderTasks,
};
