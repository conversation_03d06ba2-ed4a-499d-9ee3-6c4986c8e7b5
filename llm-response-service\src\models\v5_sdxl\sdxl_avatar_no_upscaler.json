{"last_node_id": 32, "last_link_id": 20, "nodes": [{"id": 26, "type": "UltralyticsDetectorProvider", "pos": [100, 594], "size": {"0": 315, "1": 78}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [], "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 30, "type": "LoraLoaderModelOnly", "pos": [515, 130], "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 17}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "shape": 3}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["NSFWFilter.safetensors", -1]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [100, 130], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [17], "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [5, 6], "shape": 3}, {"name": "VAE", "type": "VAE", "links": [8], "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["realvisxlV40_v40LightningBakedvae.safetensors"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [515, 672], "size": {"0": 400, "1": 200}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 6}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [3], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(octane render, render, drawing, anime, bad photo, bad photography:1.3), (worst quality, low quality, blurry:1.2), (bad teeth, deformed teeth, deformed lips), (bad anatomy, bad proportions:1.1), (deformed iris, deformed pupils), (deformed eyes, bad eyes), (deformed face, ugly face, bad face), (deformed hands, bad hands, fused fingers), morbid, mutilated, mutation, disfigured\n"]}, {"id": 5, "type": "EmptyLatentImage", "pos": [100, 358], "size": {"0": 315, "1": 106}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [4], "shape": 3}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 4]}, {"id": 8, "type": "VAEDecode", "pos": [1430, 130], "size": {"0": 210, "1": 46}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [19], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1015, 130], "size": {"0": 315, "1": 262}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 2}, {"name": "negative", "type": "CONDITIONING", "link": 3}, {"name": "latent_image", "type": "LATENT", "link": 4}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1024916184972002, "fixed", 5, 1.5, "dpmpp_sde", "karras", 1]}, {"id": 32, "type": "SaveImage", "pos": [1879, -340], "size": [1128.0302031199221, 1045.818890410547], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 19}], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [515, 342], "size": {"0": 400, "1": 200}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["minecraft rendering, steve"]}], "links": [[1, 30, 0, 3, 0, "MODEL"], [2, 6, 0, 3, 1, "CONDITIONING"], [3, 7, 0, 3, 2, "CONDITIONING"], [4, 5, 0, 3, 3, "LATENT"], [5, 4, 1, 6, 0, "CLIP"], [6, 4, 1, 7, 0, "CLIP"], [7, 3, 0, 8, 0, "LATENT"], [8, 4, 2, 8, 1, "VAE"], [17, 4, 0, 30, 0, "MODEL"], [19, 8, 0, 32, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}