const adjectives = [
  "happy",
  "angry",
  "tired",
  "funny",
  "silly",
  "shiny",
  "sleepy",
  "honest",
  "loud",
  "quiet",
  "brave",
  "crazy",
  "humble",
  "lively",
  "timid",
  "swift",
  "jolly",
  "vivid",
  "fancy",
  "sunny",
  "zesty",
  "cozy",
  "smart",
  "sneaky",
  "shy",
  "shady",
  "sharp",
  "crisp",
  "fuzzy",
  "plain",
  "blunt",
  "fresh",
  "clear",
  "salty",
  "sweet",
  "juicy",
  "spicy",
  "breezy",
  "calm",
  "bumpy",
  "fiery",
  "frosty",
  "soggy",
  "bitter",
  "sour",
  "vague",
  "neat",
  "lucky",
  "solid",
  "quick",
  "dear",
  "loose",
  "dizzy",
  "dull",
  "brisk",
  "stiff",
  "tasty",
  "faint",
  "flaky",
  "flimsy",
  "hasty",
  "lush",
  "hazy",
  "mild",
  "plump",
  "proud",
  "rooky",
  "sleek",
  "tense",
  "giddy",
  "zippy",
  "swank",
  "snug",
  "zany",
  "toxic",
  "smug",
  "oily",
  "vast",
  "furry",
  "ample",
  "eager",
  "witty",
  "bland",
  "ocean",
  "sandy",
  "smoky",
  "chilly",
  "tidy",
  "hefty",
  "stale",
  "dandy",
  "noisy",
  "curly",
  "wavy",
];

const nouns = [
  "cats",
  "dogs",
  "houses",
  "cars",
  "trees",
  "books",
  "balls",
  "chairs",
  "birds",
  "flowers",
  "pens",
  "shirts",
  "phones",
  "suns",
  "tables",
  "doors",
  "fish",
  "rivers",
  "shoes",
  "bikes",
  "cakes",
  "hats",
  "moons",
  "keys",
  "rings",
  "clouds",
  "cups",
  "fishes",
  "boats",
  "lamps",
  "bells",
  "flags",
  "clocks",
  "socks",
  "maps",
  "frogs",
  "snakes",
  "pears",
  "forks",
  "spoons",
  "knives",
  "trucks",
  "apples",
  "bears",
  "ducks",
  "fires",
  "ships",
  "desks",
  "bees",
  "boxes",
  "bags",
  "bats",
  "buses",
  "eggs",
  "locks",
  "nests",
  "roses",
  "stars",
  "watches",
  "leaves",
  "kites",
  "masks",
  "oceans",
  "mountains",
  "butterflies",
  "candles",
  "dinosaurs",
  "unicorns",
  "rainbows",
  "volcanoes",
  "islands",
  "tigers",
  "elephants",
  "lions",
  "giraffes",
  "glasses",
  "insects",
  "jars",
  "kettles",
  "noses",
  "oranges",
  "pencils",
  "queens",
  "umbrellas",
  "vases",
  "wheels",
  "xylophones",
  "yachts",
  "zebras",
  "fruits",
  "gloves",
  "horses",
  "jewels",
  "lemons",
  "mirrors",
  "needles",
  "owls",
  "pizzas",
  "quilts",
  "violets",
  "windows",
  "yarns",
  "zippers",
  "ants",
  "dishes",
  "engines",
  "fans",
  "grapes",
  "hammers",
  "icebergs",
  "jackets",
  "kangaroos",
  "mice",
  "nails",
  "oysters",
  "quails",
  "rabbits",
  "vans",
  "walnuts",
  "x-rays",
  "yams",
  "zeppelins",
  "dolphins",
  "erasers",
  "hives",
  "igloos",
  "jokes",
  "monkeys",
  "nuts",
  "octopuses",
  "parrots",
  "rats",
  "tomatoes",
  "urinals",
  "veins",
  "yards",
  "trumpets",
  "bananas",
  "eyes",
  "gates",
  "hands",
  "creams",
  "juices",
  "knees",
  "ladders",
  "necks",
  "olives",
  "pumpkins",
  "questions",
  "rocks",
  "towels",
  "uniforms",
  "villages",
  "whales",
  "years",
  "zones",
  "brooms",
  "donkeys",
  "ears",
  "feathers",
  "gears",
  "hills",
  "ideas",
  "jungles",
  "leopards",
  "mangoes",
  "napkins",
  "ostriches",
  "pillows",
  "quarters",
  "roads",
  "teaspoons",
  "users",
  "valleys",
  "wolves",
  "yogurts",
  "zombies",
  "acorns",
  "coins",
  "elbows",
  "gardens",
  "harps",
  "isles",
  "jams",
  "knots",
  "lobsters",
  "onions",
  "peaches",
  "blankets",
  "ravens",
  "stamps",
  "thimbles",
  "universes",
  "vipers",
  "waves",
  "yaks",
  "zooms",
  "arrows",
  "dolls",
  "envelopes",
  "hangers",
  "jugs",
  "kittens",
  "muffins",
  "notebooks",
  "pockets",
  "quills",
  "ribbons",
  "turtles",
  "wallets",
  "airplanes",
  "buckets",
  "earrings",
  "fences",
  "hearts",
  "rugs",
  "trains",
  "daisies",
  "utensils",
  "vegetables",
  "whistles",
  "anchors",
  "toys",
  "walls",
  "antennas",
  "donuts",
  "kiwis",
  "batteries",
  "dresses",
  "flutes",
  "jacks",
  "magnets",
  "pancakes",
  "seeds",
  "yolks",
  "hooks",
  "cubes",
  "lights",
  "mosquitoes",
  "numbers",
  "sunglasses",
  "teapots",
  "violins",
  "worms",
  "angels",
  "brushes",
  "cliffs",
  "eyebrows",
  "fingers",
  "grains",
  "heels",
  "jeans",
  "knobs",
  "lakes",
  "mushrooms",
  "nickels",
  "oaks",
  "pines",
  "papers",
  "rattles",
  "stones",
  "telephones",
  "urchins",
  "yawns",
  "lenses",
];

const verbs = [
  "run",
  "jump",
  "walk",
  "talk",
  "eat",
  "drink",
  "sleep",
  "write",
  "read",
  "sing",
  "dance",
  "swim",
  "play",
  "work",
  "study",
  "think",
  "drive",
  "cook",
  "clean",
  "listen",
  "speak",
  "watch",
  "love",
  "hate",
  "laugh",
  "cry",
  "smile",
  "frown",
  "climb",
  "fly",
  "wait",
  "help",
  "call",
  "visit",
  "buy",
  "sell",
  "create",
  "destroy",
  "draw",
  "paint",
  "build",
  "plant",
];

const adverbs = [
  "always",
  "usually",
  "often",
  "sometimes",
  "rarely",
  "never",
  "quickly",
  "slowly",
  "loudly",
  "quietly",
  "well",
  "badly",
  "hard",
  "easily",
  "soon",
  "late",
  "early",
  "too",
  "now",
  "here",
  "there",
  "still",
  "away",
  "yet",
  "again",
  "almost",
  "fast",
  "down",
  "up",
  "far",
  "much",
  "more",
  "less",
  "only",
  "together",
  "apart",
  "forever",
  "inside",
  "outside",
  "back",
  "today",
  "tomorrow",
  "yesterday",
  "nowhere",
  "anywhere",
  "somewhere",
  "everyday",
  "sometime",
  "anytime",
  "nowadays",
  "finally",
  "really",
  "so",
  "just",
  "very",
  "quite",
  "even",
  "simply",
  "absolutely",
  "totally",
  "completely",
  "entirely",
  "fully",
  "highly",
  "pretty",
  "rather",
  "fairly",
  "hardly",
  "mostly",
  "partly",
  "perfectly",
  "barely",
  "briefly",
  "bravely",
  "clearly",
  "deeply",
  "generally",
  "gently",
  "high",
  "low",
  "lightly",
  "loud",
  "nearly",
  "nicely",
  "sharply",
  "slow",
  "soft",
];

module.exports = { adjectives, nouns, verbs, adverbs };
