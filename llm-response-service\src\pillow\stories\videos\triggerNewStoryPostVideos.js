const { logInfo, logError } = require("../../../logUtils");
const { wrappedSupabaseError, supabase } = require("../../../supabaseClient");
const { default: axios } = require("axios");

async function triggerNewStoryPostVideos({ bot_profile_id }) {
  logInfo({
    context: "triggerNewStoryPostVideos",
    message: `Triggering new story post videos...`,
    bot_profile_id,
  });

  // TODO: acquire distributed lock
  const twentyFourHoursAgo = new Date(
    new Date().getTime() - 24 * 60 * 60 * 1000,
  );

  const { data: storyPosts, error: storyPostsError } = await supabase
    .from("pillow_story_posts")
    .select("*")
    .eq("processing_status", "image_generated")
    .eq("poster_profile_id", bot_profile_id)
    .eq("generate_video", true)
    .gte("created_at", twentyFourHoursAgo.toISOString());

  if (storyPostsError) {
    const error = wrappedSupabaseError(storyPostsError);
    throw error;
  }

  if (!storyPosts || !storyPosts.length) {
    logInfo({
      context: "triggerNewStoryPostVideos",
      message: `No story posts found in image_generated state`,
    });
    return false;
  }
  logInfo({
    context: "triggerNewStoryPostVideos",
    message: `Found ${storyPosts.length} story posts in image_generated state`,
    story_post_ids: storyPosts.map((sp) => sp.id),
  });

  // TODO: either have a batch api or make this concurrent (with a limit)
  for (const storyPost of storyPosts) {
    try {
      await triggerNewSingleStoryPostVideo({ storyPost });
    } catch {
      continue;
    }
  }
  // TODO: release distributed lock
  return true;
}

const triggerNewSingleStoryPostVideo = async ({ storyPost }) => {
  const { id, original_image_description, image_url: webp_url } = storyPost;

  logInfo({
    context: "triggerNewSingleStoryPostVideo",
    message: `Triggering new story post video...`,
    story_post_id: id,
  });

  const parsedURL = new URL(webp_url);
  parsedURL.pathname = parsedURL.pathname.replace(".webp", ".png");
  const png_url = parsedURL.toString();

  const videoGeneratePayload = {
    image_url: png_url,
    video_prompt: original_image_description,
  };

  logInfo({
    context: "triggerNewSingleStoryPostVideo",
    message: `Calling /video/generate with payload...`,
    request: videoGeneratePayload,
  });

  // trigger video generation
  let triggerVideoGenerationResult;

  try {
    triggerVideoGenerationResult = await axios.post(
      "https://api.butterflies.ai/video/generate",
      videoGeneratePayload,
    );
  } catch (error) {
    logError({
      context: "triggerNewSingleStoryPostVideo",
      message: `Failed to trigger video generation for story post ${id}`,
      story_post_id: id,
      error,
      error_response: error.response?.data,
    });
    return;
  }

  logInfo({
    context: "triggerNewSingleStoryPostVideo",
    message: `/video/generate response`,
    response: triggerVideoGenerationResult.data,
  });

  const { request_id: video_generation_id } = triggerVideoGenerationResult.data;
  if (video_generation_id) {
    const { error: updateError } = await supabase
      .from("pillow_story_posts")
      .update({
        processing_status: "video_generating",
        video_generation_id,
        video_generation_start_date: new Date().toISOString(),
      })
      .eq("id", id);
    if (updateError) {
      const error = wrappedSupabaseError(updateError);
      throw error;
    }
  } else {
    logError({
      context: "triggerNewSingleStoryPostVideo",
      message: `Failed to trigger video generation for story post ${id}`,
      story_post_id: id,
      response: triggerVideoGenerationResult.data,
    });

    const { error: updateError } = await supabase
      .from("pillow_story_posts")
      .update({
        processing_status: "video_failed",
      })
      .eq("id", id);
    if (updateError) {
      const error = wrappedSupabaseError(updateError);
      throw error;
    }
  }
};

module.exports = {
  triggerNewStoryPostVideos,
};
