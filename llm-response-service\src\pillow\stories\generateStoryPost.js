const { logInfo } = require("../../logUtils");
const { schedulePillowImageGenerationTask } = require("../../pillowUtils");
const { PillowGenerationType } = require("../../pillowImageUtils");

async function generateStoryPost({
  bot_profile_id,
  bot_local_time,
  new_state,
  decision,
  clip_text,
}) {
  // new_state.state is a string (that's how it's stored in the db)
  const parsed_new_state = JSON.parse(new_state.state);
  const { location, upper_body_clothing } = parsed_new_state;

  const snapRequest = {
    text: decision.text,
    location: location,
    current_time: bot_local_time,
    image_description: clip_text,
    upper_body_clothing: upper_body_clothing,
    selfie: decision.selfie ? "true" : "false",
    emote: decision.emote,
    lighting: decision.lighting,
  };
  logInfo({
    context: "generateStoryPost",
    message: "will schedule image generation task",
    snapRequest,
    bot_profile_id,
  });
  const task = await schedulePillowImageGenerationTask({
    snapRequest,
    generationType: PillowGenerationType.STORY,
    senderProfileId: bot_profile_id,
    decision,
  });
  logInfo({
    context: "generateStoryPost",
    message: "scheduled pillow image generation task",
    task_id: task.id,
    snapRequest,
    bot_profile_id,
  });
}

module.exports = {
  generateStoryPost,
};
