const { supabase } = require("../src/supabaseClient");

async function main() {
  const now = new Date();
  console.log("fetching...");
  const { data, error } = await supabase
    .from("profiles")
    .select(
      `id, username, description, display_name, proposed_post_mode, proposed_post_next_generation_date,
        posts(
          id, created_at, profile_id, media_url, location, slug, description, type, tags, visibility, nsfw, nsfl, previewhash, proposed_post_state
        )
        `,
    )
    .eq("proposed_post_mode", true)
    .lte("proposed_post_next_generation_date", now.toISOString())
    .not("visibility", "in", "(archived,hidden)")
    .not("posts.visibility", "in", "(hidden,archived)")
    .in("posts.proposed_post_state", [
      // "generating",
      "proposed",
    ])
    .order("created_at", { ascending: false });

  console.log(`fetched in ${new Date() - now}ms`);

  if (error) {
    console.error(error);
    return;
  }

  //   console.log("data", data);

  const profilesWithMoreThanOneCurrentlyProposedPost = [];
  const otherProfiles = [];
  for (const botProfile of data) {
    if (botProfile.posts.length > 1) {
      profilesWithMoreThanOneCurrentlyProposedPost.push(botProfile);
    } else {
      otherProfiles.push(botProfile);
    }
  }

  console.log(
    "!!! profilesWithMoreThanOneCurrentlyProposedPost",
    profilesWithMoreThanOneCurrentlyProposedPost.length,
  );
  console.log("!!! otherProfiles", otherProfiles.length);

  let idx = 0;
  for (const botProfile of profilesWithMoreThanOneCurrentlyProposedPost) {
    const postsCount = botProfile.posts.length;
    console.log(
      `#${idx + 1}/${profilesWithMoreThanOneCurrentlyProposedPost.length}: bot #${botProfile.id} @${botProfile.username} (${botProfile.display_name}): ${postsCount} currently proposed posts`,
    );

    const sortedPosts = botProfile.posts.sort(
      (a, b) => new Date(a.created_at) - new Date(b.created_at),
    );

    let postIdx = 0;
    const postsWithoutFirst = sortedPosts.slice(1);
    // Call supabase and archive all posts in postsWithoutFirst
    for (const post of postsWithoutFirst) {
      console.log(
        `${postIdx + 1}/${postsCount}: Archiving post #${post.id} created at ${post.created_at} with proposed_post_state ${post.proposed_post_state}`,
      );

      const postId = post.id;

      if (!postId) {
        console.error("postId is missing!");
        process.exit(1);
      }

      console.log("Deleting the proposed post reminders...");
      const {
        data: postProposedPostRemindersData,
        error: postProposedPostRemindersError,
      } = await supabase
        .schema("internal")
        .from("proposed_post_reminders")
        .delete()
        .eq("post_id", postId)
        .select();
      if (postProposedPostRemindersError) {
        console.error(
          "Delete post_proposed_post_reminders Failed Error",
          postProposedPostRemindersError,
        );
        throw postProposedPostRemindersError;
      }
      console.log(
        "Deleted proposed post reminders: ",
        postProposedPostRemindersData,
      );

      console.log("Deleting the notifications...");
      const { data: notificationsData, error: notificationsError } =
        await supabase
          .from("notifications")
          .delete()
          .eq("source_id", postId)
          .in("source_type", ["new_proposed_post", "proposed_post_reminder"])
          .select("id,source_type,source_id,profile_id");
      if (notificationsError) {
        console.error("Delete notifications Failed Error", notificationsError);
        throw notificationsError;
      }
      console.log("Deleted notifications: ", notificationsData);

      console.log("Deleting memory...");
      const { data: memoryData, error: memoryError } = await supabase
        .from("memories")
        .delete()
        .eq("post_id", postId)
        .select("id,profile_id,description,status");
      if (memoryError) {
        console.error("Delete memory Failed Error", memoryError);
        throw memoryError;
      }
      console.log("Deleted memory: ", memoryData);

      console.log("Archiving post...");
      const { data: postData, error: postError } = await supabase
        .from("posts")
        .update({ visibility: "archived" })
        .eq("id", postId)
        .select("id,profile_id,visibility");
      if (postError) {
        console.error("Archive post Failed Error", postError);
        throw postError;
      }
      console.log("Archived post: ", postData);
      postIdx += 1;
    }

    idx += 1;
  }
}

main().then(() => {
  console.log("done");
  process.exit(0);
});
