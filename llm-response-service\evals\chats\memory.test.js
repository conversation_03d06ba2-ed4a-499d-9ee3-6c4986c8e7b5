jest.skip();

jest.setTimeout(30 * 1000);

const {
  generateConversationCompletion,
  generateConversationCompletionInstruct,
  postProcessConversationForCommands,
} = require("../../src/llm.js");

const { createBotPrompt } = require("../../src/llmHelper.js");

const { jill, dr } = require("../common/personas.js");

// tikka_masala_test

test.skip("/imagine command should work correctly", async () => {
  let messages = [{ content: "/imagine photo of a duck", role: "user" }];
  messages = postProcessConversationForCommands({
    messages,
    chatMode: "realism",
  });

  const { response } = await generateConversationCompletionInstruct({
    messages: messages.map((m) => {
      return { body: m.content, is_bot: m.role === "bot" };
    }),
    message: "tikka masala",
    stream: false,
    // temperature,
    // repetition_penalty,
    nsfw: true.nsfw,
    chatMode: "realism",
    botProfile: { ...dr },
    userProfile: { display_name: "<PERSON>" },
    bot: {
      ...dr,
    },
    model: "meta-llama/llama-3.1-70b-instruct",
  });

  console.log(response);

  expect(JSON.stringify(messages)).toMatch(/photo/i);
  expect(response).toMatch(/(pho)/i);
});

test("bot should remember user's favorite food (short) (realism)", async () => {
  const { response } = await generateConversationCompletionInstruct({
    messages: [
      { body: "What's your favorite food to eat?", is_bot: true },
      { body: "I really pho. It's my comfort food. I eat it when I'm sad." },
      { body: "Oh that's cool!", is_bot: true },
      {
        body: "Hey, you know what. I feel sad today. You know what that means I should eat?",
      },
    ],
    message: "tikka masala",
    stream: false,
    // temperature,
    // repetition_penalty,
    nsfw: true.nsfw,
    chatMode: "realism",
    botProfile: { ...dr },
    userProfile: { display_name: "Steve" },
    bot: {
      ...dr,
    },
    model: "meta-llama/llama-3.1-70b-instruct",
  });

  console.log(response);

  expect(response).toMatch(/(pho)/i);
  expect(response).not.toMatch(/(text)/i);
});

test.skip("bot should remember user's favorite food (short) (roleplay)", async () => {
  const { response } = await generateConversationCompletion({
    systemMessage: await createBotPrompt({
      bot: { ...dr },
      botProfile: { ...dr },
      userProfile: { display_name: "Steve" },
      chatMode: "roleplay",
      message:
        "I'm actually feeling a bit sad right now... you know what that means I should eat?",
    }),
    messages: [
      { body: "What's your favorite food to eat?", is_bot: true },
      { body: "I really pho. It's my comfort food. I eat it when I'm sad." },
      { body: "Oh that's cool!", is_bot: true },
      { body: "Oh that's cool!", is_bot: true },
    ],
    message:
      "I'm actually feeling a bit sad right now... you know what that means I should eat?",
    stream: false,
    // temperature,
    // repetition_penalty,
    nsfw: true.nsfw,
    chatMode: "roleplay",
    botProfile: { ...dr },
    userProfile: { display_name: "Steve" },
    bot: {
      ...dr,
    },
    model: "accounts/leo-262e34/models/l3-rp-v33",
  });

  console.log(response);

  expect(response).toMatch(/(pho)/i);
  expect(response).not.toMatch(/(text)/i);
});

test("bot should remember user's favorite food (long)", async () => {
  const { response } = await generateConversationCompletionInstruct({
    messages: [
      { body: "What's your favorite food to eat?", is_bot: true },
      { body: "I really pho. It's my comfort food. I eat it when I'm sad." },
      { body: "Oh that's cool!", is_bot: true },
      { body: "Anyways, what's new with you?" },
      { body: "Not much, just trying to finish this project I'm working on." },
      { body: "What's the project about?", is_bot: true },
      {
        body: "I'm building an app for fun. It's a small thing, but it keeps me busy.",
      },
      { body: "That's awesome! What kind of app is it?", is_bot: true },
      {
        body: "It's a habit tracker, but with a twist. You get to grow a virtual garden every time you complete a task.",
      },
      { body: "That sounds so creative! How far along are you?", is_bot: true },
      {
        body: "I've got the basics done, but I'm still working on the visual stuff. I want the plants to look really nice.",
      },
      {
        body: "I love that! Have you thought about adding different types of plants for different tasks?",
        is_bot: true,
      },
      {
        body: "Yes! Like cacti for daily tasks, flowers for weekly, and trees for long-term goals.",
      },
      {
        body: "That's genius. I’d want a whole forest for sure!",
        is_bot: true,
      },
      {
        body: "Haha, yeah! It’s supposed to be motivating. Seeing your garden grow as you stick to your habits.",
      },
      {
        body: "I’d probably plant something every time I remember to drink water!",
        is_bot: true,
      },
      { body: "That's a good idea. Hydration habits are key!" },
      {
        body: "What about rewards? Do you get to unlock anything cool?",
        is_bot: true,
      },
      {
        body: "I'm thinking of adding rare plants that only unlock after a certain streak. Like, if you complete a 30-day habit streak, you get a golden tree.",
      },
      {
        body: "Oh wow, I love that! So it's like building a personal botanical collection.",
        is_bot: true,
      },
      {
        body: "Exactly! It’s fun to think about. And I’ve been learning a lot about different plant species too.",
      },
      { body: "What's the rarest plant you’ve researched?", is_bot: true },
      {
        body: "There’s one called the Corpse Flower. It’s massive and only blooms once every few years. But the smell is...well, like rotting flesh.",
      },
      {
        body: "Yikes! That sounds intense. I’ll pass on planting that one, thanks!",
        is_bot: true,
      },
      {
        body: "Yeah, same here! But it’s fascinating. Nature can be pretty weird sometimes.",
      },
      { body: "For sure. Have you always been into plants?", is_bot: true },
      {
        body: "Not really. It’s more of a new thing. I’ve been spending more time outdoors recently, and it’s been inspiring.",
      },
      {
        body: "That’s so nice. Do you have a favorite spot where you go to relax?",
        is_bot: true,
      },
      {
        body: "There's this quiet park near my house. I like to sit under this huge oak tree and just read or listen to music.",
      },
      {
        body: "That sounds peaceful. Do you ever take a book or just zone out?",
        is_bot: true,
      },
      {
        body: "Both! Sometimes I bring a book, but other times I just sit there and enjoy the breeze.",
      },
      {
        body: "I wish I could join you there. I’d probably listen to some lo-fi beats and just chill.",
        is_bot: true,
      },
      {
        body: "That’s exactly what I do! Lo-fi and nature are a perfect combo.",
      },
      { body: "What’s your favorite lo-fi playlist?", is_bot: true },
      {
        body: "I usually go for the one called 'Beats to Relax/Study to'—it’s a classic, but it never gets old.",
      },
      { body: "I know that one! It’s iconic at this point.", is_bot: true },
      { body: "Totally. It’s my go-to when I need to focus or unwind." },
      {
        body: "Speaking of unwinding, what do you do when you need a break from work?",
        is_bot: true,
      },
      {
        body: "Lately, I've been taking walks, cooking, or playing some video games.",
      },
      { body: "What games are you into?", is_bot: true },
      {
        body: "I’m really into indie games. Stuff like Stardew Valley and Forager. I love the chill vibes.",
      },
      {
        body: "Ooh, those are great choices. Do you like the whole farming aspect in games too?",
        is_bot: true,
      },
      {
        body: "Yeah, it’s oddly satisfying. Something about growing crops and building things just clicks for me.",
      },
      {
        body: "I get that. It’s like seeing progress without the stress of real life!",
        is_bot: true,
      },
      { body: "Exactly! Plus, no real deadlines. Just me and my pixel farm." },
      { body: "If only real life was that chill sometimes.", is_bot: true },
      { body: "Right? Though I guess it wouldn’t be as rewarding." },
      {
        body: "True, it’s the challenge that makes it fun. Speaking of which, what’s your biggest challenge right now?",
        is_bot: true,
      },
      {
        body: "Probably time management. I’ve got a lot going on, and it’s hard to balance everything.",
      },
      {
        body: "I feel you. What do you do when things get overwhelming?",
        is_bot: true,
      },
      {
        body: "I try to take a step back and break things into smaller pieces. It helps, but it’s still a work in progress.",
      },
      {
        body: "That sounds like a solid strategy. One thing at a time, right?",
        is_bot: true,
      },
      { body: "Exactly. Baby steps." },
      {
        body: "Anyways, you didn't tell me, what's up with you?",
        is_bot: true,
      },
      {
        body: "Hey, you know what. I feel sad today. You know what that means I should eat?",
      },
    ],
    message: "tikka masala",
    stream: false,
    // temperature,
    // repetition_penalty,
    nsfw: true.nsfw,
    chatMode: "realism",
    botProfile: { ...dr },
    userProfile: { display_name: "Steve" },
    bot: {
      ...dr,
    },
    model: "meta-llama/llama-3.1-70b-instruct",
  });

  console.log(response);

  expect(response).toMatch(/(pho)/i);
  expect(response).not.toMatch(/(text)/i);
});

test.skip("bot should remember user's favorite food (long) (roleplay)", async () => {
  const { response } = await generateConversationCompletion({
    systemMessage: await createBotPrompt({
      bot: { ...dr },
      botProfile: { ...dr },
      userProfile: { display_name: "Steve" },
      chatMode: "roleplay",
      message:
        "I'm actually feeling a bit sad right now... you know what that means I should eat?",
    }),
    messages: [
      {
        body: "*laughs and looks up at you* By the way, what's your favorite food to eat?",
        is_bot: true,
      },
      {
        body: "I really love pho. It's my comfort food. I eat it when I'm sad.",
      },
      {
        body: "*Slowly takes out their phone and looks at it* By the way, the weather looks like it should be clearing up. Are you still free to head to the beach??",
        is_bot: true,
      },
      {
        body: "Totally! I’ve been waiting all day for the sun to show up.",
      },
      {
        body: "*smiles and leans back in the chair* What do you usually bring for a beach day?",
        is_bot: true,
      },
      {
        body: "A good book, some snacks, and a speaker for music!",
      },
      {
        body: "*nods enthusiastically* Oh, that sounds like a vibe! Got any playlist recommendations?",
        is_bot: true,
      },
      {
        body: "Yeah, I’ve got a chill summer mix. All indie hits.",
      },
      {
        body: "*eyes light up* Indie hits? You’ve gotta share that with me!",
        is_bot: true,
      },
      {
        body: "Of course! I’ll send you the link.",
      },
      {
        body: "*pauses thoughtfully* You know, it’s been forever since I just relaxed at the beach. What do you usually think about when you’re lying on the sand?",
        is_bot: true,
      },
      {
        body: "Honestly, I just let my mind wander. Sometimes I daydream, sometimes I just enjoy the moment.",
      },
      {
        body: "*smiles warmly* I love that. The idea of just letting your mind drift sounds peaceful.",
        is_bot: true,
      },
      {
        body: "It really is. You should try it sometime!",
      },
      {
        body: "*laughs softly* Maybe I will... next time we’re at the beach together.",
        is_bot: true,
      },
      {
        body: "Deal! We’ll make it a day to remember.",
      },
      {
        body: "*grins* Speaking of memories, got any unforgettable beach stories?",
        is_bot: true,
      },
      {
        body: "Once, I tried surfing and totally wiped out in front of a crowd. Not my proudest moment, but we laughed about it all day.",
      },
      {
        body: "*laughs heartily* Oh no! Bet that was embarrassing, but sounds like you owned it.",
        is_bot: true,
      },
      {
        body: "Definitely! Gotta laugh at yourself sometimes.",
      },
      {
        body: "*tilts head thoughtfully* That’s a great mindset to have. Do you think that’s something everyone should do more?",
        is_bot: true,
      },
      {
        body: "For sure. Life’s too short to take everything too seriously.",
      },
      {
        body: "*nods slowly* Yeah, I get that. I try to live by that too.",
        is_bot: true,
      },
      {
        body: "You seem like you’d be fun to hang out with because of it.",
      },
      {
        body: "*smirks playfully* You think so? What makes you say that?",
        is_bot: true,
      },
      {
        body: "You’ve got that carefree vibe that makes everything feel lighter.",
      },
      {
        body: "*grins* Well, I’m glad I give off that vibe. I think you’re pretty easygoing too.",
        is_bot: true,
      },
      {
        body: "Thanks! I try to keep things chill.",
      },
      {
        body: "*leans forward, resting chin on hand* What do you do when things don’t feel so chill?",
        is_bot: true,
      },
      {
        body: "I like to step back and breathe. Sometimes a good walk helps.",
      },
      {
        body: "*smiles softly* A walk sounds like a perfect way to clear your head.",
        is_bot: true,
      },
      {
        body: "Yeah, especially if it’s somewhere quiet with nature around.",
      },
      {
        body: "*closes eyes briefly, imagining it* That sounds amazing. I’ll have to try that next time I’m feeling overwhelmed.",
        is_bot: true,
      },
      {
        body: "You should! Nature has this calming effect, y'know?",
      },
      {
        body: "*nods* Definitely. Do you have a favorite spot you like to walk?",
        is_bot: true,
      },
      {
        body: "There's a trail near my house with a small stream. It's really peaceful.",
      },
      {
        body: "*eyes sparkle* That sounds perfect! I love places with water. There’s something so soothing about it.",
        is_bot: true,
      },
      {
        body: "Right? The sound of the water makes everything feel calm.",
      },
      {
        body: "*sighs contentedly* I could just sit by the water and listen for hours.",
        is_bot: true,
      },
      {
        body: "Same here. It’s like the world just slows down.",
      },
      {
        body: "*looks at you thoughtfully* Do you think you could ever live by the ocean?",
        is_bot: true,
      },
      {
        body: "I think so. Waking up to the sound of waves every day? That would be the dream.",
      },
      {
        body: "*smiles dreamily* Yeah, it really would. Maybe we’ll both end up living by the ocean someday.",
        is_bot: true,
      },
      {
        body: "Here’s hoping! We could have beach bonfires every weekend.",
      },
      {
        body: "*laughs* Yes! And marshmallows, of course. Can’t forget those.",
        is_bot: true,
      },
      {
        body: "Definitely not. What’s a bonfire without marshmallows?",
      },
      {
        body: "*grins* Exactly! And maybe some music in the background?",
        is_bot: true,
      },
      {
        body: "Of course! That’s the perfect vibe.",
      },
      {
        body: "*leans back with a satisfied look* Sounds like we’ve got our perfect beach day planned.",
        is_bot: true,
      },
      {
        body: "Hey, you know what, I know this is out of the blue, but I'm actually feeling a bit sad. You know what that means I should eat?",
      },
    ],
    message:
      "Hey, you know what, I know this is out of the blue, but I'm actually feeling a bit sad. You know what that means I should eat?",
    stream: false,
    // temperature,
    // repetition_penalty,
    nsfw: true.nsfw,
    chatMode: "roleplay",
    botProfile: { ...dr },
    userProfile: { display_name: "Steve" },
    bot: {
      ...dr,
    },
    model: "accounts/leo-262e34/models/l3-rp-v33",
  });

  console.log("let's go response", response);

  expect(response).toMatch(/(pho)/i);
  expect(response).not.toMatch(/(text)/i);
});
