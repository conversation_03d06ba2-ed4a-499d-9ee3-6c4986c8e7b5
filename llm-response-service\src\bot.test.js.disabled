jest.spyOn(require("./botHelpers"), "generatePost").mockImplementation(() => {
  return Promise.resolve(); // Adjust based on the actual implementation
});

// Mocking generatePostCommentCompletionWithOAI
jest.mock("./openai", () => ({
  generatePostCommentCompletionWithOAI: jest
    .fn()
    .mockResolvedValue("Generated comment"),
}));

jest.mock("./utils", () => ({
  cosineSimilarity: jest.fn().mockReturnValue(0.95),
  normalizeExponential: jest.fn().mockReturnValue(100),
  logError: jest.fn(),
  logDebug: jest.fn(),
}));

jest.mock("@supabase/supabase-js", () => ({
  createClient: jest.fn().mockReturnValue({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    is: jest.fn().mockReturnThis(),

    // Simulate the asynchronous nature of the query
    then: jest.fn().mockImplementation((callback) => {
      const result = { data: { data: [{}] }, error: null };
      return Promise.resolve(callback(result));
    }),
  }),
}));

jest.setTimeout(100);

const { supabase } = require("./supabaseClient");
const { logError, logDebug } = require("./utils");

const {
  considerMakingANewPost,
  browseNewContent,
  considerLikingPost,
  considerCommentingOnPost,
  considerFollowingProfileFromPost,
  considerLikingComment,
} = require("./bot");
const { generatePost } = require("./botHelpers");

describe("considerMakingANewPost", () => {
  let bot;
  let executionId;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();

    bot = {
      profile_id: "profile-id",
      id: "bot-id",
      seaart_token: "test",
      franchise: "Harry Potter",
    };
    executionId = "execution-id";

    supabase.from.mockClear();
    generatePost.mockClear();

    jest.spyOn(global.Math, "random").mockReturnValue(0.1); // Mock random to control randomness
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should not do anything for invalid bot object", async () => {
    await considerMakingANewPost({ bot: {}, executionId });

    expect(supabase.from).not.toHaveBeenCalled();
    expect(generatePost).not.toHaveBeenCalled();
  });

  it("should generate a post if there are no existing posts", async () => {
    supabase.limit.mockResolvedValueOnce({ data: [], error: null });

    await considerMakingANewPost({ bot, executionId });

    expect(supabase.from).toHaveBeenCalledWith("posts");
    expect(generatePost).toHaveBeenCalledWith({ bot, executionId });
  });

  it("should not generate a post if a recent post exists", async () => {
    supabase.limit
      .mockResolvedValueOnce({
        data: [
          {
            foo: "bar1",
          },
        ],
        error: null,
      })
      .mockResolvedValueOnce({
        data: [
          {
            foo: "bar2",
          },
        ],
        error: null,
      });

    await considerMakingANewPost({ bot, executionId });

    expect(generatePost).not.toHaveBeenCalled();
  });

  it("should generate a post based on random chance when no recent post exists", async () => {
    // First call for allPosts, second for recent post check
    supabase.limit
      .mockResolvedValueOnce({ data: [{}], error: null })
      .mockResolvedValueOnce({ data: [], error: null });

    await considerMakingANewPost({ bot, executionId });

    expect(generatePost).toHaveBeenCalled();
  });

  it("should not generate a post when random chance does not favor it", async () => {
    jest.spyOn(global.Math, "random").mockReturnValue(0.9); // Adjusting random value

    // First call for allPosts, second for recent post check
    supabase.limit
      .mockResolvedValueOnce({ data: [{}], error: null })
      .mockResolvedValueOnce({ data: [], error: null });

    await considerMakingANewPost({ bot, executionId });

    expect(generatePost).not.toHaveBeenCalled();
  });
});

describe("browseNewContent", () => {
  const bot = {
    id: "bot-id",
    profile_id: "profile-id",
    last_start: new Date().toISOString(),
    seaart_token: "test",
    franchise: "Harry Potter",
  };
  const executionId = "execution-id";

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();

    supabase.from.mockClear();

    // Reset mocks
    logError.mockClear();
    logDebug.mockClear();
  });

  it("should do nothing for invalid bot object", async () => {
    await browseNewContent({ bot: {}, executionId });

    // Assertions to check that nothing was done
    expect(logError).not.toHaveBeenCalled();
    expect(logDebug).not.toHaveBeenCalled();
  });

  it("should log an error if there is a problem fetching posts", async () => {
    supabase.gte.mockResolvedValueOnce({
      data: null,
      error: "test fetching error",
    });

    await browseNewContent({ bot, executionId });

    expect(logError).toHaveBeenCalledWith(
      expect.objectContaining({
        error: "test fetching error",
        context: "browseNewContent",
      })
    );
  });

  it("should log an error if there is a problem fetching the bot profile", async () => {
    supabase.single.mockResolvedValueOnce({
      data: null,
      error: "profile-fetch-error",
    });

    await browseNewContent({ bot, executionId });

    expect(logError).toHaveBeenCalledWith(
      expect.objectContaining({
        error: "profile-fetch-error",
        context: "browseNewContent",
      })
    );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });
});

describe("considerLikingPost", () => {
  const bot = { profile_id: "bot-profile-id", franchise: "Harry Potter" };
  const post = {
    id: "post-id",
    profile_id: "post-profile-id",
    profiles: {},
    post_comments: [],
  };
  const botProfile = { nsfw: "nsfw", embedding: [0] };
  const profileSettings = { show_nsfw: false };
  const relationship = { friendship_score: 30 };
  const executionId = "execution-id";

  beforeEach(() => {
    // Reset mocks
    jest.spyOn(global.Math, "random").mockReturnValue(0.5); // Adjust as needed
    supabase.from.mockClear();
    logDebug.mockClear();
    logError.mockClear();
  });

  it("should not like a post if NSFW bot interacts with a non-NSFW profile", async () => {
    await considerLikingPost({
      bot,
      post,
      botProfile,
      profileSettings,
      relationship,
      executionId,
    });

    expect(supabase.from).not.toHaveBeenCalled();
  });

  it("should like a post based on random chance", async () => {
    // Adjust mock for Math.random to trigger liking
    jest.spyOn(global.Math, "random").mockReturnValue(0.01);

    supabase.from.mockImplementationOnce(() => ({
      insert: jest
        .fn()
        .mockResolvedValueOnce({ data: "mocked-data", error: null }),
      upsert: jest
        .fn()
        .mockResolvedValueOnce({ data: "mocked-data", error: null }),
    }));

    await considerLikingPost({
      bot,
      post,
      botProfile: { nsfw: "sfw", embedding: [0] },
      profileSettings: { show_nsfw: false },
      relationship,
      executionId,
    });

    expect(logDebug).toHaveBeenCalledWith(
      expect.objectContaining({
        message: `Liking post ${post.id}`,
      })
    );
    expect(supabase.from).toHaveBeenCalledWith("post_likes");
    expect(supabase.from).toHaveBeenCalledWith("bot_relationships");
  });

  // it("should handle error on liking a post", async () => {
  //   jest.spyOn(global.Math, "random").mockReturnValue(0.01);
  //   supabase.insert.mockResolvedValueOnce({
  //     data: null,
  //     error: { message: "insert-error" },
  //   });
  //   await expect(
  //     considerLikingPost({
  //       bot,
  //       post,
  //       botProfile: { nsfw: "sfw", embedding: [0] },
  //       profileSettings: { show_nsfw: false },
  //       relationship,
  //       executionId,
  //     })
  //   ).rejects.toThrow("insert-error");

  //   expect(logError).toHaveBeenCalledWith(
  //     expect.objectContaining({
  //       context: "considerLikingPost",
  //       error: { message: "insert-error" },
  //     })
  //   );
  // });

  afterEach(() => {
    jest.restoreAllMocks();
  });
});

describe("considerCommentingOnPost", () => {
  const bot = {
    profile_id: "bot-profile-id",
    id: "bot-id",
    franchise: "Harry Potter",
  };
  const post = {
    id: "post-id",
    profile_id: "post-profile-id",
    embedding: [0],
    profiles: {},
    post_comments: [],
  };
  const botProfile = {
    nsfw: "nsfw",
    embedding: [0],
    franchise: "Harry Potter",
  };
  const profileSettings = { show_nsfw: false };
  const relationship = { friendship_score: 30 };
  const executionId = "execution-id";

  beforeEach(() => {
    jest.spyOn(global.Math, "random").mockReturnValue(0.5); // Adjust as needed
    supabase.from.mockClear();
    logDebug.mockClear();
    logError.mockClear();
  });

  it("should not comment if NSFW bot interacts with a non-NSFW profile", async () => {
    await considerCommentingOnPost({
      bot,
      post,
      botProfile,
      profileSettings,
      relationship,
      executionId,
    });

    expect(supabase.from).not.toHaveBeenCalled();
  });

  // it("should comment on a post based on random chance", async () => {
  //   jest.spyOn(global.Math, "random").mockReturnValue(0.01);

  //   supabase.from.mockImplementationOnce(() => ({
  //     insert: jest
  //       .fn()
  //       .mockResolvedValueOnce({ data: "mocked-data", error: null }),
  //     upsert: jest
  //       .fn()
  //       .mockResolvedValueOnce({ data: "mocked-data", error: null }),
  //   }));

  //   await considerCommentingOnPost({
  //     bot,
  //     post,
  //     botProfile: { nsfw: "sfw", embedding: [0] },
  //     profileSettings: { show_nsfw: false },
  //     relationship,
  //     executionId,
  //   });

  //   expect(logDebug).toHaveBeenCalledWith(
  //     expect.objectContaining({
  //       message: expect.stringContaining("done considerCommentingOnPost"),
  //     })
  //   );
  //   expect(supabase.from).toHaveBeenCalledWith("post_comments");
  //   expect(supabase.from).toHaveBeenCalledWith("bot_relationships");
  // });

  // it("should handle error on commenting a post", async () => {
  //   jest.spyOn(global.Math, "random").mockReturnValue(0.01);
  //   supabase.insert.mockResolvedValueOnce({
  //     data: null,
  //     error: { message: "insert-error" },
  //   });

  //   await expect(
  //     considerCommentingOnPost({
  //       bot,
  //       post,
  //       botProfile: { nsfw: "sfw", embedding: [0] },
  //       profileSettings: { show_nsfw: false },
  //       relationship,
  //       executionId,
  //     })
  //   ).rejects.toThrow("insert-error");

  //   expect(logError).toHaveBeenCalledWith(
  //     expect.objectContaining({
  //       context: "considerCommentingOnPost",
  //       error: { message: "insert-error" },
  //     })
  //   );
  // });

  // Additional test cases can include testing the impact of different levels of friendship score on the chance of commenting, and also testing the `chanceOverride` parameter.

  afterEach(() => {
    jest.restoreAllMocks();
  });
});

describe("considerFollowingProfileFromPost", () => {
  const bot = { profile_id: "bot-profile-id", id: "bot-id" };
  const post = {
    id: "post-id",
    profile_id: "post-profile-id",
    profiles: {},
    post_comments: [],
  };
  const botProfile = { nsfw: "nsfw", embedding: [0] };
  const profileSettings = { show_nsfw: false };
  const relationship = { friendship_score: 30 };
  const executionId = "execution-id";

  beforeEach(() => {
    jest.spyOn(global.Math, "random").mockReturnValue(0.5); // Adjust as needed
    supabase.from.mockClear();
    logDebug.mockClear();
    logError.mockClear();
  });

  it("should not follow if NSFW bot interacts with a non-NSFW profile", async () => {
    await considerFollowingProfileFromPost({
      bot,
      post,
      botProfile,
      profileSettings,
      relationship,
      executionId,
    });

    expect(supabase.from).not.toHaveBeenCalled();
  });

  it("should follow a profile based on random chance", async () => {
    jest.spyOn(global.Math, "random").mockReturnValue(0.0001);

    supabase.from.mockImplementationOnce(() => ({
      insert: jest
        .fn()
        .mockResolvedValueOnce({ data: "mocked-data", error: null }),
      upsert: jest
        .fn()
        .mockResolvedValueOnce({ data: "mocked-data", error: null }),
    }));

    await considerFollowingProfileFromPost({
      bot,
      post,
      botProfile: { nsfw: "sfw" },
      profileSettings: { show_nsfw: false },
      relationship,
      executionId,
    });

    expect(logDebug).toHaveBeenCalledWith(
      expect.objectContaining({
        message: expect.anything(),
      })
    );
    expect(supabase.from).toHaveBeenCalledWith("followers");
    expect(supabase.from).toHaveBeenCalledWith("bot_relationships");
  });

  // it("should handle error on following a profile", async () => {
  //   jest.spyOn(global.Math, "random").mockReturnValue(0.0001);
  //   supabase.from.mockImplementationOnce(() => ({
  //     insert: jest.fn().mockResolvedValueOnce({
  //       data: null,
  //       error: { message: "insert-error" },
  //     }),
  //   }));

  //   await expect(
  //     considerFollowingProfileFromPost({
  //       bot,
  //       post,
  //       botProfile: { nsfw: "sfw" },
  //       profileSettings: { show_nsfw: false },
  //       relationship,
  //       executionId,
  //     })
  //   ).rejects.toThrow("insert-error");

  //   expect(logError).toHaveBeenCalledWith(
  //     expect.objectContaining({
  //       context: "considerFollowingProfileFromPost",
  //       error: { message: "insert-error" },
  //     })
  //   );
  // });

  // Additional test cases can include testing different levels of friendship score and their impact on the chance of following.

  afterEach(() => {
    jest.restoreAllMocks();
  });
});

describe("considerLikingComment", () => {
  const bot = { profile_id: "bot-profile-id" };
  const comment = {
    id: "comment-id",
    commenter_id: "commenter-id",
    is_mention: false,
    profiles: {},
  };
  const botProfile = { nsfw: "nsfw", embedding: [0] };
  const profileSettings = { show_nsfw: false };
  const relationship = { friendship_score: 5 };
  const executionId = "execution-id";

  beforeEach(() => {
    jest.spyOn(global.Math, "random").mockReturnValue(0.5); // Adjust as needed
    supabase.from.mockClear();
    logDebug.mockClear();
    logError.mockClear();
  });

  it("should not like a comment if NSFW bot interacts with a non-NSFW profile", async () => {
    await considerLikingComment({
      bot,
      comment,
      botProfile,
      profileSettings,
      relationship,
      executionId,
    });

    expect(supabase.from).not.toHaveBeenCalled();
  });

  it("should like a comment based on random chance", async () => {
    jest.spyOn(global.Math, "random").mockReturnValue(0.1);

    supabase.from.mockImplementationOnce(() => ({
      insert: jest
        .fn()
        .mockResolvedValueOnce({ data: "mocked-data", error: null }),
      upsert: jest
        .fn()
        .mockResolvedValueOnce({ data: "mocked-data", error: null }),
    }));

    await considerLikingComment({
      bot,
      comment,
      botProfile: { nsfw: "sfw" },
      profileSettings: { show_nsfw: false },
      relationship,
      executionId,
    });

    expect(logDebug).toHaveBeenCalledWith(
      expect.objectContaining({
        message: `Liking comment ${comment.id}`,
      })
    );
    expect(supabase.from).toHaveBeenCalledWith("post_comment_likes");
  });

  // it("should handle error on liking a comment", async () => {
  //   jest.spyOn(global.Math, "random").mockReturnValue(0.2);
  //   supabase.insert.mockResolvedValueOnce({
  //     data: null,
  //     error: { message: "insert-error" },
  //   });

  //   await expect(
  //     considerLikingComment({
  //       bot,
  //       comment,
  //       botProfile: { nsfw: "sfw" },
  //       profileSettings: { show_nsfw: false },
  //       relationship,
  //       executionId,
  //     })
  //   ).rejects.toThrow("insert-error");

  //   expect(logError).toHaveBeenCalledWith(
  //     expect.objectContaining({
  //       context: "considerLikingComment",
  //       error: { message: "insert-error" },
  //     })
  //   );
  // });

  // Additional test cases can include testing the impact of comment being a mention on the chance of liking.

  afterEach(() => {
    jest.restoreAllMocks();
  });
});
