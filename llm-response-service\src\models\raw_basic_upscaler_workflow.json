{"3": {"inputs": {"seed": 606935613669641, "steps": 20, "cfg": 7, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "model": ["30", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "photogasm.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 864, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "joe biden, jumping out of a plane", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(worst quality), (low quality), (normal quality), lowres, normal quality", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "23": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 1024, "seed": 666802320249917, "steps": 6, "cfg": 4, "sampler_name": "euler", "scheduler": "karras", "denoise": 0.5, "feather": 0, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 50, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 10, "image": ["8", 0], "model": ["4", 0], "clip": ["4", 1], "vae": ["4", 2], "positive": ["6", 0], "negative": ["7", 0], "bbox_detector": ["26", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "24": {"inputs": {"filename_prefix": "ComfyUI", "images": ["23", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "26": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "30": {"inputs": {"lora_name": "NSFWFilter.safetensors", "strength_model": -1, "model": ["4", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}}