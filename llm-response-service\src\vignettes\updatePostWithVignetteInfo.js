const { supabase } = require("../supabaseClient");
const { logError, logInfo, wrappedSupabaseError } = require("../utils");

/**
 * Update a post with finalized vignette details
 *
 * @param {Object} params - The parameters object
 * @param {number} params.postVignetteId - The post_vignette ID to process
 * @returns {Promise<Object>} - The updated post
 */
async function updatePostWithVignetteInfo({ postVignetteId }) {
  try {
    // Get the post_vignette record with its scenes and post
    const { data: postVignette, error: postVignetteError } = await supabase
      .from("post_vignettes")
      .select("*, vignette_scenes(*), posts(*)")
      .eq("id", postVignetteId)
      .single();

    if (postVignetteError) {
      const error = wrappedSupabaseError(postVignetteError);
      logError({
        context: "updatePostWithVignetteInfo - Failed to fetch post_vignette",
        error,
        postVignetteId,
      });
      throw error;
    }

    if (
      !postVignette ||
      !postVignette.vignette_scenes ||
      postVignette.vignette_scenes.length === 0
    ) {
      throw new Error(
        `No vignette scenes found for post_vignette ID: ${postVignetteId}`,
      );
    }

    // Verify that the post_vignette status is 'completed'
    if (postVignette.status !== "completed") {
      logError({
        context: "updatePostWithVignetteInfo",
        message: `Post vignette ${postVignetteId} status is not 'completed' (current: ${postVignette.status}), skipping post update`,
        postVignetteId,
      });
      return null;
    }

    // Get completed scenes with images
    const validScenes = postVignette.vignette_scenes
      .filter((scene) => scene.status === "completed" && scene.image_url)
      .sort((a, b) => a.id - b.id); // Sort by ID to maintain original order

    if (validScenes.length !== postVignette.vignette_scenes.length) {
      logError({
        context: "updatePostWithVignetteInfo",
        message: `Post vignette ${postVignetteId} has ${validScenes.length} completed scenes with images, expected ${postVignette.vignette_scenes.length}`,
        postVignetteId,
      });
      return null;
    }

    const vignetteData = {
      id: postVignette.id,
      scenes: validScenes.map((scene) => ({
        id: scene.id,
        image_url: scene.image_url,
        blurhash: scene.blurhash,
        scene_caption: scene.scene_caption,
      })),
    };

    // Update the post with vignette information
    const { data: updatedPost, error: updateError } = await supabase
      .from("posts")
      .update({
        vignette_data: vignetteData,
      })
      .eq("id", postVignette.post_id)
      .select("*")
      .single();

    if (updateError) {
      const error = wrappedSupabaseError(updateError);
      logError({
        context: "updatePostWithVignetteInfo - Failed to update post",
        error,
        post_id: postVignette.post_id,
      });
      throw error;
    }

    logInfo({
      context: "updatePostWithVignetteInfo",
      message: `Successfully updated post ${postVignette.post_id} with vignette details`,
      post_id: postVignette.post_id,
      postVignetteId,
      scenes_count: validScenes.length,
    });

    return updatedPost;
  } catch (error) {
    logError({
      context: "updatePostWithVignetteInfo",
      error,
      postVignetteId,
    });
    throw error;
  }
}

module.exports = {
  updatePostWithVignetteInfo,
};
