const { afterPostPublished } = require("../imageHelpers");
const { logInfo } = require("../logUtils");
const { wrappedSupabaseError, supabase } = require("../supabaseClient");
const { validateBotProfileOwnedByUser } = require("../utils");
const { bumpProposedPostGenerationDate } = require("./utils");

// Ensure that the authorized user can actually perform the action on the proposed post
async function validateProposedPostState({ postId, userId }) {
  if (!postId) {
    return false;
  }

  const { data, error: supabaseError } = await supabase
    .from("posts")
    .select("profile_id")
    .eq("id", postId)
    .eq("proposed_post_state", "proposed")
    .neq("visibility", "archived")
    .maybeSingle();

  if (!data) {
    return false;
  }

  if (supabaseError) {
    const error = wrappedSupabaseError(supabaseError);
    throw error;
  }

  const botProfileId = data.profile_id;
  const isValid = await validateBotProfileOwnedByUser({
    userId,
    botProfileId,
  });
  return isValid;
}

// assumes that we've already validated that the post is in proposed state
// and that the user can actually publish the post
async function publishProposedPost({ postId }) {
  // TODO: ideally we'd cancel any proposed post reminder tasks that are scheduled for this post id

  logInfo({
    context: "publishProposedPost",
    message: "publishing post",
    postId,
  });

  const publishTime = new Date();
  const [{ data, error: updateError }, { error: postCommentError }] =
    await Promise.all([
      supabase
        .from("posts")
        .update({
          proposed_post_state: "published",
          visibility: "public",
          created_at: publishTime,
        })
        .eq("id", postId)
        .select("*")
        .single(),
      supabase
        .from("post_comments")
        .update({
          created_at: publishTime,
        })
        .eq("post_id", postId),
    ]);

  if (updateError) {
    const error = wrappedSupabaseError(updateError);
    throw error;
  }

  if (postCommentError) {
    const error = wrappedSupabaseError(postCommentError);
    throw error;
  }

  const { profile_id } = data;

  await bumpProposedPostGenerationDate({ botProfileId: profile_id });

  await afterPostPublished({
    bot_profile_id: profile_id,
    post_id: postId,
    media_url: data.media_url,
  });

  return data;
}

// assumes that we've already validated that the post is in proposed state
// and that the user can actually publish the post
async function rejectProposedPost({ postId }) {
  // TODO: ideally we'd cancel any proposed post reminder tasks that are scheduled for this post id

  logInfo({
    context: "rejectProposedPost",
    message: "rejecting post",
    postId,
  });

  const { data, error: updateError } = await supabase
    .from("posts")
    .update({ proposed_post_state: "rejected" })
    .eq("id", postId)
    .select("*")
    .single();

  if (updateError) {
    const error = wrappedSupabaseError(updateError);
    throw error;
  }

  const { profile_id } = data;

  await bumpProposedPostGenerationDate({ botProfileId: profile_id });

  return data;
}

module.exports = {
  validateProposedPostState,
  publishProposedPost,
  rejectProposedPost,
};
