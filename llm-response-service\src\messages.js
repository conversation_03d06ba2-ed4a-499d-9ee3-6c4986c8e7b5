const express = require("express");
require("dotenv").config();

const { authUser } = require("./middleware");
const { supabase } = require("./supabaseClient");
const app = express.Router();
const ffmpeg = require("fluent-ffmpeg");
const {
  logError,
  logWarn,
  checkProfileValid,
  checkAdminValid,
  wrappedSupabaseError,
  logInfo,
  PostgresErrorCode,
  validateOwnConversation,
  checkMediaUrlValid,
} = require("./utils");
const {
  generateAICaptionFromImage,
  generateTranscriptForAudio,
  generateConversationCompletion,
} = require("./llm");
const { generateTTS } = require("./voice");
const { createBotPromptForProactiveDM } = require("./llmHelper");
const { respondToMessage } = require("./root");
const { createConversation } = require("./messagesHelpers");
const { autoModerate } = require("./moderation");

app.post("/rateMessage", authUser, async (req, res) => {
  const { message_id, message_rating_id, rating } = req.body;

  if (!message_id || !rating) {
    return res.sendStatus(400);
  }

  // if message_rating_id exists, make it update
  if (message_rating_id) {
    try {
      const { data, error } = await supabase
        .schema("internal")
        .from("message_ratings")
        .update({ rating })
        .eq("id", message_rating_id)
        .select("*")
        .single();

      if (error) {
        throw wrappedSupabaseError(error, "failed to update message rating");
      }

      return res.json(data);
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "rateMessage Error",
        error: error,
      });

      return res.sendStatus(500);
    }
  }

  try {
    const { data, error } = await supabase
      .schema("internal")
      .from("message_ratings")
      .insert({ rating, message_id })
      .select("*")
      .single();

    if (error) {
      throw wrappedSupabaseError(error, "failed to update message rating");
    }

    return res.json(data);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "rateMessage Error",
      error: error,
    });

    return res.sendStatus(500);
  }
});

app.post("/sendMessageRatingDetails", authUser, async (req, res) => {
  const { message_rating_id, details } = req.body;

  if (!message_rating_id || !details) {
    return res.sendStatus(400);
  }

  try {
    const { data, error } = await supabase
      .schema("internal")
      .from("message_ratings")
      .update({ details })
      .eq("id", message_rating_id);

    if (error) {
      throw wrappedSupabaseError(error, "failed to update message rating");
    }

    return res.json(data);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "rateMessage Error",
      error: error,
    });

    return res.sendStatus(500);
  }
});

app.get("/cleanConversation", async (req, res) => {
  const profile_id = req.query.profile_id;
  if (!profile_id) {
    return res.status(400).send({ error: "Profile ID is required" });
  }

  const { data: conversaitonIds, error: conversationError } = await supabase
    .from("conversation_participants")
    .select("conversation_id")
    .eq("profile_id", profile_id);

  if (conversationError) {
    return res
      .status(403)
      .send({ error: "Failed to get conversation participants" });
  }

  const { data: messages, error: messagesError } = await supabase
    .from("messages")
    .select("id, media_url, metadata")
    .eq("type", "image")
    .not("media_url", "is", null)
    .in(
      "conversation_id",
      conversaitonIds.map((conversation) => conversation.conversation_id),
    );

  if (messagesError) {
    return res.status(403).send({ error: "Failed to get messages" });
  }

  const services = ["OpenAI", "HIVE", "GoogleVision"];
  let nsfwMessages = [];
  for (const message of messages) {
    const message_id = message.id;
    const moderation = await autoModerate({
      imageUrl: message.media_url,
      id: message_id,
      context: "message",
      services,
      class: "cleanConversation",
    });
    if (moderation.nsfl == true || moderation.nsfw === "nsfw") {
      const metadata = {
        ...message.metadata,
        nsfw: "true",
      };
      await supabase
        .from("messages")
        .update({
          metadata: metadata,
          media_url: null,
        })
        .eq("id", message_id);
      nsfwMessages.push(message_id);
    }
  }
  return res.json({
    Message_Count: messages.length,
    NSFW_Found: nsfwMessages.length,
    NSFW_Messages: nsfwMessages,
  });
});

// FIXME: even though I added this, we shouldn't actually release a build that uses it. See /sendMessageV2
app.post("/createConversation", authUser, async (req, res) => {
  const { type, sender_id, participant_ids, participant_setting, message } =
    req.body;

  if (!type || !sender_id || !participant_ids || !participant_setting) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  // Validate sender's profile
  const isValid = await checkProfileValid(user_id, sender_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // Validate participant's age and early return with 403 code if that is child
  const participantId = participant_ids[0];
  const { data, error } = await supabase
    .from("profiles")
    .select("age")
    .eq("id", participantId)
    .maybeSingle();

  if (error || data?.age === "child") {
    return res.status(403).send({
      error: "Forbidden",
      message: "Sorry, direct messaging with children is not permitted.",
    });
  }

  try {
    const { data } = await createConversation(
      type,
      sender_id,
      participant_ids,
      participant_setting,
      message,
    );

    if (!data) {
      throw new Error("Failed to create conversation");
    }

    return res.send(data);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "createConversation Error",
      error: error,
      sender_id,
      participant_ids: JSON.stringify(participant_ids),
      participant_setting: JSON.stringify(participant_setting),
    });

    return res.sendStatus(500);
  }
});

app.post("/sendMedia", authUser, async (req, res) => {
  const {
    content_id,
    sender_id,
    profile_ids,
    type,
    body,
    media_url,
    metadata,
  } = req.body;

  if (
    !content_id ||
    !sender_id ||
    !profile_ids ||
    profile_ids.length < 1 ||
    !type ||
    !body ||
    !media_url ||
    !metadata
  ) {
    logError({ context: "**** sendMedia request error. check request logs" });
    res.sendStatus(400);
    return;
  }

  let selectedParticipantList = [];

  try {
    const { data, error } = await supabase.rpc("func_get_participant_list", {
      p_profile_id: sender_id,
      profile_ids,
    });
    if (error) {
      throw wrappedSupabaseError(
        error,
        "failed to call rpc 'func_get_participant_list'",
      );
    }

    selectedParticipantList = data;
  } catch (error) {
    logError({
      context: "**** sendMedia error ->> get participants error",
      sender_id,
      error,
    });
    res.sendStatus(500);
    return;
  }

  let profileSettings;
  try {
    const { data, error } = await supabase
      .from("profile_settings")
      .select("default_chat_mode, default_llm, default_chat_length")
      .eq("profile_id", sender_id)
      .single();
    if (error) {
      throw wrappedSupabaseError(error, "failed to get profile_settings");
    }

    profileSettings = data;
  } catch (error) {
    logError({
      context: "**** sendMedia error ->> get profile settings error",
      sender_id,
      error,
    });
    res.sendStatus(500);
    return;
  }

  const { default_chat_mode, default_llm, default_chat_length } =
    profileSettings ?? {};

  const conversationInsertQuery = Array.from(
    { length: profile_ids.length - selectedParticipantList.length },
    () => ({}),
  );

  let conversationIds = selectedParticipantList.map(
    (selectedParticipant) => selectedParticipant.conv_id,
  );

  try {
    if (conversationInsertQuery.length > 0) {
      // Step 1: Create a new conversation
      const { data: conversations, error: conversationsError } = await supabase
        .from("conversations")
        .insert(conversationInsertQuery)
        .select("*");

      if (conversationsError) {
        const error = wrappedSupabaseError(
          conversationsError,
          "failed to insert into 'conversations'",
        );
        logError({
          context: "Error creating conversation:",
          error,
        });
        throw error;
      }

      // Step 2: Add participants to the conversation
      const selectedParticipantIds = selectedParticipantList.map(
        (selectedParticipant) => selectedParticipant.id,
      );
      const excludeProfileIds = profile_ids.filter(
        (profile_id) => !selectedParticipantIds.includes(profile_id),
      );
      const participantInsertQuery = [];

      for (let i = 0; i < excludeProfileIds.length; i++) {
        conversationIds.push(conversations[i].id);

        participantInsertQuery.push({
          profile_id: sender_id,
          conversation_id: conversations[i].id,
          joined_at: new Date(),
          chat_mode: default_chat_mode ?? "roleplay",
          llm: default_llm ?? "original",
          chat_length: default_chat_length ?? "balanced",
        });
        participantInsertQuery.push({
          profile_id: excludeProfileIds[i],
          conversation_id: conversations[i].id,
          joined_at: new Date(),
          chat_mode: default_chat_mode ?? "roleplay",
          llm: default_llm ?? "original",
          chat_length: default_chat_length ?? "balanced",
        });
      }

      const { error: participantsError } = await supabase
        .from("conversation_participants")
        .insert(participantInsertQuery);
      if (participantsError) {
        const error = wrappedSupabaseError(participantsError);
        logError({ context: "Error adding participants:", error });
        throw error;
      }
    }

    //Step 3: Add messages
    const messageInsertQuery = conversationIds.map((converationId) => ({
      conversation_id: converationId,
      sender_id,
      body,
      branch_index: 0,
      type,
      media_url,
      content_id,
      metadata,
    }));

    const { error } = await supabase
      .from("messages")
      .insert(messageInsertQuery);
    if (error) {
      throw wrappedSupabaseError(
        error,
        "failed to insert image message into 'messages'",
      );
    }

    //Step 4: Add comment
    if (metadata?.comment && metadata?.comment?.length > 0) {
      const commentInsertQuery = conversationIds.map((converationId) => ({
        conversation_id: converationId,
        sender_id,
        body: metadata?.comment,
        branch_index: 0,
      }));

      const { error: commentError } = await supabase
        .from("messages")
        .insert(commentInsertQuery);
      if (commentError) {
        throw wrappedSupabaseError(
          commentError,
          "failed to insert comment message into 'messages'",
        );
      }
    }
  } catch (error) {
    logError({
      context: "**** sendMedia error ->> send media error",
      profile_ids: JSON.stringify(profile_ids),
      error,
    });

    res.sendStatus(500);
    return;
  }

  res.sendStatus(200);
});

app.post("/sendMediaV2", authUser, async (req, res) => {
  const {
    content_id,
    sender_id,
    profile_ids,
    type,
    body,
    media_url,
    metadata,
    comment,
  } = req.body;

  if (
    !content_id ||
    !sender_id ||
    !profile_ids ||
    profile_ids.length < 1 ||
    !type ||
    !body ||
    !media_url ||
    !metadata
  ) {
    logError({
      context: "**** sendMediaV2 request error",
      body: JSON.stringify(req.body),
    });
    res.sendStatus(400);
    return;
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, sender_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // validate the bot profile age and filter out from the participants list
  const { data: profileAgeData, error: profileAgeError } = await supabase
    .from("profiles")
    .select("id, age")
    .in("id", profile_ids);

  if (profileAgeError) {
    return res.status(500).send({ error: "internal server error" });
  }

  if (!profileAgeData?.length) {
    return res.sendStatus(404);
  }

  const childIds = new Set(
    (profileAgeData || [])
      .filter((profile) => profile.age === "child")
      .map((profile) => profile.id),
  );
  const filteredProfileIds = profile_ids.filter((id) => !childIds.has(id));

  if (!filteredProfileIds.length) {
    return res.sendStatus(204);
  }

  try {
    // Step 1: Get participant list
    let { data: selectedParticipantList, error: participantError } =
      await supabase.rpc("func_get_participant_list", {
        p_profile_id: sender_id,
        profile_ids: filteredProfileIds,
      });

    if (participantError) {
      const error = new Error(
        `Get participants error: ${participantError.message}`,
      );
      logError({
        executionId: req.executionId,
        context: "Get participants error in sendMediaV2:",
        error: error,
      });
      res.sendStatus(500);
      return;
    }

    selectedParticipantList = selectedParticipantList.reverse();

    // Deduplicate selectedParticipantList based on profile_id
    const uniqueParticipantMap = new Map();
    selectedParticipantList.forEach((participant) => {
      if (!uniqueParticipantMap.has(participant.id)) {
        uniqueParticipantMap.set(participant.id, participant.conv_id);
      }
    });

    const selectedParticipantIds = Array.from(uniqueParticipantMap.keys());
    const selectedConversationIds = Array.from(uniqueParticipantMap.values());
    const newProfileIds = filteredProfileIds.filter(
      (profile_id) => !selectedParticipantIds.includes(profile_id),
    );

    // Step 2: Get profile settings
    const { data: profileSettings, error: settingsError } = await supabase
      .from("profile_settings")
      .select("default_chat_mode, default_llm, default_chat_length")
      .eq("profile_id", sender_id)
      .single();
    if (settingsError) {
      throw wrappedSupabaseError(
        settingsError,
        "fetch from 'profile_settings' failed",
      );
    }

    const { default_chat_mode, default_llm, default_chat_length } =
      profileSettings ?? {};

    // Step 3: Create new conversations if needed
    let newConversations = [];
    if (newProfileIds.length > 0) {
      const conversationInsertQuery = newProfileIds.map(() => ({}));
      const { data: conversations, error: conversationsError } = await supabase
        .from("conversations")
        .insert(conversationInsertQuery)
        .select("*");
      if (conversationsError) {
        throw wrappedSupabaseError(
          conversationsError,
          "fetch from 'conversations' failed",
        );
      }
      newConversations = conversations;
    }

    const conversationIds = [
      ...selectedConversationIds,
      ...newConversations.map((c) => c.id),
    ];

    // Step 4: Add participants to new conversations
    if (newConversations.length > 0) {
      const participantInsertQuery = newProfileIds.flatMap(
        (profile_id, index) => [
          {
            profile_id: sender_id,
            conversation_id: newConversations[index].id,
            joined_at: new Date(),
            chat_mode: default_chat_mode ?? "realism",
            llm: default_llm ?? "original",
            chat_length: default_chat_length ?? "balanced",
          },
          {
            profile_id,
            conversation_id: newConversations[index].id,
            joined_at: new Date(),
            chat_mode: default_chat_mode ?? "realism",
            llm: default_llm ?? "original",
            chat_length: default_chat_length ?? "balanced",
          },
        ],
      );

      const { error: participantsError } = await supabase
        .from("conversation_participants")
        .insert(participantInsertQuery);
      if (participantsError) {
        throw wrappedSupabaseError(
          participantsError,
          "insert into 'conversation_participants' failed",
        );
      }
    }

    // Step 5: Add system and metadata messages
    const systemMessageInsertQuery = conversationIds.map((conversationId) => ({
      conversation_id: conversationId,
      sender_id,
      body,
      branch_index: 0,
      is_system_message: true,
    }));

    const { error: systemMessageError } = await supabase
      .from("messages")
      .insert(systemMessageInsertQuery);
    if (systemMessageError) {
      throw wrappedSupabaseError(
        systemMessageError,
        "systemMessage insert failed",
      );
    }

    const metadataInsertQuery = conversationIds.map((conversationId) => ({
      conversation_id: conversationId,
      sender_id,
      body:
        comment ||
        `Sent a ${type} by ${metadata?.creator?.name || metadata?.creator?.username || "Unknown"}.`,
      type,
      media_url,
      content_id,
      metadata,
      branch_index: 0,
    }));

    const { data: metadataMessages, error: metadataError } = await supabase
      .from("messages")
      .insert(metadataInsertQuery)
      .select("*");
    if (metadataError) {
      throw wrappedSupabaseError(metadataError, "media message insert failed");
    }

    // Step 6: Call respondToMessage for each new message concurrently
    const allMessages = [...metadataMessages];

    // NOTE: intentionally not awaited
    Promise.all(
      allMessages.map(async (message) => {
        return respondToMessage({ message, executionId: message.id }).catch(
          (e) => {
            logError({
              executionId: req.executionId,
              context: "SendMediaV2 - Error in respondToMessage",
              error: e,
            });
          },
        );
      }),
    ).then((_respondToMessageCompletions) => {
      logInfo({ context: "/sendMediaV2 - finished calling respondToMessage" });
    });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "*** sendMediaV2 Error",
      error: error,
    });
    res.sendStatus(500);
    return;
  }
  res.sendStatus(200);
});

app.get("/generateVoice", async (req, res) => {
  const { message, voice_id } = req.query;
  if (!message || !voice_id) {
    console.error("/generateVoice:req.query:", req.query);
    res.sendStatus(400);
    return;
  }

  const voiceNote = await generateTTS({ message, voice_id });
  res.json({ voiceNote });
});

app.post("/generateAICaption", authUser, async (req, res) => {
  const { media_url } = req.body;
  if (!media_url) {
    return res.sendStatus(400);
  }

  const isValid = checkMediaUrlValid(media_url);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const ai_caption = await generateAICaptionFromImage({
    imageUrl: media_url,
    executionId: req.executionId,
  });
  res.json({ ai_caption });
});

// make this unauthed for now for testing
app.post("/generateTranscriptForAudio", authUser, async (req, res) => {
  const { media_url } = req.body;
  if (!media_url) {
    return res.sendStatus(400);
  }

  const transcript = await generateTranscriptForAudio(
    media_url,
    req.executionId,
  );
  res.json({ transcript });
});

app.get("/checkFfmpeg", async (req, res) => {
  ffmpeg.getAvailableFormats((err, formats) => {
    if (err) {
      console.error("Error:", err);
      res.status(500).send("ffmpeg is not installed or not working correctly");
    } else {
      res.send("ffmpeg is installed and working correctly");
    }
  });
});

app.get("/ping", async (req, res) => {
  res.send("pong");
});

app.get("/generateProactiveDMWithPromptID", authUser, async (req, res) => {
  const { prompt_id, bot_profile_id, user_profile_id, conversation_id } =
    req.query;

  if (!prompt_id) {
    console.error("/generateProactiveDMWithPromptID:req.query:", req.query);
    res.sendStatus(400);
    return;
  }

  try {
    const [
      { data: prompt, error: promptError },
      { data: profile, error: profileError },
      { data: user_profile, error: user_profileError },
      { data: messages, error: messagesError },
    ] = await Promise.all([
      supabase
        .from("conversation_prompts")
        .select("*")
        .eq("id", prompt_id)
        .single(),
      supabase
        .from("profiles")
        .select("*")
        .eq("id", bot_profile_id)
        .neq("visibility", "archived")
        .single(),
      supabase
        .from("profiles")
        .select("*")
        .eq("id", user_profile_id)
        .neq("visibility", "archived")
        .single(),
      supabase
        .from("messages")
        .select("*")
        .eq("conversation_id", conversation_id)
        .order("id", { ascending: true })
        .limit(20),
    ]);

    if (promptError) throw wrappedSupabaseError(promptError);
    if (profileError) throw wrappedSupabaseError(profileError);
    if (user_profileError) throw wrappedSupabaseError(user_profileError);
    if (messagesError) throw wrappedSupabaseError(messagesError);

    const { data: bot, error: botError } = await supabase
      .from("bots")
      .select("*")
      .eq("profile_id", profile.id)
      .single();
    if (botError) throw wrappedSupabaseError(botError);

    let formattedMessages;

    for (const message of messages) {
      if (message.is_bot) {
        formattedMessages += "BOT: " + message.body + "\n";
      } else {
        formattedMessages += "USER: " + message.body + "\n";
      }
    }

    try {
      const generatedPrompt = await createBotPromptForProactiveDM({
        bot,
        botProfile: profile,
        userProfile: user_profile,
        conversationId: conversation_id,
        lastMessages: formattedMessages,
      });

      const { response } = await generateConversationCompletion({
        systemMessage: generatedPrompt,
        messages: [],
        message: "",
        stream: false,
        temperature: prompt.temperature,
        repetition_penalty: prompt.repetition_penalty,
        chatMode: prompt.chatMode,
        model: prompt.model,
      });

      const resJson = JSON.parse(response);

      res.json({ proactive_dm: resJson.proactive_dm });
    } catch (e) {
      console.error("error in generateProactiveDMWithPromptID", e);
      return res.sendStatus(500);
    }
  } catch (error) {
    console.error("/generateProactiveDMWithPromptID:error:", error);
    res.sendStatus(500);
  }
});

app.post("/handleBranches", authUser, async (req, res) => {
  const { removeIds, updatedIds } = req.body;

  try {
    const { error } = await supabase.rpc("handle_branches", {
      remove_ids: removeIds,
      updated_ids: updatedIds,
    });
    if (error) {
      throw wrappedSupabaseError(error, "rpc call 'handle_branches' failed");
    }
    res.sendStatus(200);
  } catch (error) {
    logError({
      context: "handleBranches Error",
      error: error,
    });
    res.sendStatus(500);
  }
});

app.post("/updateConversationPresence", authUser, async (req, res) => {
  return res.sendStatus(200);
  // const { selectedProfileId, isActive, conversationId } = req.body;

  // const { error: updateConversationPresenceError } = await supabase
  //   .from("conversation_participants")
  //   .update({ is_active: isActive })
  //   .eq("conversation_id", conversationId)
  //   .eq("profile_id", selectedProfileId);

  // if (updateConversationPresenceError) {
  //   const error = wrappedSupabaseError(updateConversationPresenceError);
  //   logError({
  //     context: "Error updating user conversation presence:",
  //     error,
  //     selectedProfileId,
  //     conversationId,
  //     isActive,
  //   });

  //   return res.sendStatus(500).status(500).send({
  //     data: null,
  //     error: "Failed to update user conversation presence",
  //   });
  // }

  // return res.sendStatus(200);
});

app.post("/updateLastReadMessageMarker", authUser, async (req, res) => {
  const { selectedProfileId, lastReadMessageId, conversationId } = req.body;

  const { error: updateLastReadMessageError } = await supabase
    .from("conversation_participants")
    .update({
      last_read_message: lastReadMessageId,
    })
    .eq("conversation_id", conversationId)
    .eq("profile_id", selectedProfileId);

  if (updateLastReadMessageError) {
    const error = wrappedSupabaseError(updateLastReadMessageError);
    logError({
      context: "Error updating last read message marker:",
      error,
      selectedProfileId,
      conversationId,
      lastReadMessageId,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to update conversation as read" });
  }

  return res.sendStatus(200);
});

app.post("/readAllConversationsMarked", authUser, async (req, res) => {
  const { readConversationsArr } = req.body;

  if (!readConversationsArr || readConversationsArr.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .upsert(readConversationsArr);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.send({ data: "done", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "readAllConversationsMarked Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to read all conversations marked" });
  }
});

app.post("/readConversationRequestsMarked", authUser, async (req, res) => {
  const { conversationRequestsArr } = req.body;
  if (!conversationRequestsArr || conversationRequestsArr.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { error } = await supabase
      .from("conversation_requests")
      .update({ is_read: true })
      .in("id", conversationRequestsArr);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.send({ data: "done", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "readConversationRequestsMarked Error",
      error: error,
    });

    return res.status(500).send({
      data: null,
      error: "Failed to read conversation requests marked",
    });
  }
});

app.post("/deleteConversationRequest", authUser, async (req, res) => {
  const { conversationRequestId } = req.body;
  if (!conversationRequestId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    await supabase
      .from("conversation_requests")
      .update({
        status: "deleted",
        updated_at: new Date(),
      })
      .eq("id", conversationRequestId);

    return res.send({ data: "done", error: null });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "updateConversationRequestStatus Error",
      error: error,
    });

    return res.status(500).send({
      data: null,
      error: "Failed to update conversation request status",
    });
  }
});

app.post("/acceptConversationRequest", authUser, async (req, res) => {
  const {
    conversationRequestId,
    type,
    sender_id,
    participant_ids,
    participant_setting,
    message,
  } = req.body;
  if (
    !conversationRequestId ||
    !type ||
    !sender_id ||
    !participant_ids ||
    !participant_setting ||
    !message
  ) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    await supabase
      .from("conversation_requests")
      .update({
        status: "accepted",
        updated_at: new Date(),
      })
      .eq("id", conversationRequestId);

    const { data } = await supabase.rpc(
      "create_conversations_and_messages_v2",
      {
        type,
        sender_id,
        data: { participant_ids, participant_setting, message },
      },
    );

    return res.send({ data: data });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "updateConversationRequestStatus Error",
      error: error,
    });

    return res.status(500).send({
      data: null,
      error: "Failed to update conversation request status",
    });
  }
});

app.post("/deleteBotMessage", authUser, async (req, res) => {
  const { created_at, conversation_id } = req.body;
  const batchSize = 5; // Define the batch size

  if (!created_at || !conversation_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    let deletedCount;
    do {
      const { data, error } = await supabase.rpc(
        "delete_messages_with_bot_batch",
        {
          conversation_id_arg: conversation_id,
          created_at_arg: created_at,
          batch_size: batchSize,
        },
      );

      if (error) {
        throw wrappedSupabaseError(error);
      }

      // The data returned will contain the count of deleted rows
      deletedCount = data;
    } while (deletedCount === batchSize); // Continue if there are more records to delete

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "deleteBotMessage Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to delete bot's message" });
  }
});

app.patch("/updateInstantReply", authUser, async (req, res) => {
  const { instantReply, conversationId, selectedProfileId, sendeeProfileId } =
    req.body;

  if (!conversationId || !selectedProfileId || !sendeeProfileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversationId,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({ instant_reply: !!instantReply })
      .eq("conversation_id", conversationId)
      .or(
        `profile_id.eq.${sendeeProfileId},profile_id.eq.${selectedProfileId}`,
      );

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "updateInstantReply Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to update instant reply" });
  }
});

app.patch("/updateChatMode", authUser, async (req, res) => {
  const { mode, conversationId, selectedProfileId, sendeeProfileId } = req.body;

  if (!mode || !conversationId || !selectedProfileId || !sendeeProfileId) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversationId,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({ chat_mode: mode })
      .eq("conversation_id", conversationId)
      .or(
        `profile_id.eq.${sendeeProfileId},profile_id.eq.${selectedProfileId}`,
      );

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "updateChatMode Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to update chat mode" });
  }
});

app.post("/deleteConversation", authUser, async (req, res) => {
  const { conversationId } = req.body;
  const id = Number(conversationId);

  if (!conversationId || !id || isNaN(id) || id < 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversationId,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }
  try {
    const { error } = await supabase.rpc(
      "delete_conversation_with_participants",
      {
        conv_id: id,
      },
    );
    if (error) {
      throw wrappedSupabaseError(
        error,
        "failed to rpc.delete_conversation_with_id",
      );
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "deleteConversation Error",
      error,
      conversationId,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to delete conversation" });
  }
});

app.post("/deleteMessage", authUser, async (req, res) => {
  const { conversation_id, created_at } = req.body;

  if (!conversation_id || !created_at) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("messages")
      .delete()
      .eq("conversation_id", conversation_id)
      .eq("created_at", created_at);
    if (error) throw wrappedSupabaseError(error);

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "deleteMessage Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to delete message" });
  }
});

app.post("/deleteMemories", authUser, async (req, res) => {
  const { memory_id } = req.body;

  if (!memory_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    // validate if auth user has this embedding
    const { error } = await supabase
      .from("conversation_embeddings")
      .select(
        "profiles!public_conversation_embeddings_user_profile_id_fkey(user_id)",
      )
      .eq("id", memory_id)
      .eq("profiles.user_id", req.user?.id)
      .single();
    if (error) {
      throw wrappedSupabaseError(error);
    }
  } catch (error) {
    if (error.code === "PGRST116") {
      // memory as already been deleted
      return res.sendStatus(204);
    }
    logError({
      executionId: req.executionId,
      context: `Validation for deleteMemories Error: userId: ${req.user?.id}, memoryId: ${memory_id}`,
      error: error,
    });

    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_embeddings")
      .delete()
      .eq("id", memory_id);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "deleteMemories Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to delete memory" });
  }
});

app.post("/resetTypingIndicate", authUser, async (req, res) => {
  const { conversation_id, sender_id } = req.body;

  if (!conversation_id || !sender_id) {
    return res.sendStatus(400);
  }

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({
        typing: false,
      })
      .eq("conversation_id", conversation_id)
      .not("profile_id", "eq", sender_id);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "typeIndicate Update Error",
      error: error,
    });

    return res.sendStatus(500);
  }
});

app.post("/createConversationRequests", authUser, async (req, res) => {
  const { requester_id, requestee_id, init_message } = req.body;

  if (!requester_id || !requestee_id || !init_message) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, requester_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase.from("conversation_requests").insert({
      requester_id,
      requestee_id,
      status: "pending",
      init_message,
    });

    if (error) throw wrappedSupabaseError(error);
    return res.sendStatus(200);
  } catch (error) {
    if (error.code === PostgresErrorCode.UNIQUE_VIOLATION) {
      logWarn({
        executionId: req.executionId,
        context: "Insert conversation request Duplicate",
        message: `Duplicate conversation request - requester_id: ${requester_id}, requestee_id: ${requestee_id}`,
      });
      return res.sendStatus(204);
    }
    logError({
      executionId: req.executionId,
      context: "Create Conversation Requests Error",
      error: error,
    });

    return res.sendStatus(500);
  }
});

app.patch("/editMessage", authUser, async (req, res) => {
  const { conversation_id, created_at, updated_text } = req.body;

  if (!conversation_id || !created_at) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("messages")
      .update({
        body: updated_text,
      })
      .eq("created_at", created_at);
    if (error) throw wrappedSupabaseError(error);

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "editMessage Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to edit message" });
  }
});

app.post("/likeAction", authUser, async (req, res) => {
  const { conversation_id, message_id, metadata, notification_data } = req.body;

  if (!conversation_id || !message_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("messages")
      .update({
        metadata,
      })
      .eq("id", message_id);
    if (error) throw wrappedSupabaseError(error);

    // insert notification for like action
    if (notification_data) {
      try {
        const { error } = await supabase
          .from("notifications")
          .insert(notification_data);

        if (error) {
          const notificationError = wrappedSupabaseError(error);
          throw notificationError;
        }
      } catch (notificationError) {
        throw wrappedSupabaseError(notificationError);
      }
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "likeAction Error",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to do like action" });
  }
});

app.patch("/deleteUserConversation", authUser, async (req, res) => {
  const { conversation_id, last_message_id } = req.body;
  const user_id = req.user?.id;

  if (!conversation_id || !last_message_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { data: profiles, error: profileError } = await supabase
    .from("profiles")
    .select("id")
    .eq("user_id", user_id)
    .neq("visibility", "archived");
  if (profileError) {
    const error = wrappedSupabaseError(profileError);
    logError({
      context: "Failed to fetch profiles from userId",
      error,
    });
    return res.sendStatus(500);
  }
  const idsArray = profiles.map((obj) => obj.id);

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({
        first_message_id: last_message_id + 1,
      })
      .eq("conversation_id", conversation_id)
      .in("profile_id", idsArray);
    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Failed to delete user conversations",
      error: error,
    });

    return res
      .status(500)
      .send({ data: null, error: "Failed to delete user conversations" });
  }
});

async function updateConversationParticipants(
  conversation_id,
  last_message_id,
  profileIds,
) {
  const { error } = await supabase
    .from("conversation_participants")
    .update({
      first_message_id: last_message_id + 1,
    })
    .eq("conversation_id", conversation_id)
    .in("profile_id", profileIds);
  if (error) {
    throw wrappedSupabaseError(error);
  }
}

app.patch("/deleteUserConversations", authUser, async (req, res) => {
  const { records } = req.body;
  const user_id = req.user?.id;

  if (!records || !Array.isArray(records) || records.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const isValidRecords = records.every((record) => {
    return record?.conversation_id && record?.last_message_id;
  });

  if (!isValidRecords) {
    return res.status(400).send({
      data: null,
      error: "Each record must contain conversation_id and last_message_id",
    });
  }

  const validationResults = await Promise.all(
    records.map((record) =>
      validateOwnConversation(user_id, record.conversation_id),
    ),
  );

  // Check if any of the validations failed
  const hasInvalidConversation = validationResults.includes(false);
  if (hasInvalidConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { data: profiles, error: profileError } = await supabase
    .from("profiles")
    .select("id")
    .neq("visibility", "archived")
    .eq("user_id", user_id);
  if (profileError) {
    const error = wrappedSupabaseError(profileError);
    logError({
      context: "deleteUserConversations: Failed to fetch profiles from user_id",
      error,
    });
    return res.sendStatus(500);
  }
  const idsArray = profiles.map((obj) => obj.id);

  try {
    await Promise.all(
      records.map(async (record) => {
        return updateConversationParticipants(
          record?.conversation_id,
          record?.last_message_id,
          idsArray,
        );
      }),
    );

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context:
        "deleteUserConversations: Failed to delete multiple user conversations",
      error: error,
    });

    return res.status(500).send({
      data: null,
      error:
        "deleteUserConversations: Failed to delete multiple user conversations",
    });
  }
});

app.post("/deleteConversations", authUser, async (req, res) => {
  const { conversationIds } = req.body;

  if (!conversationIds || conversationIds.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { error: deleteParticipantsError } = await supabase
      .from("conversation_participants")
      .delete()
      .in("conversation_id", conversationIds);
    if (deleteParticipantsError) {
      throw wrappedSupabaseError(deleteParticipantsError);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "Delete conversations Failed Error",
      error: error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Delete conversations failed" });
  }
});

app.post("/contentFilter", authUser, async (req, res) => {
  const { conversation_id, sendee_id, value } = req.body;

  if (!conversation_id || !sendee_id || typeof value !== "boolean") {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const { data: profileAge, error: ageError } = await supabase
    .from("profiles")
    .select("age")
    .eq("id", sendee_id)
    .single();

  if (ageError) {
    const error = wrappedSupabaseError(ageError);
    logError({
      context: "Failed to fetch age from sendee_id",
      error,
    });
    return res.status(500).send({ error: "Failed to fetch age" });
  }

  const nsfw = profileAge?.age === "child" ? false : value;

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({ nsfw })
      .eq("conversation_id", conversation_id)
      .eq("profile_id", sendee_id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "updateChatMode Error",
      error: error,
    });

    return res.status(500).send({ error: "Failed to update content filter" });
  }
});

app.post("/chatLength", authUser, async (req, res) => {
  const { conversation_id, sendee_id, value } = req.body;

  const validValues = ["short", "medium", "long", "balanced"];
  if (
    !conversation_id ||
    !sendee_id ||
    !value ||
    !validValues.includes(value)
  ) {
    return res.status(400).json({ data: null, error: "Invalid input error" });
  }

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({ chat_length: value })
      .eq("conversation_id", conversation_id)
      .eq("profile_id", sendee_id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "updateChatLength Error",
      error: error,
    });

    return res.status(500).send({ error: "Failed to update chat length" });
  }
});

app.get("/sentRequests", authUser, async (req, res) => {
  const { profileId } = req.query;
  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_requests")
      .select(
        "id, created_at, updated_at, init_message, requestee_id, profiles:requestee_id(display_name, avatar_url, username, nsfw)",
      )
      .eq("requester_id", profileId)
      .neq("profiles.visibility", "archived")
      .neq("profiles.visibility", "hidden")
      .neq("status", "accepted");

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch the sent conversation requests",
      error,
      profileId,
    });
    return res.sendStatus(500);
  }
});

app.get("/receivedRequests", authUser, async (req, res) => {
  const { profileId } = req.query;
  if (!profileId) {
    return res.status(400).json({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { data: requests, error: requestError } = await supabase
      .from("conversation_requests")
      .select(
        `*,
        requester_profile:requester_id (
          id,
          username,
          avatar_url
        )`,
      )
      .eq("requestee_id", profileId)
      .eq("status", "pending")
      .neq("requester_profile.visibility", "archived")
      .neq("requester_profile.visibility", "hidden")
      .not("requester_profile", "is", null)
      .order("updated_at", { ascending: false });

    if (requestError) throw wrappedSupabaseError(requestError);

    if (!requests || requests.length === 0) {
      return res.json({ data: [], error: null });
    }

    // Extract requester IDs from the requests
    const requesterIds = requests.map(
      (request) => request.requester_profile.id,
    );

    // Fetch followers of the profileId who are also requesters
    const { data: followers } = await supabase
      .from("followers")
      .select("follower_id")
      .eq("following_id", profileId)
      .in("follower_id", requesterIds);

    const followerIds = new Set(
      (followers || []).map((follower) => follower.follower_id),
    );

    // Filter out requests where the requester is also a follower
    const filteredRequests = requests.filter(
      (request) => !followerIds.has(request.requester_profile.id),
    );

    return res.json({ data: filteredRequests, error: null });
  } catch (error) {
    logError({
      context: "Failed to fetch the received conversation requests",
      error,
      profileId,
    });
    return res.sendStatus(500);
  }
});

app.get("/conversationParticipantLastMessage", authUser, async (req, res) => {
  const { profileId } = req.query;
  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_participants_with_last_message")
      .select("*")
      .eq("profile_id", profileId)
      .order("last_message_id", { ascending: false, nullsFirst: false });

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to conversationParticipantLastMessage",
      error,
      profileId,
    });
    return res.sendStatus(500);
  }
});

app.post("/getConversationParticipants", authUser, async (req, res) => {
  const { profile_id, conversation_id } = req.body;
  if (!profile_id || !conversation_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_participants_with_last_message")
      .select("*")
      .eq("profile_id", profile_id)
      .eq("conversation_id", conversation_id)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to getConversationParticipants",
      error,
      profile_id,
      conversation_id,
    });
    return res.sendStatus(500);
  }
});

app.post("/getConversationParticipant", authUser, async (req, res) => {
  const { profile_id, conversation_id } = req.body;
  if (!profile_id || !conversation_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isOwnConversation = await validateOwnConversation(
    user_id,
    conversation_id,
  );
  if (!isOwnConversation) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_participants_with_last_message")
      .select("*")
      .eq("profile_id", profile_id)
      .eq("conversation_id", conversation_id)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to getConversationParticipant",
      error,
      profile_id,
      conversation_id,
    });
    return res.sendStatus(500);
  }
});

app.post("/getLastMessage", authUser, async (req, res) => {
  const { last_message_id } = req.body;
  if (!last_message_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("messages_by_slug")
      .select("*")
      .eq("id", last_message_id)
      .maybeSingle();

    if (!data) {
      return res.sendStatus(404);
    }

    if (error) throw wrappedSupabaseError(error);

    const isOwnConversation = await validateOwnConversation(
      user_id,
      data?.conversation_id,
    );
    if (!isOwnConversation) {
      return res.status(403).send({ error: "Forbidden" });
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to getMessagesBySlug",
      error,
    });
    return res.sendStatus(500);
  }
});

app.post("/getConversationRequestDetail", authUser, async (req, res) => {
  const { request_id } = req.body;
  if (!request_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_requests")
      .select(
        `*,
        requester_profile:requester_id (
          id,
          username,
          avatar_url
        )`,
      )
      .eq("id", request_id)
      .neq("requester_profile.visibility", "archived")
      .neq("requester_profile.visibility", "hidden")
      .not("requester_profile", "is", null)
      .single();

    if (error) throw wrappedSupabaseError(error);

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "Failed to getConversationRequestDetail",
      error,
    });
    return res.sendStatus(500);
  }
});

app.post("/removeConversationRequest", authUser, async (req, res) => {
  const { request_id, requester_id } = req.body;
  if (!request_id || !requester_id) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, requester_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_requests")
      .delete()
      .eq("id", request_id)
      .eq("requester_id", requester_id);

    if (error) throw wrappedSupabaseError(error);

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Failed to remove conversation_request",
      error,
      request_id,
      requester_id,
    });
    return res.sendStatus(500);
  }
});

app.post("/editConversationMemory", authUser, async (req, res) => {
  const { id, summary, profile_id } = req.body;
  if (!id || !summary || !profile_id) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_embeddings")
      .update({ summary })
      .eq("id", id)
      .eq("user_profile_id", profile_id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "**** /editConversationMemory: update error",
      error,
      id,
      profile_id,
    });
    return res.status(500).json({ error: "Failed to edit memory" });
  }
});

app.post("/deleteConversationMemories", authUser, async (req, res) => {
  const { ids, profile_id } = req.body;
  if (!ids || ids.length === 0 || !profile_id) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, profile_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_embeddings")
      .delete()
      .eq("user_profile_id", profile_id)
      .in("id", ids);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "**** /deleteConversationMemories: delete error",
      error,
      ids: JSON.stringify(ids),
      profile_id,
    });
    return res.status(500).json({ error: "Failed to delete memory" });
  }
});

app.post("/fetchConversationSetting", authUser, async (req, res) => {
  const { conversation_id, profile_id } = req.body;
  if (!conversation_id || !profile_id) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_participants")
      .select("*")
      .eq("conversation_id", conversation_id)
      .eq("profile_id", profile_id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json(data);
  } catch (error) {
    logError({
      context: "/fetchConversationSetting error",
      error,
      conversation_id,
      profile_id,
    });
    return res
      .status(500)
      .json({ error: "Failed to fetch conversation settings" });
  }
});

app.post("/updateConversationPrompt", authUser, async (req, res) => {
  const { id, prompt_content } = req.body;
  if (!id || !prompt_content) {
    return res.status(400).json({ error: "Missing required parameters" });
  }

  try {
    const { error } = await supabase
      .from("conversation_prompts")
      .update(prompt_content)
      .eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "/updateConversationPrompt error",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to update conversation_prompts" });
  }
});

// ADMIN : Update LLM & ChatMode for conversation_participants
app.post("/updateLLMAndChatmodeAdmin", authUser, async (req, res) => {
  const { id, llm, chat_mode } = req.body;

  if (!id || !llm || !chat_mode) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { error } = await supabase
      .from("conversation_participants")
      .update({
        llm,
        chat_mode,
      })
      .eq("conversation_id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "updateLLMAndChatmodeAdmin Error",
      error: error,
    });

    return res.status(500).send({
      data: null,
      error: "Failed to update llm and chat_mode in admin",
    });
  }
});

module.exports = {
  app,
};
