const { wrappedSupabaseError, supabase } = require("../../supabaseClient");
const { logError } = require("../../utils");

async function respondWithTextMessage(
  ctx,
  /*      ids= */ { conversationId, messageGroupingId },
  /* profiles= */ { botProfile },
  /* metadata= */ { branchIndex },
  /*  message= */ { text },
) {
  ctx.startOperation("database");
  const { error: insertError } = await supabase
    .from("messages")
    .insert({
      conversation_id: conversationId,
      body: text,
      is_bot: true,
      sender_id: botProfile.id,
      branch_index: branchIndex,
      is_system_message: false,
      is_proactive: false,
      message_grouping_id: messageGroupingId,
    })
    .select();
  if (insertError) {
    const error = wrappedSupabaseError(
      insertError,
      "Failed to insert bot message",
    );
    logError({
      ...ctx.logging,
      msg: "failed to insert bot message",
      error,
    });
    throw error;
  }
  ctx.endOperation("database");
}

module.exports = {
  respondWithTextMessage,
};
