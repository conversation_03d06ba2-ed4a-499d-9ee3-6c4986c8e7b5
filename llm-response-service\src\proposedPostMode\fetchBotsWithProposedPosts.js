const { logError } = require("../logUtils");
const { wrappedSupabaseError, supabase } = require("../supabaseClient");

async function fetchBotsWithProposedPosts({ userProfileId }) {
  // bad query, going full dumb here
  const { data: bots, error } = await supabase
    .from("bots")
    .select(
      `
        id,
        profile_id,
        profiles!bots_profile_id_fkey (
          id,
          username,
          avatar_url,
          proposed_post_mode,
          display_name,
          posts (
            id,
            description,
            ai_caption,
            location,
            tags,
            proposed_post_state,
            created_at
          )
        )
      `,
    )
    .eq("creator_id", userProfileId)
    .eq("profiles.proposed_post_mode", true)
    .not("profiles.visibility", "in", "(archived,hidden)")
    .not("profiles.posts.visibility", "in", "(hidden,archived)")
    .eq("profiles.posts.proposed_post_state", "proposed");

  if (error) {
    const wrappedError = wrappedSupabaseError(
      error,
      "failed to fetch bots with proposed posts",
    );
    logError({
      context: "fetchBotsWithProposedPosts",
      error: wrappedError,
      user_profile_id: userProfileId,
    });
    throw wrappedError;
  }

  const filtered = bots.filter((bot) => {
    return bot.profiles?.posts && bot.profiles?.posts.length > 0;
  });

  return filtered;
}

module.exports = {
  fetchBotsWithProposedPosts,
};
