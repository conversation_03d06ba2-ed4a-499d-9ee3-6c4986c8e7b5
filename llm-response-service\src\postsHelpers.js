const { supabase } = require("./supabaseClient");
const { generateEmbedding } = require("./utils");
const { generateComfyRequestForMultipleCharacters } = require("./comfy");

// Define a function to calculate time difference in minutes
const timeDifferenceInMinutes = (date1, date2) => {
  const diff = new Date(date2).getTime() - new Date(date1).getTime();
  return diff / (1000 * 60); // Convert milliseconds to minutes
};
async function generateEmbeddingForPost({ post: data, overwrite = false }) {
  const keysToExtract = ["location", "ai_caption", "tags", "description"];

  // Filter the data object to include only the specified keys
  const filteredEmbedInput = keysToExtract.reduce((acc, key) => {
    if (data[key] !== null && data[key] !== undefined) {
      acc[key] = data[key];
    }
    return acc;
  }, {});

  const embedding = await generateEmbedding(JSON.stringify(filteredEmbedInput));

  await supabase.from("posts").update({ embedding }).eq("id", data.id);

  return embedding;
}

async function generateMultipleCharactersImage({
  bot_1_id,
  bot_2_id,
  bot_1_action,
  bot_2_action,
  background,
  ...otherParams
}) {
  const { data: bot_1, error: bot1Error } = await supabase
    .from("bots")
    .select("*")
    .eq("id", bot_1_id)
    .single();

  if (bot1Error) {
    // no op
  }

  const { data: bot_2, error: bot2Error } = await supabase
    .from("bots")
    .select("*")
    .eq("id", bot_2_id)
    .single();

  if (bot2Error) {
    // no op
  }

  const task = await generateComfyRequestForMultipleCharacters({
    bot_1,
    bot_2,
    bot_1_action,
    bot_2_action,
    background,
    ...otherParams,
  });

  return task;
}

function processComments(comments) {
  // Helper function to find top-level parent for a comment
  function findTopLevelParent(comment) {
    let parent = comments.find((c) => c.id === comment.reply_to_id);
    while (parent && parent.reply_to_id) {
      parent = comments.find((c) => c.id === parent.reply_to_id);
    }
    return parent;
  }

  const topLevelComments = comments
    .filter((comment) => !comment.reply_to_id)
    .map((comment) => ({ ...comment, replies: [] }));

  comments
    .filter((comment) => comment.reply_to_id)
    .forEach((comment) => {
      const topLevelParent = findTopLevelParent(comment);
      if (topLevelParent) {
        const parentStructure = topLevelComments.find(
          (tc) => tc.id === topLevelParent.id,
        );
        if (parentStructure) {
          parentStructure.replies.push(comment);
        }
      }
    });

  return topLevelComments;
}

module.exports = {
  generateEmbeddingForPost,
  timeDifferenceInMinutes,
  generateMultipleCharactersImage,
  processComments,
};
