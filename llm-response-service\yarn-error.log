Arguments: 
  /opt/homebrew/Cellar/node/20.5.1/bin/node /opt/homebrew/Cellar/yarn/1.22.18/libexec/bin/yarn.js add openai

PATH: 
  /Users/<USER>/.rvm/gems/ruby-3.1.2/bin:/Users/<USER>/.rvm/gems/ruby-3.1.2@global/bin:/Users/<USER>/.rvm/rubies/ruby-3.1.2/bin:/Users/<USER>/Desktop/google-cloud-sdk/bin:/Users/<USER>/.rbenv/shims:/Users/<USER>/.rbenv/bin:/Users/<USER>/opt/miniconda3/bin:/Users/<USER>/opt/miniconda3/condabin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Apple/usr/bin://Applications/Topaz Photo AI.app/Contents/Resources/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/.yarn/bin:/Users/<USER>/Library/Android/sdk/emulator:/Users/<USER>/Library/Android/sdk/tools:/Users/<USER>/Library/Android/sdk/tools/bin:/Users/<USER>/Library/Android/sdk/platform-tools:/Users/<USER>/.rvm/bin

Yarn version: 
  1.22.18

Node version: 
  20.5.1

Platform: 
  darwin arm64

Trace: 
  SyntaxError: /Users/<USER>/Documents/butterflies/llm-response-service/package.json: Expected double-quoted property name in JSON at position 447
      at JSON.parse (<anonymous>)
      at /opt/homebrew/Cellar/yarn/1.22.18/libexec/lib/cli.js:1625:59
      at Generator.next (<anonymous>)
      at step (/opt/homebrew/Cellar/yarn/1.22.18/libexec/lib/cli.js:310:30)
      at /opt/homebrew/Cellar/yarn/1.22.18/libexec/lib/cli.js:321:13

npm manifest: 
  {
    "name": "image-generation-service",
    "version": "1.0.0",
    "description": "",
    "main": "index.js",
    "scripts": {
      "test": "echo \"Error: no test specified\" && exit 1",
      "start": "npx functions-framework --target=generateText [--signature-type=http]"
    },
    "author": "",
    "license": "ISC",
    "dependencies": {
      "@google-cloud/functions-framework": "^3.3.0",
      "@supabase/supabase-js": "^2.37.0",
      "node-fetch": "^2.6.12",
    },
    "type": "module"
  }

yarn manifest: 
  No manifest

Lockfile: 
  # THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
  # yarn lockfile v1
  
  
  "@babel/code-frame@^7.0.0":
    version "7.22.5"
    resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.5.tgz"
    integrity sha512-Xmwn266vad+6DAqEB2A6V/CcZVp62BbwVmcOJc2RPuwih1kw02TjQvWVWlcKGbBPd+8/0V5DEkOcizRGYsspYQ==
    dependencies:
      "@babel/highlight" "^7.22.5"
  
  "@babel/helper-validator-identifier@^7.22.5":
    version "7.22.5"
    resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.5.tgz"
    integrity sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==
  
  "@babel/highlight@^7.22.5":
    version "7.22.5"
    resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.5.tgz"
    integrity sha512-BSKlD1hgnedS5XRnGOljZawtag7H1yPfQp0tdNJCHoH6AZ+Pcm9VvkrK59/Yy593Ypg0zMxH2BxD1VPYUQ7UIw==
    dependencies:
      "@babel/helper-validator-identifier" "^7.22.5"
      chalk "^2.0.0"
      js-tokens "^4.0.0"
  
  "@google-cloud/functions-framework@^3.3.0":
    version "3.3.0"
    resolved "https://registry.npmjs.org/@google-cloud/functions-framework/-/functions-framework-3.3.0.tgz"
    integrity sha512-+4O1dX5VNRK1W1NyAia7zy5jLf88ytuz39/1kVUUaNiOf76YbMZKV0YjZwfk7uEwRrC6l2wynK1G+q8Gb5DeVw==
    dependencies:
      "@types/express" "4.17.17"
      body-parser "^1.18.3"
      cloudevents "^7.0.0"
      express "^4.16.4"
      minimist "^1.2.7"
      on-finished "^2.3.0"
      read-pkg-up "^7.0.1"
      semver "^7.3.5"
  
  "@supabase/functions-js@^2.1.5":
    version "2.1.5"
    resolved "https://registry.yarnpkg.com/@supabase/functions-js/-/functions-js-2.1.5.tgz#ed1b85f499dfda21d40fe39b86ab923117cb572b"
    integrity sha512-BNzC5XhCzzCaggJ8s53DP+WeHHGT/NfTsx2wUSSGKR2/ikLFQTBCDzMvGz/PxYMqRko/LwncQtKXGOYp1PkPaw==
    dependencies:
      "@supabase/node-fetch" "^2.6.14"
  
  "@supabase/gotrue-js@^2.54.0":
    version "2.54.0"
    resolved "https://registry.yarnpkg.com/@supabase/gotrue-js/-/gotrue-js-2.54.0.tgz#e80756bf320ab46e2f9c12cfab63fc27619c6021"
    integrity sha512-JjtbchtPbpgK0O8NIMIvKLk7HHv0kd23L3UO5a398nczCcBkI0IvmbPtbS4Xs5AUIuJ+JHtV6siOZR1ha5EzQw==
    dependencies:
      "@supabase/node-fetch" "^2.6.14"
  
  "@supabase/node-fetch@^2.6.14":
    version "2.6.14"
    resolved "https://registry.yarnpkg.com/@supabase/node-fetch/-/node-fetch-2.6.14.tgz#6a3e2924e3de8aeeb82c193c786ffb25da9af23f"
    integrity sha512-w/Tsd22e/5fAeoxqQ4P2MX6EyF+iM6rc9kmlMVFkHuG0rAltt2TLhFbDJfemnHbtvnazWaRfy5KnFU/SYT37dQ==
    dependencies:
      whatwg-url "^5.0.0"
  
  "@supabase/postgrest-js@^1.8.4":
    version "1.8.4"
    resolved "https://registry.yarnpkg.com/@supabase/postgrest-js/-/postgrest-js-1.8.4.tgz#89e8355503979ad25e7340b910d17704507ab325"
    integrity sha512-ELjpvhb04wILUiJz9zIsTSwaz9LQNlX+Ig5/LgXQ7k68qQI6NqHVn+ISRNt53DngUIyOnLHjeqqIRHBZ7zpgGA==
    dependencies:
      "@supabase/node-fetch" "^2.6.14"
  
  "@supabase/realtime-js@^2.8.0":
    version "2.8.0"
    resolved "https://registry.yarnpkg.com/@supabase/realtime-js/-/realtime-js-2.8.0.tgz#061071d0726554de4f20db928ed5fb6a0fed8243"
    integrity sha512-j1OP2nRJhqLNEoYSMkIl1+cHK/Ow9fektemazkF2CvrIrmwgfJJGaFGiWGVgwoKtwVcrdknSsYWpxs90hys1EQ==
    dependencies:
      "@supabase/node-fetch" "^2.6.14"
      "@types/phoenix" "^1.5.4"
      "@types/websocket" "^1.0.3"
      websocket "^1.0.34"
  
  "@supabase/storage-js@^2.5.4":
    version "2.5.4"
    resolved "https://registry.yarnpkg.com/@supabase/storage-js/-/storage-js-2.5.4.tgz#15946fa03574e94cdeff2b7fa2cd5b85880239f5"
    integrity sha512-yspHD19I9uQUgfTh0J94+/r/g6hnhdQmw6Y7OWqr/EbnL6uvicGV1i1UDkkmeUHqfF9Mbt2sLtuxRycYyKv2ew==
    dependencies:
      "@supabase/node-fetch" "^2.6.14"
  
  "@supabase/supabase-js@^2.37.0":
    version "2.37.0"
    resolved "https://registry.yarnpkg.com/@supabase/supabase-js/-/supabase-js-2.37.0.tgz#3f56c3f6e2ec381162fb5fedb2245d3221d5f98a"
    integrity sha512-kWXVvGWAkThQodHh3yaSQoNHNDm5bwp+H6f1BfC4tr0k096zzTb3ACMnZLQBS0qOXEEbuAnGxIWUv+RE8GaIhg==
    dependencies:
      "@supabase/functions-js" "^2.1.5"
      "@supabase/gotrue-js" "^2.54.0"
      "@supabase/node-fetch" "^2.6.14"
      "@supabase/postgrest-js" "^1.8.4"
      "@supabase/realtime-js" "^2.8.0"
      "@supabase/storage-js" "^2.5.4"
  
  "@types/body-parser@*":
    version "1.19.2"
    resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz"
    integrity sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==
    dependencies:
      "@types/connect" "*"
      "@types/node" "*"
  
  "@types/connect@*":
    version "3.4.35"
    resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz"
    integrity sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==
    dependencies:
      "@types/node" "*"
  
  "@types/express-serve-static-core@^4.17.33":
    version "4.17.35"
    resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.35.tgz"
    integrity sha512-wALWQwrgiB2AWTT91CB62b6Yt0sNHpznUXeZEcnPU3DRdlDIz74x8Qg1UUYKSVFi+va5vKOLYRBI1bRKiLLKIg==
    dependencies:
      "@types/node" "*"
      "@types/qs" "*"
      "@types/range-parser" "*"
      "@types/send" "*"
  
  "@types/express@4.17.17":
    version "4.17.17"
    resolved "https://registry.npmjs.org/@types/express/-/express-4.17.17.tgz"
    integrity sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q==
    dependencies:
      "@types/body-parser" "*"
      "@types/express-serve-static-core" "^4.17.33"
      "@types/qs" "*"
      "@types/serve-static" "*"
  
  "@types/http-errors@*":
    version "2.0.1"
    resolved "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.1.tgz"
    integrity sha512-/K3ds8TRAfBvi5vfjuz8y6+GiAYBZ0x4tXv1Av6CWBWn0IlADc+ZX9pMq7oU0fNQPnBwIZl3rmeLp6SBApbxSQ==
  
  "@types/mime@*":
    version "3.0.1"
    resolved "https://registry.npmjs.org/@types/mime/-/mime-3.0.1.tgz"
    integrity sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==
  
  "@types/mime@^1":
    version "1.3.2"
    resolved "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz"
    integrity sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw==
  
  "@types/node@*":
    version "20.4.5"
    resolved "https://registry.npmjs.org/@types/node/-/node-20.4.5.tgz"
    integrity sha512-rt40Nk13II9JwQBdeYqmbn2Q6IVTA5uPhvSO+JVqdXw/6/4glI6oR9ezty/A9Hg5u7JH4OmYmuQ+XvjKm0Datg==
  
  "@types/normalize-package-data@^2.4.0":
    version "2.4.1"
    resolved "https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz"
    integrity sha512-Gj7cI7z+98M282Tqmp2K5EIsoouUEzbBJhQQzDE3jSIRk6r9gsz0oUokqIUR4u1R3dMHo0pDHM7sNOHyhulypw==
  
  "@types/phoenix@^1.5.4":
    version "1.6.2"
    resolved "https://registry.yarnpkg.com/@types/phoenix/-/phoenix-1.6.2.tgz#9a3838b6f45c895b0d7d9072025abb7525933a86"
    integrity sha512-I3mm7x5XIi+5NsIY3nfreY+H4PmQdyBwJ84SiUSOxSg1axwEPNmkKWYVm56y+emDpPPUL3cPzrLcgRWSd9gI7g==
  
  "@types/qs@*":
    version "6.9.7"
    resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz"
    integrity sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==
  
  "@types/range-parser@*":
    version "1.2.4"
    resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz"
    integrity sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==
  
  "@types/send@*":
    version "0.17.1"
    resolved "https://registry.npmjs.org/@types/send/-/send-0.17.1.tgz"
    integrity sha512-Cwo8LE/0rnvX7kIIa3QHCkcuF21c05Ayb0ZfxPiv0W8VRiZiNW/WuRupHKpqqGVGf7SUA44QSOUKaEd9lIrd/Q==
    dependencies:
      "@types/mime" "^1"
      "@types/node" "*"
  
  "@types/serve-static@*":
    version "1.15.2"
    resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.2.tgz"
    integrity sha512-J2LqtvFYCzaj8pVYKw8klQXrLLk7TBZmQ4ShlcdkELFKGwGMfevMLneMMRkMgZxotOD9wg497LpC7O8PcvAmfw==
    dependencies:
      "@types/http-errors" "*"
      "@types/mime" "*"
      "@types/node" "*"
  
  "@types/websocket@^1.0.3":
    version "1.0.7"
    resolved "https://registry.yarnpkg.com/@types/websocket/-/websocket-1.0.7.tgz#94ef83be9414db30c147d400cee08d5d767deeb0"
    integrity sha512-62Omr8U0PO+hgjLCpPnMsmjh2/FRwIGOktZHyYAUzooEJotwkXHMp7vCacdYi8haxBNOiw9bc2HIHI+b/MPNjA==
    dependencies:
      "@types/node" "*"
  
  accepts@~1.3.8:
    version "1.3.8"
    resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
    integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
    dependencies:
      mime-types "~2.1.34"
      negotiator "0.6.3"
  
  ajv-formats@^2.1.1:
    version "2.1.1"
    resolved "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
    integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
    dependencies:
      ajv "^8.0.0"
  
  ajv@^8.0.0, ajv@^8.11.0:
    version "8.12.0"
    resolved "https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz"
    integrity sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==
    dependencies:
      fast-deep-equal "^3.1.1"
      json-schema-traverse "^1.0.0"
      require-from-string "^2.0.2"
      uri-js "^4.2.2"
  
  ansi-styles@^3.2.1:
    version "3.2.1"
    resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
    integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
    dependencies:
      color-convert "^1.9.0"
  
  array-flatten@1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
    integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==
  
  available-typed-arrays@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
    integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==
  
  bignumber.js@^9.0.0:
    version "9.1.1"
    resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.1.1.tgz"
    integrity sha512-pHm4LsMJ6lzgNGVfZHjMoO8sdoRhOzOH4MLmY65Jg70bpxCKu5iOHNJyfF6OyvYw7t8Fpf35RuzUyqnQsj8Vig==
  
  body-parser@1.20.1:
    version "1.20.1"
    resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz"
    integrity sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==
    dependencies:
      bytes "3.1.2"
      content-type "~1.0.4"
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      on-finished "2.4.1"
      qs "6.11.0"
      raw-body "2.5.1"
      type-is "~1.6.18"
      unpipe "1.0.0"
  
  body-parser@^1.18.3:
    version "1.20.2"
    resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz"
    integrity sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==
    dependencies:
      bytes "3.1.2"
      content-type "~1.0.5"
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      on-finished "2.4.1"
      qs "6.11.0"
      raw-body "2.5.2"
      type-is "~1.6.18"
      unpipe "1.0.0"
  
  bufferutil@^4.0.1:
    version "4.0.7"
    resolved "https://registry.yarnpkg.com/bufferutil/-/bufferutil-4.0.7.tgz#60c0d19ba2c992dd8273d3f73772ffc894c153ad"
    integrity sha512-kukuqc39WOHtdxtw4UScxF/WVnMFVSQVKhtx3AjZJzhd0RGZZldcrfSEbVsWWe6KNH253574cq5F+wpv0G9pJw==
    dependencies:
      node-gyp-build "^4.3.0"
  
  bytes@3.1.2:
    version "3.1.2"
    resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
    integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==
  
  call-bind@^1.0.0, call-bind@^1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz"
    integrity sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==
    dependencies:
      function-bind "^1.1.1"
      get-intrinsic "^1.0.2"
  
  chalk@^2.0.0:
    version "2.4.2"
    resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
    integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
    dependencies:
      ansi-styles "^3.2.1"
      escape-string-regexp "^1.0.5"
      supports-color "^5.3.0"
  
  cloudevents@^7.0.0:
    version "7.0.2"
    resolved "https://registry.npmjs.org/cloudevents/-/cloudevents-7.0.2.tgz"
    integrity sha512-WiOqWsNkMZmMMZ6xa3kzx/MA+8+V+c5eGkStZIcik+Px2xCobmzcacw1EOGyfhODaQKkIv8TxXOOLzV69oXFqA==
    dependencies:
      ajv "^8.11.0"
      ajv-formats "^2.1.1"
      json-bigint "^1.0.0"
      process "^0.11.10"
      util "^0.12.4"
      uuid "^8.3.2"
  
  color-convert@^1.9.0:
    version "1.9.3"
    resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
    integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
    dependencies:
      color-name "1.1.3"
  
  color-name@1.1.3:
    version "1.1.3"
    resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
    integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==
  
  content-disposition@0.5.4:
    version "0.5.4"
    resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
    integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
    dependencies:
      safe-buffer "5.2.1"
  
  content-type@~1.0.4, content-type@~1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
    integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==
  
  cookie-signature@1.0.6:
    version "1.0.6"
    resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
    integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==
  
  cookie@0.5.0:
    version "0.5.0"
    resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
    integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==
  
  d@1, d@^1.0.1:
    version "1.0.1"
    resolved "https://registry.yarnpkg.com/d/-/d-1.0.1.tgz#8698095372d58dbee346ffd0c7093f99f8f9eb5a"
    integrity sha512-m62ShEObQ39CfralilEQRjH6oAMtNCV1xJyEx5LpRYUVN+EviphDgUc/F3hnYbADmkiNs67Y+3ylmlG7Lnu+FA==
    dependencies:
      es5-ext "^0.10.50"
      type "^1.0.1"
  
  debug@2.6.9, debug@^2.2.0:
    version "2.6.9"
    resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
    integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
    dependencies:
      ms "2.0.0"
  
  depd@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
    integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==
  
  destroy@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
    integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==
  
  ee-first@1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
    integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==
  
  encodeurl@~1.0.2:
    version "1.0.2"
    resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
    integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==
  
  error-ex@^1.3.1:
    version "1.3.2"
    resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
    integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
    dependencies:
      is-arrayish "^0.2.1"
  
  es5-ext@^0.10.35, es5-ext@^0.10.50:
    version "0.10.62"
    resolved "https://registry.yarnpkg.com/es5-ext/-/es5-ext-0.10.62.tgz#5e6adc19a6da524bf3d1e02bbc8960e5eb49a9a5"
    integrity sha512-BHLqn0klhEpnOKSrzn/Xsz2UIW8j+cGmo9JLzr8BiUapV8hPL9+FliFqjwr9ngW7jWdnxv6eO+/LqyhJVqgrjA==
    dependencies:
      es6-iterator "^2.0.3"
      es6-symbol "^3.1.3"
      next-tick "^1.1.0"
  
  es6-iterator@^2.0.3:
    version "2.0.3"
    resolved "https://registry.yarnpkg.com/es6-iterator/-/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
    integrity sha512-zw4SRzoUkd+cl+ZoE15A9o1oQd920Bb0iOJMQkQhl3jNc03YqVjAhG7scf9C5KWRU/R13Orf588uCC6525o02g==
    dependencies:
      d "1"
      es5-ext "^0.10.35"
      es6-symbol "^3.1.1"
  
  es6-symbol@^3.1.1, es6-symbol@^3.1.3:
    version "3.1.3"
    resolved "https://registry.yarnpkg.com/es6-symbol/-/es6-symbol-3.1.3.tgz#bad5d3c1bcdac28269f4cb331e431c78ac705d18"
    integrity sha512-NJ6Yn3FuDinBaBRWl/q5X/s4koRHBrgKAu+yGI6JCBeiu3qrcbJhwT2GeR/EXVfylRk8dpQVJoLEFhK+Mu31NA==
    dependencies:
      d "^1.0.1"
      ext "^1.1.2"
  
  escape-html@~1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
    integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==
  
  escape-string-regexp@^1.0.5:
    version "1.0.5"
    resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
    integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==
  
  etag@~1.8.1:
    version "1.8.1"
    resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
    integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==
  
  express@^4.16.4:
    version "4.18.2"
    resolved "https://registry.npmjs.org/express/-/express-4.18.2.tgz"
    integrity sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==
    dependencies:
      accepts "~1.3.8"
      array-flatten "1.1.1"
      body-parser "1.20.1"
      content-disposition "0.5.4"
      content-type "~1.0.4"
      cookie "0.5.0"
      cookie-signature "1.0.6"
      debug "2.6.9"
      depd "2.0.0"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      finalhandler "1.2.0"
      fresh "0.5.2"
      http-errors "2.0.0"
      merge-descriptors "1.0.1"
      methods "~1.1.2"
      on-finished "2.4.1"
      parseurl "~1.3.3"
      path-to-regexp "0.1.7"
      proxy-addr "~2.0.7"
      qs "6.11.0"
      range-parser "~1.2.1"
      safe-buffer "5.2.1"
      send "0.18.0"
      serve-static "1.15.0"
      setprototypeof "1.2.0"
      statuses "2.0.1"
      type-is "~1.6.18"
      utils-merge "1.0.1"
      vary "~1.1.2"
  
  ext@^1.1.2:
    version "1.7.0"
    resolved "https://registry.yarnpkg.com/ext/-/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
    integrity sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==
    dependencies:
      type "^2.7.2"
  
  fast-deep-equal@^3.1.1:
    version "3.1.3"
    resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
    integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==
  
  finalhandler@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz"
    integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
    dependencies:
      debug "2.6.9"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      on-finished "2.4.1"
      parseurl "~1.3.3"
      statuses "2.0.1"
      unpipe "~1.0.0"
  
  find-up@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
    integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
    dependencies:
      locate-path "^5.0.0"
      path-exists "^4.0.0"
  
  for-each@^0.3.3:
    version "0.3.3"
    resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
    integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
    dependencies:
      is-callable "^1.1.3"
  
  forwarded@0.2.0:
    version "0.2.0"
    resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
    integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==
  
  fresh@0.5.2:
    version "0.5.2"
    resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
    integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==
  
  function-bind@^1.1.1:
    version "1.1.1"
    resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz"
    integrity sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==
  
  get-intrinsic@^1.0.2, get-intrinsic@^1.1.3:
    version "1.2.1"
    resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.1.tgz"
    integrity sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==
    dependencies:
      function-bind "^1.1.1"
      has "^1.0.3"
      has-proto "^1.0.1"
      has-symbols "^1.0.3"
  
  gopd@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
    integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
    dependencies:
      get-intrinsic "^1.1.3"
  
  has-flag@^3.0.0:
    version "3.0.0"
    resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
    integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==
  
  has-proto@^1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.1.tgz"
    integrity sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==
  
  has-symbols@^1.0.2, has-symbols@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
    integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==
  
  has-tostringtag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
    integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
    dependencies:
      has-symbols "^1.0.2"
  
  has@^1.0.3:
    version "1.0.3"
    resolved "https://registry.npmjs.org/has/-/has-1.0.3.tgz"
    integrity sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==
    dependencies:
      function-bind "^1.1.1"
  
  hosted-git-info@^2.1.4:
    version "2.8.9"
    resolved "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
    integrity sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==
  
  http-errors@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
    integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
    dependencies:
      depd "2.0.0"
      inherits "2.0.4"
      setprototypeof "1.2.0"
      statuses "2.0.1"
      toidentifier "1.0.1"
  
  iconv-lite@0.4.24:
    version "0.4.24"
    resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
    integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
    dependencies:
      safer-buffer ">= 2.1.2 < 3"
  
  inherits@2.0.4, inherits@^2.0.3:
    version "2.0.4"
    resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
    integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==
  
  ipaddr.js@1.9.1:
    version "1.9.1"
    resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
    integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==
  
  is-arguments@^1.0.4:
    version "1.1.1"
    resolved "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz"
    integrity sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==
    dependencies:
      call-bind "^1.0.2"
      has-tostringtag "^1.0.0"
  
  is-arrayish@^0.2.1:
    version "0.2.1"
    resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
    integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==
  
  is-callable@^1.1.3:
    version "1.2.7"
    resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
    integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==
  
  is-core-module@^2.11.0:
    version "2.12.1"
    resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.12.1.tgz"
    integrity sha512-Q4ZuBAe2FUsKtyQJoQHlvP8OvBERxO3jEmy1I7hcRXcJBGGHFh/aJBswbXuS9sgrDH2QUO8ilkwNPHvHMd8clg==
    dependencies:
      has "^1.0.3"
  
  is-generator-function@^1.0.7:
    version "1.0.10"
    resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz"
    integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
    dependencies:
      has-tostringtag "^1.0.0"
  
  is-typed-array@^1.1.3:
    version "1.1.12"
    resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz"
    integrity sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==
    dependencies:
      which-typed-array "^1.1.11"
  
  is-typedarray@^1.0.0:
    version "1.0.0"
    resolved "https://registry.yarnpkg.com/is-typedarray/-/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
    integrity sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==
  
  js-tokens@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
    integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==
  
  json-bigint@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz"
    integrity sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==
    dependencies:
      bignumber.js "^9.0.0"
  
  json-parse-even-better-errors@^2.3.0:
    version "2.3.1"
    resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
    integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==
  
  json-schema-traverse@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
    integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==
  
  lines-and-columns@^1.1.6:
    version "1.2.4"
    resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
    integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==
  
  locate-path@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
    integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
    dependencies:
      p-locate "^4.1.0"
  
  lru-cache@^6.0.0:
    version "6.0.0"
    resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
    integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
    dependencies:
      yallist "^4.0.0"
  
  media-typer@0.3.0:
    version "0.3.0"
    resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
    integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==
  
  merge-descriptors@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
    integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==
  
  methods@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
    integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==
  
  mime-db@1.52.0:
    version "1.52.0"
    resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
    integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==
  
  mime-types@~2.1.24, mime-types@~2.1.34:
    version "2.1.35"
    resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
    integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
    dependencies:
      mime-db "1.52.0"
  
  mime@1.6.0:
    version "1.6.0"
    resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
    integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==
  
  minimist@^1.2.7:
    version "1.2.8"
    resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
    integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==
  
  ms@2.0.0:
    version "2.0.0"
    resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
    integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==
  
  ms@2.1.3:
    version "2.1.3"
    resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
    integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==
  
  negotiator@0.6.3:
    version "0.6.3"
    resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
    integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==
  
  next-tick@^1.1.0:
    version "1.1.0"
    resolved "https://registry.yarnpkg.com/next-tick/-/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"
    integrity sha512-CXdUiJembsNjuToQvxayPZF9Vqht7hewsvy2sOWafLvi2awflj9mOC6bHIg50orX8IJvWKY9wYQ/zB2kogPslQ==
  
  node-fetch@^2.6.12:
    version "2.6.12"
    resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.12.tgz"
    integrity sha512-C/fGU2E8ToujUivIO0H+tpQ6HWo4eEmchoPIoXtxCrVghxdKq+QOHqEZW7tuP3KlV3bC8FRMO5nMCC7Zm1VP6g==
    dependencies:
      whatwg-url "^5.0.0"
  
  node-gyp-build@^4.3.0:
    version "4.6.1"
    resolved "https://registry.yarnpkg.com/node-gyp-build/-/node-gyp-build-4.6.1.tgz#24b6d075e5e391b8d5539d98c7fc5c210cac8a3e"
    integrity sha512-24vnklJmyRS8ViBNI8KbtK/r/DmXQMRiOMXTNz2nrTnAYUwjmEEbnnpB/+kt+yWRv73bPsSPRFddrcIbAxSiMQ==
  
  normalize-package-data@^2.5.0:
    version "2.5.0"
    resolved "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
    integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
    dependencies:
      hosted-git-info "^2.1.4"
      resolve "^1.10.0"
      semver "2 || 3 || 4 || 5"
      validate-npm-package-license "^3.0.1"
  
  object-inspect@^1.9.0:
    version "1.12.3"
    resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.3.tgz"
    integrity sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==
  
  on-finished@2.4.1, on-finished@^2.3.0:
    version "2.4.1"
    resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
    integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
    dependencies:
      ee-first "1.1.1"
  
  p-limit@^2.2.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
    integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
    dependencies:
      p-try "^2.0.0"
  
  p-locate@^4.1.0:
    version "4.1.0"
    resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
    integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
    dependencies:
      p-limit "^2.2.0"
  
  p-try@^2.0.0:
    version "2.2.0"
    resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
    integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==
  
  parse-json@^5.0.0:
    version "5.2.0"
    resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
    integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
    dependencies:
      "@babel/code-frame" "^7.0.0"
      error-ex "^1.3.1"
      json-parse-even-better-errors "^2.3.0"
      lines-and-columns "^1.1.6"
  
  parseurl@~1.3.3:
    version "1.3.3"
    resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
    integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==
  
  path-exists@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
    integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==
  
  path-parse@^1.0.7:
    version "1.0.7"
    resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
    integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==
  
  path-to-regexp@0.1.7:
    version "0.1.7"
    resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
    integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==
  
  process@^0.11.10:
    version "0.11.10"
    resolved "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
    integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==
  
  proxy-addr@~2.0.7:
    version "2.0.7"
    resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
    integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
    dependencies:
      forwarded "0.2.0"
      ipaddr.js "1.9.1"
  
  punycode@^2.1.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz"
    integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==
  
  qs@6.11.0:
    version "6.11.0"
    resolved "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz"
    integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
    dependencies:
      side-channel "^1.0.4"
  
  range-parser@~1.2.1:
    version "1.2.1"
    resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
    integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==
  
  raw-body@2.5.1:
    version "2.5.1"
    resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz"
    integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
    dependencies:
      bytes "3.1.2"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      unpipe "1.0.0"
  
  raw-body@2.5.2:
    version "2.5.2"
    resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
    integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
    dependencies:
      bytes "3.1.2"
      http-errors "2.0.0"
      iconv-lite "0.4.24"
      unpipe "1.0.0"
  
  read-pkg-up@^7.0.1:
    version "7.0.1"
    resolved "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
    integrity sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==
    dependencies:
      find-up "^4.1.0"
      read-pkg "^5.2.0"
      type-fest "^0.8.1"
  
  read-pkg@^5.2.0:
    version "5.2.0"
    resolved "https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz"
    integrity sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
    dependencies:
      "@types/normalize-package-data" "^2.4.0"
      normalize-package-data "^2.5.0"
      parse-json "^5.0.0"
      type-fest "^0.6.0"
  
  replicate@^0.18.1:
    version "0.18.1"
    resolved "https://registry.yarnpkg.com/replicate/-/replicate-0.18.1.tgz#50b1952b468c83d725aa9e8ae8c399d905d24ea6"
    integrity sha512-JFK5qWL7AAajsIjtkW/nTaoX+yKp8zkaANe+pQpBh9RjH5vIy6/tn/sVNlRF1z5bCJLupshJBHFgnHRdWjbKXg==
  
  require-from-string@^2.0.2:
    version "2.0.2"
    resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
    integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==
  
  resolve@^1.10.0:
    version "1.22.2"
    resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.2.tgz"
    integrity sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==
    dependencies:
      is-core-module "^2.11.0"
      path-parse "^1.0.7"
      supports-preserve-symlinks-flag "^1.0.0"
  
  safe-buffer@5.2.1:
    version "5.2.1"
    resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
    integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==
  
  "safer-buffer@>= 2.1.2 < 3":
    version "2.1.2"
    resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
    integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==
  
  "semver@2 || 3 || 4 || 5":
    version "5.7.2"
    resolved "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz"
    integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==
  
  semver@^7.3.5:
    version "7.5.4"
    resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
    integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
    dependencies:
      lru-cache "^6.0.0"
  
  send@0.18.0:
    version "0.18.0"
    resolved "https://registry.npmjs.org/send/-/send-0.18.0.tgz"
    integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
    dependencies:
      debug "2.6.9"
      depd "2.0.0"
      destroy "1.2.0"
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      etag "~1.8.1"
      fresh "0.5.2"
      http-errors "2.0.0"
      mime "1.6.0"
      ms "2.1.3"
      on-finished "2.4.1"
      range-parser "~1.2.1"
      statuses "2.0.1"
  
  serve-static@1.15.0:
    version "1.15.0"
    resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz"
    integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
    dependencies:
      encodeurl "~1.0.2"
      escape-html "~1.0.3"
      parseurl "~1.3.3"
      send "0.18.0"
  
  setprototypeof@1.2.0:
    version "1.2.0"
    resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
    integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==
  
  side-channel@^1.0.4:
    version "1.0.4"
    resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
    integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
    dependencies:
      call-bind "^1.0.0"
      get-intrinsic "^1.0.2"
      object-inspect "^1.9.0"
  
  spdx-correct@^3.0.0:
    version "3.2.0"
    resolved "https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz"
    integrity sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
    dependencies:
      spdx-expression-parse "^3.0.0"
      spdx-license-ids "^3.0.0"
  
  spdx-exceptions@^2.1.0:
    version "2.3.0"
    resolved "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz"
    integrity sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==
  
  spdx-expression-parse@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
    integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
    dependencies:
      spdx-exceptions "^2.1.0"
      spdx-license-ids "^3.0.0"
  
  spdx-license-ids@^3.0.0:
    version "3.0.13"
    resolved "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.13.tgz"
    integrity sha512-XkD+zwiqXHikFZm4AX/7JSCXA98U5Db4AFd5XUg/+9UNtnH75+Z9KxtpYiJZx36mUDVOwH83pl7yvCer6ewM3w==
  
  statuses@2.0.1:
    version "2.0.1"
    resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
    integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==
  
  supports-color@^5.3.0:
    version "5.5.0"
    resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
    integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
    dependencies:
      has-flag "^3.0.0"
  
  supports-preserve-symlinks-flag@^1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
    integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==
  
  toidentifier@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
    integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==
  
  tr46@~0.0.3:
    version "0.0.3"
    resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
    integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==
  
  type-fest@^0.6.0:
    version "0.6.0"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz"
    integrity sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==
  
  type-fest@^0.8.1:
    version "0.8.1"
    resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz"
    integrity sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==
  
  type-is@~1.6.18:
    version "1.6.18"
    resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
    integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
    dependencies:
      media-typer "0.3.0"
      mime-types "~2.1.24"
  
  type@^1.0.1:
    version "1.2.0"
    resolved "https://registry.yarnpkg.com/type/-/type-1.2.0.tgz#848dd7698dafa3e54a6c479e759c4bc3f18847a0"
    integrity sha512-+5nt5AAniqsCnu2cEQQdpzCAh33kVx8n0VoFidKpB1dVVLAN/F+bgVOqOJqOnEnrhp222clB5p3vUlD+1QAnfg==
  
  type@^2.7.2:
    version "2.7.2"
    resolved "https://registry.yarnpkg.com/type/-/type-2.7.2.tgz#2376a15a3a28b1efa0f5350dcf72d24df6ef98d0"
    integrity sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw==
  
  typedarray-to-buffer@^3.1.5:
    version "3.1.5"
    resolved "https://registry.yarnpkg.com/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
    integrity sha512-zdu8XMNEDepKKR+XYOXAVPtWui0ly0NtohUscw+UmaHiAWT8hrV1rr//H6V+0DvJ3OQ19S979M0laLfX8rm82Q==
    dependencies:
      is-typedarray "^1.0.0"
  
  unpipe@1.0.0, unpipe@~1.0.0:
    version "1.0.0"
    resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
    integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==
  
  uri-js@^4.2.2:
    version "4.4.1"
    resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
    integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
    dependencies:
      punycode "^2.1.0"
  
  utf-8-validate@^5.0.2:
    version "5.0.10"
    resolved "https://registry.yarnpkg.com/utf-8-validate/-/utf-8-validate-5.0.10.tgz#d7d10ea39318171ca982718b6b96a8d2442571a2"
    integrity sha512-Z6czzLq4u8fPOyx7TU6X3dvUZVvoJmxSQ+IcrlmagKhilxlhZgxPK6C5Jqbkw1IDUmFTM+cz9QDnnLTwDz/2gQ==
    dependencies:
      node-gyp-build "^4.3.0"
  
  util@^0.12.4:
    version "0.12.5"
    resolved "https://registry.npmjs.org/util/-/util-0.12.5.tgz"
    integrity sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==
    dependencies:
      inherits "^2.0.3"
      is-arguments "^1.0.4"
      is-generator-function "^1.0.7"
      is-typed-array "^1.1.3"
      which-typed-array "^1.1.2"
  
  utils-merge@1.0.1:
    version "1.0.1"
    resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
    integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==
  
  uuid@^8.3.2:
    version "8.3.2"
    resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
    integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==
  
  validate-npm-package-license@^3.0.1:
    version "3.0.4"
    resolved "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
    integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
    dependencies:
      spdx-correct "^3.0.0"
      spdx-expression-parse "^3.0.0"
  
  vary@~1.1.2:
    version "1.1.2"
    resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
    integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==
  
  webidl-conversions@^3.0.0:
    version "3.0.1"
    resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
    integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==
  
  websocket@^1.0.34:
    version "1.0.34"
    resolved "https://registry.yarnpkg.com/websocket/-/websocket-1.0.34.tgz#2bdc2602c08bf2c82253b730655c0ef7dcab3111"
    integrity sha512-PRDso2sGwF6kM75QykIesBijKSVceR6jL2G8NGYyq2XrItNC2P5/qL5XeR056GhA+Ly7JMFvJb9I312mJfmqnQ==
    dependencies:
      bufferutil "^4.0.1"
      debug "^2.2.0"
      es5-ext "^0.10.50"
      typedarray-to-buffer "^3.1.5"
      utf-8-validate "^5.0.2"
      yaeti "^0.0.6"
  
  whatwg-url@^5.0.0:
    version "5.0.0"
    resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
    integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
    dependencies:
      tr46 "~0.0.3"
      webidl-conversions "^3.0.0"
  
  which-typed-array@^1.1.11, which-typed-array@^1.1.2:
    version "1.1.11"
    resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.11.tgz"
    integrity sha512-qe9UWWpkeG5yzZ0tNYxDmd7vo58HDBc39mZ0xWWpolAGADdFOzkfamWLDxkOWcvHQKVmdTyQdLD4NOfjLWTKew==
    dependencies:
      available-typed-arrays "^1.0.5"
      call-bind "^1.0.2"
      for-each "^0.3.3"
      gopd "^1.0.1"
      has-tostringtag "^1.0.0"
  
  yaeti@^0.0.6:
    version "0.0.6"
    resolved "https://registry.yarnpkg.com/yaeti/-/yaeti-0.0.6.tgz#f26f484d72684cf42bedfb76970aa1608fbf9577"
    integrity sha512-MvQa//+KcZCUkBTIC9blM+CU9J2GzuTytsOUwf2lidtvkx/6gnEp1QvJv34t9vdjhFmha/mUiNDbN0D0mJWdug==
  
  yallist@^4.0.0:
    version "4.0.0"
    resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
    integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==
