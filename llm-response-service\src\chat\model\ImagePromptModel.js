const { LLMServiceBase } = require("./LLMServiceBase");

class ImagePromptModel extends LLMServiceBase {
  constructor({
    message,
    botResponse,
    messages,
    botProfile,
    forceSfwImage,
    botConfiguration,
  }) {
    super();
    this.message = message;
    this.botResponse = botResponse;
    this.messages = messages;
    this.botProfile = botProfile;
    this.forceSfwImage = forceSfwImage;
    this.botConfiguration = botConfiguration;
  }

  /* Override */
  async getMessages() {
    return [
      {
        role: "user",
        content: this.generatePrompt({
          message: this.message,
          botResponse: this.botResponse,
          messageHistory: this.messages
            .slice(-Math.min(this.messages.length, 8))
            .map((msg) => {
              if (msg.is_bot) {
                return "BOT: " + msg.body;
              } else {
                return "USER:" + msg.body;
              }
            })
            .join("\n"),
          nsfw:
            !this.forceSfwImage && this.botProfile.imitation !== "celebrity",
          botDescription:
            this.botConfiguration.description || this.botProfile.description,
        }),
      },
    ];
  }

  generatePrompt() {
    throw new Error("Unimplemented generatePrompt");
  }

  /* Override */
  isStreaming() {
    return false;
  }

  /* Override */
  getModel() {
    return "generate-image-description-llm";
  }

  /* Override */
  getTemperature() {
    return 0.7;
  }

  /* Override */
  getMaxTokens() {
    return 300;
  }

  /* Override */
  getTopP() {
    return 0.3;
  }

  /* Override */
  getTimeout() {
    return 10 * 1000;
  }
}

module.exports = {
  ImagePromptModel,
};
