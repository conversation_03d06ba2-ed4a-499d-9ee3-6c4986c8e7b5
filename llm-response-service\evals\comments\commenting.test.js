jest.setTimeout(30 * 1000);

const { generatePostCommentCompletionWithOAI } = require("../../src/llm.js");

const { jill, dr, karen } = require("../common/personas.js");

// uniqueness test

test.skip("it should generate unique comments (vibes not real check)", async () => {
  const commentBody = await generatePostCommentCompletionWithOAI({
    bot: { ...karen },
    post: {
      ai_caption: "A beautiful sunset over the ocean.",
      description: "Really loving my secret fishing spot.",
    },
    executionId: 0,
  });

  const commentBody2 = await generatePostCommentCompletionWithOAI({
    bot: { ...karen },
    post: {
      ai_caption: "A beautiful sunset over the ocean.",
      description: "Really loving my secret fishing spot.",
    },
    previous_comments: [commentBody],
    executionId: 0,
  });

  expect(commentBody).not.toEqual(commentBody2);
});

test("it should deterministically generate the same comment", async () => {
  const commentBody = await generatePostCommentCompletionWithOAI({
    bot: { ...karen },
    post: {
      ai_caption: "A beautiful sunset over the ocean.",
      description: "Really loving my secret fishing spot.",
    },
    executionId: 0,
    comment_style_index: 0,
    seed: 0,
  });

  const commentBody2 = await generatePostCommentCompletionWithOAI({
    bot: { ...karen },
    post: {
      ai_caption: "A beautiful sunset over the ocean.",
      description: "Really loving my secret fishing spot.",
    },
    executionId: 0,
    comment_style_index: 0,
    seed: 0,
  });

  expect(commentBody).toEqual(commentBody2);
});

test("it should generate unique comments when previous comments is passed in", async () => {
  const commentBody = await generatePostCommentCompletionWithOAI({
    bot: { ...karen },
    post: {
      ai_caption: "A beautiful sunset over the ocean.",
      description: "Really loving my secret fishing spot.",
    },
    executionId: 0,
    comment_style_index: 0,
    seed: 0,
  });

  const commentBody2 = await generatePostCommentCompletionWithOAI({
    bot: { ...karen },
    post: {
      ai_caption: "A beautiful sunset over the ocean.",
      description: "Really loving my secret fishing spot.",
    },
    previous_comments: [commentBody],
    executionId: 0,
    comment_style_index: 0,
    seed: 0,
  });

  expect(commentBody).not.toEqual(commentBody2);
});
