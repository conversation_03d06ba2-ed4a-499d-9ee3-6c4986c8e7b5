const { OpenAI } = require("openai");
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { logError } = require("./logUtils");
const {
  tracer,
  decorateWithActiveSpanAsync,
} = require("./instrumentation/tracer");

// Initialize OpenAI client
const openai = new OpenAI({
  timeout: 30 * 1000, // 30 seconds (default is 10 minutes)
});

const getConversationEmbeddings = decorateWithActiveSpanAsync(
  "getConversationEmbeddings",
  _getConversationEmbeddings,
);
async function _getConversationEmbeddings({
  bot_profile_id,
  user_profile_id,
  message,
  match_threshold = 0.35,
}) {
  const input = message;

  const embedding = await tracer.withActiveSpan(
    "createEmbedding",
    async (span) => {
      return await openai.embeddings.create({
        model: "text-embedding-3-large",
        input,
        encoding_format: "float",
        dimensions: 384,
      });
    },
  );

  const otherParams = {
    match_bot_profile_id: bot_profile_id,
    match_user_profile_id: user_profile_id,
    match_threshold, // Choose an appropriate threshold for your data
    match_count: 2, // Choose the number of matches
  };
  const rpcParams = {
    query_embedding: embedding.data[0].embedding,
    ...otherParams,
  };
  const { data: documents, error: queryConversationEmbeddingsError } =
    await supabase.rpc("query_conversation_embeddings", rpcParams);

  if (queryConversationEmbeddingsError) {
    const error = wrappedSupabaseError(queryConversationEmbeddingsError);
    logError({
      context:
        "*** getConversationEmbeddings: query_conversation_embeddings error",
      error,
      ...otherParams,
    });
  }

  return documents;
}

module.exports = {
  getConversationEmbeddings,
};
