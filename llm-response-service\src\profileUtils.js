const { supabase } = require("./supabaseClient");
const { wrappedSupabaseError, logError } = require("./utils");

/**
 * Checks if a user is premium.
 * @param {number|string} user_id
 * @returns {Promise<boolean>}
 */
async function isUserPremium(user_id) {
  try {
    const { data: currentPlan, error: currentPlanError } = await supabase
      .from("user_current_plans_view")
      .select("current_plan_id, current_plan_is_active")
      .eq("user_id", user_id)
      .maybeSingle();

    if (currentPlanError) {
      const error = wrappedSupabaseError(
        currentPlanError,
        "Failed to fetch user current plan",
      );
      logError({
        context: "isUserPremium - failed to fetch user current plan",
        error,
        user_id,
      });
      return false;
    }

    return (
      currentPlan &&
      currentPlan.current_plan_id === 2 &&
      currentPlan.current_plan_is_active
    );
  } catch (planError) {
    logError({
      context: "isUserPremium - premium check error",
      error: planError,
      user_id,
    });
    return false;
  }
}

module.exports = {
  isUserPremium,
};
