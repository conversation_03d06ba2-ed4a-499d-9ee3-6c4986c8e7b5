const express = require("express");
const app = express.Router();
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { authUser } = require("./middleware");
const { logError } = require("./logUtils");
require("dotenv").config();

app.get("/ping", async (req, res) => {
  return res.send("promo codes ping");
});

app.post("/redeem", authUser, async (req, res) => {
  const { code } = req.body;
  const user_id = req.user.id;

  const { error } = await redeemPromoCode({ code, user_id });

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res.json({ success: true });
});

async function redeemPromoCode({ code, user_id }) {
  // check if exists
  const { data: promoCode, error: queryPromoCodesError } = await supabase
    .from("promo_codes")
    .select("id")
    .eq("code", code.toLowerCase())
    .single();

  if (queryPromoCodesError) {
    const error = wrappedSupabaseError(queryPromoCodesError);
    logError({
      context: "redeemPromoCode: failed to query promo codes",
      error,
      code,
      user_id,
    });
    throw error;
  }

  // check if already redeemed by a user
  const { data: userInviteRedemption, error: userInviteRedemptionError } =
    await supabase
      .from("promo_code_redemptions")
      .select("*")
      .eq("user_id", user_id)
      .eq("promo_code_id", promoCode.id)
      .maybeSingle();

  if (userInviteRedemptionError) {
    const error = wrappedSupabaseError(userInviteRedemptionError);
    logError({
      context: "redeemPromoCode: failed to check if already redeemed",
      error,
      promoCode,
      user_id,
    });
    throw error;
  }

  if (userInviteRedemption) {
    return { error: new Error("User already redeemed the code") };
  }

  // check how many redemeptions
  const { count, error: countRedemptionsError } = await supabase
    .from("promo_code_redemptions")
    .select("id", { count: "exact", head: true })
    .eq("promo_code_id", promoCode.id);

  if (countRedemptionsError) {
    const error = wrappedSupabaseError(countRedemptionsError);
    logError({
      context: "redeemPromoCode: failed to count promo_code_redemptions",
      error,
      promoCode,
      user_id,
    });
    throw error;
  }

  if (count >= 1000) {
    // Too many redemptions
    return { error: new Error("Code has already been used over 1000 times") };
  }

  const { error: updateRedemptionError } = await supabase
    .from("promo_code_redemptions")
    .insert({ user_id, promo_code_id: promoCode.id })
    .single();

  if (updateRedemptionError) {
    const error = wrappedSupabaseError(updateRedemptionError);
    logError({
      context: "redeemPromoCode: failed to update promo_code_redemptions",
      error,
      promoCode,
      user_id,
    });
    throw error;
  }

  const { data: userData, error: queryUserError } = await supabase
    .from("users")
    .select("*")
    .eq("id", user_id)
    .single();

  if (queryUserError) {
    const error = wrappedSupabaseError(queryUserError);
    logError({
      context: "redeemPromoCode: failed to query user",
      error,
      promoCode,
      user_id,
    });
    throw error;
  }

  let updateError;

  if (userData && code === "herefirst") {
    // Give 3 months of premium to user
    const result = await supabase
      .from("users")
      .update({
        premium_months_quantity: userData.premium_months_quantity + 3,
      })
      .eq("id", user_id);
    updateError = result.error;
  } else if (userData && code === "reviewthankyou") {
    const result = await supabase
      .from("users")
      .update({
        premium_months_quantity: userData.premium_months_quantity + 1,
      })
      .eq("id", user_id);
    updateError = result.error;
  }
  if (updateError) {
    const error = wrappedSupabaseError(updateError);
    logError({
      context: "redeemPromoCode: failed to update user after redeeming code",
      error,
      promoCode,
      user_id,
    });
    throw error;
  }
  return { success: true };
}

module.exports = {
  app,
};
