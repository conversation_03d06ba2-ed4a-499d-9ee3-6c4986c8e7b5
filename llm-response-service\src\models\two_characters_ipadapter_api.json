{"3": {"inputs": {"seed": 321727230054734, "steps": 8, "cfg": 10, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["4", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "meinamix.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 864, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "two people at the beach, high quality, facing camera", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "text, watermark", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "10": {"inputs": {"images": ["8", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "15": {"inputs": {"mask_color": "#FFFFFF", "weight": 1, "noise": 0.5, "weight_type": "original", "start_at": 0.2, "end_at": 1, "unfold_batch": false, "faceid_v2": false, "weight_v2": 3, "combine_embeds": "concat", "color_mask": ["27", 0], "image": ["65", 0]}, "class_type": "RegionalIPAdapterColorMask //Inspire", "_meta": {"title": "Regional IPAdapter By Color Mask (Inspire)"}}, "21": {"inputs": {"model_name": "segm/person_yolov8m-seg.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "23": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["21", 0], "image": ["8", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "24": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["23", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "26": {"inputs": {"segs": ["24", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "27": {"inputs": {"mask": ["26", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "29": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 1, "take_count": 2, "segs": ["23", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "30": {"inputs": {"segs": ["29", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "33": {"inputs": {"mask": ["30", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "43": {"inputs": {"width": 864, "height": 1024, "batch_size": 1, "color": 255}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "47": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["43", 0], "source": ["33", 0], "mask": ["30", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "50": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["47", 0], "source": ["27", 0], "mask": ["26", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "51": {"inputs": {"images": ["50", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "53": {"inputs": {"mask_color": "#FFFFFF", "weight": 1, "noise": 0.5, "weight_type": "original", "start_at": 0, "end_at": 1, "unfold_batch": false, "faceid_v2": false, "weight_v2": 3, "combine_embeds": "concat", "color_mask": ["33", 0], "image": ["63", 0]}, "class_type": "RegionalIPAdapterColorMask //Inspire", "_meta": {"title": "Regional IPAdapter By Color Mask (Inspire)"}}, "58": {"inputs": {"seed": 321727230054734, "steps": 20, "cfg": 10, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["72", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "59": {"inputs": {"samples": ["58", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "60": {"inputs": {"images": ["59", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "61": {"inputs": {"image": "02550172-c6d9-436c-b14d-e9b17fbaf518_1.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "62": {"inputs": {"image": "4fkgc02tcs241.webp", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "63": {"inputs": {"interpolation": "LANCZOS", "crop_position": "center", "sharpening": 0, "image": ["62", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "65": {"inputs": {"interpolation": "LANCZOS", "crop_position": "center", "sharpening": 0, "image": ["61", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "72": {"inputs": {"ipadapter_pipe": ["75", 0], "regional_ipadapter1": ["15", 0], "regional_ipadapter2": ["53", 0]}, "class_type": "ApplyRegionalIPAdapters //Inspire", "_meta": {"title": "Apply Regional IPAdapters (Inspire)"}}, "75": {"inputs": {"ipadapter": ["82", 0], "model": ["4", 0], "clip_vision": ["80", 0]}, "class_type": "ToIPAdapterPipe //Inspire", "_meta": {"title": "ToIPAdapterPipe (Inspire)"}}, "78": {"inputs": {"images": ["33", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "79": {"inputs": {"images": ["27", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "80": {"inputs": {"clip_name": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "81": {"inputs": {"images": ["65", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "82": {"inputs": {"ipadapter_file": "ip-adapter-anime.safetensors"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "IPAdapter Model Loader"}}, "83": {"inputs": {"mask": ["15", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "84": {"inputs": {"images": ["83", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "86": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["87", 0], "image": ["59", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "87": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "88": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 4, "segs": ["86", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "89": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 1024, "seed": ***************, "steps": 20, "cfg": 8, "sampler_name": "euler", "scheduler": "normal", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "image": ["59", 0], "segs": ["88", 0], "model": ["72", 0], "clip": ["4", 1], "vae": ["4", 2], "positive": ["6", 0], "negative": ["7", 0]}, "class_type": "Detailer<PERSON>or<PERSON>ach", "_meta": {"title": "<PERSON>ail<PERSON> (SEGS)"}}, "90": {"inputs": {"images": ["89", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}}