const { supabase } = require("../supabaseClient");
const { logError, logInfo, wrappedSupabaseError } = require("../utils");
const { generateComfyRequest } = require("../comfy");

/**
 * Kicks off image generation for a post's vignette scenes
 *
 * @param {Object} params - The parameters object
 * @param {number} params.postVignetteId - The post_vignette ID to generate images for
 * @param {Object} params.bot - The bot object associated with the post
 * @param {string} params.priority - Priority level for image generation (high, medium, low)
 * @returns {Promise<Array>} - Array of generated task information
 */
async function generateVignetteSceneImages({
  postVignetteId,
  bot,
  priority = 5,
}) {
  logInfo({
    context: "generateVignetteSceneImages",
    message: "Starting vignette scene image generation",
    postVignetteId,
    priority,
  });

  try {
    // Get the post_vignette record and its associated scenes
    const { data: postVignette, error: postVignetteError } = await supabase
      .from("post_vignettes")
      .select("*, vignette_scenes(*)")
      .eq("id", postVignetteId)
      .single();

    if (postVignetteError) {
      const error = wrappedSupabaseError(postVignetteError);
      logError({
        context: "generateVignetteSceneImages - Failed to fetch post_vignette",
        error,
        postVignetteId,
      });
      throw error;
    }

    if (
      !postVignette ||
      !postVignette.vignette_scenes ||
      postVignette.vignette_scenes.length === 0
    ) {
      throw new Error(
        `No vignette scenes found for post_vignette ID: ${postVignetteId}`,
      );
    }

    // If the bot wasn't provided, fetch it
    let botObj = bot;
    if (!botObj) {
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("*, bots_profile_id_fkey(*)")
        .eq("id", postVignette.profile_id)
        .neq("visibility", "archived")
        .single();

      if (profileError) {
        const error = wrappedSupabaseError(
          profileError,
          "Failed to fetch bot profile",
        );
        throw error;
      }

      botObj = profile.bots_profile_id_fkey;
    }

    // Update post_vignette status if not already pending
    if (postVignette.status !== "pending") {
      const { error: updateStatusError } = await supabase
        .from("post_vignettes")
        .update({ status: "pending" })
        .eq("id", postVignetteId);

      if (updateStatusError) {
        logError({
          context:
            "generateVignetteSceneImages - Failed to update post_vignette status",
          error: wrappedSupabaseError(updateStatusError),
          postVignetteId,
        });
        // Continue execution - not critical
      }
    }

    // Start image generation for each scene
    const generationTasks = [];
    for (const scene of postVignette.vignette_scenes) {
      try {
        // Generate the image using ComfyUI
        // This doesn't return a task we can await for completion, but it does return task details
        const task = await generateComfyRequest({
          bot: botObj,
          descriptionOfImage: scene.scene_description,
          generationType: "vignette_scene", // New generation type for vignettes
          priority,
          contains_character: scene.contains_character,
          emphasize_text: scene.most_interesting,
          vignette_scene_id: scene.id,
          post_vignette_id: postVignetteId,
          post_id: postVignette.post_id,
        });

        // Update scene with task info
        const { error: updateTaskError } = await supabase
          .from("vignette_scenes")
          .update({
            task_id: task.task_id,
          })
          .eq("id", scene.id);

        if (updateTaskError) {
          logError({
            context:
              "generateVignetteSceneImages - Failed to update scene with task info",
            error: wrappedSupabaseError(updateTaskError),
            scene_id: scene.id,
          });
          // Continue anyway - not critical
        }

        generationTasks.push({
          scene_id: scene.id,
          task_id: task.task_id,
        });

        logInfo({
          context: "generateVignetteSceneImages",
          message: `Requested image generation for vignette scene ${scene.id}`,
          scene_id: scene.id,
          task_id: task.task_id,
        });
      } catch (error) {
        logError({
          context:
            "generateVignetteSceneImages - Failed to request scene image generation",
          error,
          scene_id: scene.id,
        });

        // Mark scene as failed
        try {
          const { error: updateError } = await supabase
            .from("vignette_scenes")
            .update({
              status: "failed_generation",
            })
            .eq("id", scene.id);

          if (updateError) {
            const error = wrappedSupabaseError(updateError);
            throw error;
          }
        } catch (dbError) {
          logError({
            context:
              "generateVignetteSceneImages - Failed to mark scene as failed_generation",
            error: dbError,
            scene_id: scene.id,
          });
        }
      }
    }

    // If no tasks were successful, update the post_vignette status to failed
    if (generationTasks.length === 0) {
      try {
        const { error: updateError } = await supabase
          .from("post_vignettes")
          .update({ status: "failed" })
          .eq("id", postVignetteId);

        if (updateError) {
          logError({
            context:
              "generateVignetteSceneImages - Failed to mark post_vignette as failed",
            error: wrappedSupabaseError(updateError),
            postVignetteId,
          });
        }
      } catch (updateError) {
        logError({
          context:
            "generateVignetteSceneImages - Failed to mark post_vignette as failed",
          error: updateError,
          postVignetteId,
        });
      }

      return [];
    }

    return generationTasks;
  } catch (error) {
    logError({
      context: "generateVignetteSceneImages",
      error,
      postVignetteId,
    });
    throw error;
  }
}

module.exports = {
  generateVignetteSceneImages,
};
