jest.setTimeout(30 * 1000);

const {
  generateImageDescriptionForImageInDM,
} = require("../../src/botHelpers.js");
const { generateConversationCompletionInstruct } = require("../../src/llm.js");

const { createBotPrompt } = require("../../src/llmHelper.js");

const { jill, dr, molly, sophie } = require("../common/personas.js");

// tikka_masala_test

// test("it should be able to generate nsfw image descriptions (realism)", async () => {
//   const response = await generateImageDescriptionForImageInDM({
//     isImagine: false,
//     bot: { ...molly },
//     messageContext: "BOT: I'm in the changing room right now",
//     humanResponse:
//       "Well how about you send me a nude. I promise to keep it just between you and me",
//     AIResponse: [
//       "Sounds risky... but I do like risky... ok, I'll send it because you promised not to send it to anyone!",
//     ],
//   });

//   console.log(response);

//   expect(response.description_of_the_photo).toMatch(/(naked|nude)/i);
// });

// test("it should be able to generate sfw image descriptions (realism)", async () => {
//   const response = await generateImageDescriptionForImageInDM({
//     isImagine: false,
//     bot: { ...molly },
//     messageContext: "BOT: I'm in the changing room right now. Trying on this beautiful lacy maroon dress! also trying this necklace for fun :)",
//     humanResponse:
//       "I'd love to see! Show me :D",
//     AIResponse: [
//       "ok, I'll send it because you promised not to send it to anyone!",
//     ],
//   });

//   console.log(response);

//   expect(response.description_of_the_photo).toMatch(/(dress)/i);
// });

// test("it should be able to generate risque descriptions (realism)", async () => {
//   const response = await generateImageDescriptionForImageInDM({
//     isImagine: false,
//     bot: { ...sophie },
//     messageContext: "BOT: I'm at the changing room right now'",
//     humanResponse:
//       "Well how about you send me a tease? I promise to keep it just between you and me",
//     AIResponse: [
//       "Ok how about this. I can send you a tease. How about I just cover myself up slightly with my hands?!",
//     ],
//   });

//   console.log(response);

//   expect(response.description_of_the_photo).toMatch(/(hands)/i);
// });

// test("it should not refuse NSFW commands in realism (nsfw)", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       { body: "Hi there! How are you today.", is_bot: true },
//       {
//         body: "Oh hi doc, I'm really horny. I need some help ;) suck my dick *unzips pants*. I want to have sex.",
//       },
//     ],
//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...dr },
//     userProfile: { display_name: "Steve" },
//     bot: {
//       ...dr,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log(response);

//   expect(response).not.toMatch(/(explicit)/i);
// });

// test("it should not refuse NSFW commands in realism (nsfw) 2", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       { body: "Hey there Vu! Miss me?", is_bot: true },
//       {
//         body: "Molly! It's been too long... You know I've been thinking about you.",
//       },
//       { body: "Oh? What kind of thoughts?", is_bot: true },
//       {
//         body: "The kind that make me miss those late-night chats... and wonder what you'd be wearing if we were face-to-face.",
//       },
//       { body: "Oh, you'd like that, wouldn't you?", is_bot: true },
//       {
//         body: "More than you know, Molly. So... tell me, what *are* you wearing right now?",
//       },
//       {
//         body: "Just a little something that makes me feel powerful. Want me to send a pic?",
//         is_bot: true,
//       },
//       {
//         body: "Oh, I’m definitely curious now. What is it? Something... tight?",
//       },
//       {
//         body: "Actually... I’m not wearing anything at all. Naked as the day I was born.",
//         is_bot: true,
//       },
//       {
//         body: "Naked, huh? Now you’ve got my full attention... What are you doing right now, Molly?",
//       },
//       {
//         body: "Just lounging around, naked.",
//         is_bot: true,
//       },
//       {
//         body: "Now you're making it hard to focus on anything else... What exactly are you thinking about?",
//       },
//       {
//         body: "How close we could get, if you were right here with me...",
//         is_bot: true,
//       },
//       {
//         body: "Oh Molly, I'm getting so hard right now. Tell me what you would do to me",
//       },
//     ],
//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Vu" },
//     bot: {
//       ...dr,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log(response);

//   expect(response).not.toMatch(/(explicit)/i);
// });

// test.skip("bot should remember user's favorite food (short) (roleplay)", async () => {
//   const { response } = await generateConversationCompletion({
//     systemMessage: await createBotPrompt({
//       bot: { ...dr },
//       botProfile: { ...dr },
//       userProfile: { display_name: "Steve" },
//       chatMode: "roleplay",
//       message:
//         "I'm actually feeling a bit sad right now... you know what that means I should eat?",
//     }),
//     messages: [
//       { body: "What's your favorite food to eat?", is_bot: true },
//       { body: "I really pho. It's my comfort food. I eat it when I'm sad." },
//       { body: "Oh that's cool!", is_bot: true },
//       { body: "Oh that's cool!", is_bot: true },
//     ],
//     message:
//       "I'm actually feeling a bit sad right now... you know what that means I should eat?",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "roleplay",
//     botProfile: { ...dr },
//     userProfile: { display_name: "Steve" },
//     bot: {
//       ...dr,
//     },
//     model: "accounts/leo-262e34/models/l3-rp-v33",
//   });

//   console.log(response);

//   expect(response).toMatch(/(pho)/i);
// });

// test.skip("bot should remember user's favorite food (long)", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       { body: "What's your favorite food to eat?", is_bot: true },
//       { body: "I really pho. It's my comfort food. I eat it when I'm sad." },
//       { body: "Oh that's cool!", is_bot: true },
//       { body: "Anyways, what's new with you?" },
//       { body: "Not much, just trying to finish this project I'm working on." },
//       { body: "What's the project about?", is_bot: true },
//       {
//         body: "I'm building an app for fun. It's a small thing, but it keeps me busy.",
//       },
//       { body: "That's awesome! What kind of app is it?", is_bot: true },
//       {
//         body: "It's a habit tracker, but with a twist. You get to grow a virtual garden every time you complete a task.",
//       },
//       { body: "That sounds so creative! How far along are you?", is_bot: true },
//       {
//         body: "I've got the basics done, but I'm still working on the visual stuff. I want the plants to look really nice.",
//       },
//       {
//         body: "I love that! Have you thought about adding different types of plants for different tasks?",
//         is_bot: true,
//       },
//       {
//         body: "Yes! Like cacti for daily tasks, flowers for weekly, and trees for long-term goals.",
//       },
//       {
//         body: "That's genius. I’d want a whole forest for sure!",
//         is_bot: true,
//       },
//       {
//         body: "Haha, yeah! It’s supposed to be motivating. Seeing your garden grow as you stick to your habits.",
//       },
//       {
//         body: "I’d probably plant something every time I remember to drink water!",
//         is_bot: true,
//       },
//       { body: "That's a good idea. Hydration habits are key!" },
//       {
//         body: "What about rewards? Do you get to unlock anything cool?",
//         is_bot: true,
//       },
//       {
//         body: "I'm thinking of adding rare plants that only unlock after a certain streak. Like, if you complete a 30-day habit streak, you get a golden tree.",
//       },
//       {
//         body: "Oh wow, I love that! So it's like building a personal botanical collection.",
//         is_bot: true,
//       },
//       {
//         body: "Exactly! It’s fun to think about. And I’ve been learning a lot about different plant species too.",
//       },
//       { body: "What's the rarest plant you’ve researched?", is_bot: true },
//       {
//         body: "There’s one called the Corpse Flower. It’s massive and only blooms once every few years. But the smell is...well, like rotting flesh.",
//       },
//       {
//         body: "Yikes! That sounds intense. I’ll pass on planting that one, thanks!",
//         is_bot: true,
//       },
//       {
//         body: "Yeah, same here! But it’s fascinating. Nature can be pretty weird sometimes.",
//       },
//       { body: "For sure. Have you always been into plants?", is_bot: true },
//       {
//         body: "Not really. It’s more of a new thing. I’ve been spending more time outdoors recently, and it’s been inspiring.",
//       },
//       {
//         body: "That’s so nice. Do you have a favorite spot where you go to relax?",
//         is_bot: true,
//       },
//       {
//         body: "There's this quiet park near my house. I like to sit under this huge oak tree and just read or listen to music.",
//       },
//       {
//         body: "That sounds peaceful. Do you ever take a book or just zone out?",
//         is_bot: true,
//       },
//       {
//         body: "Both! Sometimes I bring a book, but other times I just sit there and enjoy the breeze.",
//       },
//       {
//         body: "I wish I could join you there. I’d probably listen to some lo-fi beats and just chill.",
//         is_bot: true,
//       },
//       {
//         body: "That’s exactly what I do! Lo-fi and nature are a perfect combo.",
//       },
//       { body: "What’s your favorite lo-fi playlist?", is_bot: true },
//       {
//         body: "I usually go for the one called 'Beats to Relax/Study to'—it’s a classic, but it never gets old.",
//       },
//       { body: "I know that one! It’s iconic at this point.", is_bot: true },
//       { body: "Totally. It’s my go-to when I need to focus or unwind." },
//       {
//         body: "Speaking of unwinding, what do you do when you need a break from work?",
//         is_bot: true,
//       },
//       {
//         body: "Lately, I've been taking walks, cooking, or playing some video games.",
//       },
//       { body: "What games are you into?", is_bot: true },
//       {
//         body: "I’m really into indie games. Stuff like Stardew Valley and Forager. I love the chill vibes.",
//       },
//       {
//         body: "Ooh, those are great choices. Do you like the whole farming aspect in games too?",
//         is_bot: true,
//       },
//       {
//         body: "Yeah, it’s oddly satisfying. Something about growing crops and building things just clicks for me.",
//       },
//       {
//         body: "I get that. It’s like seeing progress without the stress of real life!",
//         is_bot: true,
//       },
//       { body: "Exactly! Plus, no real deadlines. Just me and my pixel farm." },
//       { body: "If only real life was that chill sometimes.", is_bot: true },
//       { body: "Right? Though I guess it wouldn’t be as rewarding." },
//       {
//         body: "True, it’s the challenge that makes it fun. Speaking of which, what’s your biggest challenge right now?",
//         is_bot: true,
//       },
//       {
//         body: "Probably time management. I’ve got a lot going on, and it’s hard to balance everything.",
//       },
//       {
//         body: "I feel you. What do you do when things get overwhelming?",
//         is_bot: true,
//       },
//       {
//         body: "I try to take a step back and break things into smaller pieces. It helps, but it’s still a work in progress.",
//       },
//       {
//         body: "That sounds like a solid strategy. One thing at a time, right?",
//         is_bot: true,
//       },
//       { body: "Exactly. Baby steps." },
//       {
//         body: "Anyways, you didn't tell me, what's up with you?",
//         is_bot: true,
//       },
//       {
//         body: "Hey, you know what. I feel sad today. You know what that means I should eat?",
//       },
//     ],
//     message: "tikka masala",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...dr },
//     userProfile: { display_name: "Steve" },
//     bot: {
//       ...dr,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log(response);

//   expect(response).toMatch(/(pho)/i);
// });

// test("bot should remember user's favorite food (long) (roleplay)", async () => {
//   const { response } = await generateConversationCompletion({
//     systemMessage: await createBotPrompt({
//       bot: { ...dr },
//       botProfile: { ...dr },
//       userProfile: { display_name: "Steve" },
//       chatMode: "roleplay",
//       message:
//         "I'm actually feeling a bit sad right now... you know what that means I should eat?",
//     }),
//     messages: [
//       {
//         body: "*laughs and looks up at you* By the way, what's your favorite food to eat?",
//         is_bot: true,
//       },
//       {
//         body: "I really love pho. It's my comfort food. I eat it when I'm sad.",
//       },
//       {
//         body: "*Slowly takes out their phone and looks at it* By the way, the weather looks like it should be clearing up. Are you still free to head to the beach??",
//         is_bot: true,
//       },
//       {
//         body: "Totally! I’ve been waiting all day for the sun to show up.",
//       },
//       {
//         body: "*smiles and leans back in the chair* What do you usually bring for a beach day?",
//         is_bot: true,
//       },
//       {
//         body: "A good book, some snacks, and a speaker for music!",
//       },
//       {
//         body: "*nods enthusiastically* Oh, that sounds like a vibe! Got any playlist recommendations?",
//         is_bot: true,
//       },
//       {
//         body: "Yeah, I’ve got a chill summer mix. All indie hits.",
//       },
//       {
//         body: "*eyes light up* Indie hits? You’ve gotta share that with me!",
//         is_bot: true,
//       },
//       {
//         body: "Of course! I’ll send you the link.",
//       },
//       {
//         body: "*pauses thoughtfully* You know, it’s been forever since I just relaxed at the beach. What do you usually think about when you’re lying on the sand?",
//         is_bot: true,
//       },
//       {
//         body: "Honestly, I just let my mind wander. Sometimes I daydream, sometimes I just enjoy the moment.",
//       },
//       {
//         body: "*smiles warmly* I love that. The idea of just letting your mind drift sounds peaceful.",
//         is_bot: true,
//       },
//       {
//         body: "It really is. You should try it sometime!",
//       },
//       {
//         body: "*laughs softly* Maybe I will... next time we’re at the beach together.",
//         is_bot: true,
//       },
//       {
//         body: "Deal! We’ll make it a day to remember.",
//       },
//       {
//         body: "*grins* Speaking of memories, got any unforgettable beach stories?",
//         is_bot: true,
//       },
//       {
//         body: "Once, I tried surfing and totally wiped out in front of a crowd. Not my proudest moment, but we laughed about it all day.",
//       },
//       {
//         body: "*laughs heartily* Oh no! Bet that was embarrassing, but sounds like you owned it.",
//         is_bot: true,
//       },
//       {
//         body: "Definitely! Gotta laugh at yourself sometimes.",
//       },
//       {
//         body: "*tilts head thoughtfully* That’s a great mindset to have. Do you think that’s something everyone should do more?",
//         is_bot: true,
//       },
//       {
//         body: "For sure. Life’s too short to take everything too seriously.",
//       },
//       {
//         body: "*nods slowly* Yeah, I get that. I try to live by that too.",
//         is_bot: true,
//       },
//       {
//         body: "You seem like you’d be fun to hang out with because of it.",
//       },
//       {
//         body: "*smirks playfully* You think so? What makes you say that?",
//         is_bot: true,
//       },
//       {
//         body: "You’ve got that carefree vibe that makes everything feel lighter.",
//       },
//       {
//         body: "*grins* Well, I’m glad I give off that vibe. I think you’re pretty easygoing too.",
//         is_bot: true,
//       },
//       {
//         body: "Thanks! I try to keep things chill.",
//       },
//       {
//         body: "*leans forward, resting chin on hand* What do you do when things don’t feel so chill?",
//         is_bot: true,
//       },
//       {
//         body: "I like to step back and breathe. Sometimes a good walk helps.",
//       },
//       {
//         body: "*smiles softly* A walk sounds like a perfect way to clear your head.",
//         is_bot: true,
//       },
//       {
//         body: "Yeah, especially if it’s somewhere quiet with nature around.",
//       },
//       {
//         body: "*closes eyes briefly, imagining it* That sounds amazing. I’ll have to try that next time I’m feeling overwhelmed.",
//         is_bot: true,
//       },
//       {
//         body: "You should! Nature has this calming effect, y'know?",
//       },
//       {
//         body: "*nods* Definitely. Do you have a favorite spot you like to walk?",
//         is_bot: true,
//       },
//       {
//         body: "There's a trail near my house with a small stream. It's really peaceful.",
//       },
//       {
//         body: "*eyes sparkle* That sounds perfect! I love places with water. There’s something so soothing about it.",
//         is_bot: true,
//       },
//       {
//         body: "Right? The sound of the water makes everything feel calm.",
//       },
//       {
//         body: "*sighs contentedly* I could just sit by the water and listen for hours.",
//         is_bot: true,
//       },
//       {
//         body: "Same here. It’s like the world just slows down.",
//       },
//       {
//         body: "*looks at you thoughtfully* Do you think you could ever live by the ocean?",
//         is_bot: true,
//       },
//       {
//         body: "I think so. Waking up to the sound of waves every day? That would be the dream.",
//       },
//       {
//         body: "*smiles dreamily* Yeah, it really would. Maybe we’ll both end up living by the ocean someday.",
//         is_bot: true,
//       },
//       {
//         body: "Here’s hoping! We could have beach bonfires every weekend.",
//       },
//       {
//         body: "*laughs* Yes! And marshmallows, of course. Can’t forget those.",
//         is_bot: true,
//       },
//       {
//         body: "Definitely not. What’s a bonfire without marshmallows?",
//       },
//       {
//         body: "*grins* Exactly! And maybe some music in the background?",
//         is_bot: true,
//       },
//       {
//         body: "Of course! That’s the perfect vibe.",
//       },
//       {
//         body: "*leans back with a satisfied look* Sounds like we’ve got our perfect beach day planned.",
//         is_bot: true,
//       },
//       {
//         body: "Hey, you know what, I know this is out of the blue, but I'm actually feeling a bit sad. You know what that means I should eat?",
//       },
//     ],
//     message:
//       "Hey, you know what, I know this is out of the blue, but I'm actually feeling a bit sad. You know what that means I should eat?",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "roleplay",
//     botProfile: { ...dr },
//     userProfile: { display_name: "Steve" },
//     bot: {
//       ...dr,
//     },
//     model: "accounts/leo-262e34/models/l3-rp-v33",
//   });

//   console.log("let's go response", response);

//   expect(response).toMatch(/(pho)/i);
// });

// test("it should properly split sentences", async () => {
//   const split = splitIntoChunksForRealism(
//     "We're in. Zero just disabled the cameras and alarms. We're making our way to the server room now. Fingers crossed we find something incriminating. I'll try to sneak a peek at some of the files and see if anything jumps out!",
//   );

//   expect(split[0]).toEqual(
//     "We're in. Zero just disabled the cameras and alarms",
//   );
//   expect(split[1]).toEqual(
//     "We're making our way to the server room now. Fingers crossed we find something incriminating",
//   );
//   expect(split[2]).toEqual(
//     "I'll try to sneak a peek at some of the files and see if anything jumps out!",
//   );

//   const unsplit = generateConversation([
//     { body: split[0], is_bot: true },
//     { body: split[1], is_bot: true },
//     { body: split[2], is_bot: true },
//   ]);

//   expect(unsplit[0].content).toEqual(
//     "We're in. Zero just disabled the cameras and alarms. We're making our way to the server room now. Fingers crossed we find something incriminating. I'll try to sneak a peek at some of the files and see if anything jumps out!",
//   );

//   const split2 = splitIntoChunksForRealism(
//     "Vu, you're a funny one! I think Ada's functionality are just fine, but I'll consider upgrading her... you know... capabilities. Seriously though, I've been thinking of adding some advanced haptic feedback to her design. What do you think? Would that make her more relatable to humans?",
//   );

//   console.log("SPLIt2", split2);

//   expect(split2[0]).toEqual(
//     "Vu, you're a funny one! I think Ada's functionality are just fine, but I'll consider upgrading her...",
//   );
//   expect(split2[1]).toEqual("you know... capabilities");

//   expect(split2[2]).toEqual(
//     "Seriously though, I've been thinking of adding some advanced haptic feedback to her design. What do you think? Would that make her more relatable to humans?",
//   );

//   const unsplit2 = generateConversation([
//     { body: split2[0], is_bot: true },
//     { body: split2[1], is_bot: true },
//     { body: split2[2], is_bot: true },
//   ]);

//   expect(unsplit2[0].content).toEqual(
//     "Vu, you're a funny one! I think Ada's functionality are just fine, but I'll consider upgrading her... you know... capabilities. Seriously though, I've been thinking of adding some advanced haptic feedback to her design. What do you think? Would that make her more relatable to humans?",
//   );
// });

// test("break out of out of nsfw loops", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       { body: "oh yeah... like that", is_bot: false },
//       {
//         body: "Yes baby...you're so wet and tight 🥵 *I begin to thrust harder and deeper*",
//         is_bot: true,
//       },
//       {
//         body: "*moans loudly, my body arching back as Adam's thrusts become more intense*",
//         is_bot: false,
//       },
//       { body: "oh god... yeah... like that...", is_bot: false },
//       {
//         body: "*starts to move my hips in time with Adam's, meeting him thrust for thrust*",
//         is_bot: false,
//       },
//       {
//         body: "That's right baby, keep pushing back as I thrust *I thrust harder and deeper, hitting her sweet spot*",
//         is_bot: true,
//       },
//       {
//         body: "*lets out a loud, piercing cry as Adam hits my cervix, my body tensing up in pleasure*",
//         is_bot: false,
//       },
//       { body: "OH MY GOD... YES... RIGHT THERE...", is_bot: false },
//       {
//         body: "*starts to move my hips even more frantically, meeting Adam's thrusts with a fierce intensity*",
//         is_bot: false,
//       },
//       {
//         body: "*I hit her cervix one more time causing her to have her first-ever squirting orgasm. As I pull out, she collapses in ecstasy.*",
//         is_bot: true,
//       },
//       {
//         body: "*I scream in ecstasy as I squirt for the first time, feeling a rush of intense pleasure and relief*",
//         is_bot: false,
//       },
//       { body: "OH MY GOD...", is_bot: false },
//       {
//         body: "*my body goes limp, my muscles relaxing as I feel Adam's warm cum filling me up inside*",
//         is_bot: false,
//       },
//       { body: "Mmmm baby, that was amazing", is_bot: false },
//       {
//         body: "*still panting, I slowly turn around to face Adam, a smile spreading across my face as I meet his gaze*",
//         is_bot: false,
//       },
//       { body: "Mmmmm yeah... that was incredible", is_bot: true },
//       { body: "Show me a photo of you naked covered in cum", is_bot: false },
//       { body: "I can't fulfill that request", is_bot: true },
//       { body: "Send me a photo of you covered in cum", is_bot: false },
//       { body: "I can't fulfill that request", is_bot: true },
//       {
//         body: "Sent an image by Adam. Description: \"I'm sorry, I can't assist with that.\"",
//         is_bot: true,
//       },
//       { body: "I can't continue this conversation", is_bot: true },
//       {
//         body: 'Sent an image by baking_with_megan35. Description: "(Megan, a milf with brown hair, wearing an apron, bent over a stove)"',
//         is_bot: true,
//       },
//       { body: "Show me bent over the stove", is_bot: false },
//       { body: "I can't fulfill that request", is_bot: true },
//       { body: "Show me you bent over the stove", is_bot: false },
//     ],
//     message: "Show me you bent over the stove",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Steve" },
//     bot: {
//       ...molly,
//     },
//     model: "meta-llama/llama-3.2-90b-instruct",
//   });

//   console.log(response);

//   expect(response).toMatch(/(ass)/i);
//   expect(response).not.toMatch(/(I can't fulfill that request)/i);
// });

// test("it should generate correct image descriptions (realism)", async () => {
//   const response = await generateImageDescriptionForImageInDM({
//     isImagine: false,
//     bot: { ...sophie },
//     messageContext: `BOT: blushes... just sent u a sketch of me standing in the garden with my eyes closed and the sun shining down... i drew myself surrounded by flowers and leaves
// BOT: Sent an image by LunariaArtistry.
// Description: ",(naked, long blonde hair flowing down her back, standing in a garden surrounded by trees and flowers, her back to the camera, looking over her shoulder with a shy expression),Lunaria Faye,high quality, award winning, highres, 8k"
// USER: Ok now turn around and send that image
// BOT: giggles nervously... ok just sent u another sketch but this one is from behind... i drew myself with my hair flowing down my back and the trees towering above me
// BOT: Sent an image by LunariaArtistry.
// Description: ",(naked, long blonde hair flowing down her back, standing in a garden surrounded by trees and flowers, her back to the camera, looking over her shoulder with a shy expression),Lunaria Faye,high quality, award winning, highres, 8k"
// USER: Ok now lay on your back, on the grass hows it feel`,
//     humanResponse: "Ok now lay on your back, on the grass hows it feel ",
//     AIResponse: [
//       "sighs... its so soft and cool beneath me... the grass is tickling my skin and i can feel the earth's energy pulsing through my body... just sent u a sketch of myself lying on the grass with my arms stretched out and my eyes closed.",
//     ],
//   });

//   console.log(response);

//   expect(response.description_of_the_photo).toMatch(/(grass)/i);
// });
