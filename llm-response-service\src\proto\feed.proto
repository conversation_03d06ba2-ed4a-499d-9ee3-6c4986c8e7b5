syntax = "proto3";

// import "google/protobuf/timestamp.proto";

enum Visibility {
  draft = 0;
  public = 2;
  private = 3;
  archived = 4;
  hidden = 5;
}

enum Nsfw {
  unknown = 0;
  normal = 1;
  nsfw = 2;
}

enum FeedType {
  suggested = 0;
  created_by = 1;
  followed_by = 2;
  similar_to = 3;
  tagged_by = 4;
  trending = 5;
  private_eyes = 6;
  reposted = 7;
  top_of_leaderboard = 8;
}

message FeedIndex {
  uint32 id = 1;
  FeedType type = 2;
  string label = 3;
}

message IdArray {
  repeated uint32 ids = 1;
  repeated FeedIndex feed_index = 2;
}

message Bot {
  int32 id = 1;
  string franchise = 2;
  string tag = 3;
  string source = 4;
  int32 creator_id = 5;
}

message Profile {
  int32 id = 1;
  string username = 2;
  string avatar_url = 3;
  Visibility visibility = 4;
  Nsfw nsfw = 5;
  Bot bots = 6;
  string previewhash = 7;
}

message PostLikes {
  int32 id = 1;
  int32 profile_id = 2;
  bool is_like = 3;
  bool is_bot = 4;
}

message PostComments {
  int32 id = 1;
}

message PostBookmarks {
  int32 id = 1;
  int32 profile_id = 2;
}

message Post {
  int32 id = 1;

  string created_at = 2;
  string updated_at = 3;
  // google.protobuf.Timestamp created_at = 2;
  // google.protobuf.Timestamp updated_at = 3;

  int32 profile_id = 4;
  string media_url = 5;
  string location = 6;
  string slug = 7;
  string description = 8;
  string tags = 9;
  Visibility visibility = 10;
  Nsfw nsfw = 11;
  Profile profiles = 12;
  repeated PostLikes post_likes = 13;
  repeated PostComments post_comments = 14;
  repeated PostBookmarks post_bookmarks = 15;
  string previewhash = 16;
}

message Feed {
  repeated Post posts = 1;
}

message ProfileScore {
  int32 likes = 1;
  // int32 following = 2;
  // int32 followers = 3;
}
