const { supabase } = require("./supabaseClient");
const { wrappedSupabaseError } = require("./utils");

async function createConversation(
  type,
  sender_id,
  participant_ids,
  participant_setting,
  message,
) {
  if (!participant_ids?.length) {
    throw new Error("Invalid participant_ids");
  }

  const { data, error } = await supabase.rpc(
    "create_conversations_and_messages_v3",
    {
      type,
      sender_id,
      data: message
        ? { participant_ids, participant_setting, message }
        : { participant_ids, participant_setting },
    },
  );

  if (error) {
    throw wrappedSupabaseError(error, "failed to create conversation");
  }

  return { data, error };
}
module.exports = {
  createConversation,
};
