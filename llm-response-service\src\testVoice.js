/**
 * Test script for voice generation using Hume.ai
 *
 * This script tests the voiceHelpers module by:
 * 1. Generating a voice with a custom description
 * 2. Using that voice to generate audio for a test phrase
 * 3. Saving the audio URL
 *
 * Run with: node src/testVoice.js
 */

const {
  generateVoiceWithDescription,
  generateAndUploadVoiceAudio,
} = require("./voiceHelpers");

// Sample voice descriptions to try
const voiceDescriptions = [
  "Deep masculine voice with a slight British accent, warm and authoritative tone, speaking at a moderate pace with clear articulation",
  "Youthful feminine voice with a slight Southern drawl, energetic and friendly tone, speaking with an upbeat rhythm and smooth delivery",
  "Middle-aged masculine voice with a clear, rhythmic Scots lilt, rounded vowels, and a warm, steady tone with an articulate, academic quality",
];

// Test phrase to generate
const testPhrase =
  "The voice you're hearing now is generated by artificial intelligence. It can be used to narrate videos, create audio content, or provide voiceovers for various applications.";

// Run the test
async function runVoiceTest() {
  try {
    console.log("Starting voice generation test...");

    // Pick a random voice description from the samples
    const description =
      voiceDescriptions[Math.floor(Math.random() * voiceDescriptions.length)];
    console.log(`Using voice description: "${description}"`);

    // Generate a voice with the description
    console.log("Generating voice...");
    const voiceId = await generateVoiceWithDescription(description);
    console.log(`Generated voice ID: ${voiceId}`);

    // Generate audio with the voice
    console.log("Generating audio with the voice...");
    const audioUrl = await generateAndUploadVoiceAudio(
      voiceId,
      testPhrase,
      "test-profile",
      `test-${Date.now()}`,
    );

    console.log("Test completed successfully!");
    console.log(`Voice ID: ${voiceId}`);
    console.log(`Audio URL: ${audioUrl}`);
    console.log(
      "You can access this audio at the URL above to hear the generated voice.",
    );

    return { voiceId, audioUrl };
  } catch (error) {
    console.error("Error in voice test:", error);
    throw error;
  }
}

// Execute the test if this file is run directly
if (require.main === module) {
  runVoiceTest()
    .then((result) => console.log("Test finished successfully"))
    .catch((err) => console.error("Test failed:", err));
}

module.exports = { runVoiceTest };
