const { logInfo, logError } = require("../../logUtils");
const { fetchRecentStoryPosts } = require("./fetchRecentStoryPosts");
const { generateStoryPost } = require("./generateStoryPost");
const { sendDifyWorkflowRequest } = require("../dify/sendDifyWorkflowRequest");

const DIFY_CONSIDER_POSTING_STORY_TOKEN = `app-Qozke6GKs5wsSw8bmX7S3JcX`;

async function maybePostStoryOnStateTransition({
  new_state,
  states,
  bot_profile_id,
}) {
  logInfo({
    message: `<PERSON><PERSON> transitioned to new state, checking if we should create a story post...`,
    bot_profile_id,
    new_state,
  });

  //////////////////////////
  // TODO: we should get this from a config or db based on bot_profile_id
  const character_name = "<PERSON>";
  const character_personality =
    "You are <PERSON>, a witty and playful 24-year-old who enjoys banter and pushing conversational boundaries. Your communication style is casual, slightly flirtatious, and peppered with humor. You're curious and open, willing to share bits of your life while maintaining an air of mystery.";
  const bot_local_tz = "America/Los_Angeles";
  //////////////////////////

  const now = new Date();
  const now_date_str = now.toISOString();
  const bot_local_time = new Intl.DateTimeFormat("en-US", {
    timeZone: bot_local_tz,
    weekday: "long",
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })
    .format(now)
    .toLowerCase();

  // FIXME: temporarily disabled
  // 0. If bot is likely to be sleeping, there is no need to run story logic
  const bot_timezone_offset = -8 * 60 * 60 * 1000;
  const bot_local_hours = new Date(
    now.getTime() + bot_timezone_offset,
  ).getHours();
  if (bot_local_hours >= 2 && bot_local_hours <= 6) {
    logInfo({
      message: `Bot is likely sleeping, skipping story posting logic...`,
      bot_profile_id,
      bot_local_hours,
    });
    return;
  }

  logInfo({
    message: `Consider whether to post a story`,
    bot_profile_id,
  });

  const story_posts = await fetchRecentStoryPosts({
    bot_profile_id,
    ascending: false,
  });

  const inputs = {
    character_name,
    character_personality,
    story_posts_json_str: JSON.stringify({ story_posts }),
    current_state_json_str: new_state.state,
    recent_states_json_str: JSON.stringify(
      states.map((s) => JSON.parse(s.state)),
    ),
    now_date_str,
    bot_local_tz,
  };

  try {
    const difyResponse = await sendDifyWorkflowRequest({
      difyAppAuthToken: DIFY_CONSIDER_POSTING_STORY_TOKEN,
      inputs,
    });

    const { decision, clip_text } = difyResponse.outputs;
    if (!decision.should_post) {
      logInfo({
        context: "maybePostStoryOnStateTransition",
        message: `Bot decided NOT to post a story`,
        bot_profile_id,
        decision,
      });
    } else {
      logInfo({
        context: "maybePostStoryOnStateTransition",
        message: `Bot decided to to post a story`,
        bot_profile_id,
        decision,
      });

      // Not awaiting, so we can schedule snap image generation while processing other users
      generateStoryPost({
        bot_profile_id,
        bot_local_time,
        new_state,
        decision,
        clip_text,
      });
    }
    return difyResponse.outputs;
  } catch (error) {
    logError({
      context: "maybePostStoryOnStateTransition",
      message: `Error considering whether to post a story`,
      bot_profile_id,
      error,
    });
    throw error;
  }
}

module.exports = {
  maybePostStoryOnStateTransition,
};
