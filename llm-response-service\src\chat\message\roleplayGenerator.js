const { RoleplayMessageModel } = require("../model/RoleplayMessageModel");
const { messageGenerator } = require("./messageGenerator");

function roleplayGenerator(
  ctx,
  {
    imageRequested,
    message,
    messages,
    botProfile,
    userProfile,
    botConfiguration,
    conversationConfiguration,
    forceSfwImage,
  },
) {
  const textModel = new RoleplayMessageModel({
    message,
    messages,
    botProfile,
    userProfile,
    botConfiguration,
    conversationConfiguration,
  });

  return messageGenerator(ctx, textModel, {
    imageRequested,
    message,
    messages,
    botProfile,
    forceSfwImage,
    botConfiguration,
  });
}

module.exports = {
  roleplayGenerator,
};
