{"3": {"inputs": {"seed": 22, "steps": 10, "cfg": 2, "sampler_name": "ddpm", "scheduler": "exponential", "denoise": 1, "model": ["60", 0], "positive": ["108", 0], "negative": ["80", 0], "latent_image": ["116", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "realvisxlV40_v40LightningBakedvae.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "11": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "13": {"inputs": {"image": "payton.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "FACE 1"}}, "16": {"inputs": {"control_net_name": "diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "38": {"inputs": {"provider": "CPU"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "39": {"inputs": {"text": "a man, wearing a suit, standing in front of a mcdonald's", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "PROMPT 1"}}, "40": {"inputs": {"text": "hands", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "NEGATIVE PROMPT"}}, "60": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["11", 0], "insightface": ["38", 0], "control_net": ["16", 0], "image": ["13", 0], "model": ["4", 0], "positive": ["39", 0], "negative": ["40", 0], "image_kps": ["100", 0], "mask": ["102", 1]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "77": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["11", 0], "insightface": ["38", 0], "control_net": ["16", 0], "image": ["78", 0], "model": ["60", 0], "positive": ["89", 0], "negative": ["40", 0], "image_kps": ["101", 0], "mask": ["103", 1]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "78": {"inputs": {"image": "Screenshot 2024-09-23 at 7.48.34 PM.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "FACE 2"}}, "79": {"inputs": {"conditioning_1": ["77", 1], "conditioning_2": ["60", 1]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "80": {"inputs": {"conditioning_1": ["77", 2], "conditioning_2": ["60", 2]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "89": {"inputs": {"text": "photo, roger federer, standing in front of a mcdonald's", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "PROMPT 2"}}, "100": {"inputs": {"image": "bride3.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "101": {"inputs": {"axis": "x", "image": ["100", 0]}, "class_type": "ImageFlip+", "_meta": {"title": "🔧 Image Flip"}}, "102": {"inputs": {"image": "clipspace/clipspace-mask-2481472.3000000007.png [input]", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "103": {"inputs": {"image": "clipspace/clipspace-mask-2488434.200000001.png [input]", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "104": {"inputs": {"x": 0, "y": 0, "operation": "add", "destination": ["102", 1], "source": ["103", 1]}, "class_type": "MaskComposite", "_meta": {"title": "MaskComposite"}}, "105": {"inputs": {"mask": ["104", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "106": {"inputs": {"text": "in front of a mcdonald's", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "BACKGROUND PROMPT"}}, "107": {"inputs": {"strength": 1, "set_cond_area": "default", "conditioning": ["106", 0], "mask": ["105", 0]}, "class_type": "ConditioningSetMask", "_meta": {"title": "Conditioning (Set Mask)"}}, "108": {"inputs": {"conditioning_1": ["79", 0], "conditioning_2": ["107", 0]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "116": {"inputs": {"width": 1280, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "117": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "GPEN-BFR-512.onnx", "face_restore_visibility": 1, "codeformer_weight": 0, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "1", "source_faces_index": "0", "console_log_level": 1, "input_image": ["8", 0], "source_image": ["13", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "118": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "GPEN-BFR-512.onnx", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["117", 0], "source_image": ["78", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "120": {"inputs": {"filename_prefix": "ComfyUI", "images": ["118", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "121": {"inputs": {"seed": 99916452036769, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "karras", "denoise": 1, "model": ["4", 0], "positive": ["123", 0], "negative": ["125", 0], "latent_image": ["116", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "122": {"inputs": {"samples": ["121", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "123": {"inputs": {"text": "two people, wearing suits, standing in front of a mcdonald's", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "124": {"inputs": {"images": ["122", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "125": {"inputs": {"text": "", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "128": {"inputs": {"threshold": 0.5, "dilation": 10, "crop_factor": 13.600000000000001, "drop_size": 10, "labels": "all", "segm_detector": ["130", 1], "image": ["122", 0]}, "class_type": "SegmDetectorSEGS", "_meta": {"title": "SEGM Detector (SEGS)"}}, "130": {"inputs": {"model_name": "segm/person_yolov8m-seg.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}}