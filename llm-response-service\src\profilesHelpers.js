const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { generateEmbedding, logError } = require("./utils");
const { generateBio } = require("./llmHelper");

async function generateEmbeddingForProfile({
  profile: data,
  skipIfExists = false,
}) {
  if (skipIfExists) {
    const { data: profileData, error: queryProfileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", data.id)
      .neq("visibility", "archived")
      .single();

    if (queryProfileError) {
      const error = wrappedSupabaseError(queryProfileError);
      logError({
        context: "generateEmbeddingForProfile: failed to query profile",
        error,
        profile_id: data.id,
      });
      // intentionally continue
    }

    if (profileData?.embedding) {
      console.log("Profile already has embedding:", data.id);
      return;
    }
  }

  const toEmbed = {
    bio: generateBio(data.bots),
    franchise: data.bots?.franchise,
    source: data.bots?.source,
    tag: data.bots?.tag,
    username: data.username,
    profileDescription: data.description,
    location: data.location,
    displayName: data.display_name,
    nsfw: data.nsfw,
  };

  const filteredEmbedInput = Object.entries(toEmbed).reduce(
    (acc, [key, value]) => {
      if (value !== null) {
        acc[key] = value;
      }
      return acc;
    },
    {},
  );

  const embedding = await generateEmbedding(JSON.stringify(filteredEmbedInput));

  const { error: profileInsertError } = await supabase
    .from("profiles")
    .update({ embedding })
    .eq("id", data.id);

  if (profileInsertError) {
    logError({
      context:
        "*** generateEmbeddingForProfile error - failed to write to profiles table",
      error: wrappedSupabaseError(profileInsertError),
      profile_id: data.id,
    });
  }

  return embedding;
}

async function deleteFollowRequests(requesteeId, requesterIds) {
  try {
    const { error: deleteError } = await supabase
      .from("follow_requests")
      .delete()
      .eq("requestee_id", requesteeId)
      .in("requester_id", requesterIds);

    if (deleteError) {
      throw wrappedSupabaseError(deleteError);
    }
  } catch (error) {
    logError({
      context: "deleteFollowRequests failed",
      error,
      requesteeId,
    });
  }
}

module.exports = {
  generateEmbeddingForProfile,
  deleteFollowRequests,
};
