const { logInfo, logError } = require("../logUtils");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");
const {
  scheduleProposedPostGenerationTask,
} = require("./scheduleProposedPostGenerationTask");

async function enableProposedPostModeForBot({
  botProfileId,
  proposedPostNextGenerationDate,
  userProfileId = undefined,
}) {
  const { data: botProfile, error: botProfileError } = await supabase
    .from("profiles")
    .update({
      proposed_post_mode: true,
      proposed_post_next_generation_date:
        proposedPostNextGenerationDate.toISOString(),
    })
    .eq("id", botProfileId)
    .select("*");

  if (botProfileError) {
    const error = wrappedSupabaseError(botProfileError);
    logError({
      context: "enableProposedPostModeForBot",
      error,
      botProfileId,
    });
    throw error;
  }

  logInfo({
    context: "enableProposedPostModeForBot",
    message: `Scheduling proposed post generation task for bot profile ${botProfileId}...`,
    userProfileId,
    botProfileId,
  });
  const task = await scheduleProposedPostGenerationTask({
    botProfileId,
    scheduleDate: proposedPostNextGenerationDate,
  });
  logInfo({
    context: "enableProposedPostModeForBot",
    message: `Scheduled proposed post generation task for bot profile ${botProfileId}`,
    userProfileId,
    botProfileId,
    task,
  });

  return botProfile;
}

async function enableProposedPostModeForAllBotsOfUser({
  userProfileId,
  nowDate,
}) {
  logInfo({
    context: "enableProposedPostModeForAllBotsOfUser",
    message: "enabling proposed post mode for user's bots...",
    userProfileId,
  });

  const { data: bots, error: botsError } = await supabase
    .from("bots")
    .select(
      `*,
        profiles:profile_id!inner(
          id,
          avatar_url,
          username,
          display_name,
          description,
          visibility,
          nsfw,
          nsfl, 
          proposed_post_mode, 
          proposed_post_next_generation_date
        )
      `,
    )
    .eq("creator_id", userProfileId)
    .not("profiles.visibility", "in", "(archived,hidden)");

  if (botsError) {
    const error = wrappedSupabaseError(botsError);
    logError({
      context: "enableProposedPostModeForAllBotsOfUser",
      error,
      userProfileId,
    });
    throw error;
  }

  if (!bots.length) {
    logInfo({
      context: "enableProposedPostModeForAllBotsOfUser",
      message: "no bots to enable proposed post mode for",
      userProfileId,
    });
    return [];
  }

  logInfo({
    context: "enableProposedPostModeForAllBotsOfUser",
    message: `found ${bots.length} bots to enable proposed post mode for`,
    userProfileId,
    botsCount: bots.length,
  });

  const enabledBotProfiles = [];
  for (const bot of bots) {
    const botProfileId = bot.profile_id;

    logInfo({
      context: "enableProposedPostModeForAllBotsOfUser",
      message: `Enabling proposed post mode for bot profile ${botProfileId}...`,
      userProfileId,
      botProfileId,
    });
    const proposedPostNextGenerationDate = nowDate;
    const updatedBotProfile = await enableProposedPostModeForBot({
      botProfileId,
      proposedPostNextGenerationDate,
      userProfileId,
    });
    enabledBotProfiles.push(updatedBotProfile);
  }

  return enabledBotProfiles;
}

module.exports = {
  enableProposedPostModeForBot,
  enableProposedPostModeForAllBotsOfUser,
};
