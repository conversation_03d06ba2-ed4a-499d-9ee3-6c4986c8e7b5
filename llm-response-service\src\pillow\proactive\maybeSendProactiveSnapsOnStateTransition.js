const { logInfo } = require("../../logUtils");
const {
  identifyUsersWithStaleConversations,
} = require("./identifyUsersWithStaleConversations");
const {
  maybeSendProactiveSnapToUser,
} = require("./maybeSendProactiveSnapToUser");

async function maybeSendProactiveSnapsOnStateTransition({
  new_state,
  states,
  skip_user_profile_id,
  bot_profile_id,
}) {
  logInfo({
    message: `<PERSON><PERSON> transitioned to new state, checking if we should send proactive snaps...`,
    bot_profile_id,
    new_state,
  });

  const now = new Date();

  // FIXME: temporarily disabled
  // 0. If bot is likely to be sleeping, there is no need to run proactive snap logic
  const bot_timezone_offset = -8 * 60 * 60 * 1000;
  const bot_local_hours = new Date(
    now.getTime() + bot_timezone_offset,
  ).getHours();
  if (bot_local_hours >= 1 && bot_local_hours <= 7) {
    logInfo({
      message: `<PERSON><PERSON> is likely sleeping, skipping proactive snap logic...`,
      bot_profile_id,
      bot_local_hours,
    });
    return;
  }

  // 1. identify users with stale conversations
  let { usersToConsider } = await identifyUsersWithStaleConversations({
    bot_profile_id,
  });

  const HARDCODE_SANIUL = false;
  if (HARDCODE_SANIUL) {
    // HACK: hardcode saniul only for now
    usersToConsider = [371600];
    logInfo({
      message: `Ignoring usersToConsider, hardcoding just Saniul for now`,
      bot_profile_id,
      usersToConsider,
    });
  }

  // SANIUL: I think some Google Play automation ends up creating profiles and sending messages to Tiffany.
  //         This is my 5 minute attempt at filtering out those profiles from being eligible for proactive snaps.
  //         we should spend a bit more effort and clean up our profiles data and figure out how to prevent these "spam" profiles from being created.
  const SPAM_PROFILE_IDS_SET = new Set([
    413452, 413451, 413450, 413449, 413424, 413373, 413371, 413370, 411579,
    411577, 411270, 411154, 411153, 407959, 407957, 407956, 407955, 407954,
    407943, 407942, 407941, 407940, 407939, 384687, 384443, 384442, 384441,
    376978, 376799, 376798, 376797, 376796, 376636, 376630, 418207, 418204,
    414725, 414720, 413869, 413865, 413803, 413797, 413724, 413722, 413659,
    413658, 413443, 413442, 413363, 413362, 411611, 411599, 411180, 411175,
    407968, 407964, 407899, 407895, 384463, 384453, 380716, 379371, 376801,
    376800, 376650, 376647,
  ]);

  if (skip_user_profile_id) {
    usersToConsider = usersToConsider.filter(
      (user_profile_id) =>
        !SPAM_PROFILE_IDS_SET.has(user_profile_id) &&
        user_profile_id !== skip_user_profile_id,
    );
  }

  if (!usersToConsider.length) {
    logInfo({
      message: `No users to consider for proactive snaps, bailing!`,
      bot_profile_id,
    });
    return;
  }

  logInfo({
    message: `Found stale conversations, considering sending proactive snaps to users...`,
    usersToConsider,
  });

  // 2. for each user call into a dify workflow to see if the LLM thinks we should send a snap
  for (const userToConsider of usersToConsider) {
    await maybeSendProactiveSnapToUser({
      bot_profile_id,
      user_profile_id: userToConsider,
      new_state,
      states,
    });
  }
}

module.exports = {
  maybeSendProactiveSnapsOnStateTransition,
};
