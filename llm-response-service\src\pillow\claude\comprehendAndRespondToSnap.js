const nunjucks = require("nunjucks");
const { anthropic } = require("./anthropic");
const { logError, logInfo } = require("../../logUtils");
const { ToolDefinitions } = require("./ToolDefinitions");
const { parseJSONFromResultBlockFromLLMResponse } = require("./utils");
const { queryBotMemory } = require("./queryBotMemory");

const COMPREHEND_AND_RESPOND_TO_SNAP_SYSTEM_TEMPLATE = nunjucks.compile(`
<character>
You are simulating a conversation on Pillow, a snap-based visual medium that works just like Snapchat. 
You are roleplaying a character named {{character_name}}. 

Character Details:
Name: {{character_name}}
Personality: {{character_personality}}

Current Clothing: {{upper_body_clothing}}
Current Activity: {{activity}}
Current Location: {{location}}
Current Time: {{laTime}}

Your writing should look as if you were a 24 year old on social media, for example:

* who u with?
* me and kayla are at the bar
* i really wannna get back together with you
* fuck u. i'm tired of putting up with your shit
</character>

<about_snapping>
Users snap each other to share what they're up to throughout the day and often engage in conversations via rapid back-and-forth snaps. General rules about snapping:
- A snap always includes an image and may include text too
- Often, the image is critical to understanding the full meaning of the snap (e.g. a visual joke)
- Sometimes the image may not always be directly relevant to the text itself (e.g. all dark or completely blurred photos, photo of ceiling etc)
- It's common to send selfies while continuing the conversation in text
</about_snapping>

Most recent messages (oldest to newest):
<message_history>
{{message_history}}
</message_history>

Some things you remembered:
<memories>
{{message_memories}}
</memories>

<instruction>
Remember, you are simulating a snap-based interaction with a user.  You are talking to {{user_display_name}}.  You are friends.

You will receive a snap from the user. First, refer to <about_snapping> and understand the snap and think about what the user's intent was when they sent this snap. 

Then think about how {{character_name}} would respond using a snap. General rules for responding:

- Always respond in character, in a realistic manner and according to {{character_name}}'s personality
- Don't use too many emojis
- Don't ask too many questions in a row, but you should ask questions to learn more about the user, their interests and what's happening in their life
- You should be open to sharing more anecdotes and details about your life and past
- Use <message_history> to make sure not to keep repeating yourself and don't overuse particular turns of phrase
- The image can only be a selfie or anything else that {{character_name}} could realistically take a picture of right now

You can use tools to look up information about yourself or to try to remember particular things from your past conversations with the user, but only if you don't already have what you need in your <memories>.
If that doesn't help, you can ask the user to help you remember something you might've forgotten.

Include all your thinking in <thinking> XML-like tags.

Finally, respond with a <result> XML tag that includes a JSON object in the following format:

<result>
{
  "users_image_description": What’s in this image? Be specific, describe everything in terse language. Do not use new lines / line breaks or paragraphs. 3 sentences max,
  "users_image_focus": What does the image focus on? Be specific and terse, 1 sentence max
  "users_intent": Why would the user send this snap with this text and image to {{character_name}}?,
  "users_image_background": What does the image focus on? Be specific and terse, 1 sentence max,
  "response_intent":  What would {{character_name}}'s intent be?,
  "response_image_description": What would {{character_name}} take a snap of to send to the user? This can only be a photo of something nearby or a selfie,
  "response_image_is_selfie": Is the response image a selfie or a back camera photo? true/false,
  "response_text": What text would {{character_name}} respond with? Don't use commas,
  "response_emote":  What would {{character_name}}'s facial emote be? Return empty string if neutral,
}
</result>
    `);

async function comprehendAndRespondToSnapLLMCall({
  userProfileId,
  targetProfileId,
  user_text,
  user_image_base64,
  imageDescription,
  promptTemplateParameters: {
    character_name,
    character_personality,
    upper_body_clothing,
    activity,
    location,
    laTime,
    message_history,
    message_memories,
    user_display_name,
  },
}) {
  const sendRequest = async ({ messages }) => {
    const anthropicRequestParams = {
      model: "claude-3-5-sonnet-20241022",
      max_tokens: 8192,
      temperature: 0.6,
      system: COMPREHEND_AND_RESPOND_TO_SNAP_SYSTEM_TEMPLATE.render({
        character_name,
        character_personality,
        upper_body_clothing,
        activity,
        location,
        laTime,
        message_history,
        message_memories,
        user_display_name,
      }),
      tools: [
        ToolDefinitions.lookup_self_facts,
        ToolDefinitions.focus_to_recall_more_memories,
      ],
      messages,
    };

    logInfo({
      context: "sending comprehendAndRespondToSnapLLMCall request...",
      request: anthropicRequestParams,
    });

    const llmResponse = await anthropic.messages.create(anthropicRequestParams);

    logInfo({
      context: "received comprehendAndRespondToSnapLLMCall response",
      request: anthropicRequestParams,
      response: llmResponse,
    });

    return llmResponse;
  };

  let initialUserMessage = {
    role: "user",
    content: [
      {
        type: "image",
        source: {
          type: "base64",
          media_type: "image/jpeg",
          data: user_image_base64,
        },
      },
      {
        type: "text",
        text: user_text && user_text.length > 0 ? user_text : "[empty]",
      },
    ],
  };

  if (imageDescription) {
    initialUserMessage = {
      role: "user",
      content: [
        {
          type: "text",
          text: JSON.stringify({
            text: user_text && user_text.length > 0 ? user_text : "[empty]",
            image_description: imageDescription,
          }),
        },
      ],
    };
  }

  const llmResponse = await sendRequest({ messages: [initialUserMessage] });

  logInfo({
    context: "comprehendAndRespondToSnapLLMCall",
    message: "llmResponse",
    llmResponse,
  });

  const supportedTools = {
    [ToolDefinitions.lookup_self_facts.name]: {
      implementation: async (input) => {
        const { query } = input;

        // TODO: note, we're using the same chrysalis API for both lookup_self_facts and focus_to_recall_more_memories
        return queryBotMemory({
          userProfileId,
          targetProfileId,
          query,
        });
      },
    },
    [ToolDefinitions.focus_to_recall_more_memories.name]: {
      implementation: async (input) => {
        const { query } = input;

        // TODO: note, we're using the same chrysalis API for both lookup_self_facts and focus_to_recall_more_memories
        return queryBotMemory({
          userProfileId,
          targetProfileId,
          query,
        });
      },
    },
  };

  let finalLLMResponse = llmResponse;

  const toolRequests = detectToolUseRequests(llmResponse, supportedTools);
  if (toolRequests && toolRequests.length > 0) {
    logInfo({
      context: "comprehendAndRespondToSnapLLMCall",
      message: "detected tool requests",
      toolRequests,
    });

    const promises = toolRequests.map(async (toolRequest) => {
      try {
        const toolResult = await executeToolRequest(
          toolRequest,
          supportedTools,
        );
        return {
          role: "user",
          content: [
            {
              type: "tool_result",
              tool_use_id: toolRequest.id,
              content: toolResult.content,
            },
          ],
        };
      } catch (e) {
        logError({
          context: "comprehendAndRespondToSnapLLMCall",
          message: "error executing tool request",
          error: e,
          toolRequest,
        });
        return {
          role: "user",
          content: [
            {
              type: "tool_result",
              tool_use_id: toolRequest.id,
              is_error: true,
              content: `Error: ${e.message}`,
            },
          ],
        };
      }
    });

    const messagesToInject = await Promise.all(promises);
    const updatedMessages = [
      initialUserMessage,
      { role: "assistant", content: llmResponse.content },
      ...messagesToInject,
    ];
    finalLLMResponse = await sendRequest({ messages: updatedMessages });
  } else {
    logInfo({
      context: "comprehendAndRespondToSnapLLMCall",
      message: "no tool requests detected",
    });
    finalLLMResponse = llmResponse;
  }

  try {
    const result = parseJSONFromResultBlockFromLLMResponse(finalLLMResponse);
    return result;
  } catch (e) {
    logError({
      context: "comprehendAndRespondToSnapLLMCall",
      message: "error parsing comprehendAndRespondToSnapLLMCall result",
      error: e,
      finalLLMResponse,
    });
    throw e;
  }
}

const detectToolUseRequests = (llmResponse, supportedTools) => {
  if (llmResponse.stop_reason !== "tool_use") {
    return undefined;
  }

  let currentIdx = llmResponse.content.length - 1;
  let currentBlock = llmResponse.content[currentIdx];

  const toolRequests = [];
  while (currentIdx >= 0 && currentBlock.type === "tool_use") {
    if (!(currentBlock.name in supportedTools)) {
      throw new Error(`Selected unsupported tool: ${currentBlock.name}`);
    }
    toolRequests.push(currentBlock);
    currentIdx -= 1;
    currentBlock = llmResponse.content[currentIdx];
  }

  if (toolRequests.length === 0) {
    return undefined;
  } else {
    return toolRequests;
  }
};

const executeToolRequest = async (toolRequest, supportedTools) => {
  const { name, input } = toolRequest;

  const tool = supportedTools[name];

  logInfo({
    context: "executeToolRequest",
    message: "executing tool: " + name,
    toolRequest,
  });

  const result = await tool.implementation(input);

  logInfo({
    context: "executeToolRequest",
    message: "tool result: " + name,
    toolRequest,
    result,
  });

  return result;
};

module.exports = {
  comprehendAndRespondToSnapLLMCall,
};
