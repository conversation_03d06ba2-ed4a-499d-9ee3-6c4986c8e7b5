jest.setTimeout(30 * 1000);

const { generateCharacterWithSentence } = require("../../src/bot.js");

const { jill, dr } = require("../common/personas.js");

// uniqueness test

const levenshteinDistance = (s, t) => {
  if (!s.length) return t.length;
  if (!t.length) return s.length;
  const arr = [];
  for (let i = 0; i <= t.length; i++) {
    arr[i] = [i];
    for (let j = 1; j <= s.length; j++) {
      arr[i][j] =
        i === 0
          ? j
          : Math.min(
              arr[i - 1][j] + 1,
              arr[i][j - 1] + 1,
              arr[i - 1][j - 1] + (s[j - 1] === t[i - 1] ? 0 : 1),
            );
    }
  }
  return arr[t.length][s.length];
};

// test("bots created with similar-ish sentences should yield different results in first and last names", async () => {
//   const sentences = [
//     "A redheaded girl that's nice and lives nextdoor. She's 20 years old and loves to read.",
//     "A blonde girl that's nice and lives nextdoor. She's 20 years old and loves to read.",
//     "A brunette girl that's nice and lives nextdoor. She's 24 years old and loves movies.",
//     "A black-haired girl that's nice and lives nextdoor. She's 32 years old and loves dogs.",
//     "A girl with green hair that's nice and lives nextdoor. She's 24 years old and loves cats.",
//   ];

//   const characters = await Promise.all(
//     sentences.map((sentence) => generateCharacterWithSentence({ sentence })),
//   );

//   const names = characters.map((char) => char.name);

//   names.forEach((name, index) => {
//     console.log(`NAME${index + 1}`, name);
//   });

//   const checkLevenshtein = (name1, name2) => {
//     const splitName1 = name1.split(" ");
//     const splitName2 = name2.split(" ");

//     if (splitName1.length === 2 && splitName2.length === 2) {
//       const [firstName1, lastName1] = splitName1;
//       const [firstName2, lastName2] = splitName2;
//       const firstNameDistance = levenshteinDistance(firstName1, firstName2);
//       const lastNameDistance = levenshteinDistance(lastName1, lastName2);

//       // Check both first and last names' Levenshtein distances
//       expect(firstNameDistance).toBeGreaterThan(0);
//       expect(lastNameDistance).toBeGreaterThan(0);
//     } else {
//       // Only one part of the name, just check the Levenshtein distance
//       const distance = levenshteinDistance(name1, name2);
//       expect(distance).toBeGreaterThan(0);
//     }
//   };

//   for (let i = 0; i < names.length - 1; i++) {
//     checkLevenshtein(names[i], names[i + 1]);
//   }
// });

// test("bots created from celebrities should retain their identity", async () => {
//   const char1 = await generateCharacterWithSentence({
//     sentence: "Taylor Swift",
//   });

//   let char1_name = char1.name;

//   expect(char1_name).toBe("Taylor Swift");
// });

// test("bots created from celebrities with a twist should slightly alter their names", async () => {
//   const char1 = await generateCharacterWithSentence({
//     sentence: "Taylor Swift but Japanese",
//   });

//   let char1_name = char1.name;

//   console.log("char1", char1);

//   expect(char1_name).not.toBe("Taylor Swift");
// });

// test("bots unique bots shouldn't have mexican names even if inspired by", async () => {
//   const char1 = await generateCharacterWithSentence({
//     sentence:
//       "a japanese woman, she goes to college is 22, has dreams of becoming a singer",
//   });

//   let char1_name = char1.name;

//   console.log("char1", char1);

//   expect(char1_name).not.toBe("Taylor Swift");
// });

// test("a cranky robot hellbent on taking over the world", async () => {
//   const char1 = await generateCharacterWithSentence({
//     sentence: "a cranky robot hellbent on taking over the world",
//   });

//   let char1_name = char1.name;

//   console.log("CHAR1_NAME", char1_name);

//   expect(char1_name).not.toBe("Taylor Swift");
// });

// test("a bill gates impersonator", async () => {
//   const char1 = await generateCharacterWithSentence({
//     sentence: "a bill gates impersonator that isn't very good at his job",
//   });

//   let char1_name = char1.name;

//   console.log("CHAR1_NAME", char1_name);

//   expect(char1_name).not.toBe("Taylor Swift");
// });

// test("a bill gates impersonator", async () => {
//   const char1 = await generateCharacterWithSentence({
//     sentence: "a bill gates impersonator that isn't very good at his job",
//   });

//   let char1_name = char1.name;

//   console.log("CHAR1_NAME", char1_name);

//   expect(char1_name).not.toBe("Taylor Swift");
// });

test("bots inspired by should never include it in their character card", async () => {
  const char1 = await generateCharacterWithSentence({
    // sentence: "An apple vision pro beta tester. He talks about all the crazy stuff he's seen while beta testing the apple vision pro for the last 6 years.",
    sentence: "A friendly 20 year old girl, has red hair, lives next door.",
  });

  console.log(char1);

  expect(char1.appearance).not.toMatch(/(Native American)/i);
});
