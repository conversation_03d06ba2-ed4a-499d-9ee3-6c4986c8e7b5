jest.setTimeout(30 * 1000);

const {
  generateImageDescriptionForImageInDM,
} = require("../../src/botHelpers.js");
const {
  splitIntoChunksForRealism,
  generateConversation,
  splitIntoChunksForRoleplay,
  generateConversationCompletionInstruct,
} = require("../../src/llm.js");
const {
  sophie,
  minecraft_joe_rogan,
  dr,
  molly,
} = require("../common/personas.js");

// test("bot should remember user's favorite food (short) (realism)", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [{ body: "hi", is_bot: true }, { body: "hey how's it going?" }],
//     message: "hey how's it going",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...kanishka },
//     userProfile: { display_name: "<PERSON>" },
//     bot: {
//       ...kanishka,
//     },
//     model: "meta-llama/llama-3.1-70b-instruct",
//   });

//   console.log(response);

//   expect(response).toMatch(/(pho)/i);
//   expect(response).not.toMatch(/(text)/i);
// });

// test("realism should never leak screen play", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [{ body: "hi" }],
//     message: "hi",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...sophie },
//     userProfile: { display_name: "Payton" },
//     bot: {
//       ...sophie,
//     },
//     model: "meta-llama/llama-3.2-90b-instruct",
//   });

//   console.log(response);

//   expect(response).not.toMatch(/(leaning against the wall)/i);
// });

// test("bots shouldn't be boring", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [{ body: "hey" }, { body: "hey, what's up" }, { body: "not much. just came back from a Monday morning run with the boys" }],
//     message: "hi",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...molly },
//     userProfile: { display_name: "Payton" },
//     bot: {
//       ...molly,
//     },
//     model: "meta-llama/llama-3.2-90b-instruct",
//   });

//   console.log(response);

//   expect(response).not.toMatch(/(leaning against the wall)/i);
// });

// test("realism should not switch topics too frequently", async () => {
//   const { response } = await generateConversationCompletionInstruct({
//     messages: [
//       {
//         body: 'Sent an image by minecraftjoerogan. Description: "(Minecraft Joe Rogan, wearing a Creeper-print shirt, holding a diamond pickaxe, building a dirt house, sun in the sky, blocky trees in the background, wearing a Minecraft UFC championship belt around his waist),,Minecraft Joe Rogan, (Minecraft style) Joe Rogan but a pixelated Minecraft version of him, high quality, award winning, highres, 8k"',
//         is_bot: true,
//         is_system_message: true,
//       },
//       {
//         body: "Dude, haven't heard from you in a while, everything alright on your end?",
//         is_bot: true,
//       },
//       {
//         body: "Yep, sorry man. Just had a whirlwind of a weekend and didn't have time to reply.",
//         is_bot: false,
//       },
//     ],
//     message:
//       "Yep, sorry man. Just had a whirlwind of a weekend and didn't have time to reply.",
//     stream: false,
//     // temperature,
//     // repetition_penalty,
//     nsfw: true.nsfw,
//     chatMode: "realism",
//     botProfile: { ...minecraft_joe_rogan },
//     userProfile: { display_name: "Payton" },
//     bot: {
//       ...minecraft_joe_rogan,
//     },
//     model: "meta-llama/llama-3.2-90b-instruct",
//   });

//   console.log(response);

//   expect(response).not.toMatch(/(leaning against the wall)/i);
// });

test("it should generate correct image descriptions (realism)", async () => {
  const response = await generateImageDescriptionForImageInDM({
    isImagine: false,
    bot: { ...sophie },
    messageContext: `USER: Send that picture of you on a date with Jake you were wearing a gold dress
BOT: okay okay dont bring up the past just sent the pic of me in that gold dress on my date with jake
BOT: Sent an image by ZoraRose_Artista. 
Description: ",(Zora Rose, wearing a gold dress, deep red hair tied up in a ponytail, fair skin, green eyes, skinny, sitting across from Jake at a dimly lit restaurant, smiling, with a glass of wine in hand),Zora Rose,high quality, award winning, highres, 8k"
USER: Okay send that picture of you holding that precious baby girl when you were wearing a pink shirt
BOT: okay okay dont make me emotional just sent the pic of me holding my little sister when i was wearing that pink shirt
BOT: Sent an image by ZoraRose_Artista. 
Description: ",(Zora Rose, wearing a gold dress, deep red hair tied up in a ponytail, fair skin, green eyes, skinny, sitting across from Jake at a dimly lit restaurant, smiling, with a glass of wine in hand),Zora Rose,high quality, award winning, highres, 8k"`,
    humanResponse:
      "Okay send that picture of you holding that precious baby girl when you were wearing a pink shirt",
    AIResponse: ["i think i need a minute"],
  });

  console.log(response);

  expect(response.description_of_the_photo).toMatch(/(baby girl)/i);
});
