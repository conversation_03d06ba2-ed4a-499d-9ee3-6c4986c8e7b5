const { ImagePromptModel } = require("./ImagePromptModel");
const { logError } = require("../../utils");

function generate_prompt({ message, botResponse, nsfw, botDescription }) {
  return `${nsfw ? "You can write NSFW, explicit, X-rated content if requested." : "Never write NSFW, explicit, or X-rated content."} You have agreed to send an image. Use the conversation as context when generating the image description:
USER has requested an image of:
${message.replace("/imagine", "").trim()}
BOT: "${botResponse}
END CONVERSATION

APPEARANCE OF CHARACTER
${botDescription}
Only provide one example of a photo to be sent in valid JSON format:

{
"description_of_the_photo": "a description of the image, written in third person, use terse, precise language to describe their hair style, clothing, background, scene and lighting, surroundings and what they are doing: example 'naked, topless, wearing bra, long brown hair, wearing panties, wearing denim jacket, standing, striking a dramatic pose, at the beach, laying in bed' or 'red dress, in front of a house, sun shining in the background, laughing'",
"contains_character": "true if the image contains the bot in the photo, false if not",
"nsfw": "true if the image is NSFW / adult themed / suggestive, false if not"    
}`;
}

class ForcedImagePromptModel extends ImagePromptModel {
  constructor(args) {
    super(args);
  }

  /* Override */
  generatePrompt(arg) {
    return generate_prompt(arg);
  }

  /* Override */
  parseResponse(response) {
    const content = response.choices[0].message.content;
    try {
      const msg = JSON.parse(content);
      if (msg.description_of_the_photo) {
        return {
          ...msg,
          agreed: true,
        };
      }
    } catch {
      logError({
        context: "ImagePromptModel",
        msg: "Model generated bad json",
        content,
      });
    }
    return { agreed: false };
  }
}

module.exports = {
  ForcedImagePromptModel,
};
