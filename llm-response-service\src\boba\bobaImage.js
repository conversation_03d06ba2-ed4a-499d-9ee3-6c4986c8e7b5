/**
 * Boba Image Routes
 *
 * This module provides routes for image generation in the boba service.
 */

const express = require("express");
const { Storage } = require("@google-cloud/storage");
const { v4: uuid } = require("uuid");
const {
  generateImagesWithFireworks,
  generateImagesWithRunware,
  generateImagesWithRecraft,
  modelManager,
} = require("./bobaImageHelpers");
const { logInfo, logError } = require("../utils");
const { Anthropic } = require("@anthropic-ai/sdk");

// Initialize Anthropic client
const anthropic = new Anthropic({
  // apiKey: process.env.ANTHROPIC_API_KEY,
  apiKey:
    "************************************************************************************************************",
});

const app = express.Router();

// GCS bucket name
const bucketName = process.env.GCS_BUCKET_NAME || "butterflies-images-v1-us";

/**
 * Get available models for image generation
 *
 * @route GET /models
 * @returns {Object} The available models
 */
app.get("/models", (req, res) => {
  try {
    const availableModels = modelManager.getAvailableModels();

    res.json({
      models: availableModels,
    });
  } catch (error) {
    logError({
      message: "Error getting available models",
      error,
    });

    res.status(500).json({
      error: "Error getting available models",
      message: error.message,
    });
  }
});

/**
 * Generate an image from a prompt
 *
 * @route POST /generateImage
 * @param {string} prompt - The prompt to generate an image from
 * @param {string} [aspect_ratio="1:1"] - The aspect ratio of the image
 * @param {number} [guidance_scale=3.5] - The guidance scale for image generation
 * @param {number} [num_inference_steps=30] - The number of inference steps
 * @param {number} [num_generations=1] - The number of images to generate
 * @returns {Object} The generated image URLs
 */
app.post("/generateImage", async (req, res) => {
  try {
    // Extract parameters from request
    const {
      prompt,
      aspect_ratio = "9:16",
      guidance_scale,
      num_inference_steps,
      num_generations = 1,
      seed,
      optimize = true,
      model = "flux",
      provider = "runware", // Default to runware
      rendering_style,
    } = req.body;

    if (!prompt) {
      return res.status(400).json({
        error: "Prompt is required",
      });
    }

    // Validate model
    if (!modelManager.getModelId(model)) {
      return res.status(400).json({
        error: `Invalid model: ${model}. Available models: ${modelManager
          .getAvailableModels()
          .join(", ")}`,
      });
    }

    // Get model settings
    const modelSettings = modelManager.getModelSettings(model);

    // Set default rendering style based on model if not provided
    let finalRenderingStyle = rendering_style;
    if (!finalRenderingStyle) {
      if (model === "pony" || model === "autism") {
        finalRenderingStyle = "anime";
      } else {
        // Default for flux, realvisxl, and any other models
        finalRenderingStyle = "photo realistic";
      }
    }

    // Use model-specific defaults if not provided
    const finalGuidanceScale = guidance_scale || modelSettings.cfgScale;
    const finalInferenceSteps = num_inference_steps || modelSettings.steps;

    logInfo({
      message: "Generating images",
      prompt,
      aspect_ratio,
      guidance_scale: finalGuidanceScale,
      num_inference_steps: finalInferenceSteps,
      num_generations,
      seed,
      model,
      provider,
      rendering_style: finalRenderingStyle,
    });

    let new_prompt = prompt;

    if (optimize) {
      // Different prompt optimization instructions based on model
      let promptInstructions = "";

      if (model === "flux") {
        promptInstructions = `Return a new description using the user's image_prompt written in detailed language. Make sure to describe the lighting, and scenery and characters. Also describe rendering style, like art style, photo realistic, 3d rendering, etc. When describing characters, vividly describe their clothing and pose and what they may be holding if anything.`;
      } else if (
        model === "realvisxl" ||
        model === "pony" ||
        model === "autism"
      ) {
        promptInstructions = `Return a new description using the user's image_prompt. Do not use periods, write in short descriptions separated by commas. Include details about lighting, scenery, characters, and rendering style.`;
      } else {
        // Default instructions
        promptInstructions = `Return a new description using the user's image_prompt written in detailed language. Make sure to describe the lighting, and scenery and characters. Also describe rendering style, like art style, photo realistic, 3d rendering, etc.`;
      }

      const messages = [
        {
          role: "user",
          content: `Take this user's prompt for a image.
  <image_prompt>
  ${prompt}
  </image_prompt>
  <rendering_style>
  ${finalRenderingStyle}
  </rendering_style>
  
  ${promptInstructions}`,
        },
      ];

      // Call Claude API to generate video content
      const completion = await anthropic.messages.create({
        model: "claude-3-7-sonnet-20250219",
        max_tokens: 1024,
        messages,
        system: "Only return the output, nothing else.",
      });

      new_prompt = completion.content[0].text;

      console.log("new_prompt", new_prompt);
    }

    // Generate images based on provider
    let images;
    let finalPrompt = new_prompt;
    if (provider === "runware") {
      const result = await generateImagesWithRunware({
        prompt: new_prompt,
        aspect_ratio,
        guidance_scale: finalGuidanceScale,
        num_inference_steps: finalInferenceSteps,
        num_generations,
        seed,
        model,
        rendering_style: finalRenderingStyle,
      });
      images = result.images;
      finalPrompt = result.finalPrompt;
    } else if (provider === "recraft") {
      const result = await generateImagesWithRecraft({
        prompt: new_prompt,
        aspect_ratio,
        guidance_scale: finalGuidanceScale,
        num_inference_steps: finalInferenceSteps,
        num_generations,
        seed,
        model, // Note: model is not used directly in Recraft as it always uses "recraftv3"
        rendering_style: finalRenderingStyle,
      });
      images = result.images;
      finalPrompt = result.finalPrompt;
    } else {
      // Default to Fireworks
      const result = await generateImagesWithFireworks({
        prompt: new_prompt,
        aspect_ratio,
        guidance_scale: finalGuidanceScale,
        num_inference_steps: finalInferenceSteps,
        num_generations,
        seed,
        model,
        rendering_style: finalRenderingStyle,
      });
      images = result.images;
      finalPrompt = result.finalPrompt;
    }

    if (!images || images.length === 0) {
      return res.status(500).json({
        error: "Failed to generate images",
      });
    }

    // Upload images to Google Cloud Storage
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);
    const uploadPromises = images.map(async (image, index) => {
      const fileName = `boba/images/${uuid()}.png`;
      const file = bucket.file(fileName);

      await file.save(image.buffer, {
        metadata: {
          contentType: "image/png",
        },
        resumable: false,
      });

      // Generate a public URL for the file
      const publicUrl = `https://storage.googleapis.com/${bucketName}/${fileName}`;

      return {
        url: publicUrl,
        seed: image.seed,
        index,
      };
    });

    const imageUrls = await Promise.all(uploadPromises);

    // Log the format of the imageUrls
    console.log("Image URLs format:", JSON.stringify(imageUrls[0], null, 2));

    // Return the image URLs
    res.json({
      images: imageUrls,
      prompt: finalPrompt,
      negative_prompt: modelManager.getNegativePrompt(model),
      optimize,
      model,
      provider,
      rendering_style: finalRenderingStyle,
    });
  } catch (error) {
    logError({
      message: "Error generating images",
      error,
    });

    res.status(500).json({
      error: "Error generating images",
      message: error.message,
    });
  }
});

module.exports = {
  app,
};
