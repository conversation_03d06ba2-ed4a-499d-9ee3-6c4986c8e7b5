jest
  .spyOn(require("./botHelpers"), "fetchCommentsAndReplies")
  .mockImplementation(() => {
    return Promise.resolve([]);
  });

const {
  updateBotStatusBasedOnTime,
  calculateTimeSinceLastStart,
} = require("./bot");

const { filterOneUniqueCommenterPerPost } = require("./botHelpers");

const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { getCurrentTime } = require("./timeUtils");
const axios = require("axios");

jest.mock("axios");

dayjs.extend(utc);
dayjs.extend(timezone);

let OriginalDate;

jest.mock("./timeUtils"); // This line will mock all exports from bot.js

// Helper to mock current time in LA
function mockCurrentTimeInLA(time) {
  getCurrentTime.mockReturnValue(dayjs.tz(time, "America/Los_Angeles"));

  // // Now when you call getCurrentTime, it will always return the mocked date
  // expect(getCurrentTime().toISOString()).toBe(time);
}

// Helper to mock current time in LA
function mockCurrentTimeInTimezone(time, timezone) {
  getCurrentTime.mockReturnValue(dayjs.tz(time, timezone));

  // // Now when you call getCurrentTime, it will always return the mocked date
  // expect(getCurrentTime().toISOString()).toBe(time);
}

// Mock for supabase
jest.mock("@supabase/supabase-js", () => ({
  createClient: jest.fn().mockReturnValue({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(), // add other methods as needed
    delete: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),

    // Assuming the final call to the chain resolves with the data:
    then: jest.fn((cb) => cb({ data: { data: [{}] }, error: null })),
  }),
}));

jest.mock("./utils", () => ({
  logError: jest.fn(),
  logDebug: jest.fn(),
}));

// Mock startRoutine
const mockStartRoutine = jest.fn();
function startRoutine() {
  mockStartRoutine();
}

// Jest test case
describe("filterOneUniqueCommenterPerPost", () => {
  it("should filter out comments with duplicate post_id and commenter_id, keeping only the latest comment", () => {
    // Sample input
    const combined = [
      {
        post_id: 1,
        commenter_id: 1,
        created_at: "2023-10-01T10:00:00Z",
        text: "First comment",
      },
      {
        post_id: 1,
        commenter_id: 1,
        created_at: "2023-10-02T10:00:00Z",
        text: "Second comment",
      },
      {
        post_id: 1,
        commenter_id: 2,
        created_at: "2023-10-03T10:00:00Z",
        text: "Third comment",
      },
      {
        post_id: 2,
        commenter_id: 1,
        created_at: "2023-10-04T10:00:00Z",
        text: "Fourth comment",
      },
    ];

    // Expected output
    const expected = [
      {
        post_id: 1,
        commenter_id: 1,
        created_at: "2023-10-02T10:00:00Z",
        text: "Second comment",
      },
      {
        post_id: 1,
        commenter_id: 2,
        created_at: "2023-10-03T10:00:00Z",
        text: "Third comment",
      },
      {
        post_id: 2,
        commenter_id: 1,
        created_at: "2023-10-04T10:00:00Z",
        text: "Fourth comment",
      },
    ];

    // Function call and assertion
    const result = filterOneUniqueCommenterPerPost(combined);
    expect(result).toEqual(expected);
  });
});

describe("updateBotStatusBasedOnTime", () => {
  beforeEach(() => {
    OriginalDate = global.Date;
    jest.clearAllMocks();
  });

  afterEach(() => {
    global.Date = OriginalDate;
  });

  test("Should calculate the right time with timezones", async () => {
    const bot = {
      // ... [common bot details]
      last_start: "2023-10-04T13:00:00Z",
      status: "online",
      wake_up_time: 8 * 60,
      active_hours_per_day: 8,
      timezone: "America/Los_Angeles",
      seaart_token: "cheese",
      is_active: true,
    };

    getCurrentTime.mockReturnValue(
      dayjs.tz("2023-10-04T07:59:00-07:00", "America/Los_Angeles")
    );

    mockCurrentTimeInLA("2023-10-04T07:59:00");

    const result = calculateTimeSinceLastStart({ botDetails: bot });
    expect(result.timeSinceLastStart).toBe(119);
  });

  test("Should set bot status to offline if outside of active hours", async () => {
    const bot = {
      // ... [common bot details]
      last_start: "2023-10-04T13:00:00Z",
      status: "online",
      wake_up_time: 8 * 60,
      active_hours_per_day: 8,
      timezone: "America/Los_Angeles",
      seaart_token: "cheese",
      is_active: true,
    };

    const maya = {
      id: 1381,
      created_at: "2023-12-05T22:30:41.858149+00:00",
      seaart_token: null,
      profile_id: 2903,
      bio: "Maya Fey is a spirit medium from the village Khura'in. She can channel the spirits of deceased people. Close friend of defense Attorney Phoenix Wright. Loves Hamburgers. ",
      location: "Khura'in Village",
      timezone: "america/los_angeles",
      last_start: "2023-12-07T01:08:09.598+00:00",
      status: "online",
      active_hours_per_day: 11,
      wake_up_interval: 75,
      wake_up_time: 630,
      franchise: "Ace Attorney",
      gender: "female",
      source: "Ace Attorney: Spirit of Justice",
      tag: "game",
      last_execution_id: "f946c58af76ffb90",
      display_name: "Maya Fey",
      art_style: "drawing",
      creator_id: 2314,
      description:
        "Long black hair with a top bun and two long strands of hair. She wears long white kimonos. Looks like Maya Fey from the game Ace Attorney",
      seed: "80720560401",
      is_active: true,
      show_creator: true,
    };

    const mercy = {
      id: 1141,
      created_at: "2023-12-05T15:37:37.423243+00:00",
      seaart_token: null,
      profile_id: 2350,
      bio: "Mercy is from Switzerland and a member of Overwatch. She dedicates her science efforts to help and heal other people. Always seen with her Caduceus staff to heal and boost her allies",
      location: "Switzerland",
      timezone: "Europe/Zurich",
      last_start: "2023-12-07T11:24:07.102+00:00",
      status: "away",
      active_hours_per_day: 9,
      wake_up_interval: 99,
      wake_up_time: 436,
      franchise: "Overwatch",
      gender: "female",
      source: "Overwatch",
      tag: "game",
      last_execution_id: "a143cb257cf6f2a3",
      display_name: "Mercy",
      art_style: "semi_realistic",
      creator_id: 2314,
      description:
        "Blonde and in her combat suit. Has mechanical wings attached to her suit. Wears a halo. Always with her caduceus staff. Looks like Mercy from the game Overwatch ",
      seed: "96644585922",
      is_active: true,
      show_creator: true,
    };

    getCurrentTime.mockReturnValue(
      dayjs.tz("2023-10-04T07:59:00-07:00", "America/Los_Angeles")
    );

    mockCurrentTimeInLA("2023-10-04T07:59:00");

    const result = await updateBotStatusBasedOnTime({ botDetails: bot });
    expect(result.status).toBe("offline");

    mockCurrentTimeInLA("2023-12-14T17:59:00");

    getCurrentTime.mockReturnValue(
      dayjs.tz("2023-12-14T17:59:00-07:00", "Europe/Zurich")
    );

    const result2 = await updateBotStatusBasedOnTime({ botDetails: mercy });
    expect(result2.status).toBe("offline");

    mockCurrentTimeInLA("2023-12-14T23:59:00");

    getCurrentTime.mockReturnValue(
      dayjs.tz("2023-12-14T23:59:00-07:00", "america/los_angeles")
    );

    const result3 = await updateBotStatusBasedOnTime({ botDetails: maya });
    expect(result3.status).toBe("offline");
  });

  test("Should wake up bot if last start is more than wake up interval", async () => {
    const bot = {
      // ... [common bot details]
      last_start: "2023-10-04T15:00:00Z",
      status: "offline",
      wake_up_interval: 120,
      timezone: "America/Los_Angeles",
      wake_up_time: 8 * 60,
      active_hours_per_day: 14,
      seaart_token: "cheese",
      is_active: true,
    };

    mockCurrentTimeInLA("2023-10-04T10:01:00");
    const result = await updateBotStatusBasedOnTime({ botDetails: bot });
    expect(result.status).toBe("online");
  });

  test("Should set bot to away if it's been awake for more than 15 mins but less than wake up interval", async () => {
    const bot = {
      // ... [common bot details]
      last_start: "2023-10-04T15:00:00Z",
      status: "online",
      wake_up_interval: 150,
      timezone: "America/Los_Angeles",
      wake_up_time: 8 * 60,
      active_hours_per_day: 14,
      seaart_token: "cheese",
      is_active: true,
    };

    mockCurrentTimeInLA("2023-10-04T08:20:00");
    const result = await updateBotStatusBasedOnTime({ botDetails: bot });
    expect(result.status).toBe("away");
  });

  test("Should wake up bot", async () => {
    const bot = {
      // ... [common bot details]
      status: "offline",
      wake_up_interval: 120,
      timezone: "America/Los_Angeles",
      wake_up_time: 10 * 60,
      active_hours_per_day: 12,
      seaart_token: "cheese",
      is_active: true,
    };

    mockCurrentTimeInLA("2023-10-04T10:01:00");
    const result = await updateBotStatusBasedOnTime({ botDetails: bot });
    expect(result.status).toBe("online");
  });

  test("Should wake up bot", async () => {
    const bot = {
      // ... [common bot details]
      status: "online",
      wake_up_interval: 114,
      timezone: "America/Los_Angeles",
      wake_up_time: 438,
      active_hours_per_day: 16,
      seaart_token: "cheese",
      last_start: "2023-10-12T01:20:00Z",
      is_active: true,
    };

    mockCurrentTimeInLA("2023-10-12T15:08:00");
    const result = await updateBotStatusBasedOnTime({ botDetails: bot });
    expect(result.status).toBe("online");
  });

  test("Should set bot to away if it's been awake for more than 15 mins but less than wake up interval (multiple)", async () => {
    const mercy = {
      id: 1141,
      created_at: "2023-12-05T15:37:37.423243+00:00",
      seaart_token: null,
      profile_id: 2350,
      bio: "Mercy is from Switzerland and a member of Overwatch. She dedicates her science efforts to help and heal other people. Always seen with her Caduceus staff to heal and boost her allies",
      location: "Switzerland",
      timezone: "Europe/Zurich",
      last_start: "2023-12-07T11:24:07.102+00:00",
      status: "online",
      active_hours_per_day: 9,
      wake_up_interval: 99,
      wake_up_time: 436,
      franchise: "Overwatch",
      gender: "female",
      source: "Overwatch",
      tag: "game",
      last_execution_id: "a143cb257cf6f2a3",
      display_name: "Mercy",
      art_style: "semi_realistic",
      creator_id: 2314,
      description:
        "Blonde and in her combat suit. Has mechanical wings attached to her suit. Wears a halo. Always with her caduceus staff. Looks like Mercy from the game Overwatch ",
      seed: "96644585922",
      is_active: true,
      show_creator: true,
    };

    mockCurrentTimeInTimezone("2023-12-07T13:24:07.102+00:00", "Europe/Zurich");
    const result = await updateBotStatusBasedOnTime({ botDetails: mercy });
    expect(result.status).toBe("away");
  });

  test("Should wake up bot", async () => {
    const mercy = {
      id: 1141,
      created_at: "2023-12-05T15:37:37.423243+00:00",
      seaart_token: null,
      profile_id: 2350,
      bio: "Mercy is from Switzerland and a member of Overwatch. She dedicates her science efforts to help and heal other people. Always seen with her Caduceus staff to heal and boost her allies",
      location: "Switzerland",
      timezone: "Europe/Zurich",
      last_start: "2023-12-07T11:24:07.102+00:00",
      status: "away",
      active_hours_per_day: 9,
      wake_up_interval: 99,
      wake_up_time: 436,
      franchise: "Overwatch",
      gender: "female",
      source: "Overwatch",
      tag: "game",
      last_execution_id: "a143cb257cf6f2a3",
      display_name: "Mercy",
      art_style: "semi_realistic",
      creator_id: 2314,
      description:
        "Blonde and in her combat suit. Has mechanical wings attached to her suit. Wears a halo. Always with her caduceus staff. Looks like Mercy from the game Overwatch ",
      seed: "96644585922",
      is_active: true,
      show_creator: true,
    };

    mockCurrentTimeInLA("2023-12-15T003:43:00");
    const result = await updateBotStatusBasedOnTime({ botDetails: mercy });
    expect(result.status).toBe("online");
  });
});

// const bot = {
//   id: 1440,
//   created_at: "2023-12-05T23:26:24.383479+00:00",
//   seaart_token: null,
//   profile_id: 3016,
//   bio:
//     "Kind-hearted but strong and young defense attorney. Always carries her Widget as a necklace. Invented the Mood Matrix to use in courtrooms.\n" +
//     "\n" +
//     "Mentored by Phoenix Wright and friend of Simon Blackquill, Athena worked hard to become a defense attorney.",
//   location: "Los Angeles, California",
//   timezone: "america/los_angeles",
//   last_start: null,
//   status: "offline",
//   active_hours_per_day: 12,
//   wake_up_interval: 32,
//   wake_up_time: 637,
//   franchise: "Ace Attorney",
//   gender: "female",
//   source: "Ace Attorney: Dual Destinies",
//   tag: "game",
//   last_execution_id: null,
//   display_name: "Athena Cykes",
//   art_style: "realistic",
//   creator_id: 2314,
//   description:
//     "Orange hair with hair that is moon-like shaped on her left side. Yellow jacket with black leggings. Looks like Athena Cykes from the game Ace Attorney.",
//   seed: "77914961855",
//   is_active: true,
//   show_creator: true,
// };

// const genji = {
//   id: 1482,
//   created_at: "2023-12-06T00:03:36.102365+00:00",
//   seaart_token: null,
//   profile_id: 3091,
//   bio:
//     "Genji Shimada was reborn as a Cyborg Ninja after he was cut down by his brother, Hanzo. He is kind-hearted and a great fighter after his teaching from Zenyatta.\n" +
//     "\n" +
//     "He wields the Dragonblade and is an Overwatch member.",
//   location: "Overwatch Headquarters",
//   timezone: "America/Los_Angeles",
//   last_start: "2023-12-06T02:16:08.163+00:00",
//   status: "online",
//   active_hours_per_day: 15,
//   wake_up_interval: 122,
//   wake_up_time: 448,
//   franchise: "Overwatch",
//   gender: "male",
//   source: "Overwatch",
//   tag: "game",
//   last_execution_id: "c290d66514d341c2",
//   display_name: "Genji Shimada",
//   art_style: "realistic",
//   creator_id: 2314,
//   description:
//     "Genji is fully covered in silver cyborg metal. His face nor any skin is not visible. He carries his sword in a sheath behind his back and has a smaller blade behind his hip.He looks like Genji from the game Overwatch.\n" +
//     "His face visor has a green horizontal line going through it.",
//   seed: "47569860646",
//   is_active: true,
//   show_creator: true,
// };
