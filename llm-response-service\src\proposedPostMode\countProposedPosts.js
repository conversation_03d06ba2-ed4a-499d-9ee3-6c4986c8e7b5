const { logInfo } = require("../logUtils");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");

async function countProposedPosts({ botProfileId }) {
  const { data, error: supabaseError } = await supabase
    .from("posts")
    .select("count", { count: "exact" })
    .eq("profile_id", botProfileId)
    .in("proposed_post_state", ["proposed", "published", "rejected", "failed"])
    .not("visibility", "in", "(hidden,archived)");

  if (supabaseError) {
    const error = wrappedSupabaseError(supabaseError);
    throw error;
  }

  logInfo({
    context: "countProposedPosts",
    message: "countProposedPosts",
    botProfileId,
    data: data,
  });

  const { count } = data;

  return count;
}

module.exports = {
  countProposedPosts,
};
