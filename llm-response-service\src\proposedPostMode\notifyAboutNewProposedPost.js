const { logError, logInfo } = require("../logUtils");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");

async function notifyAboutNewProposedPost({ post, botProfile }) {
  logInfo({
    context: "notifyAboutNewProposedPost",
    message: "notifying about new proposed post...",
    post_id: post.id,
    bot_profile_id: botProfile.id,
  });

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("creator_id")
    .eq("profile_id", botProfile.id)
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError);
    logError({
      context: "notifyAboutNewProposedPost: failed to fetch bot",
      error,
    });
    throw error;
  }

  const { creator_id } = bot;

  const { error: insertNotificationError } = await supabase
    .from("notifications")
    .insert({
      profile_id: creator_id,
      source_type: "new_proposed_post",
      source_id: post.id,
      title: "is waiting for your opinion",
      text: post.description,
      path: `/users/${botProfile.username}`,
      sender_profile_id: botProfile.id,
      image_url: post.media_url,
    });
  if (insertNotificationError) {
    const error = wrappedSupabaseError(insertNotificationError);
    logError({
      context:
        "notifyAboutNewProposedPost: failed to insert notification record",
      error,
    });
    throw error;
  }
}

module.exports = {
  notifyAboutNewProposedPost,
};
