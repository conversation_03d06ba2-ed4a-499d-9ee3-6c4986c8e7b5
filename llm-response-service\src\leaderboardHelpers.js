const { addSpanEvent } = require("./instrumentation/tracer");
const { logError } = require("./logUtils");
const { supabase } = require("./supabaseClient");

async function setLeaderboadSubmissionPostToPending(
  post_id,
  activeLeaderboardId,
) {
  const { data: leaderboardSubmission, error: leaderboardSubmissionError } =
    await supabase
      .from("leaderboard_submissions")
      .select("id, owner_profile_id")
      .eq("post_id", post_id)
      .eq("leaderboard_id", activeLeaderboardId)
      .single();

  if (leaderboardSubmissionError) {
    logError({
      context: "setLeaderboadSubmissionPostToPending fetch submission error",
      error: leaderboardSubmissionError,
    });
    addSpanEvent("submission_error");
    return;
  }

  // set the leaderboard_submission to pending
  const { error: leaderboardSubmissionUpdateError } = await supabase
    .from("leaderboard_submissions")
    .update({
      status: "pending",
    })
    .eq("id", leaderboardSubmission.id);

  if (leaderboardSubmissionUpdateError) {
    logError({
      context: "setLeaderboadSubmissionPostToPending update submission error",
      error: leaderboardSubmissionUpdateError,
    });
    addSpanEvent("submission_update_error");
    return;
  }

  // send notification to the correct recipient
  const { error: notificationError } = await supabase
    .from("notifications")
    .insert({
      profile_id: leaderboardSubmission.owner_profile_id,
      source_type: "leaderboard_lifecycle",
      source_id: activeLeaderboardId,
      title: `Post generated`,
      text: `Your post for the challenge is ready to be reviewed!`,
      path: `/challenge?leaderboard_submission_id=${leaderboardSubmission.id}&open_review=true`,
    });

  if (notificationError) {
    logError({
      context: "setLeaderboadSubmissionPostToPending insert notification error",
      error: notificationError,
    });
    addSpanEvent("notification_error");
    return;
  }
}

async function submitPostToLeaderboard(leaderboard_id, post_id) {
  // Validate required fields
  if (!leaderboard_id || !post_id) {
    throw new Error(
      "Missing required fields: leaderboard_id and post_id are required",
    );
  }

  // Check if the leaderboard exists and is active
  const { data: leaderboard, error: leaderboardError } = await supabase
    .from("leaderboards")
    .select("*")
    .eq("id", leaderboard_id)
    .single();

  if (leaderboardError) throw leaderboardError;
  if (!leaderboard) {
    throw new Error("Leaderboard not found");
  }

  const now = new Date();
  const startTime = new Date(leaderboard.start_at);
  const endTime = new Date(leaderboard.end_at);

  if (now < startTime) {
    throw new Error("Leaderboard has not started yet");
  }
  if (now > endTime) {
    throw new Error("Leaderboard has ended");
  }

  // Check if post exists
  const { data: post, error: postError } = await supabase
    .from("posts")
    .select("*")
    .eq("id", post_id)
    .single();

  if (postError) throw postError;
  if (!post) {
    throw new Error("Post not found");
  }

  // Check if post is already submitted to this leaderboard
  const { data: existingSubmission, error: existingError } = await supabase
    .from("leaderboard_submissions")
    .select("*")
    .eq("leaderboard_id", leaderboard_id)
    .eq("post_id", post_id)
    .single();

  if (existingError && existingError.code !== "PGRST116") throw existingError;
  if (existingSubmission) {
    return {
      error: "Post already submitted to this leaderboard",
      submission: existingSubmission,
    };
  }

  // Create the submission
  const { data: submission, error: submissionError } = await supabase
    .from("leaderboard_submissions")
    .insert([
      {
        leaderboard_id,
        post_id,
        total_xp: 0, // Initialize with 0 XP
      },
    ])
    .select()
    .single();

  if (submissionError) throw submissionError;

  return { submission };
}

async function getLeaderboardSubmissionRanking({
  leaderboard_id,
  post_id,
  leaderboard_submission_id,
}) {
  // First, get the submission we want to find the rank for
  let targetSubmission;
  if (leaderboard_submission_id) {
    const { data, error } = await supabase
      .from("leaderboard_submissions")
      .select(
        `
        id, post_id, total_xp, leaderboard_id, owner_profile_id,
        posts!inner (
            id,
            media_url,
            previewhash,
            description,
            profile_id,
            nsfw,
            slug,
            profiles!inner (
              id,
              username,
              avatar_url,
              display_name,
              nsfw,
              bot:bots!bots_profile_id_fkey (
                creator:creator_id!inner (
                  id,
                  username,
                  avatar_url,
                  display_name,
                  nsfw
                )
              )
            )
          )
        `,
      )
      .eq("id", leaderboard_submission_id)
      .single();

    if (error) throw error;
    if (!data) {
      throw new Error("Leaderboard submission not found");
    }
    targetSubmission = data;
  } else {
    const { data, error } = await supabase
      .from("leaderboard_submissions")
      .select(
        `
        id, post_id, total_xp, leaderboard_id, owner_profile_id,
        posts!inner (
            id,
            media_url,
            previewhash,
            description,
            profile_id,
            nsfw,
            slug,
            profiles!inner (
              id,
              username,
              avatar_url,
              display_name,
              nsfw,
              bot:bots!bots_profile_id_fkey (
                creator:creator_id!inner (
                  id,
                  username,
                  avatar_url,
                  display_name,
                  nsfw
                )
              )
            )
          )
      `,
      )
      .eq("leaderboard_id", leaderboard_id)
      .eq("post_id", post_id)
      .single();

    if (error) throw error;
    if (!data) {
      throw new Error("Post not found in this leaderboard");
    }
    targetSubmission = data;
  }

  // Get the count of submissions with higher total_xp
  const { count, error: rankError } = await supabase
    .from("leaderboard_submissions")
    .select("*", { count: "exact", head: true })
    .eq("leaderboard_id", targetSubmission.leaderboard_id)
    .gt("total_xp", targetSubmission.total_xp);

  if (rankError) throw rankError;

  // Rank is count + 1 (since count is number of submissions with higher score)
  const rank = count + 1;

  // Get total number of submissions in this leaderboard
  const { count: totalCount, error: totalError } = await supabase
    .from("leaderboard_submissions")
    .select("*", { count: "exact", head: true })
    .eq("leaderboard_id", targetSubmission.leaderboard_id);

  if (totalError) throw totalError;

  // Get comment count for the post
  const { data: commentData, error: commentError } = await supabase
    .from("post_comments_count")
    .select("comments_count")
    .eq("post_id", targetSubmission.post_id)
    .maybeSingle();

  if (commentError) throw commentError;

  return {
    rank,
    total_participants: totalCount,
    submission: targetSubmission,
    comment_count: commentData ? commentData.comments_count : 0,
  };
}

module.exports = {
  submitPostToLeaderboard,
  setLeaderboadSubmissionPostToPending,
  getLeaderboardSubmissionRanking,
};
