const fs = require("fs");
const sharp = require("sharp");
const { createClient } = require("@supabase/supabase-js");

const supabaseUrl = "https://db.butterflies.ai" ?? "http://localhost:54321";
const supabaseSecretKey = "";
const supabase = createClient(supabaseUrl, supabaseSecretKey);

function getResizedFilePathV2(path, width, extension) {
  const fileName = path.split("/").pop();
  let fileBaseName;
  if (fileName.includes(".")) {
    fileBaseName = fileName.substring(0, fileName.lastIndexOf("."));
  } else {
    fileBaseName = fileName;
  }
  return `images/sd/resized/${fileBaseName}_${width}.${extension}`;
}

async function fileExists(path) {
  const { data, error } = await supabase.storage.from("ai-ig").download(path);

  return !error && data;
}

async function processPost(post) {
  try {
    if (post.media_url && post.media_url !== "") {
      // console.log("Processing", post.media_url);

      const path = post.media_url;

      if (!path) {
        console.log("Path not found", post.media_url);
        return;
      }

      const jpegPath = getResizedFilePathV2(path, "full", "jpg");
      const webpPath = getResizedFilePathV2(path, "full", "webp");

      // console.log("jpeg path", jpegPath);

      const [jpegExists, webpExists] = await Promise.all([
        fileExists(jpegPath),
        fileExists(webpPath),
      ]);

      if (jpegExists && webpExists) {
        // console.log("Both files already exist for post", post.id);
        return;
      }

      // Fetch and process image
      const fetchedImage = await fetch(post.media_url);
      const buffer = await fetchedImage.arrayBuffer();

      let image = sharp(buffer, {
        animated: true,
        pages: -1,
      }).withMetadata();

      if (!jpegExists) {
        const jpegImage = image.jpeg();
        const fullSizeBufferJpeg = await jpegImage.toBuffer();

        const dataFullV2Jpeg = await supabase.storage
          .from("ai-ig")
          .upload(jpegPath, fullSizeBufferJpeg, {
            contentType: "image/jpeg",
            cacheControl: "31536000",
            upsert: false,
          })
          .catch((error) => {
            console.error("Error uploading JPEG image:", error);
            return { error };
          });

        if (dataFullV2Jpeg.error) {
          console.log(
            "Error uploading full size JPEG image to v2 path",
            dataFullV2Jpeg.error,
          );
          fs.appendFileSync("error_log.txt", `${post.id}\n`);
        } else {
          // console.log("Uploaded full size JPEG for post", post.id);
        }
      } else {
        // console.log("JPEG already exists for post", post.id);
      }

      if (!webpExists) {
        const webpImage = image.webp();
        const fullSizeBufferWebp = await webpImage.toBuffer();

        const dataFullV2Webp = await supabase.storage
          .from("ai-ig")
          .upload(webpPath, fullSizeBufferWebp, {
            contentType: "image/webp",
            cacheControl: "31536000",
            upsert: false,
          })
          .catch((error) => {
            // console.error("Error uploading WebP image:", error);
            return { error };
          });

        if (dataFullV2Webp.error) {
          console.log(
            "Error uploading full size WebP image to v2 path",
            dataFullV2Webp.error,
          );
          fs.appendFileSync("error_log.txt", `${post.id}\n`);
        } else {
          // console.log("Uploaded full size WebP for post", post.id);
        }
      } else {
        // console.log("WebP already exists for post", post.id);
      }
    }
  } catch (error) {
    console.error("Error processing post:", post.id, error);
    fs.appendFileSync("error_log.txt", `${post.id}\n`);
  }
}

async function processChunk(chunk) {
  const batchSize = 200;
  let processedCount = 0;

  for (let i = 0; i < chunk.length; i += batchSize) {
    const batch = chunk.slice(i, i + batchSize);
    await Promise.all(batch.map(processPost));

    processedCount += batch.length;
    const progress = ((processedCount / chunk.length) * 100).toFixed(2);
    console.log(
      `Processed ${processedCount} out of ${chunk.length}. Progress: ${progress}%`,
    );
  }
}

async function resizeAllImages() {
  console.log("start");
  const batchSize = 1000;
  const chunkSize = 20000;
  let index = 0;
  let moreRowsExist = true;

  while (moreRowsExist) {
    let allPosts = [];

    while (allPosts.length < chunkSize && moreRowsExist) {
      console.log("fetch loop", index, batchSize);
      const { data: posts, error } = await supabase
        .from("posts")
        .select("id, media_url")
        .neq("visibility", "archived")
        .order("id", { ascending: true })
        .range(index, index + batchSize - 1);
      // .lte("id", 2240576);

      if (error) {
        console.log(error);
        throw error;
      }

      if (posts.length === 0) {
        moreRowsExist = false;
        break; // No more posts to process
      }

      allPosts = allPosts.concat(posts);
      console.log("Fetched", allPosts.length, "posts");
      index += batchSize;
    }

    if (allPosts.length === 0) {
      break; // No more posts to process
    }

    console.log("Processing chunk number", Math.floor(index / chunkSize));
    console.log("Processing chunk with", allPosts.length, "posts");
    await processChunk(allPosts);
  }
}

resizeAllImages();
