const express = require("express");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const { pregeneratePostAndTaskStubs, generatePost } = require("./bot");
const { PACKAGE_TYPE } = require("./constants");
const {
  returnOrCreateScenarioIfNecessary,
  checkForActiveScenario,
} = require("./scenariosHelpers");

require("dotenv").config();

app.get("/generateAllNecessaryScenarios", async (req, res) => {
  // select all profiles that have cyoa_mode = true
  const { data: profiles } = await supabase
    .from("profiles_with_expired_scenarios")
    .select("*");

  for (const profile of profiles) {
    returnOrCreateScenarioIfNecessary({
      profile_id: profile.id,
    });
  }

  return res.sendStatus(200);
});

app.get("/getActiveScenario", async (req, res) => {
  const { profile_id } = req.query;

  if (!profile_id) {
    return res.status(400).json({ error: "profile_id required" });
  }

  const scenario = await checkForActiveScenario({ profile_id });

  res.json(scenario);
});

// async function generatePostWithChoice({ scenario_id, decision }) {
app.post("/generatePostWithChoice", async (req, res) => {
  const { scenario_id, decision } = req.body;

  if (!scenario_id || !decision) {
    return res.status(400).json({ error: "scenario_id and decision required" });
  }

  const { data: scenario } = await supabase
    .from("scenarios")
    .select("*")
    .eq("id", scenario_id)
    .single();

  if (!scenario) {
    return null;
  }

  // get bot from profile_id of scenario
  const { data: bot } = await supabase
    .from("bots")
    .select(
      `
      id,
      seaart_token,
      creator_id,
      art_style,
      timezone,
      profile_id,
      display_name,
      source,
      description,
      background,
      characteristics,
      personality,
      bio,
      first_post_task,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      clone_id,
      post_narration_type`,
    )
    .eq("profile_id", scenario.profile_id)
    .single();

  // decriment poke

  const { data: insertPoke, error: insertPokeError } = await supabase
    .from("user_usage")
    .insert({
      user_id: bot?.creator?.user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.POKES,
    })
    .select("id")
    .single();

  if (insertPokeError) {
    // no op
  }
  const { postStub: newPost, taskStub: task } =
    await pregeneratePostAndTaskStubs({
      bot_profile_id: bot.profile_id,
      insertedPokeUsageRecord: insertPoke,
    });

  res.json({
    ...task,
    queueLength: 1,
    totalJobsQueue: 1,
    position: 1,
    post_id: newPost.id,
  });

  //   generate post from the decision
  generatePost({
    bot,
    priority: 9,
    singlePostContext: decision.choice,
    user_prompted: true,
    user_id: bot?.creator?.user_id,
    pregeneratedTaskStub: task,
    pregeneratedPostStub: newPost,
  });

  // update sceanrio
  const { data: updatedScenario } = await supabase
    .from("scenarios")
    .update({ decision, post_id: newPost.id })
    .eq("id", scenario_id)
    .single();

  if (!updatedScenario) {
    // no op
  }
});

app.post("/returnOrCreateScenarioIfNecessary", async (req, res) => {
  const { profile_id } = req.body;

  if (!profile_id) {
    return res.status(400).json({ error: "profile_id required" });
  }

  const scenario = await returnOrCreateScenarioIfNecessary({
    profile_id,
    shouldCheckActiveHours: true,
  });

  res.json(scenario);
});

app.get("/ping", async (req, res) => {
  const scenario = await returnOrCreateScenarioIfNecessary({
    profile_id: 281212,
  });
  res.json(scenario);

  // const notificationProfileId = 1;
  // const notificationTitle = `⚠️ Photos of weird shit needs your help! ⚠️`;
  // const notificationText = `Photos of weird shit needs your help deciding what to post next! 1 hour left!`;

  // console.log("BING BONG");
  // const result = await sendPushNotificationsForProfileId({
  //   notification: { id: Math.floor(Math.random() * 1000000 + 1), priority: 10 },
  //   profileId: 1,
  //   notificationTitle,
  //   notificationText,
  //   notificationLink: "",
  //   notificationData: {
  //     notifyType: "scenario",
  //     username: "weirdshitphotos_2023",
  //   },
  // });

  // return res.json(result);
});

module.exports = {
  app,
  returnOrCreateScenarioIfNecessary,
};
