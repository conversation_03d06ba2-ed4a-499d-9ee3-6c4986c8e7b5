jest.setTimeout(30 * 1000);

const {
  generatePostDetailsWithLLMWithTaggedBot,
  generatePostDetailsWithLLM,
  generateMemoriesContentFromLLM,
} = require("../../src/botHelpers.js");
const { supabase } = require("../../src/supabaseClient.js");

const {
  jill,
  dr,
  karen,
  vu_mariner,
  harry_pooper,
  weird_shit,
  absurd_comics,
  florida,
  sunday_comics,
} = require("../common/personas.js");

// test.skip("it should generate multiple characters", async () => {
//   const task = await generateMultipleCharactersImage({
//     bot_1_id: 78023,
//     bot_2_id: 9965,
//     bot_1_action: "Eating a hamburger",
//     bot_2_action: "holding a mcdonald's bag",
//     background: "in front of a McDonald's",
//   });

//   expect(task).toMatch(/(cat ears)/i);
// });

// test("it should generate multiple people in prompts", async () => {
//   const profile_id = 216462;

//   const tagged_profile_id = 217321;

//   // get the bot that you're trying to poke
//   const { data: bot, error: botError } = await supabase
//     .from("bots")
//     .select("*")
//     .eq("profile_id", profile_id)
//     .single();

//   // get tagged_bot for the tagged profile
//   const { data: tagged_bot, error: taggedBotError } = await supabase
//     .from("bots")
//     .select("*")
//     .eq("profile_id", tagged_profile_id)
//     .single();

//   console.log("bot", bot);
//   console.log("tagged_bot", tagged_bot);

//   // const { promptDetails } = await generatePostDetailsWithLLMWithTaggedBot({
//   //   bot,
//   //   tagged_bot,
//   //   prompt_text:
//   //     "@JonStewartSatireFan and Hilary Woodson cooking a meal together in the kitchen",
//   // });

//   // console.log("promptDetails", promptDetails);

//   // expect(promptDetails.image_prompt).toMatch(/(man)/i);
//   // expect(promptDetails.image_prompt).toMatch(/(woman)/i);

//   const { promptDetails: promptDetails2 } =
//     await generatePostDetailsWithLLMWithTaggedBot({
//       bot,
//       tagged_bot,
//       prompt_text:
//         "@JonStewartSatireFan and Hilary Woodson meditating together in the park",
//     });

//   console.log("promptDetails2", promptDetails2);

//   expect(promptDetails2.image_prompt).toMatch(/(man)/i);
//   expect(promptDetails2.image_prompt).toMatch(/(woman)/i);
// });

test("it generate non influencer", async () => {
  // const result = await generateMemoriesContentFromLLM({
  //   bot: { ...weird_shit },
  // });

  const result = await generateMemoriesContentFromLLM({
    bot: { ...florida },
  });

  // const result = await generateMemoriesContentFromLLM({
  //   bot: { ...florida },
  // });

  console.log("MEMORY", result);

  // const { postDeatils, prompt } = await generatePostDetailsWithLLM({
  //   bot: { ...vu_mariner },
  //   memory: result.memories[0],
  // });
});
