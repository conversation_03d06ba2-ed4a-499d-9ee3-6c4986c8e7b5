const { supabase } = require("../supabaseClient");
const { logError, logInfo, wrappedSupabaseError } = require("../utils");
const { generateVignetteScenes } = require("./generateVignetteScenes");
const {
  generateVignetteSceneImages,
} = require("./generateVignetteSceneImages");
const { processVignetteSceneImage } = require("./processVignetteSceneImage");
const { updatePostWithVignetteInfo } = require("./updatePostWithVignetteInfo");

/**
 * Creates a post_vignettes record and inserts the scenes into vignette_scenes
 *
 * @param {Object} params - The parameters object
 * @param {Object} params.post - The post object to generate vignettes for
 * @param {number} params.numScenes - The number of vignette scenes to generate
 * @returns {Promise<Object>} - The created post_vignettes record with scenes
 */
async function createVignetteForPost({ post, numScenes }) {
  try {
    // Generate the vignette scenes
    const scenes = await generateVignetteScenes({ post, numScenes });

    // Create a record in post_vignettes table
    const { data: postVignette, error: postVignetteError } = await supabase
      .from("post_vignettes")
      .insert({
        post_id: post.id,
        profile_id: post.profile_id,
        status: "pending", // Initial status
        scene_count: scenes.length,
      })
      .select()
      .single();

    if (postVignetteError) {
      const error = wrappedSupabaseError(postVignetteError);
      logError({
        context:
          "createVignetteForPost - Failed to insert post_vignettes record",
        error,
        post_id: post.id,
      });
      throw error;
    }

    // Insert scenes into vignette_scenes table
    const scenesWithPostVignetteId = scenes.map((scene) => ({
      post_vignette_id: postVignette.id,
      scene_description: scene.scene_description,
      scene_caption: scene.scene_caption,
      most_interesting: scene.most_interesting,
      contains_character: scene.contains_character,
      status: "pending", // Initial status
    }));

    const { data: insertedScenes, error: insertScenesError } = await supabase
      .from("vignette_scenes")
      .insert(scenesWithPostVignetteId)
      .select();

    if (insertScenesError) {
      const error = wrappedSupabaseError(insertScenesError);
      logError({
        context: "createVignetteForPost - Failed to insert vignette_scenes",
        error,
        post_vignette_id: postVignette.id,
        post_id: post.id,
      });
      throw error;
    }

    logInfo({
      context: "createVignetteForPost",
      message: `Successfully created ${insertedScenes.length} vignette scenes for post ${post.id}`,
      post_id: post.id,
      post_vignette_id: postVignette.id,
    });

    return {
      postVignette,
      scenes: insertedScenes,
    };
  } catch (error) {
    logError({
      context: "createVignetteForPost",
      error,
      post_id: post.id,
    });
    throw error;
  }
}

/**
 * Get all vignette scenes for a post
 *
 * @param {string} postId - The post ID to get vignettes for
 * @returns {Promise<Object>} - The post_vignettes record with scenes
 */
async function getVignetteForPost(postId) {
  try {
    // Get the post_vignettes record
    const { data: postVignette, error: postVignetteError } = await supabase
      .from("post_vignettes")
      .select("*")
      .eq("post_id", postId)
      .single();

    if (postVignetteError) {
      if (postVignetteError.code === "PGRST116") {
        // No record found - this is normal if vignettes haven't been created yet
        return null;
      }

      const error = wrappedSupabaseError(postVignetteError);
      logError({
        context: "getVignetteForPost - Failed to fetch post_vignettes",
        error,
        post_id: postId,
      });
      throw error;
    }

    // Get the associated scenes
    const { data: scenes, error: scenesError } = await supabase
      .from("vignette_scenes")
      .select("*")
      .eq("post_vignette_id", postVignette.id)
      .order("id", { ascending: true });

    if (scenesError) {
      const error = wrappedSupabaseError(scenesError);
      logError({
        context: "getVignetteForPost - Failed to fetch vignette_scenes",
        error,
        post_vignette_id: postVignette.id,
        post_id: postId,
      });
      throw error;
    }

    return {
      postVignette,
      scenes,
    };
  } catch (error) {
    logError({
      context: "getVignetteForPost",
      error,
      post_id: postId,
    });
    throw error;
  }
}

module.exports = {
  createVignetteForPost,
  getVignetteForPost,
  generateVignetteSceneImages,
  processVignetteSceneImage,
  updatePostWithVignetteInfo,
};
