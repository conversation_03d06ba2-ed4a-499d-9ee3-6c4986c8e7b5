function sdxlReactor({
  prompt,
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
  steps = 5,
  cfg = 1.5,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  badquality = 0,
  blurxl = 0,
  envyzoomslider = 0,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 3,
        cfg: 1.5,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["57", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["58", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${
          nsfw ? "" : ", (nsfw, nudity, naked)"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    12: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sd15.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    13: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    25: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    27: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["25", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    33: {
      inputs: {
        enabled: true,
        swap_model: "inswapper_128.onnx",
        facedetection: "YOLOv5l",
        face_restore_model: "GPEN-BFR-512.onnx",
        face_restore_visibility: 1,
        codeformer_weight: 0,
        detect_gender_input: "no",
        detect_gender_source: "no",
        input_faces_index: "0",
        source_faces_index: "0",
        console_log_level: 2,
        input_image: ["42", 0],
        source_image: ["27", 0],
      },
      class_type: "ReActorFaceSwap",
      _meta: {
        title: "ReActor 🌌 Fast Face Swap",
      },
    },
    36: {
      inputs: {
        provider: "CPU",
      },
      class_type: "InstantIDFaceAnalysis",
      _meta: {
        title: "InstantID Face Analysis",
      },
    },
    37: {
      inputs: {
        weight: 1,
        start_at: 0,
        end_at: 1,
        instantid: ["38", 0],
        insightface: ["36", 0],
        control_net: ["39", 0],
        image: ["27", 0],
        model: ["56", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        image_kps: ["44", 0],
      },
      class_type: "ApplyInstantID",
      _meta: {
        title: "Apply InstantID",
      },
    },
    38: {
      inputs: {
        instantid_file: "ip-adapter.bin",
      },
      class_type: "InstantIDModelLoader",
      _meta: {
        title: "Load InstantID Model",
      },
    },
    39: {
      inputs: {
        control_net_name: "diffusion_pytorch_model.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    40: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps,
        cfg,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["37", 0],
        positive: ["37", 1],
        negative: ["37", 2],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    42: {
      inputs: {
        samples: ["40", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    44: {
      inputs: {
        faceanalysis: ["36", 0],
        image: ["8", 0],
      },
      class_type: "FaceKeypointsPreprocessor",
      _meta: {
        title: "Face Keypoints Preprocessor",
      },
    },
    52: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["33", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
    53: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    54: {
      inputs: {
        lora_name: "badquality.safetensors",
        strength_model: badquality,
        model: ["53", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    56: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: blurxl,
        model: ["54", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    57: {
      inputs: {
        lora_name: "envyzoomslider.safetensors",
        strength_model: envyzoomslider,
        model: ["56", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    58: {
      inputs: {
        width,
        height,
        batch_size: 1,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
  };
}

module.exports = { sdxlReactor };
