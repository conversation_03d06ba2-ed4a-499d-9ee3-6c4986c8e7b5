const { logError } = require("../logUtils");
const { default: axios } = require("axios");
const { CHRYSALIS_BASE_URL } = require("./config");

// currently this calls chrysalis, but once we move to blanket the lookup can just happen in blanket
async function fetchConversationDetails({ bot_profile_id, user_profile_id }) {
  const params = {
    profile_id: user_profile_id.toString(), // chrysalis expects this to be a string
  };

  try {
    const result = await axios.post(
      `${CHRYSALIS_BASE_URL}/fetch_conversation_details`,
      params,
    );

    return result.data;
  } catch (error) {
    logError({
      context: "fetchConversationDetails failed",
      error,
      params,
      response_data: error.response.data,
    });
    throw error;
  }
}

module.exports = {
  fetchConversationDetails,
};
