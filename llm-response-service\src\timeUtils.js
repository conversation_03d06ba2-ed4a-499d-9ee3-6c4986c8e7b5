const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { logWarn } = require("./utils");

dayjs.extend(utc);
dayjs.extend(timezone);

const isBetween = require("dayjs/plugin/isBetween");
dayjs.extend(isBetween);

function getCurrentTime() {
  return dayjs();
}

function getSafeTimezone(timezone) {
  // check if valid
  try {
    getCurrentTime().tz(timezone);
    return timezone;
  } catch (error) {
    logWarn({
      context: "**** getSafeTimezone failed to parse timezone",
      error,
      message: "check where it comes from",
      timezone,
    });
    return "America/Los_Angeles";
  }
}

function isToday(date, timezone) {
  const providedDate = dayjs(date)
    .tz(timezone || "America/Los_Angeles")
    .startOf("day");
  const currentDate = dayjs()
    .tz(timezone || "America/Los_Angeles")
    .startOf("day");
  return providedDate.isSame(currentDate);
}

module.exports = {
  getCurrentTime,
  isToday,
  getSafeTimezone,
};
