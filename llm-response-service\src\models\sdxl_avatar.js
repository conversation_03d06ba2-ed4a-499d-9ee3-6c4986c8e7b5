function sdxlAvatar({
  prompt,
  seed,
  model = "photogasm",
  width = 864,
  height = 1024,
  batch_size = 1,
  nsfw = false,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  steps = 5,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps,
        cfg: 1.5,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["30", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, solid color background, white background, (nudity, nsfw, naked)`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    26: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    32: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["8", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { sdxlAvatar };
