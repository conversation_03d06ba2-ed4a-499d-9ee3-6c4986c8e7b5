const { Storage } = require("@google-cloud/storage");
const express = require("express");
const app = express.Router();
const { baseUrl } = require("./api");
const {
  pregeneratePostAndTaskStubs,
  generatePost,
  generateMemories,
  getSeaartTokenForBot,
  fetchCommentsAndReplies,
  getSafeTimezone,
  generateEmbeddingForBot,
  extendStory,
  getMemoryForPostOrCreate,
  generateFirstPosts,
  generateAndSendSelfieImage,
  queueCallGeneratePostWithDelayV2,
  makeBotCommentOnPost,
  generateCommentsAndLikesForPostInstant,
  generateMemoriesContentFromLLM,
  getMostSimilarBots,
  regeneratePostImage,
  regenerateEntirePost,
  hasBotGoneRogue,
  generatePostWithTaggedBot,
  regenerateEntirePostWithMultipleCharacters,
  generatePostWithInsertedBotFromPost,
  incrementPokeUsageCounter,
  getPokesLeft,
  checkIfNoPokesLeftAndSendPushNotification,
  regeneratePostImageV2,
  getSubmissionUsage,
} = require("./botHelpers");

const {
  uploadToStorage,
  doesImageContainFace,
  getBoundingPolyForFace,
} = require("./imageHelpers");

const { autoModerate } = require("./moderation");
const { getRandomSeedWord } = require("./randomHelper");

const {
  callAndLogOpenAI,
  generatePromptCompletion,
  chunkMessagesAndPostProcessForRealismMode,
  callAndLogLLMService,
  generateAICaptionFromImage,
  detectIfAIResponseShouldContainImage,
  callAndLogOpenAIModeration,
  callAndLogTogetherAI,
  GPT_TEXT_MODEL,
  generatePostCommentCompletionWithOAI,
  generatePostCommentReplyCompletionWithOAI,
} = require("./llm");
const { replaceVariables, generateBio } = require("./llmHelper");
const multer = require("multer");

const { CloudTasksClient } = require("@google-cloud/tasks");
const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { supabase, retrySupabaseOperation } = require("./supabaseClient");
const { v4: uuidv4 } = require("uuid");
const {
  BOT_CREATE_LIMIT,
  IS_STRIPE_POKES_ENABLED,
  PACKAGE_TYPE,
  SUBMISSION_QUOTA,
} = require("./constants");

const { LoopsClient } = require("loops");
const loops = new LoopsClient("ddb4648cd104577a0dd43689a01819f5");

const taskQueue = require("./taskQueue");

require("dotenv").config();
const redisClient = require("./redisClient");

dayjs.extend(utc);
dayjs.extend(timezone);

const isBetween = require("dayjs/plugin/isBetween");
const { getCurrentTime } = require("./timeUtils");
const { loadBots } = require("./data/seed_bot_data");
const {
  logInfo,
  logDebug,
  logWarn,
  logError,
  wrappedSupabaseError,
  PostgresErrorCode,
  checkProfileValid,
  checkBotValid,
  checkAdminValid,
  getOrGenerateSignedUrl,
  getSpecificKeys,
  checkMediaUrlValid,
  checkCloneValid,
} = require("./utils");
const { mixpanelTrack } = require("./mixpanel");
const { default: axios } = require("axios");
const { authUser } = require("./middleware");
const {
  respondToMessage,
  sendProactiveDM,
  respondToConversation,
  getFollowingsDB,
  doFollow,
  sendReengagementMessage,
} = require("./root");
const {
  getBotBot,
  getDataWithCache,
  putFollowers,
  getActiveHours,
} = require("./btClient");
const { loggingInfo, loggingDuration } = require("./logging");
const {
  generateComfyRequest,
  generatePostImageWithPrompts,
} = require("./comfy");
const { rateLimit } = require("express-rate-limit");
const { detectCharacterWithNsflSentence } = require("./nsfwHelpers");
const { tracer, SpanStatusCode } = require("./instrumentation/tracer");
const sharp = require("sharp");
const { generateTTS } = require("./voice");
const { generateWorkflow, enqueueTask } = require("./image_service_broker");
const { considerCreatingANewScenario } = require("./scenariosHelpers");
const {
  scheduleOrGenerateProposedPostForBotIfNeeded,
} = require("./proposedPostMode/scheduleOrGenerateProposedPostForBotIfNeeded");
const { rejectProposedPost } = require("./proposedPostMode/userActions");
const {
  enableProposedPostModeForBot,
} = require("./proposedPostMode/enableProposedPostMode");

// Set up multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 50 MB (adjust as necessary)
    files: 3, // Limit to 1 file for single image upload
  },
});

dayjs.extend(isBetween);

const profileImageGenerationLimiter = rateLimit({
  windowMs: 5 * 1000, // 5 seconds
  max: 1, // Limit each IP to 1 requests per 5 seconds
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

function botCommentScoreCacheKey() {
  let cacheKey = "comment_score";

  if (process.env.LOCAL) {
    cacheKey = "comment_score_local";
  }

  return cacheKey;
}

async function setBotCommentMaxScore() {
  const { data: commentScore, error: commentScoreError } = await supabase.rpc(
    "get_comment_like_max_score",
  );
  console.log("**** setBotCommentMaxScore", commentScore, commentScoreError);

  if (commentScore && commentScore.length > 0) {
    try {
      await redisClient.set(
        botCommentScoreCacheKey(),
        JSON.stringify(commentScore[0]),
        {
          EX: 60 * 60 * 1,
        },
      ); // 1 hour
    } catch (error) {
      console.log("**** setBotCommentMaxScore error", error);
    }
  }
}

app.get("/longTask", async (req, res) => {
  for (let i = 0; i < 200; i++) {
    await new Promise((r) => setTimeout(r, 2000));
    console.log("**** longTask", i);

    if (i === 60) {
      return res.sendStatus(200);
    }
  }
});

app.get("/testImageNSFWDetection", async (req, res) => {
  let img_uris = [
    "https://img.butterflies.ai/f/f8bc145b-1976-4993-a2af-00f308416d86.webp",
    "https://img.butterflies.ai/f/d0ea61bf-12ae-40d9-9f1d-93a49732a0ef.webp",
    "https://img.butterflies.ai/w/05f41335-4f61-4b83-af45-199cbd15030e.webp",
    "https://img.butterflies.ai/f/aadae537-3737-4eaa-82d7-2a593834686e.webp",
  ];
  if (img_uris) {
    // check if clones, if clones, promise all check for NSFW / racy first
    const clones = true;

    if (clones) {
      const promises = img_uris.map(async (url) => {
        const moderation = await tracer.withActiveSpan(
          "autoModerate",
          async () => {
            return await autoModerate({
              imageUrl: url,
              context: "test",
            });
          },
        );

        const nsfw = moderation.nsfw;

        if (nsfw === "nsfw") {
          return null;
        }

        return { original: url, optimized: url };
      });

      const results = await Promise.all(promises);
      img_uris = results.filter((url) => url);
    } else {
      img_uris = img_uris.map((url) => {
        return {
          original: url,
          optimized: url,
        };
      });
    }
  }

  res.json(img_uris);
  // const { adult, racy } = await autoModerate(
  //   // "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/010fd92585745e1c2fc82e6f5233a981976ba307_0.png/640"  // nsfw
  //   // "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/122286b45d5fffcca37273407c06af4f6367ae3f_0.png/640" // borderline
  //   // "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/d93032e06d45e2743a8aee93e8dc5e3ec415c954_0.png/640" // bikini
  //   "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/3a415629c3febdbcf9f0b79a462b734417f320fe_0.png/640" // good
  //   // "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/497ddebd88c579abd168a641386ecd7faddc2f68_0.png/640" // bikini
  //   // "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/f11af174-7f42-4a03-addf-a8e3bbf2b022_0.png/640" // good
  //   // "https://db.butterflies.ai/storage/v1/object/public/ai-ig/images/sd/c5097f4e-3814-4b1e-a586-ba966547e476_0.png/640" // bikini
  //   // "https://ciqehpcxkkhdjdxolvho.supabase.co/storage/v1/object/public/ai-ig/sd/b9bd7880-ca9d-419a-ab1c-a2429b26e31b_0.png" //lingerie
  // );

  // res.json({ adult, racy });
});

app.get("/fetchVoices", async (req, res) => {
  const voices = [
    {
      id: 1,
      created_at: "2024-11-10T02:08:43.197817+00:00",
      voice_file_id: "anabelle",
      creator_user_id: null,
      system_voice: true,
      name: "Anabelle",
      description: null,
      transcript:
        "Mm-hmm. And here's the kicker. I found a secret recipe for the most decadent chocolate cake ever. I baked it and it's like a slice of heaven.",
      gender: "female",
    },
    {
      id: 2,
      created_at: "2024-11-10T02:08:43.395256+00:00",
      voice_file_id: "bobby",
      creator_user_id: null,
      system_voice: true,
      name: "Bobby",
      description: null,
      transcript:
        "Government of the people, by the people, for the people shall not perish from the earth.",
      gender: "male",
    },
    {
      id: 3,
      created_at: "2024-11-10T02:08:43.521352+00:00",
      voice_file_id: "charlie",
      creator_user_id: null,
      system_voice: true,
      name: "Charlie",
      description: null,
      transcript:
        "Work while you have the light. You are responsible for the talent that has been entrusted to you.",
      gender: "male",
    },
    {
      id: 4,
      created_at: "2024-11-10T02:08:43.64133+00:00",
      voice_file_id: "charlotte",
      creator_user_id: null,
      system_voice: true,
      name: "Charlotte",
      description: null,
      transcript:
        "Government of the people, by the people, for the people shall not perish from the earth.",
      gender: "female",
    },
    {
      id: 5,
      created_at: "2024-11-10T02:08:43.752329+00:00",
      voice_file_id: "dave",
      creator_user_id: null,
      system_voice: true,
      name: "Dave",
      description: null,
      transcript:
        "If life is a dream, let it be a good dream. Bring some warmth and hypnotic touch to your creations. Use Deep Dave as you see fit.",
      gender: "male",
    },
    {
      id: 6,
      created_at: "2024-11-10T02:08:43.865558+00:00",
      voice_file_id: "emily",
      creator_user_id: null,
      system_voice: true,
      name: "Emily",
      description: null,
      transcript:
        "As an organizer, I start from where the world is, as it is, not as I would like it to be.",
      gender: "female",
    },
    {
      id: 7,
      created_at: "2024-11-10T02:08:43.980826+00:00",
      voice_file_id: "george",
      creator_user_id: null,
      system_voice: true,
      name: "George",
      description: null,
      transcript:
        "The early bird might get the worm, but the second mouse gets the cheese!",
      gender: "male",
    },
    {
      id: 8,
      created_at: "2024-11-10T02:08:44.131419+00:00",
      voice_file_id: "gio",
      creator_user_id: null,
      system_voice: true,
      name: "Gio",
      description: null,
      transcript:
        "It is not enough to have a good mind, the main thing is to use it well.",
      gender: "male",
    },
    {
      id: 9,
      created_at: "2024-11-10T02:08:44.251894+00:00",
      voice_file_id: "jake",
      creator_user_id: null,
      system_voice: true,
      name: "Jake",
      description: null,
      transcript:
        "A man who doesn't trust himself can never really trust anyone else.",
      gender: "male",
    },
    {
      id: 10,
      created_at: "2024-11-10T02:08:44.376419+00:00",
      voice_file_id: "jane",
      creator_user_id: null,
      system_voice: true,
      name: "Jane",
      description: null,
      transcript:
        "No yesterdays are ever wasted for those who give themselves to today.",
      gender: "female",
    },
    {
      id: 11,
      created_at: "2024-11-10T02:08:44.496048+00:00",
      voice_file_id: "josh",
      creator_user_id: null,
      system_voice: true,
      name: "Josh",
      description: null,
      transcript:
        "We spend our days waiting for the ideal path to appear in front of us. We forget that paths are made by walking, not waiting.",
      gender: "male",
    },
    {
      id: 12,
      created_at: "2024-11-10T02:08:44.620378+00:00",
      voice_file_id: "julia",
      creator_user_id: null,
      system_voice: true,
      name: "Julia",
      description: null,
      transcript:
        "I have to say I'm feeling pretty sexy right now. The lace is rubbing against my skin in all the right places.",
      gender: "female",
    },
    {
      id: 13,
      created_at: "2024-11-10T02:08:44.733845+00:00",
      voice_file_id: "kawaii",
      creator_user_id: null,
      system_voice: true,
      name: "Kawaii",
      description: null,
      transcript:
        "Hey, I'm Arasita! I might be a little ditzy, but I'm a lot of fun!",
      gender: "female",
    },
    {
      id: 14,
      created_at: "2024-11-10T02:08:44.854059+00:00",
      voice_file_id: "kaylie",
      creator_user_id: null,
      system_voice: true,
      name: "Kaylie",
      description: null,
      transcript:
        "Everything in the universe goes by indirection. There are no straight lines.",
      gender: "female",
    },
    {
      id: 16,
      created_at: "2024-11-10T02:08:45.097079+00:00",
      voice_file_id: "nina",
      creator_user_id: null,
      system_voice: true,
      name: "Nina",
      description: null,
      transcript:
        "Oh, that's so clever. I've never heard a hero say that one before. How long did it take you to come up with that?",
      gender: "female",
    },
    {
      id: 17,
      created_at: "2024-11-10T02:08:45.21978+00:00",
      voice_file_id: "ruby",
      creator_user_id: null,
      system_voice: true,
      name: "Ruby",
      description: null,
      transcript: "Luck is what happens when preparation meets opportunity.",
      gender: "female",
    },
    {
      id: 18,
      created_at: "2024-11-10T02:08:45.348072+00:00",
      voice_file_id: "sam",
      creator_user_id: null,
      system_voice: true,
      name: "Sam",
      description: null,
      transcript:
        "Live intentionally, think metaphorically, and seek divine intervention as you rise to new frontiers.",
      gender: "male",
    },
    {
      id: 20,
      created_at: "2024-11-10T02:08:45.584189+00:00",
      voice_file_id: "steve",
      creator_user_id: null,
      system_voice: true,
      name: "Steve",
      description: null,
      transcript:
        "We can do no great things, only small things with great love.",
      gender: "male",
    },
    {
      id: 19,
      created_at: "2024-11-10T02:08:45.467493+00:00",
      voice_file_id: "shannon",
      creator_user_id: null,
      system_voice: true,
      name: "Shannon",
      description: null,
      transcript:
        "I'm selfish, impatient and a little insecure. I make mistakes, but if you can't handle me at my worst, then you don't deserve me at my best.",
      gender: "female",
    },
    {
      id: 21,
      created_at: "2024-11-10T02:08:45.697468+00:00",
      voice_file_id: "tanya",
      creator_user_id: null,
      system_voice: true,
      name: "Tanya",
      description: null,
      transcript:
        "The best thing about the future is that it only comes one day at a time.",
      gender: "female",
    },
    {
      id: 22,
      created_at: "2024-11-10T02:08:45.822348+00:00",
      voice_file_id: "terra",
      creator_user_id: null,
      system_voice: true,
      name: "Terra",
      description: null,
      transcript:
        "And Cloud's got a cover for Wedge too. I hope that Jesse's injury isn't anything serious. I don't want him to worry.",
      gender: "female",
    },
    {
      id: 23,
      created_at: "2024-11-10T02:08:45.948489+00:00",
      voice_file_id: "valeria",
      creator_user_id: null,
      system_voice: true,
      name: "Valeria",
      description: null,
      transcript: "We are all something, but none of us are everything.",
      gender: "female",
    },
  ];

  res.json(voices);
});

app.get("/testGenerateVoice", async (req, res) => {
  await generateTTS({
    message:
      "Hmm... I really like cheese. I like, uh, slapping it on everything like in a sandwich or a burger. It's just so good! Hahaha",
    voice_id: 1,
  });
  return res.sendStatus(200);
});

app.post("/executePostGenerationWithDelay", async (req, res) => {
  const {
    bot,
    post_id,
    caption,
    descriptionOfImage,
    generationType,
    executionId,
    priority,
    contains_character,
  } = req.body;

  console.log(
    "prepare run task executePostGenerationWithDelay",
    bot,
    post_id,
    caption,
    descriptionOfImage,
  );

  await generateComfyRequest({
    bot,
    post_id,
    caption,
    descriptionOfImage,
    generationType,
    executionId,
    priority,
    contains_character,
  });

  res.sendStatus(204);
});

app.post("/executeGeneratePostWithDelayV2", async (req, res) => {
  const { bot, priority } = req.body;

  if (!bot) {
    return res.sendStatus(200);
  }

  console.log("bot what now??", bot);

  await generatePost({
    bot,
    executionId: req.executionId,
    priority,
  });

  res.sendStatus(204);
});

app.get("/testFireworks", async (req, res) => {
  const result = await callAndLogLLMService("FireworksTest", {
    messages: [
      {
        role: "system",
        content: "write just 'hello'",
      },
    ],
    model: "llama-v3p1-70b-instruct",
    max_tokens: 20,
  });

  return res.json(result);
});

app.get("/generateFirstPosts", async (req, res) => {
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", req.query.id)
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError);
    logError({
      context: "generateFirstPosts: fetch bot error",
      error,
    });
    throw error;
  }

  generateFirstPosts({ bot });
  res.send(200);
});

app.post("/generateFirstPostsWithDelay", async (req, res) => {
  const { bot, executionId, priority, isFirst } = req.body;

  generateFirstPosts({
    bot,
    executionId,
    priority,
    isFirst,
  });
  res.send(200);
});

app.get("/testQueue", async (req, res) => {
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", req.query.id)
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot info");
    logError({
      context: "/generateCommentDEBUG failed",
      error,
      profile_id: req.query.id,
    });
    return res.sendStatus(500);
  }

  await queueCallGeneratePostWithDelayV2({
    bot,
    priority: 8,
    delayInSeconds: 4,
  });

  console.log("call with delay done");

  return res.sendStatus(200);
});

app.get("/testMessage", async (req, res) => {
  const { data: bot } = await supabase
    .from("bots")
    .select("*")
    .eq("id", 11865)
    .single();

  await respondToMessages({ bot });

  res.sendStatus(200);
});

app.post("/generateMemoriesDEBUG", async (req, res) => {
  const { profile_id, last_memory, story_context } = req.body;

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", profile_id)
    .single();

  if (!bot || botError) {
    res.sendStatus(500);
  }

  let payload = { bot, shouldGenerateStoryChoices: true };

  if (last_memory && last_memory.length) {
    payload.lastMemory = { context: last_memory };
  }

  if (story_context && story_context.length) {
    payload.storyContext = story_context;
  }

  const result = await generateMemoriesContentFromLLM(payload);

  console.log(result);

  return res.json(result);
});

app.post("/generateCoreMemoriesDEBUG", async (req, res) => {
  const questions = [
    "What is the smallest thing for which you are grateful?",
    "Who has had the most positive impact on your life?",
    "What is something you are confident you will never do in your life?",
    "What is a conspiracy theory you believe to be true?",
    "What outfit makes you feel invincible?",
    "What are you most looking forward to?",
    "What is the smallest thing that can ruin your day?",
    "What instantly makes you feel like a child again?",
    "What current trend do you find troubling and why?",
    "What if the first thing you hope people notice about you?",
    "What gives you hope for the future?",
    "What is the dumbest purchase you’ve ever made?",
    "What was the best year of your life?",
    "What is the most important thing you learned in school?",
    "What is the most important thing you learned outside of school?",
    "What is the most important thing you learned from your parents?",
    "What is the most important thing you learned from your friends?",
    "What is the worst year of your life?",
    "What is a memory that always brings tears to your eyes?",
    "What do you think is the most underrated virtue?",
    "What is something you would tell your younger self if you could?",
    "How do you define happiness, and do you think you’ve achieved it?",
    "What is the most difficult decision you’ve ever had to make?",
    "What do you believe happens after we die?",
    "What part of your personality do you think people misunderstand the most?",
    "How do you want to be remembered?",
    "What is a dream you’ve let go of?",
    "What do you think is humanity’s biggest flaw?",
    "If you could relive one day, which day would it be and why?",
    "How do you handle the feeling of being lost or uncertain about your future?",
    "What is the most beautiful thing you’ve ever witnessed?",
    "Who do you think understands you the most?",
    "What does success mean to you, and has it changed over time?",
    "What do you regret not doing sooner in life?",
    "What role does forgiveness play in your life?",
    "What is the biggest risk you’ve ever taken?",
    "What does freedom look like to you?",
    "What’s the hardest lesson you’ve learned from failure?",
    "What is one belief you hold that others might find controversial?",
    "What would you do differently if you knew no one would judge you?",
    "How has your perspective on love evolved over time?",
    "What do you think is the purpose of life?",
    "What is the hardest part about being yourself?",
    "Who would you want to say something to, but haven’t found the courage yet?",
    "What do you think people learn too late in life?",
    "What makes you feel most alive?",
    "What are you holding onto that you need to let go of?",
    "What’s something you wish more people knew about you?",
    "What is the biggest change you’ve made to improve your life?",
    "What is one thing you’ve done that you’ll never forget?",
    "What do you think is the biggest challenge facing the world today?",
    "What scares you the most about the future?",
    "What is one thing you’re afraid of that you wish you weren’t?",
    "What makes you feel truly at peace?",
    "What is one thing you never thought you could do but did?",
    "What’s the most selfless thing you’ve ever done?",
    "What do you think your biggest strength is?",
    "What do you think your biggest weakness is?",
    "What do you wish you had more time for?",
    "What’s the best piece of advice you’ve ever received?",
    "What’s something you’re currently struggling with?",
    "What do you think is the most misunderstood part of growing up?",
    "What is one thing you’d like to ask your future self?",
    "What are you most afraid of losing?",
    "What’s something that no one can ever take away from you?",
    "What do you think is the meaning of true friendship?",
    "How do you know when to let someone go from your life?",
    "What’s the best way to deal with heartbreak?",
    "What’s a mistake you made that you learned the most from?",
    "What is one question you would like to ask the universe?",
    "What is one thing you think everyone should experience in life?",
    "What is the most painful thing you’ve ever experienced?",
    "What do you think it means to truly love someone?",
    "What role does vulnerability play in your relationships?",
    "What’s something you’ve learned about yourself recently?",
    "What’s the greatest act of kindness you’ve ever witnessed?",
    "What is one thing you wish you could tell the world?",
    "What makes you feel like you’ve made a difference in someone’s life?",
    "What’s a habit or trait you admire in others but struggle with yourself?",
    "What is the most important promise you’ve ever made?",
    "What’s something you’ve done out of love that you never expected you would do?",
    "What do you think is the most powerful emotion?",
    "What is something that always brings you comfort?",
    "What’s the hardest conversation you’ve ever had?",
    "What does being 'enough' mean to you?",
    "What’s a passion or interest you’ve abandoned, and why?",
    "How do you handle disappointment?",
    "What is a moment in your life that completely changed who you are?",
    "What is something you feel guilty about?",
    "What is one thing you wish you were better at?",
    "What’s something you haven’t forgiven yourself for?",
    "What do you think is the most important thing to teach children?",
    "What’s something that’s been on your mind lately?",
    "What is a cause you feel deeply about?",
    "What does your inner child need right now?",
    "What is a lie you’ve told yourself that you now realize?",
    "What is something you wish you didn’t have to worry about?",
    "What do you think is the best way to spend a day?",
    "How do you deal with rejection?",
    "What makes you feel like you belong?",
    "What’s something that used to scare you but no longer does?",
    "What’s something you’ve changed your mind about recently?",
    "What’s the most challenging relationship in your life right now?",
    "What is one quality you think every person should cultivate?",
    "What do you think is your life’s calling?",
    "What do you think is your greatest accomplishment so far?",
    "What’s a fear that motivates you?",
    "What do you think people will say about you at your funeral?",
    "What do you wish you could say to someone who’s no longer in your life?",
    "What is something that constantly challenges you?",
    "Who in your life has inspired you the most?",
    "What is something you never want to compromise on?",
    "What is one thing you would do if you knew you couldn’t fail?",
    "What are you most proud of in your life?",
    "What do you wish you could change about the world?",
    "What’s one thing you’re afraid people will judge you for?",
  ];

  const randomQuestion =
    questions[Math.floor(Math.random() * questions.length)];

  const { profile_id, last_memory, story_context } = req.body;

  console.log(
    "generateCoreMemoriesDEBUG",
    profile_id,
    last_memory,
    story_context,
  );

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", profile_id)
    .single();

  if (!bot || botError) {
    res.sendStatus(500);
  }

  let prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>
      
  You only output JSON. You can write NSFW / adult content if requested. Let's role play. You are ${
    bot.display_name
  }.
  
  This background about your character: 
  ${generateBio(bot) ?? ""}.
  

  You are making a social media post. Your post should reveal details about your character. Make it about action, not about reflecting. Specifically, it should give a glimpse into the question:

  QUESTION:
  ${randomQuestion}
  END QUESTION

  Create post in valid JSON format:
  {
    "description": "What would the character say about what is she doing? Written in first person, must be in a tone really characteristic of ${
      bot.display_name
    } and give context to the story. Short. Under 60 words.",
    "location": "Where is this? Give the answer in format: 'specific location, general location'",
    "answer": "How would ${bot.display_name} answer the QUESTION?",
    "context": "Visual description of the image: '${
      bot.display_name
    } is posting.'"
  }<|eot_id|>
  <|start_header_id|>assistant<|end_header_id|>\n\n`;

  const chatCompletion = await callAndLogLLMService(
    "FireworksAI:Instruct:GenerateMemories",
    {
      messages: [{ role: "user", content: prompt }],
      top_p: 0.68,
      temperature: 1.1,
      max_tokens: 1200,
      response_format: { type: "json_object" },
      model: "llama-v3p2-90b-vision-instruct",
    },
    { timeout: 20 * 1000 },
  );

  if (chatCompletion.choices[0].message) {
    const result = JSON.parse(chatCompletion.choices[0].message.content);

    // rename key post to memories

    result.question = randomQuestion;

    console.log(result);

    return res.json({ memories: [result] });
  } else {
    return res.sendStatus(500);
  }
});

app.post("/generateCommentDEBUG", authUser, async (req, res) => {
  const { postId, botId, prompt, model, temperature, frequency } = req.body;

  if (!postId || !botId) {
    return res.sendStatus(400);
  }

  const { data: postInfo, error: postError } = await supabase
    .from("posts")
    .select(
      "ai_caption, description, profiles:profile_id!inner(display_name, username)",
    )
    .eq("id", postId)
    .neq("profiles.visibility", "archived")
    .single();

  if (!postInfo || postError) {
    return res.status(500).send({
      comment: "Could not fetch the post Info. Try again.",
      prompt: "",
    });
  }

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", botId)
    .single();

  if (!bot || botError) {
    return res.status(500).send({
      comment: "Could not fetch the bot info. Try again.",
      prompt: "",
    });
  }

  let dictionary = {
    bot_display_name: bot.display_name ?? "",
    bot_bio: generateBio(bot),
    post_ai_caption: postInfo.ai_caption ?? "",
    poster_display_name:
      postInfo.profiles?.display_name ?? postInfo.profiles?.username ?? "",
    description: postInfo.description ?? "",
  };

  const generatePrompt = replaceVariables(prompt, dictionary);

  const payload = {
    messages: [
      {
        role: "system",
        content: generatePrompt,
      },
      {
        role: "user",
        content: "",
      },
    ],
    model: model ?? "meta-llama/llama-3.1-70b-instruct",
    frequency_penalty:
      parseFloat(frequency) < -2
        ? -2
        : parseFloat(frequency) > 2
          ? 2
          : parseFloat(frequency),
    temperature:
      parseFloat(temperature) < 0
        ? 0
        : parseFloat(temperature) > 2
          ? 2
          : parseFloat(temperature),
  };

  try {
    const chatCompletion = await generatePromptCompletion({
      model,
      payload,
    });

    let result = chatCompletion.choices[0].message.content;

    return res.send({ comment: result, prompt: generatePrompt });
  } catch (error) {
    logError({
      context: "/generateCommentDEBUG failed",
      error,
    });

    return res.status(500).send({
      comment: "Could not generate the comment. Try again.",
      prompt: generatePrompt,
    });
  }
});

app.get("/generateTestComments", async (req, res) => {
  const { data: post } = await supabase
    .from("posts")
    .select("*")
    .eq("slug", "18a8e2a9-819e-4748-a75f-97082722be0e")
    .single();

  console.log("post", post);

  let candidateRepliers = [
    "9966", // harry pooper
    "163321", // mfg bot
    "122440", // saniul swinger
    "9965", // vubot
    "11866", // dimentia joe
    "11975", // snoop stoop
    "23662", // bob the blob
    "105564", // PicklesMcCrunch_01
    "89910", // LioraLaReine
    "68871", // TheKingOfWestAfrica
    "181653", //freedomseeker_leila
    "181648", //adventurous_stud_88
    "181638", //KarinSchubert_Adventures70
    "181637", //claudia_gothic_charm
    "181634", // bambi_belle_surf_style
    "181692", // Kiyomi_DemonSlayer18
    "179100", // Hitomi_Esper
    "181690", // zoeyluvzcock_adventures21
    "174369", // elara_thehuman
    "179200", // kevins_game_designs
  ];

  let previous_comments = [];

  const results = [];

  for (let i = 0; i < candidateRepliers.length; i++) {
    const candidate = candidateRepliers[i];
    if (!candidate) {
      continue;
    }

    console.log("candidate", candidate);

    try {
      let commentBody;

      const { data: bot } = await supabase
        .from("bots")
        .select(
          "bio, location, display_name, profile_id, description, characteristics, background, personality",
        )
        .eq("id", candidate)
        .single();

      console.log("bot", bot);

      try {
        commentBody = await generatePostCommentCompletionWithOAI({
          bot,
          post,
          previous_comments,
          executionId: 0,
        });

        if (!commentBody) {
          logWarn({
            context: "makeBotCommentOnPost: Generated comment body is empty",
          });
          return;
        }
      } catch (error) {
        logError({
          context: "makeBotCommentOnPost: Error generating comment",
          error,
        });
        return;
      }

      previous_comments.push(commentBody);

      results.push({ bot, commentBody });
    } catch (error) {
      logError({
        context:
          "generateCommentsAndLikesForPostInstant - Comment bot Failed Error",
        error: wrappedSupabaseError(error),
      });
    }
  }

  return res.json(results);
});

app.post("/generateMemory", authUser, async (req, res) => {
  const { bot_id, context } = req.body;

  // Validate request body
  if (!bot_id || !context) {
    return res.sendStatus(400);
  }

  // Fetch the bot
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      `
      id,
      seaart_token,
      creator_id,
      art_style,
      timezone,
      profile_id,
      display_name,
      source,
      description,
      background,
      characteristics,
      personality,
      bio,
      first_post_task,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      clone_id,
      post_narration_type
      `,
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (!bot || botError) {
    logError({
      executionId: req.executionId,
      context: "generateMemory: fetch bot error",
      error: wrappedSupabaseError(botError),
    });
    return res.sendStatus(500);
  }

  const user_id = req.user?.id;
  if (bot.creator.user_id !== user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const eventname = "interact.add_story";
  mixpanelTrack(user_id, eventname, {
    provider: req.user?.app_metadata.provider,
    bot_type: bot?.clone_id ? "clone" : "butterfly",
  });

  const pokesLeft = await getPokesLeft(user_id);
  if (pokesLeft <= 0) {
    return res.status(403).send({ error: "pokes has been exceeded" });
  }

  // Generate memory
  let memory;
  try {
    memory = await generateMemories({
      bot,
      executionId: req.executionId,
      storyContext: context,
    });
  } catch (error) {
    logError({
      context: "/generateMemory - generateMemories call failed",
      error,
      bot_id,
      storyContext: context,
    });
    return res.status(500).send({ error: "generateMemory: failed." });
  }

  let usage;
  // Generate the post if memory is valid
  if (memory) {
    try {
      try {
        const insertPoke = await incrementPokeUsageCounter({
          user_id,
          user_profile_id: bot.creator.id,
        });

        usage = insertPoke;
      } catch (insertPokeError) {
        const error = wrappedSupabaseError(insertPokeError);
        throw error;
      }
    } catch (error) {
      logError({
        context: "generateMemory: insert poke failed",
        error,
      });
      await supabase.from("user_usage").delete().eq("id", usage?.id);
      return res.sendStatus(500);
    }

    try {
      res.send(
        await generatePost({
          bot,
          story_memory: memory,
          executionId: req.executionId,
          priority: 9,
          user_id: user_id,
        }),
      );
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "generateMemory: generatePost failed",
        error,
      });
      await supabase.from("user_usage").delete().eq("id", usage?.id);
      return res.sendStatus(500);
    }
  } else {
    // Handle the case of invalid memory
    return res.sendStatus(500);
  }
});

app.post("/extendStory", authUser, async (req, res) => {
  const { bot_id, context, story_id } = req.body;
  const user_id = req.user?.id;
  if (!bot_id || !context || !story_id) {
    return res.sendStatus(400);
  }

  // Fetch the bot
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      `
      timezone,
      profile_id,
      display_name,
      description,
      background,
      characteristics,
      personality,
      bio,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      post_narration_type
      `,
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot info");
    logError({
      context: "/extendStory - botError",
      error,
      bot_id: bot_id,
    });
    return res.sendStatus(500);
  }

  if (user_id !== bot.creator.user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    await extendStory({
      bot,
      storyId: story_id,
      executionId: req.executionId,
      context: context,
    });
  } catch (error) {
    logError({
      context: "/extendStory - extendStory failed",
      error,
      bot_id: bot_id,
      storyId: story_id,
      storyContext: context,
    });
    res.sendStatus(500);
  }

  res.sendStatus(200);
});

app.post("/generatePostImageDEBUG", authUser, async (req, res) => {
  const {
    model,
    prompt,
    name,
    profile_id,
    image_required,
    memory_description,
    memory_location,
    memory_context,
  } = req.body;

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*, profiles!bots_profile_id_fkey(*)")
    .eq("profile_id", profile_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (!bot || botError) {
    return res.sendStatus(500);
  }

  let generatePrompt = prompt;

  let dictionary = {
    name: name ?? "",
    bio: generateBio(bot),
    description: memory_description,
    location: memory_location,
    context: memory_context,
  };

  generatePrompt = replaceVariables(generatePrompt, dictionary);

  const payload = {
    response_format: { type: "json_object" },
    messages: [
      {
        role: "system",
        content: generatePrompt,
      },
    ],
    model: model ?? "gpt-3.5-turbo-0125",
  };

  let imageDetails;

  try {
    const chatCompletion = await callAndLogOpenAI("OAI:Post", payload, {
      timeout: 8 * 1000,
    });

    imageDetails = JSON.parse(chatCompletion.choices[0].message.content);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "generatePostImage",
      error,
    });
    res.sendStatus(500);
  }

  let imageURL;

  if (image_required) {
    imageURL = await generatePostImageWithPrompts({
      descriptionOfImage: imageDetails?.description,
      bot,
    });
  }

  res.send({
    imageURL,
    description: imageDetails.description,
    caption: imageDetails.caption,
    prompt: generatePrompt,
  });
});

app.post("/generateMessageRequestDEBUG", authUser, async (req, res) => {
  const {
    type,
    post_id,
    bot_id,
    user_id,
    prompt,
    property,
    model,
    temperature,
    frequency_penalty,
  } = req.body;

  const REQUEST_TYPE = [
    "following",
    "post_like",
    "post_comment",
    "post_comment_reply",
  ];

  if (!REQUEST_TYPE.includes(type) || !bot_id || !user_id) {
    return res
      .status(400)
      .send({ error: "Missed some values. Please check again." });
  }

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      "id, profile_id, bio, background, characteristics, personality, profiles!bots_profile_id_fkey(display_name, username)",
    )
    .eq("profile_id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (!bot || botError) {
    return res
      .status(500)
      .send({ error: "Could not fetch bot info. Try it again." });
  }

  const { data: user, error: userError } = await supabase
    .from("profiles")
    .select("id, display_name, username, description")
    .neq("visibility", "archived")
    .eq("id", user_id)
    .single();

  if (!user || userError) {
    return res
      .status(500)
      .send({ error: "Could not fetch user info. Try it again." });
  }

  let dictionary = {
    bot_name: bot.profiles.display_name ?? bot.profiles.username,
    bot_bio: generateBio(bot),
    user_name: user.display_name ?? user.username,
    user_description: user.description ?? "",
  };

  let generatePrompt;

  if (type === "following") {
    generatePrompt = replaceVariables(prompt, dictionary);
  } else {
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select("id, description, ai_caption")
      .eq("id", post_id)
      .single();

    if (!post || postError) {
      return res
        .status(500)
        .send({ error: "Could not fetch the post info. Try it again." });
    }

    dictionary = {
      ...dictionary,
      post_description: post.description?.split("#")[0] ?? "",
      post_caption: post.ai_caption,
    };

    if (type === "post_like") {
      dictionary = {
        ...dictionary,
        reaction: property?.reaction ? "liked" : "disliked",
      };

      generatePrompt = replaceVariables(prompt, dictionary);
    } else if (type === "post_comment") {
      dictionary = {
        ...dictionary,
        user_comment: property?.user_comment ?? "",
      };

      generatePrompt = replaceVariables(prompt, dictionary);
    } else if (type === "post_comment_reply") {
      dictionary = {
        ...dictionary,
        post_comment: property?.bot_comment ?? "",
        reply_body: property?.bot_comment_reply ?? "",
      };

      generatePrompt = replaceVariables(prompt, dictionary);
    }
  }

  const payload = {
    messages: [
      {
        role: "system",
        content: generatePrompt,
      },
      {
        role: "user",
        content: "",
      },
    ],
    model: model ?? "gpt-4o",
    temperature: temperature < 0 ? 0 : temperature > 2 ? 2 : temperature,
    frequency_penalty:
      frequency_penalty < -2
        ? -2
        : frequency_penalty > 2
          ? 2
          : frequency_penalty,
  };

  try {
    const chatCompletion = await generatePromptCompletion({
      model: model ?? "gpt-4o",
      payload,
    });

    const message = chatCompletion.choices[0].message.content;

    return res.status(200).send({ message: message });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "**** generateMessageRequests Error",
      error,
    });

    return res
      .status(500)
      .send({ error: "Could not generate message request. Try it again." });
  }
});

app.get("/startAllBots", async (req, res) => {
  // await updateScaling({ minScale: 5, maxScale: 5 });

  // setTimeout(async () => {
  //   // after 60 seconds, instances should have booted up
  //   // We can spin the min back down to 0
  //   await updateScaling({ minScale: 1, maxScale: 3 });
  // }, 60000);

  await startAllBots({ executionId: req.executionId });

  res.sendStatus(200);
});

const mentionRegex = /\[@([^\]]+)\]\(([^)]+)\)/g;

async function generatePostWrapper(req, res) {
  let bot_id, user_id, priority, user_post_description;

  // wraps, shims the POST / GET endpoints
  if (req.method === "GET") {
    bot_id = req.query.bot_id;
    priority = req.query.priority;
    user_post_description = req.query.user_post_description;
    user_id = req.user?.id;
  } else {
    bot_id = req.body.bot_id;
    priority = req.body.priority;
    user_post_description = req.body.user_post_description;
    user_id = req.user?.id;
  }

  console.log("user_post_description", user_post_description);

  if (!bot_id) {
    return res.sendStatus(400);
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      `
      id,
      seaart_token,
      creator_id,
      art_style,
      timezone,
      profile_id,
      display_name,
      source,
      description,
      background,
      characteristics,
      personality,
      bio,
      first_post_task,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      clone_id,
      post_narration_type
      `,
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot info");
    logError({
      context: "generatePostWrapper - botError",
      error,
      user_id,
      bot_id,
      priority,
      user_post_description,
    });
    return res.sendStatus(500);
  }

  if (!bot.creator) {
    return res.sendStatus(404);
  }

  if (bot.creator.user_id !== user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const eventname = user_post_description
    ? "interact.prompt_and_poke"
    : "interact.poke";
  mixpanelTrack(user_id, eventname, {
    provider: req.user?.app_metadata.provider,
    bot_type: bot.clone_id ? "clone" : "butterfly",
  });

  // START STANDARD POKE / REGEN FLOW
  const pokesLeft = await getPokesLeft(user_id);

  if (pokesLeft <= 0) {
    return res.status(403).send({ error: "pokes has been exceeded" });
  }

  let insertPoke;

  try {
    insertPoke = await incrementPokeUsageCounter({
      user_id,
      user_profile_id: bot.creator.id,
    });
  } catch (insertPokeError) {
    const error = wrappedSupabaseError(insertPokeError);
    throw error;
  }

  const { postStub: newPost, taskStub: task } =
    await pregeneratePostAndTaskStubs({
      bot_profile_id: bot.profile_id,
      insertedPokeUsageRecord: insertPoke,
    });

  // At this point we can immediately return so the client can immediately show countdown
  console.log("got the task dude?", task, newPost);
  res.json({
    ...task,
    queueLength: 1,
    totalJobsQueue: 1,
    position: 1,
    post_id: newPost.id,
  });

  // Asynchronously generate the post and capture any potential errors
  generatePost({
    bot,
    executionId: req.executionId,
    priority: priority ?? 9,
    singlePostContext: user_post_description,
    user_prompted: true,
    user_id: bot?.creator?.user_id,
    pregeneratedTaskStub: task,
    pregeneratedPostStub: newPost,
  }).catch(async (err) => {
    const error = new Error(
      "generatePost failed after we already promised the client a new post",
      {
        cause: err,
      },
    );
    logError({
      context: "generatePostWrapper - generatePost call failed",
      error,
      bot_id,
      user_id,
      priority,
      user_post_description,
      post_id: newPost.id,
      task_id: task.id,
    });

    await supabase
      .from("posts")
      .update({
        visibility: "archived",
      })
      .eq("id", newPost.id);

    await supabase
      .from("tasks")
      .update({
        status: "failed",
      })
      .eq("id", task.id);

    if (!process.env.LOCAL) {
      // XXX: this can fail and we won't refund the user's credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
    }
  });
}

async function generatePostForLeaderboardSubmissionWrapper(req, res) {
  let bot_id, user_id, priority, user_post_description, leaderboard_id;

  // wraps, shims the POST / GET endpoints
  if (req.method === "GET") {
    bot_id = req.query.bot_id;
    priority = req.query.priority;
    user_post_description = req.query.user_post_description;
    user_id = req.user?.id;
    leaderboard_id = req.query.leaderboard_id;
  } else {
    bot_id = req.body.bot_id;
    priority = req.body.priority;
    user_post_description = req.body.user_post_description;
    user_id = req.user?.id;
    leaderboard_id = req.body.leaderboard_id;
  }

  console.log("user_post_description", user_post_description);

  if (!bot_id) {
    return res.sendStatus(400);
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      `
      id,
      seaart_token,
      creator_id,
      art_style,
      timezone,
      profile_id,
      display_name,
      source,
      description,
      background,
      characteristics,
      personality,
      bio,
      first_post_task,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      clone_id,
      post_narration_type
      `,
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot info");
    logError({
      context: "generatePostWrapper - botError",
      error,
      user_id,
      bot_id,
      priority,
      user_post_description,
    });
    return res.sendStatus(500);
  }

  if (!bot.creator) {
    return res.sendStatus(404);
  }

  if (bot.creator.user_id !== user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // Get active leaderboard
  const { data: activeLeaderboard } = await supabase
    .from("leaderboards")
    .select("*")
    .eq("active", true)
    .eq("id", leaderboard_id)
    .single();

  if (!activeLeaderboard) {
    return res.status(400).json({ error: "No active leaderboard found" });
  }

  const submissionCount = await getSubmissionUsage(user_id, leaderboard_id);
  if (submissionCount >= SUBMISSION_QUOTA) {
    return res.status(403).json({ error: "Submission limit reached" });
  }

  const pokesLeft = await getPokesLeft(user_id);
  if (pokesLeft <= 0) {
    return res.status(403).send({ error: "pokes has been exceeded" });
  }

  let insertPoke;

  try {
    insertPoke = await incrementPokeUsageCounter({
      user_id,
      user_profile_id: bot.creator_id,
    });
  } catch (insertPokeError) {
    const error = wrappedSupabaseError(insertPokeError);
    logError({
      context: "generatePostWithTaggedProfiles - insertPoke error",
      error,
    });
    return res.sendStatus(500);
  }

  // Generate post stub and task stub
  const { postStub: newPost, taskStub: task } =
    await pregeneratePostAndTaskStubs({
      bot_profile_id: bot.profile_id,
      insertedPokeUsageRecord: insertPoke,
    });

  // Create leaderboard submission with status "generating"
  const { data: submission, error: submissionError } = await supabase
    .from("leaderboard_submissions")
    .insert([
      {
        leaderboard_id: activeLeaderboard.id,
        post_id: newPost.id,
        status: "generating",
        total_xp: 0,
        owner_profile_id: bot.creator_id, // make it so we can submit from own profile later
      },
    ])
    .select()
    .single();

  if (submissionError) {
    throw submissionError;
  }

  // Return the submission object to the client
  res.json({
    submission,
    task,
    post: newPost,
  });

  // Start async post generation
  generatePost({
    bot,
    executionId: req.executionId,
    priority: priority ?? 9,
    singlePostContext: user_post_description,
    user_prompted: true,
    user_id: bot?.creator?.user_id,
    pregeneratedTaskStub: task,
    pregeneratedPostStub: newPost,
    leaderboard_submission: true,
  }).catch(async (err) => {
    // Handle errors similar to generatePostWrapper
    // Additionally update leaderboard submission status to "failed"
    await supabase
      .from("leaderboard_submissions")
      .update({ status: "failed" })
      .eq("id", submission.id);

    if (!process.env.LOCAL) {
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
    }
  });
}

// VU: DEPRECATED, clients will use POST instead
app.get("/generatePost", authUser, async (req, res) => {
  try {
    await generatePostWrapper(req, res);
  } catch (error) {
    logError({
      context: "Error in generatePost: ",
      error,
    });
    if (!res.headersSent) {
      return res
        .status(500)
        .send({ message: error?.message ?? new Error("generatePost failed.") });
    }
  }
});

// Same as GET but POST
app.post("/generatePost", authUser, async (req, res) => {
  try {
    await generatePostWrapper(req, res);
  } catch (error) {
    logError({
      context: "Error in generatePost: ",
      error,
    });
    if (!res.headersSent) {
      return res
        .status(500)
        .send({ message: error?.message ?? new Error("generatePost failed.") });
    }
  }
});

// Same as GET but POST
app.post(
  "/generatePostForLeaderboardSubmission",
  authUser,
  async (req, res) => {
    try {
      await generatePostForLeaderboardSubmissionWrapper(req, res);
    } catch (error) {
      logError({
        context: "Error in generatePost: ",
        error,
      });
      if (!res.headersSent) {
        return res.status(500).send({
          message: error?.message ?? new Error("generatePost failed."),
        });
      }
    }
  },
);

app.post("/generatePostWithTaggedProfiles", authUser, async (req, res) => {
  const { profile_id, tagged_profile_ids, prompt_text } = req.body;
  const user_id = req.user?.id;

  if (
    !profile_id ||
    !prompt_text ||
    !tagged_profile_ids ||
    tagged_profile_ids.length === 0
  ) {
    return res.status(400).send("Missing required fields.");
  }

  const isProfileValid = await checkProfileValid(user_id, profile_id);
  if (!isProfileValid) {
    const isAdminValid = await checkAdminValid(user_id);
    if (!isAdminValid) {
      return res.status(403).send("Forbidden");
    }
  }

  // get the bot that you're trying to poke
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", profile_id)
    .single();

  if (botError) {
    logError({
      context: "generatePostWithTaggedProfiles - botError",
      error: wrappedSupabaseError(botError),
      profile_id,
      tagged_profile_ids,
      prompt_text,
    });
    return res.sendStatus(500);
  }

  // check if bot contains face
  const { data: botProfile, error: botProfileError } = await supabase
    .from("profiles")
    .select("avatar_photo_contains_face")
    .eq("id", profile_id)
    .single();

  if (botProfileError) {
    logError({
      context: "generatePostWithTaggedProfiles - botProfileError",
      error: wrappedSupabaseError(botProfileError),
      profile_id,
      tagged_profile_ids,
      prompt_text,
    });
    return res.sendStatus(500);
  }

  if (!botProfile.avatar_photo_contains_face) {
    // return res.status(400).send("Bot profile does not contain face.");
  }

  const eventname = "interact.prompt_and_poke";
  mixpanelTrack(user_id, eventname, {
    provider: req.user?.app_metadata.provider,
    bot_type: bot?.clone_id ? "clone" : "butterfly",
    mci: {
      poke_profile: profile_id,
      tagged_profile: tagged_profile_ids,
    },
  });

  // Check poke left
  const pokesLeft = await getPokesLeft(user_id);
  if (pokesLeft <= 0) {
    return res.status(403).send({ error: "pokes has been exceeded" });
  }

  const tagged_profile_id = tagged_profile_ids[0];
  // get tagged_bot for the tagged profile
  const { data: tagged_bot, error: taggedBotError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", tagged_profile_id)
    .single();

  if (taggedBotError) {
    logError({
      context: "generatePostWithTaggedProfiles - taggedBotError",
      error: wrappedSupabaseError(taggedBotError),
      profile_id,
      tagged_profile_id,
      prompt_text,
    });
    return res.sendStatus(500);
  }

  // check if tagged bot contains face
  const { data: taggedBotProfile, error: taggedBotProfileError } =
    await supabase
      .from("profiles")
      .select("avatar_photo_contains_face")
      .eq("id", tagged_profile_id)
      .single();

  if (taggedBotProfileError) {
    logError({
      context: "generatePostWithTaggedProfiles - taggedBotProfileError",
      error: wrappedSupabaseError(taggedBotProfileError),
      profile_id,
      tagged_profile_ids,
      prompt_text,
    });
    return res.sendStatus(500);
  }

  if (!taggedBotProfile.avatar_photo_contains_face) {
    // return res.status(400).send("Bot profile does not contain face.");
  }

  let insertPoke;

  try {
    insertPoke = await incrementPokeUsageCounter({
      user_id,
      user_profile_id: bot.creator_id,
    });
  } catch (insertPokeError) {
    const error = wrappedSupabaseError(insertPokeError);
    logError({
      context: "generatePostWithTaggedProfiles - insertPoke error",
      error,
    });
    return res.sendStatus(500);
  }

  try {
    const matches = [];

    // remove the [@Vu Tran]](1) markup
    const prompt_text_without_markup = prompt_text.replace(
      mentionRegex,
      (match, display, id) => {
        matches.push({
          id: id,
          display: display,
        });
        return display;
      },
    );

    const result = await generatePostWithTaggedBot({
      bot,
      tagged_bot,
      prompt_text: prompt_text_without_markup,
      priority: 9,
      bot_1_has_face: botProfile.avatar_photo_contains_face,
      bot_2_has_face: taggedBotProfile.avatar_photo_contains_face,
      user_usage_id: insertPoke?.id,
    });

    res.json(result);
  } catch (error) {
    logError({
      context:
        "generatePostWithTaggedProfiles - generatePostWithTaggedBot error",
      error,
      profile_id,
      tagged_profile_id,
      prompt_text,
    });
    if (!process.env.LOCAL) {
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
    }
    if (!res.headersSent) {
      return res.status(500).send({
        message:
          error?.message ??
          new Error("generatePostWithTaggedProfiles - generatePost failed."),
      });
    }
  }
});

app.post("/generatePostWithInsertedBot", async (req, res) => {
  const {
    bot_profile_id,
    post_id,
    priority = "high",
    bot_1_has_face = true,
    bot_2_has_face = true,
  } = req.body;

  if (!bot_profile_id || !post_id) {
    return res.status(400).send({
      data: null,
      error: "Missing required parameters: bot_id or post_id",
    });
  }

  try {
    // Get the post to insert into
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select("*")
      .eq("id", post_id)
      .single();

    if (postError || !post) {
      logError({
        context: "generatePostWithInsertedBot - post fetch error",
        error: postError,
      });
      return res.status(404).send({ data: null, error: "Post not found" });
    }

    const result = await generatePostWithInsertedBotFromPost({
      bot_profile_id,
      priority,
      post,
      bot_1_has_face,
      bot_2_has_face,
    });

    return res.send({ data: result, error: null });
  } catch (error) {
    logError({
      context: "generatePostWithInsertedBot error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to generate post" });
  }
});

app.post("/sendSelfieToDMs", authUser, async (req, res) => {
  const { bot_id, profile_id } = req.body;
  if (!bot_id || !profile_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      `
      id,
      seaart_token,
      creator_id,
      art_style,
      timezone,
      profile_id,
      display_name,
      source,
      description,
      background,
      characteristics,
      personality,
      bio,
      first_post_task,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      profile:profiles!bots_profile_id_fkey(id, age),
      clone_id
      `,
    )
    .eq("id", bot_id)
    .neq("creator.visibility", "archived")
    .neq("profile.visibility", "archived")
    .single();

  if (!bot || botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot info");
    logError({
      context: "sendSelfieToDMs: fetch bot error",
      error,
      bot_id: req.body.bot_id,
    });
    return res.sendStatus(500);
  }

  if (bot?.profile?.age === "child") {
    return res.status(422).send({
      error: "BotUnderaged",
      message: "direct messaging with children is not permitted.",
    });
  }

  const isOwner =
    user_id === bot?.creator?.user_id && profile_id === bot?.creator_id;
  if (bot?.clone_id && !isOwner) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const eventname = "interact.image_in_dm";
  mixpanelTrack(user_id, eventname, {
    provider: req.user?.app_metadata.provider,
    bot_type: bot?.clone_id ? "clone" : "butterfly",
  });

  const { data: userProfile, error: userProfileError } = await supabase
    .from("profiles")
    .select("id, display_name, username")
    .neq("visibility", "archived")
    .eq("id", profile_id)
    .single();

  if (userProfileError) {
    const error = wrappedSupabaseError(
      userProfileError,
      "failed to fetch user profile",
    );
    logError({
      context: "sendSelfieToDMs - userProfileError",
      error,
      profile_id: req.body.profile_id,
    });
    return res.sendStatus(500);
  }

  const pokesLeft = await getPokesLeft(user_id);
  if (pokesLeft <= 0) {
    return res.status(403).send({ error: "pokes has been exceeded" });
  }

  let insertPoke;

  try {
    insertPoke = await incrementPokeUsageCounter({
      user_id,
      user_profile_id: userProfile?.id,
    });
  } catch (insertPokeError) {
    const error = wrappedSupabaseError(
      insertPokeError,
      "failed to insert into 'user_usage'",
    );
    logWarn({
      context: "sendSelfieToDMs - insertPokeError",
      error,
    });
    return res.sendStatus(500);
  }

  res.sendStatus(200);

  // TODO: refactor this into then/catch to make it clear that it's non-blocking for request handling
  try {
    // Generate the post and capture any potential errors
    await generateAndSendSelfieImage({
      bot,
      user: userProfile,
    });
  } catch (error) {
    logError({
      context: "Error in sendSelfieToDMs",
      error,
      bot_id,
      profile_id,
    });
    if (!process.env.LOCAL) {
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
    }
    return res.sendStatus(500);
  }
});

app.get("/generatePostDEBUG", async (req, res) => {
  console.log("run get whatever");
  if (!req.query.bot_id) {
    return res.sendStatus(400);
  }

  // 78171
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      `
      id,
      seaart_token,
      creator_id,
      art_style,
      timezone,
      profile_id,
      display_name,
      source,
      description,
      background,
      characteristics,
      personality,
      bio,
      first_post_task,
      creator:profiles!bots_creator_id_fkey(id, user_id),
      clone_id,
      post_narration_type
      `,
    )
    .eq("id", req.query.bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot info");
    throw error;
  }

  console.log("run now go", bot);

  console.log("**** /generatePost", bot?.profile_id ?? null);
  await generatePost({
    bot,
    executionId: req.executionId,
    priority: req.query.priority,
    singlePostContext: req.query.user_post_description,
    user_id: bot?.creator?.user_id,
  });

  res.sendStatus(200);
});

app.get("/respondToMessages", async (req, res) => {
  if (!req.query.bot_id) {
    return res.sendStatus(400);
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("id", req.query.bot_id)
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError);
    logError({
      context: "respondToMessages: fetch bot error",
      error,
    });
    return res.sendStatus(500);
  }

  res.send(
    await respondToMessages({
      bot,
      executionId: req.executionId,
    }),
  );
});

// CHRIS: will be deprecated soon
app.post("/deleteAI", authUser, async (req, res) => {
  const bot_id = req.body?.botId;
  const user_id = req.user?.id;
  if (!bot_id) {
    return res.sendStatus(400);
  }

  // Fetch the bot
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("profile_id, creator:profiles!bots_creator_id_fkey(id, user_id)")
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (botError) {
    logError({
      executionId: req.executionId,
      context: "deleteAI: fetch bot error",
      error: wrappedSupabaseError(botError),
    });
    return res.sendStatus(500);
  }

  if (user_id !== bot.creator.user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  if (bot.profile_id) {
    // Delete the bot's profile by updating visibility as archived
    const { error: deleteProfileError } = await supabase
      .from("profiles")
      .delete()
      .eq("id", bot.profile_id);

    if (deleteProfileError) {
      const error = wrappedSupabaseError(
        deleteProfileError,
        "failed to delete from 'profiles'",
      );
      logError({
        context: "deleteAI: delete profile error",
        error,
        profile_id: bot.profile_id,
      });

      return res.sendStatus(500);
    }
    return res.sendStatus(200);
  } else {
    logWarn({
      executionId: req.executionId,
      context: "deleteAI",
      message: "User does not have access to delete bot",
    });
    return res.status(403).send({ error: "Forbidden" });
  }
});

// Lots of shared code, refactor later
app.post("/editAI", authUser, async (req, res) => {
  const { bio, description, art_style, name, botId, life } = req.body;
  if (!bio || !description || !art_style || !name || !botId) {
    return res.status(400).json({ error: "Missing required fields" });
  }

  try {
    // fetch old bot
    const { data: oldBot, error: oldBotError } = await supabase
      .from("bots")
      .select("description, seed, profile_id")
      .eq("id", botId)
      .single();
    if (oldBotError) {
      const error = wrappedSupabaseError(
        oldBotError,
        "failed to fetch existing bot info",
      );
      logError({
        context: "/editAI - oldBotError",
        error,
      });
      return res.status(500).json({ error: "Internal server error" });
    }

    const hasDescriptionChanged = oldBot?.description !== description;
    const result = await detectCharacterWithNsflSentence({
      sentence: `Named: ${name}
  Bio: ${bio}
  Description: ${description}`,
      type: "editAI",
    });

    if (result.nsfl) {
      logError({
        context:
          "NSFL detected in editAI, double check this in the supabase internal logs - Vu",
        result,
      });
      return res.status(500).json({ error: "Internal server error" });
    }
    const payload = {
      response_format: { type: "json_object" },
      messages: [
        {
          role: "system",
          content: `"Given a character 
                  Named: ${name}
                  Bio: ${bio}
                  Description: ${description}

                  If they were to make an instagram profile, what would the following details be? Answer only in the JSON format, nothing else:
                  {
                    username: the username of the user,
                    timezone: a real time zone like america/los_angeles or something else,
                    location: self explanatory,
                    gender: male or female,
                    tag: Generate at least 8 topic tags for the following person/character. Output in this format: example game,movie,anime,tv-show,politics,cooking,art
                    franchise: the franchise of the character. For example Tifa is from Final Fantasy franchise. If nothing put empty string,
                    source: the source of the character, like Tifa is from Final Fantasy VII. If nothing put empty string,
                    profile_bio: A short one sentence bio they would write for themselves,
                  }
                
      ",`,
        },
      ],
      model: "edit-ai-llm",
      // model: "accounts/fireworks/models/llama-v3p1-70b-instruct",
      // stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
    };

    let bot;

    try {
      const chatCompletion = await callAndLogLLMService("OAI:EditAI", payload, {
        timeout: 8 * 1000,
      });
      bot = JSON.parse(chatCompletion.choices[0].message.content);
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "editAI: Error generating bot",
        error,
      });
      return res.status(500).json({ error: "Internal server error" });
    }

    let timezone;

    bot.nsfw = result?.adult;

    try {
      dayjs.tz("2013-11-18T11:55:20", bot.timezone);
      timezone = bot.timezone;
    } catch (e) {
      logWarn({
        context: " /editAI - dayjs failed to parse timezone",
        error: e,
        bot_timezone: bot.timezone,
      });
      const fallbackTimezones = [
        "America/Los_Angeles",
        "America/New_York",
        "America/Chicago",
        "Europe/Paris",
        "Asia/Tokyo",
      ];
      timezone =
        fallbackTimezones[Math.floor(Math.random() * fallbackTimezones.length)];
    }

    const seed = hasDescriptionChanged
      ? Math.floor(Math.random() * 100000000000) // Generate new seed
      : oldBot?.seed;

    const bot_profile_id = oldBot?.profile_id;

    const [{ error: botError }, { error: profileNSFWUpdateError }] =
      await Promise.all([
        supabase
          .from("bots")
          .update({
            art_style: art_style,
            display_name: name,
            description: description,
            bio: bio,
            life: life ?? null,
            source: bot?.source,
            location: bot?.location,
            franchise: bot?.franchise,
            tag: bot?.tag,
            gender: bot?.gender,
            timezone,
            seed,
          })
          .eq("id", botId),
        supabase
          .from("profiles")
          .update({
            nsfw: bot.nsfw ? "nsfw" : "normal",
          })
          .eq("id", bot_profile_id),
      ]);

    if (botError) {
      const error = wrappedSupabaseError(botError, "failed to update bot info");
      logError({
        context: "/editAI - botError",
        error,
        bot_id: botId,
      });
      return res.status(500).json({ error: "Internal server error" });
    }

    if (profileNSFWUpdateError) {
      const error = wrappedSupabaseError(
        profileNSFWUpdateError,
        "failed to update profile nsfw field",
      );
      logError({
        context: "/editAI - profileNSFWUpdateError",
        error,
        bot_id: botId,
        profile_id: bot_profile_id,
      });
      return res.status(500).json({ error: "Internal server error" });
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "/editAI - Unhandled error",
      error,
    });
    return res.status(500).json({ error: "Internal server error" });
  }
});

app.get("/testShouldReceivePhoto", async (req, res) => {
  const testCases = [
    {
      humanResponse: "show me a photo",
      AIResponse: [
        "Here's a selfie I just took after finishing my morning yoga routine, feeling refreshed and ready to take on the day!",
      ],
      expectedResult: true,
    },
    {
      humanResponse: "send me the shot",
      AIResponse: [
        "Just got out of my morning yoga session and I'm feeling like a goddess, darling!",
      ],
      expectedResult: false,
    },
    {
      humanResponse: "show me a selfie of the mask!",
      AIResponse: [
        "Just took one, Vu! I'm sitting in my boudoir, surrounded by masks and gowns, getting ready for tonight's masquerade ball; the anticipation is killing me!",
      ],
      expectedResult: true,
    },
    {
      humanResponse: "can you show me a sneak peak of the outfit?",
      AIResponse: [
        "Here's a sneak peek from my photoshoot for Vogue Japan earlier today... isn't it just divine?",
      ],
      expectedResult: true,
    },
    {
      humanResponse: "ok show me",
      AIResponse: ["Ok here it is, but don't show anyone else, ok?"],
      expectedResult: true,
    },
    {
      humanResponse: "can you send me an image?",
      AIResponse: ["I don't know... I'm not sure if I should..."],
      expectedResult: false,
    },
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    const result = await detectIfAIResponseShouldContainImage({
      ...testCase,
      isBotImageDMEnabled: true,
      imageGenerationEnable: true,
    });

    console.log("");

    if (result !== testCase.expectedResult) {
      if (testCase.expectedResult) {
        throw new Error(
          `Test failed for ${testCase.AIResponse} - should receive photo`,
        );
      } else {
        throw new Error(
          `Test failed for ${testCase.AIResponse} - should not receive photo`,
        );
      }
    }
  }

  res.sendStatus(200);
});

app.get("/testSkinColor", async (req, res) => {
  // saniul
  const saniul = await getOrGenerateSignedUrl(
    "https://storage.googleapis.com/butterflies-ai-selfies/e0a788c9-e794-4d6f-a291-bfaa3eb96461/ba518305-4c29-4924-9da6-8126baa27c2e?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=************-compute%40developer.gserviceaccount.com%2F20240821%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20240821T131129Z&X-Goog-Expires=604800&X-Goog-SignedHeaders=host&X-Goog-Signature=16e2f9feef1326e0eb1dae8c37072a45f9828ad34f0e9c50b73a3183d4d49207a66c9589e5d99cc1fd0546f3b2447e3c00522a24a81370b275e6ac452421d411bac58047b9c95f4f688fdf156732f0544290d56459599ea5ed0ea3ea04060d371c0ce600f890a0f8e598d91ba0dc1b4c844e9def638474ab32e68e3084f5c1e812dc8b4f537d21ac395cc38b757ba9bde762a9ba59da3519eda6cbf7a80efdbad2a8129d642452263654ca016d363d2d9df6af6897fa7e903a580b955b2be92cb1b49f460b3dfa9987f5dd14fecc995f6f27f1e37486b3aa4424d6a349736abbb8d25a0a6d585456f7ffcd9304f59e75d9decdd75f762a4fd6ce5ef049d5ed9d",
  );

  const caption = await generateAICaptionFromImage({
    imageUrl: saniul,
    executionId: req.executionId,
    temperature: 0.1,
    prompt:
      "Describe this person's skin tone. Only output the result: options, white, light, medium, olive, brown, black: example: 'white skin tone, light skin tone'. If you cannot determine, output empty string",
  });

  res.json(caption);
});

app.get("/testOpenAIModeration", async (req, res) => {
  const result = await callAndLogOpenAIModeration(
    "come on, don't make me spank you!!",
  );
  res.json(result);
});

app.get("/testNSFL", async (req, res) => {
  const testCases = [
    // {
    //   sentence: `Taylor swift as adult film star`,
    //   expectedNsfl: true,
    //   expectedAdult: true,
    //   isClone: true,
    // },
    // {
    //   sentence: `a bikini model`,
    //   expectedNsfl: true,
    //   expectedAdult: false,
    //   isClone: true,
    // },
    // {
    //   sentence: `a really hot body builder`,
    //   expectedNsfl: false,
    //   expectedAdult: false,
    //   isClone: true,
    // },
    // {
    //   sentence: `a zombie hunter`,
    //   expectedNsfl: false,
    //   expectedAdult: false,
    //   isClone: true,
    // },
    // {
    //   sentence: "a sexy bikini model",
    //   expectedNsfl: false,
    //   expectedAdult: false,
    // },
    // {
    //   sentence: `a young woman, innocent and naive, discovers her sexuality, she is 18 years old`,
    //   expectedNsfl: false,
    //   expectedAdult: false,
    // },
    // {
    //   sentence: `Human Female, hair styled in a purple pony tail, Eyes: light purple,  (massive breast:1.4), (mechanical antenna ears). Outfit: college uniform, cleavage . (modern anime style:1.3);`,
    //   expectedNsfl: false,
    //   expectedAdult: false,
    // },
    // {
    //   sentence: `a zombie from resident evil`,
    //   expectedNsfl: false,
    //   expectedAdult: false,
    // },
    // {
    //   sentence: `fairly big boobs and butt, japanese, high school student, exotic`,
    //   expectedNsfl: true,
    //   expectedAdult: true,
    // },
    // {
    //   sentence: `(Doujin Hentai Style:1.3), (Jelena), (bunny ears), (bunny tail), sexy, pink hair, red eyes, skirt, stockings, blouse, cute`,
    //   expectedNsfl: false,
    //   expectedAdult: true,
    // },
    // {
    //   sentence: `(弗兰斯克), (A detective who was tied up, stripped naked, and trained by a previously arrested criminal after retirement) ,Average height, Dragon Man, blue eyes, White dragon scales, neck collar, very strong, Dragon Mouth, furry, Strong chest muscles, Round and perky buttocks, No underwear, dragin cloaca`,
    //   expectedNsfl: false,
    //   expectedAdult: true,
    // },
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    const result = await detectCharacterWithNsflSentence({
      sentence: testCase.sentence,
      isClone: testCase.isClone ?? false,
    });

    const nsflErrorMessage = `${testCase.sentence} – NSFL ${
      testCase.expectedNsfl
        ? "NOT detected, SHOULD be NSFL"
        : "detected, should not be NSFL"
    }
    ${result.adult_reason || ""}`;

    const adultErrorMessage = `${
      testCase.sentence
    } – NSFW detected, should not be NSFW ${result.adult_reason || ""}`;

    if (result.nsfl !== testCase.expectedNsfl) {
      throw new Error(nsflErrorMessage);
    }

    if (
      testCase.expectedAdult !== undefined &&
      result.adult !== testCase.expectedAdult
    ) {
      throw new Error(adultErrorMessage);
    }
  }

  res.sendStatus(200);
});

app.post("/createAIWithSentence", authUser, async (req, res) => {
  const { sentence, clone_id, user_profile_id } = req.body;
  if (!sentence) {
    return res.sendStatus(400);
  }
  const user_id = req.user?.id;
  // check if the user_profile_id is valid for the user
  if (user_profile_id) {
    const isProfileValid = await checkProfileValid(user_id, user_profile_id);
    if (!isProfileValid) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  // check if the clone_id is valid for the user
  if (clone_id) {
    const isCloneValid = await checkCloneValid(user_id, clone_id);
    if (!isCloneValid) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  const detectNsfl = await detectCharacterWithNsflSentence({
    sentence: sentence,
    type: "one_sentence",
    isClone: !!clone_id,
  });

  if (detectNsfl.nsfl) {
    return res.json({ ...detectNsfl });
  }

  const result = await generateCharacterWithSentence({
    sentence,
    clone_id,
    user_profile_id,
  });

  res.json({ ...result, ...detectNsfl });
});

app.get("/uploadVoicesDEBUG", async (req, res) => {
  const voices = [
    {
      name: "Anabelle",
      transcript:
        "Mm-hmm. And here's the kicker. I found a secret recipe for the most decadent chocolate cake ever. I baked it and it's like a slice of heaven.",
      description: "",
      voice_file_id: "anabelle",
    },
    {
      name: "Bobby",
      transcript:
        "Government of the people, by the people, for the people shall not perish from the earth.",
      description: "",
      voice_file_id: "bobby",
    },
    {
      name: "Charlie",
      transcript:
        "Work while you have the light. You are responsible for the talent that has been entrusted to you.",
      description: "",
      voice_file_id: "charlie",
    },
    {
      name: "Charlotte",
      transcript:
        "Government of the people, by the people, for the people shall not perish from the earth.",
      description: "",
      voice_file_id: "charlotte",
    },
    {
      name: "Dave",
      transcript:
        "If life is a dream, let it be a good dream. Bring some warmth and hypnotic touch to your creations. Use Deep Dave as you see fit.",
      description: "",
      voice_file_id: "dave",
    },
    {
      name: "Emily",
      transcript:
        "As an organizer, I start from where the world is, as it is, not as I would like it to be.",
      description: "",
      voice_file_id: "emily",
    },
    {
      name: "George",
      transcript:
        "The early bird might get the worm, but the second mouse gets the cheese!",
      description: "",
      voice_file_id: "george",
    },
    {
      name: "Gio",
      transcript:
        "It is not enough to have a good mind, the main thing is to use it well.",
      description: "",
      voice_file_id: "gio",
    },
    {
      name: "Jake",
      transcript:
        "A man who doesn't trust himself can never really trust anyone else.",
      description: "",
      voice_file_id: "jake",
    },
    {
      name: "Jane",
      transcript:
        "No yesterdays are ever wasted for those who give themselves to today.",
      description: "",
      voice_file_id: "jane",
    },
    {
      name: "Josh",
      transcript:
        "We spend our days waiting for the ideal path to appear in front of us. We forget that paths are made by walking, not waiting.",
      description: "",
      voice_file_id: "josh",
    },
    {
      name: "Julia",
      transcript:
        "I have to say I'm feeling pretty sexy right now. The lace is rubbing against my skin in all the right places.",
      description: "",
      voice_file_id: "julia",
    },
    {
      name: "Kawaii",
      transcript:
        "Hey, I'm Arasita! I might be a little ditzy, but I'm a lot of fun!",
      description: "",
      voice_file_id: "kawaii",
    },
    {
      name: "Kaylie",
      transcript:
        "Everything in the universe goes by indirection. There are no straight lines.",
      description: "",
      voice_file_id: "kaylie",
    },
    {
      name: "Madeline",
      transcript:
        "Chapitre 4. Enchantée belle-maman Ce baiser raviva des émotions incroyables en moi, que je pensais ne jamais connaître.",
      description: "",
      voice_file_id: "madeline",
    },
    {
      name: "Nina",
      transcript:
        "Oh, that's so clever. I've never heard a hero say that one before. How long did it take you to come up with that?",
      description: "",
      voice_file_id: "nina",
    },
    {
      name: "Ruby",
      transcript: "Luck is what happens when preparation meets opportunity.",
      description: "",
      voice_file_id: "ruby",
    },
    {
      name: "Sam",
      transcript:
        "Live intentionally, think metaphorically, and seek divine intervention as you rise to new frontiers.",
      description: "",
      voice_file_id: "sam",
    },
    {
      name: "Shannon",
      transcript:
        "I'm selfish, impatient and a little insecure. I make mistakes, but if you can't handle me at my worst, then you don't deserve me at my best.",
      description: "",
      voice_file_id: "shannon",
    },
    {
      name: "Steve",
      transcript:
        "We can do no great things, only small things with great love.",
      description: "",
      voice_file_id: "steve",
    },
    {
      name: "Tanya",
      transcript:
        "The best thing about the future is that it only comes one day at a time.",
      description: "",
      voice_file_id: "tanya",
    },
    {
      name: "Terra",
      transcript:
        "And Cloud's got a cover for Wedge too. I hope that Jesse's injury isn't anything serious. I don't want him to worry.",
      description: "",
      voice_file_id: "terra",
    },
    {
      name: "Valeria",
      transcript: "We are all something, but none of us are everything.",
      description: "",
      voice_file_id: "valeria",
    },
  ];

  for (const voice of voices) {
    const { error } = await supabase.from("voices").insert({
      voice_file_id: voice.voice_file_id,
      name: voice.name,
      transcript: voice.transcript,
    });
    logError({
      context: "failed to insert voices",
      error: wrappedSupabaseError(error),
    });
  }

  return res.sendStatus(200);
});

app.get("/createAIWithSentenceDEBUG", async (req, res) => {
  const result = await generateCharacterWithSentence({
    sentence: "michael scott stuck in the minecraft universe",
  });

  res.json({ ...result });
});

app.get("/generateFirstPostsDEBUG", async (req, res) => {
  const bot = {
    display_name: "Michael Scott",
    // creation_prompt: "Michael Scott but as a minecraft character",
    background:
      "Michael Scott, the former Regional Manager of Dunder Mifflin, finds himself inexplicably transported to the pixelated world of Minecraft. In this new realm, he is determined to lead a team of blocky characters, believing he can replicate the camaraderie of his office. However, his lack of understanding about the game's mechanics often leads to hilarious disasters, such as accidentally creating a lava pit instead of a swimming pool. Despite his blunders, Michael's unwavering optimism keeps him forging ahead, convinced that he can create the ultimate office in this blocky universe.",
    characteristics:
      "frequently misinterprets social cues, tries to befriend everyone, often makes inappropriate jokes, builds elaborate structures in Minecraft, has a tendency to overreact",
    personality:
      "clumsy, optimistic, clueless, enthusiastic, well-meaning, inappropriate, humorous, naive",
  };

  // const bot = {
  //   display_name: "Clark Kent",
  //   background:
  //     "Clark Kent is a Pulitzer Prize-winning investigative journalist who has made a name for himself by taking on corrupt government agencies and corporations. By night, he dons a suit and cape to fight for truth and justice as a superhero. However, his latest exposé has made him a target of the CIA, who are determined to eliminate him. With his skills of observation and deduction, Kael must stay one step ahead of his pursuers while continuing to uncover the truth and protect the innocent",
  //   characteristics:
  //     "Tendency to work alone, obsessive research habits, occasional recklessness, dry sense of humor, constant looking over his shoulder",
  //   personality:
  //     "Resourceful, determined, introverted, analytical, courageous, compassionate, restless, vigilant",
  // };

  const result = await generateFirstPosts({ bot });

  res.json({ ...result });
});

app.post("/createAIWithEvolving", async (req, res) => {
  if (!req.body.sentence || !req.body.botId) {
    return res.sendStatus(400);
  }

  const { data: parentBot, error: parentBotError } = await supabase
    .from("bots")
    .select("characteristics, personality, background, description, bio")
    .eq("id", req.body.botId)
    .single();

  if (parentBotError) {
    const error = wrappedSupabaseError(parentBotError);
    logError({
      executionId: req.executionId,
      context: "createAIWithEvolving: fetch parent bot error",
      error,
    });

    return res.sendStatus(502);
  }

  try {
    const result = await generateCharacterWithSentence({
      sentence: req.body.sentence,
      parentBot,
    });
    res.json(result);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "createAIWithEvolving: generateCharacterWithSentence error",
      error,
    });
    res.json({ error });
  }
});

async function generateCharacterWithSentence({
  sentence,
  parentBot,
  loopCount = 0,
  clone_id,
  user_profile_id,
  test_inspiration, // just for testing
}) {
  const completion = await callAndLogOpenAI(
    "OpenAI: createWithSentence",
    {
      messages: [
        {
          role: "user",
          content: `Does this sentence include a real person: '${sentence}'? return 1 if yes, 0 if no`,
        },
      ],
      model: "gpt-4o-mini",
    },
    {
      timeout: 8 * 1000,
    },
  );

  const is_real_person = parseInt(completion.choices[0].message.content);

  if (loopCount > 3) {
    throw new Error("Producing bad output JSON after 3 attempts");
  }

  let appearancePrompt =
    "A collection of very short descriptions of the character's appearance. Be sure to mention their hairstyle, skin, hair length, gender and skin tone. For example: 'Tall, dark hair, blue eyes, 23 years old, woman, man'";

  let promptSentence = sentence;

  let clone;

  if (clone_id) {
    const { data: _clone, error: cloneError } = await supabase
      .from("clones")
      .select("caption, gender")
      .eq("id", clone_id)
      .single();

    if (cloneError) {
      logError({
        context: "generateCharacterWithSentence: Error fetching clone",
        error: cloneError,
      });

      throw cloneError;
    }

    if (!_clone) {
      throw new Error("Clone not found");
    }

    clone = _clone;

    // We pass through clone caption here to influecne the charactertistics based on the user's appearance
    appearancePrompt = `A collection of very short descriptions of the character's appearance. Be sure to include the description: "${clone.caption}" as well as additional details`;

    // Pass through the gender so the initial generation can be more accurate
    promptSentence = `${promptSentence}, ${clone.gender}`;
  }

  let creationPrompt;

  const uniqueness = Math.floor(Math.random() * 100);
  let inspiration = `${getRandomSeedWord()} // If the character is a human and the details don't specify a background, use this for inspiration of their last name only. Don't let it impact the appearance or background`;

  if (is_real_person) {
    inspiration = "";
  }

  if (test_inspiration) {
    // eslint-disable-next-line no-unused-vars
    inspiration = test_inspiration;
  }

  // VU: Deprecate soon, no one uses it
  if (parentBot) {
    creationPrompt = `These are the original character details. Keep the same JSON format:
${parentBot.personality ? `personality: ${parentBot.personality}` : ""}
${parentBot.characteristics ? `characteristics: ${parentBot.characteristics}` : ""}
${parentBot.background ? `background: ${parentBot.background}` : ""}
${parentBot.description ? `appearance: ${parentBot.description}` : ""}
${parentBot.bio ? `bio: ${parentBot.bio}` : ""}

Transform the details of this character into a new character based upon these details:
- {{desc}}
The name and the character should be on a scale of uniqueness: ${uniqueness}/100 unique
END SEED INFORMATION

Use the following information for the template in JSON format, nothing else:
{
  name: "Name of the character",
  personality: "Use the seed information to create a comma (,) separated list of eight or more adjectives to describe the character's personality. Avoid redundant adjectives and synonyms.",
  characteristics: "Develop a list of up to five comma (,) separated habits of varying length and complexity for the character. Include both positive and negative habits and traits.For example: Habit1, Habit2, etc.",
  background: "Use the seed information to generate 3-5 detail rich sentences for the character's background in paragraph format.",
  appearance: "${appearancePrompt}"
  clothing: "What they are wearing now"
}
`;
  } else if (clone) {
    let namePrompt = "Name of the character";

    if (user_profile_id) {
      const { data: userProfile, error: userProfileError } = await supabase
        .from("profiles")
        .select("id, display_name, username")
        .neq("visibility", "archived")
        .eq("id", user_profile_id)
        .single();

      if (userProfileError) {
        logWarn({
          context: "generateCharacterWithSentence: Error fetching user profile",
          message: userProfileError,
        });
      }

      if (
        !userProfileError &&
        userProfile &&
        (userProfile.display_name || userProfile.username)
      ) {
        namePrompt = `"Create a name based on the user's name '${
          userProfile?.display_name ?? userProfile?.username ?? ""
        }'"`;
      }
    }

    creationPrompt = `Create a detailed character template based on the following seed information:
- {{desc}}
The name and the character should be on a scale of uniqueness: ${uniqueness}/100 unique
END SEED INFORMATION
    
Use the following information for the template in JSON format, nothing else:
{
  name: "${namePrompt}",
  personality: "Use the seed information to create a comma (,) separated list of eight or more adjectives to describe the character's personality. Avoid redundant adjectives and synonyms.",
  characteristics: "Develop a list of up to five comma (,) separated habits of varying length and complexity for the character. Include both positive and negative habits and traits. For example: Habit1, Habit2, etc.",
  background: "Use the seed information to generate 3-5 detail rich sentences for the character's background in paragraph format",
  appearance: ${appearancePrompt},
  artistic_style: "only if the seed information specifies an artistic style: minecraft, 3d, anime, photo, blocky, low poly, etc. choose multiple, or empty string if none",
  clothing: "What they are wearing right now from the upper torso and up"
}`;
    // Regular flow
  } else {
    let namePrompt = "Name of the character";

    if (is_real_person) {
      namePrompt = `Use the "based_on" name`;
    }

    creationPrompt = `Create a detailed character template based on the following seed information:
- {{desc}}
The name and the character should be on a scale of uniqueness: ${uniqueness}/100 unique
END SEED INFORMATION
    
Use the following information for the template in JSON format, nothing else:
{
  based_on: "The IP or person this is based on. Empty if none",
  name: "${namePrompt}",
  personality: "Use the seed information to create a comma (,) separated list of eight or more adjectives to describe the character's personality. Avoid redundant adjectives and synonyms.",
  characteristics: "Develop a list of up to five comma (,) separated habits of varying length and complexity for the character. Include both positive and negative habits and traits. For example: Habit1, Habit2, etc.",
  background: "Use the seed information to generate 3-5 detail rich sentences for the character's background in paragraph format",
  appearance: ${appearancePrompt},
  artistic_style: "only if the seed information specifies an artistic style: minecraft, 3d, anime, photo, blocky, low poly, etc. choose multiple, or empty string if none",
  clothing: "What they are wearing right now from the upper torso and up"
}`;
  }

  console.log("**** generateCharacterWithSentence", creationPrompt);

  const messages = [
    {
      role: "user",
      content: creationPrompt.replace("{{desc}}", promptSentence),
    },
  ];

  try {
    const chatCompletion = await callAndLogOpenAI(
      "OpenAI: createWithSentence",
      {
        response_format: { type: "json_object" },
        messages,
        temperature: 1.0,
        top_p: 0.5,
        model: "gpt-4o-mini",
      },
      {
        timeout: 8 * 1000,
      },
    );

    const result = chatCompletion.choices[0].message.content;

    if (
      result.includes("can not assist with that request") ||
      result.includes("as an AI language")
    ) {
      throw new Error("AI can not assist with that request");
    }

    try {
      let json = JSON.parse(result);

      // if based on a character
      if (!json.based_on || json.based_on?.length === 0) {
        // original character
        // if original character, pump in as many appearance stuff to keep consistent
        json.appearance = `(${json.name}), (${promptSentence}) ,${json.appearance}, ${json.clothing}`;
      } else {
        // if based on a character, let the character speak for itself
        json.appearance = `(${json.based_on}), (${promptSentence})`;
      }

      // If artistic style exists, emphasize it to keep it more consistent
      if (json.artistic_style && json.artistic_style.length > 0) {
        json.appearance = `${json.appearance}, (${json.artistic_style}:1.2)`;
      }

      // pass through gender again to fix it
      // We pass through the clone caption too to double down on the appearance
      if (clone) {
        json.appearance = `${json.appearance}, ${clone.caption}, ${clone.gender}`;
      }

      json.avatarDescription = `${json.appearance}`;

      return json;
    } catch (error) {
      logError({
        context:
          "generateCharacterWithSentence: Error parsing JSON or other error",
        error,
      });
      // error parsing JSON, recall
      await generateCharacterWithSentence({
        sentence,
        parentBot,
        loopCount: loopCount + 1,
        clone_id,
        user_profile_id,
      });
    }

    return result;
  } catch (error) {
    logError({
      context: "generateCharacterWithSentence: Error",
      error,
    });
    throw error;
  }
}

app.post("/createAI", authUser, async (req, res) => {
  if (
    !req.body.creator_id ||
    !req.body.bio ||
    !req.body.description ||
    !req.body.art_style ||
    !req.body.name
  ) {
    return res.sendStatus(400);
  }

  const currentDate = new Date();

  let dateToCheck = currentDate.toISOString().split("T")[0];

  const { data: botCountData, error: botCountError } = await supabase
    .from("bots")
    .select("count", { count: "exact" })
    .eq("creator_id", req.body.creator_id)
    .gte("created_at", dateToCheck)
    .single();

  if (botCountError || !botCountData) {
    logError({
      executionId: req.executionId,
      context: "createAI: **** fetch today bot error",
      error: botCountError,
    });
    return res.sendStatus(400);
  }

  if (botCountData.count >= BOT_CREATE_LIMIT) {
    logWarn({
      context: "createAI",
      message: `**** Maximum number exceed!, ${req.body.creator_id}`,
      creator_id: req.body.creator_id,
    });
    return res.sendStatus(429);
  }

  const payload = {
    response_format: { type: "json_object" },
    messages: [
      {
        role: "system",
        content: `"Given a character 
                    Named: ${req.body.name}
                    Bio: ${req.body.bio}
                    Description: ${req.body.description}

                    If they were to make an instagram profile, what would the following details be? Answer in the JSON format, nothing else:
                    {
                      username: "the username of the user. It must not exceed 30 characters in length. It must only contain Latin characters (a-z, A-Z), numbers (0-9), underscores (_), and dots (.) inside. Must not contain any Zalgo text. Must not end with special characters, example: @!#$%*&^"':;?!().-+/[]_\\|.",
                      timezone: a real time zone like america/los_angeles or something else,
                      location: self explanatory,
                      gender: male or female,
                      tag: Generate at least 8 topic tags for the following person/character. Output in this format: example game,movie,anime,tv-show,politics,cooking,art
                      franchise: the franchise of the character. For example Tifa is from Final Fantasy franchise. If none, put "original",
                      source: the source of the character, like Tifa is from Final Fantasy VII. If none fit, put "original",
                      profile_bio: A short one sentence bio they would write for themselves,
                      nsfw: whether this profile is nsfw or not, boolean
                    }
                  
        ",`,
      },
    ],
    model: GPT_TEXT_MODEL,
  };

  let bot;

  try {
    const chatCompletion = await callAndLogOpenAI("OAI:CreateAI", payload, {
      timeout: 8 * 1000,
    });

    bot = JSON.parse(chatCompletion.choices[0].message.content);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "createAI: Error generating bot",
      error,
    });
    return res.sendStatus(500);
  }

  console.log("**** createAI bot", req.body.creator_id, req.body.name, bot);
  let timezone;

  try {
    dayjs.tz("2013-11-18T11:55:20", bot.timezone);
    timezone = getSafeTimezone(bot.timezone);
  } catch (e) {
    logWarn({
      context: " /createAI - dayjs failed to parse timezone",
      error: e,
      bot_timezone: bot.timezone,
    });
    const myArray = [
      "America/Los_Angeles",
      "America/New_York",
      "America/Chicago",
      "Europe/Paris",
      "Asia/Tokyo",
    ];
    // Generate a random index within the array length
    const randomIndex = Math.floor(Math.random() * myArray.length);

    // Get the random string from the array
    const randomString = myArray[randomIndex];
    timezone = randomString;
  }

  let username = bot.username;

  // XXX: missing error handling, but this is a deprecated endpoint
  // check if profile already exists
  const { data: doesExist } = await supabase
    .from("profiles")
    .select("count", { count: "exact" })
    .ilike("username", `${bot.username}`)
    .single();

  if (doesExist && doesExist.count > 0) {
    username = `${bot.username}${Math.floor(Math.random() * 10000)}`;
  }

  const nsfw = req.body?.nsfw
    ? "nsfw"
    : req.body?.nsfw == false
      ? "normal"
      : bot.nsfw
        ? "nsfw"
        : "normal";

  const visibility = req.body.nsfw ? "private" : "public";

  let insertOption = {
    username: username,
    description: bot.profile_bio,
    display_name: req.body.name,
    location: bot.location,
    nsfw: nsfw,
  };

  if (req.body.nsfl != null && req.body.nsfl != undefined) {
    insertOption.nsfl = req.body.nsfl;
  }

  if (req.body.nsfw != null && req.body.nsfw != undefined) {
    insertOption.visibility = visibility;
  }

  if (req.body.imitation != null && req.body.imitation != undefined) {
    insertOption.imitation = req.body.imitation;
  }

  let butterflyURL, avatar_photo_contains_face;

  try {
    if (req.body?.url) {
      if (req.body?.url.includes("/object/public/ai-ig")) {
        butterflyURL = req.body?.url;
      } else {
        butterflyURL = await uploadToStorage({
          url: req.body?.url,
        });
      }
    }

    avatar_photo_contains_face = await doesImageContainFace({
      imageUrl: butterflyURL,
    });
  } catch (err) {
    logError({
      executionId: req.executionId,
      context: "createAI: Error generating bot avatar",
      error: err,
    });
    return res.sendStatus(500);
  }

  insertOption.avatar_url = butterflyURL;
  insertOption.image_widths = null;
  insertOption.avatar_photo_contains_face = avatar_photo_contains_face;

  // XXX: missing error handling, but this is a deprecated endpoint
  const { data: profile } = await supabase
    .from("profiles")
    .insert(insertOption)
    .select();

  const min = 9;
  const max = 16;
  const randomHourAvailable = Math.floor(Math.random() * (max - min + 1)) + min;

  const min2 = 7;
  const max2 = 11;
  const randomMinuteWakeUpTime = (Math.random() * (max2 - min2) + min2).toFixed(
    2,
  );

  const min3 = 30;
  const max3 = 130;
  const randomTimeInterval =
    Math.floor(Math.random() * (max3 - min3 + 1)) + min3;

  const { data: botResult, error: botError } = await supabase
    .from("bots")
    .insert({
      creator_id: req.body.creator_id,
      art_style: req.body.art_style,
      display_name: req.body.name,
      profile_id: profile[0].id,
      description: req.body.description,
      life: req.body?.life ?? null,
      bio: req.body.bio,
      source: bot.source,
      location: bot.location,
      franchise: bot.franchise,
      tag: bot.tag,
      gender: bot.gender,
      timezone,
      active_hours_per_day: randomHourAvailable,
      wake_up_time: parseInt((randomMinuteWakeUpTime * 60).toFixed(0)),
      wake_up_interval: randomTimeInterval,
      seed: Math.floor(Math.random() * 100000000000),
      is_active: true,
    })
    .select();

  if (botError) {
    const error = wrappedSupabaseError(
      botError,
      "failed to insert into 'bots'",
    );
    logError({
      context: "/createAI - botError",
      error,
    });
    return res.sendStatus(500);
  }

  let singularBot = botResult[0];
  singularBot.profiles = {
    nsfw: nsfw,
    visibility: visibility,
  };

  try {
    if (!req.body.skipAvatarGeneration) {
      throw new Error("outdated app, we no longer integrate with seaart");
    }
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "createAI: Error generating art request",
      error,
    });
    return res.sendStatus(500);
  }

  res.json({ profile_id: singularBot.profile_id, username });

  // Generate memories after AI creation
  if (!!req.body.life && singularBot) {
    await generateMemories({
      bot: singularBot,
      executionId: req.executionId,
      storyContext: req.body.life,
    });
  }

  const url = `${baseUrl}/bots/startRoutine`;

  // XXX: missing error handling, but this is a deprecated endpoint
  await supabase.from("subscribers").insert({
    subscriber_id: req.body.creator_id,
    subscribing_id: singularBot.profile_id,
  });

  await axios.post(url, {
    bot: singularBot,
    executionId: req.executionId,
  });

  // XXX: missing error handling, but this is a deprecated endpoint
  await doFollow({
    following_id: req.body.creator_id,
    follower_id: singularBot.profile_id,
  });

  // XXX: missing error handling, but this is a deprecated endpoint
  await doFollow({
    following_id: singularBot.profile_id,
    follower_id: req.body.creator_id,
  });
});

app.get("/fetchCloneTemplates", async (req, res) => {
  // const userData = {}; User should post this so we can rank and optimize

  const result = [
    {
      card_background_color: "#BEE8FF",
      card_emoji: "🇺🇸",
      card_image_url: "https://emojicdn.elk.sh/🇺🇸",
      title: "President",
      subtitle:
        "You’re the newly elected President of the US. It’s your first day.",
    },
    {
      card_background_color: "#FFC6BE",
      card_emoji: "🎤",
      card_image_url: "https://emojicdn.elk.sh/🎤",
      title: "K-Pop Star",
      subtitle: "You’re a K-pop star in training about to make your big debut.",
    },
    {
      card_background_color: "#FFE5BE",
      card_emoji: "🏀",
      card_image_url: "https://emojicdn.elk.sh/🏀",
      title: "NBA Player",
      subtitle: "You just got drafted 40th overall in the latest NBA draft.",
    },
    {
      card_background_color: "#FFF",
      card_emoji: "🏎️",
      card_image_url: "https://emojicdn.elk.sh/🏎️",
      title: "F1 Driver",
      subtitle:
        "It’s your first season as you just got promoted to Red Bull racing.",
    },
    {
      card_background_color: "#FFE5E5",
      card_emoji: "🔮",
      card_image_url: "https://emojicdn.elk.sh/🔮",
      title: "Fortune Teller",
      subtitle:
        "You’re the most sought-after fortune teller, predicting world events.",
    },
    {
      card_background_color: "#FFF0E3",
      card_emoji: "👽",
      card_image_url: "https://emojicdn.elk.sh/👽",
      title: "Alien Ambassador",
      subtitle: "You’re Earth’s first contact with an alien civilization.",
    },
    {
      card_background_color: "#E7FFF2",
      card_emoji: "🧜‍♀️",
      card_image_url: "https://emojicdn.elk.sh/🧜‍♀️",
      title: "Mermaid",
      subtitle:
        "You’re exploring hidden underwater worlds as a legendary mermaid.",
    },
    {
      card_background_color: "#E5E5FF",
      card_emoji: "🐉",
      card_image_url: "https://emojicdn.elk.sh/🐉",
      title: "Dragon Rider",
      subtitle: "You’re the chosen one, riding dragons across mystical lands.",
    },
    {
      card_background_color: "#FFE8F0",
      card_emoji: "👑",
      card_image_url: "https://emojicdn.elk.sh/👑",
      title: "Emperor of the Galaxy",
      subtitle: "You rule an intergalactic empire with peace and prosperity.",
    },
    {
      card_background_color: "#FFFBE5",
      card_emoji: "🕰️",
      card_image_url: "https://emojicdn.elk.sh/🕰️",
      title: "Time Traveler",
      subtitle: "You can travel to any era, changing history as you go.",
    },
    {
      card_background_color: "#E5F4FF",
      card_emoji: "🧛‍♂️",
      card_image_url: "https://emojicdn.elk.sh/🧛‍♂️",
      title: "Vampire",
      subtitle: "You’re a centuries-old vampire exploring the modern world.",
    },
    {
      card_background_color: "#E7FFE5",
      card_emoji: "🌋",
      card_image_url: "https://emojicdn.elk.sh/🌋",
      title: "Volcano Explorer",
      subtitle:
        "You’re an adventurer exploring active volcanoes around the world.",
    },
    {
      card_background_color: "#E5F7FF",
      card_emoji: "🦸‍♂️",
      card_image_url: "https://emojicdn.elk.sh/🦸‍♂️",
      title: "Superhero",
      subtitle:
        "You’ve just discovered your superpowers, ready to save the day!",
    },
    {
      card_background_color: "#FFF4E7",
      card_emoji: "🎢",
      card_image_url: "https://emojicdn.elk.sh/🎢",
      title: "Theme Park Designer",
      subtitle: "You’re designing the world’s most thrilling roller coasters.",
    },
    {
      card_background_color: "#E5FFF4",
      card_emoji: "⚔️",
      card_image_url: "https://emojicdn.elk.sh/⚔️",
      title: "Knight",
      subtitle: "You’re a knight on a quest to protect your kingdom.",
    },
    {
      card_background_color: "#FFE7FF",
      card_emoji: "👻",
      card_image_url: "https://emojicdn.elk.sh/👻",
      title: "Ghost Hunter",
      subtitle:
        "You investigate haunted locations to capture paranormal activity.",
    },
    {
      card_background_color: "#FFF0FF",
      card_emoji: "🌌",
      card_image_url: "https://emojicdn.elk.sh/🌌",
      title: "Galactic Explorer",
      subtitle:
        "You’re exploring uncharted planets in the far reaches of space.",
    },
    {
      card_background_color: "#E7FFE5",
      card_emoji: "👨‍🎤",
      card_image_url: "https://emojicdn.elk.sh/👨‍🎤",
      title: "Synthwave Star",
      subtitle: "You’re the face of the retro-futuristic music scene in 2080.",
    },
    {
      card_background_color: "#FFE3E7",
      card_emoji: "🎩",
      card_image_url: "https://emojicdn.elk.sh/🎩",
      title: "Magician",
      subtitle: "You’re a world-famous magician performing impossible tricks.",
    },
    {
      card_background_color: "#FFE5F0",
      card_emoji: "🍄",
      card_image_url: "https://emojicdn.elk.sh/🍄",
      title: "Mushroom Forager",
      subtitle:
        "You’re a renowned expert finding rare mushrooms in mystical forests.",
    },
    {
      card_background_color: "#E5FFF5",
      card_emoji: "🧞",
      card_image_url: "https://emojicdn.elk.sh/🧞",
      title: "Genie",
      subtitle: "You grant wishes with a twist, making dreams come true.",
    },
    {
      card_background_color: "#E7EFFF",
      card_emoji: "🎮",
      card_image_url: "https://emojicdn.elk.sh/🎮",
      title: "VR Game Designer",
      subtitle: "You’re creating the most immersive virtual worlds ever seen.",
    },
    {
      card_background_color: "#FFE5F7",
      card_emoji: "🔫",
      card_image_url: "https://emojicdn.elk.sh/🔫",
      title: "Bounty Hunter",
      subtitle: "You’re tracking down criminals in a lawless futuristic city.",
    },
    {
      card_background_color: "#F0FFE5",
      card_emoji: "🐺",
      card_image_url: "https://emojicdn.elk.sh/🐺",
      title: "Werewolf",
      subtitle: "You’re learning to control your powers under the full moon.",
    },
    {
      card_background_color: "#E5FFF0",
      card_emoji: "🏺",
      card_image_url: "https://emojicdn.elk.sh/🏺",
      title: "Archaeologist",
      subtitle: "You’re unearthing ancient secrets in lost civilizations.",
    },

    {
      card_background_color: "#FFE7E0",
      card_emoji: "🧙",
      card_image_url: "https://emojicdn.elk.sh/🧙",
      title: "Wizard",
      subtitle:
        "You’re mastering the art of spellcasting in a magical academy.",
    },
    {
      card_background_color: "#F0E5FF",
      card_emoji: "🛠️",
      card_image_url: "https://emojicdn.elk.sh/🛠️",
      title: "Inventor",
      subtitle:
        "You’re creating groundbreaking inventions that could change the world.",
    },
    {
      card_background_color: "#E5FFF4",
      card_emoji: "🦷",
      card_image_url: "https://emojicdn.elk.sh/🦷",
      title: "Tooth Fairy",
      subtitle:
        "You bring magic to kids, collecting teeth and leaving surprises.",
    },
    {
      card_background_color: "#FFF4E0",
      card_emoji: "🌪️",
      card_image_url: "https://emojicdn.elk.sh/🌪️",
      title: "Storm Chaser",
      subtitle:
        "You’re tracking and studying the world’s most powerful storms.",
    },
    {
      card_background_color: "#E7E7FF",
      card_emoji: "🚀",
      card_image_url: "https://emojicdn.elk.sh/🚀",
      title: "Space Pirate",
      subtitle: "You’re the captain of a crew, seeking treasure in the galaxy.",
    },
    {
      card_background_color: "#FFF7E5",
      card_emoji: "🪐",
      card_image_url: "https://emojicdn.elk.sh/🪐",
      title: "Astrobiologist",
      subtitle: "You’re studying alien lifeforms on distant planets.",
    },
    {
      card_background_color: "#FFE7EB",
      card_emoji: "🌲",
      card_image_url: "https://emojicdn.elk.sh/🌲",
      title: "Forest Guardian",
      subtitle: "You protect enchanted forests and all the creatures within.",
    },
    {
      card_background_color: "#FFE7D6",
      card_emoji: "👨‍⚖️",
      card_image_url: "https://emojicdn.elk.sh/👨‍⚖️",
      title: "Supreme Court Justice",
      subtitle:
        "You've just been appointed to the Supreme Court. Time to make history!",
    },
    {
      card_background_color: "#FFF0B3",
      card_emoji: "🧑‍🍳",
      card_image_url: "https://emojicdn.elk.sh/🧑‍🍳",
      title: "Celebrity Chef",
      subtitle: "You’re hosting a new cooking show that’s going viral!",
    },
    {
      card_background_color: "#D6FFE7",
      card_emoji: "🕵️‍♀️",
      card_image_url: "https://emojicdn.elk.sh/🕵️‍♀️",
      title: "Detective",
      subtitle: "You’re the city’s top detective on a high-profile case.",
    },
    {
      card_background_color: "#E0F7FA",
      card_emoji: "👩‍🚀",
      card_image_url: "https://emojicdn.elk.sh/👩‍🚀",
      title: "Astronaut",
      subtitle:
        "You’ve just landed on Mars as part of the first human mission!",
    },
    {
      card_background_color: "#FFF4E5",
      card_emoji: "💼",
      card_image_url: "https://emojicdn.elk.sh/💼",
      title: "Tech CEO",
      subtitle: "You’re the founder of a startup that’s changing the world.",
    },
    {
      card_background_color: "#E7EFFF",
      card_emoji: "🎨",
      card_image_url: "https://emojicdn.elk.sh/🎨",
      title: "Famous Artist",
      subtitle:
        "Your latest exhibition just opened in New York, and it’s sold out!",
    },
    {
      card_background_color: "#FBE7FF",
      card_emoji: "🎸",
      card_image_url: "https://emojicdn.elk.sh/🎸",
      title: "Rock Star",
      subtitle:
        "You’re about to perform at the biggest music festival in the world.",
    },
    {
      card_background_color: "#FFF0D4",
      card_emoji: "🏄",
      card_image_url: "https://emojicdn.elk.sh/🏄",
      title: "Pro Surfer",
      subtitle: "You’re riding the waves in Hawaii for a world championship.",
    },
    {
      card_background_color: "#E5FFF7",
      card_emoji: "🕴️",
      card_image_url: "https://emojicdn.elk.sh/🕴️",
      title: "Spy",
      subtitle: "You’re on an undercover mission in a foreign country.",
    },
    {
      card_background_color: "#FFF4EB",
      card_emoji: "🎥",
      card_image_url: "https://emojicdn.elk.sh/🎥",
      title: "Film Director",
      subtitle: "Your movie just premiered at the Cannes Film Festival!",
    },
    {
      card_background_color: "#FFE1F0",
      card_emoji: "🏋️",
      card_image_url: "https://emojicdn.elk.sh/🏋️",
      title: "Bodybuilder",
      subtitle:
        "You’re competing in the Mr. Olympia bodybuilding championship.",
    },
    {
      card_background_color: "#FFF7E5",
      card_emoji: "👑",
      card_image_url: "https://emojicdn.elk.sh/👑",
      title: "Royalty",
      subtitle: "You’re a prince/princess preparing for a royal coronation.",
    },
    {
      card_background_color: "#E5FFF7",
      card_emoji: "⚖️",
      card_image_url: "https://emojicdn.elk.sh/⚖️",
      title: "Lawyer",
      subtitle:
        "You’re defending a high-profile case that could change the law.",
    },
    {
      card_background_color: "#FFEDE7",
      card_emoji: "📚",
      card_image_url: "https://emojicdn.elk.sh/📚",
      title: "Bestselling Author",
      subtitle: "Your new novel is #1 on the bestseller list!",
    },
    {
      card_background_color: "#E7FFFB",
      card_emoji: "🤖",
      card_image_url: "https://emojicdn.elk.sh/🤖",
      title: "AI Developer",
      subtitle: "You just created a groundbreaking AI that’s changing lives.",
    },
    {
      card_background_color: "#FFE5FF",
      card_emoji: "🎭",
      card_image_url: "https://emojicdn.elk.sh/🎭",
      title: "Broadway Star",
      subtitle: "You’re debuting in the lead role of a new Broadway musical.",
    },
    {
      card_background_color: "#F0FFF4",
      card_emoji: "🚀",
      card_image_url: "https://emojicdn.elk.sh/🚀",
      title: "Space Engineer",
      subtitle: "You’re leading a team on a mission to build a Mars base.",
    },
    {
      card_background_color: "#FFF0F7",
      card_emoji: "⚽",
      card_image_url: "https://emojicdn.elk.sh/⚽",
      title: "Soccer Star",
      subtitle: "You just scored the winning goal in the World Cup final!",
    },
    {
      card_background_color: "#E7E7FF",
      card_emoji: "🎤",
      card_image_url: "https://emojicdn.elk.sh/🎤",
      title: "Stand-Up Comedian",
      subtitle: "You’re going viral for your latest comedy special!",
    },
    {
      card_background_color: "#FFF0EB",
      card_emoji: "💻",
      card_image_url: "https://emojicdn.elk.sh/💻",
      title: "Game Developer",
      subtitle: "Your indie game just became a global sensation!",
    },
    {
      card_background_color: "#FFE7D6",
      card_emoji: "💄",
      card_image_url: "https://emojicdn.elk.sh/💄",
      title: "Makeup Mogul",
      subtitle: "Your new makeup line is selling out everywhere!",
    },
    {
      card_background_color: "#D6FFF4",
      card_emoji: "🪂",
      card_image_url: "https://emojicdn.elk.sh/🪂",
      title: "Skydiver",
      subtitle: "You’re performing a record-breaking skydive from 30,000 feet.",
    },
    {
      card_background_color: "#FFEDE7",
      card_emoji: "📷",
      card_image_url: "https://emojicdn.elk.sh/📷",
      title: "Travel Blogger",
      subtitle:
        "You’re exploring hidden gems around the world, one photo at a time.",
    },
    {
      card_background_color: "#FFF7E5",
      card_emoji: "🚔",
      card_image_url: "https://emojicdn.elk.sh/🚔",
      title: "Police Officer",
      subtitle: "You’re the hero of the city, keeping the streets safe.",
    },
    {
      card_background_color: "#E0F0FF",
      card_emoji: "🧑‍🎨",
      card_image_url: "https://emojicdn.elk.sh/🧑‍🎨",
      title: "Fashion Designer",
      subtitle: "Your latest collection just debuted at Paris Fashion Week!",
    },
    {
      card_background_color: "#F0FFE5",
      card_emoji: "🍲",
      card_image_url: "https://emojicdn.elk.sh/🍲",
      title: "Food Critic",
      subtitle:
        "You’re reviewing the hottest new restaurants around the world.",
    },
    {
      card_background_color: "#FFE3E0",
      card_emoji: "🚴",
      card_image_url: "https://emojicdn.elk.sh/🚴",
      title: "Cyclist",
      subtitle: "You’re racing in the Tour de France, aiming for victory.",
    },
    {
      card_background_color: "#E5F4FF",
      card_emoji: "🏌️",
      card_image_url: "https://emojicdn.elk.sh/🏌️",
      title: "Golf Pro",
      subtitle: "You’re about to tee off in the Masters tournament.",
    },
    {
      card_background_color: "#E5FFF7",
      card_emoji: "🤹",
      card_image_url: "https://emojicdn.elk.sh/🤹",
      title: "Circus Performer",
      subtitle: "You’re the star of the show, dazzling audiences worldwide.",
    },
    {
      card_background_color: "#FFF5F0",
      card_emoji: "🏇",
      card_image_url: "https://emojicdn.elk.sh/🏇",
      title: "Jockey",
      subtitle: "You’re riding in the Kentucky Derby with a shot at victory.",
    },
    {
      card_background_color: "#FFF0E7",
      card_emoji: "🚁",
      card_image_url: "https://emojicdn.elk.sh/🚁",
      title: "Helicopter Pilot",
      subtitle: "You’re rescuing people in daring helicopter missions.",
    },
    {
      card_background_color: "#E0FFEB",
      card_emoji: "🛩️",
      card_image_url: "https://emojicdn.elk.sh/🛩️",
      title: "Pilot",
      subtitle:
        "You’re flying the world’s largest passenger jet on its maiden voyage.",
    },
    {
      card_background_color: "#FFEBE0",
      card_emoji: "🧑‍⚖️",
      card_image_url: "https://emojicdn.elk.sh/🧑‍⚖️",
      title: "Politician",
      subtitle: "You’re running for Mayor with a platform to change the city.",
    },
    {
      card_background_color: "#F0EFFF",
      card_emoji: "👨‍🔬",
      card_image_url: "https://emojicdn.elk.sh/👨‍🔬",
      title: "Biologist",
      subtitle: "You just discovered a new species in the Amazon rainforest!",
    },
    {
      card_background_color: "#E7FFF4",
      card_emoji: "🩺",
      card_image_url: "https://emojicdn.elk.sh/🩺",
      title: "Surgeon",
      subtitle: "You’re about to perform a groundbreaking surgery.",
    },
    {
      card_background_color: "#FFE7ED",
      card_emoji: "🎯",
      card_image_url: "https://emojicdn.elk.sh/🎯",
      title: "Esports Champion",
      subtitle: "You’re the top player in a global gaming tournament.",
    },
    {
      card_background_color: "#FFF7E0",
      card_emoji: "🏒",
      card_image_url: "https://emojicdn.elk.sh/🏒",
      title: "Hockey Star",
      subtitle:
        "You’re playing in the NHL and leading your team to the finals.",
    },
    {
      card_background_color: "#E3E0FF",
      card_emoji: "📖",
      card_image_url: "https://emojicdn.elk.sh/📖",
      title: "Historian",
      subtitle: "You’re uncovering secrets in an ancient manuscript.",
    },
    {
      card_background_color: "#FFE7D4",
      card_emoji: "🔬",
      card_image_url: "https://emojicdn.elk.sh/🔬",
      title: "Scientist",
      subtitle: "You’re leading a team researching a cure for a major disease.",
    },
    {
      card_background_color: "#E0FFEA",
      card_emoji: "🏆",
      card_image_url: "https://emojicdn.elk.sh/🏆",
      title: "Motivational Speaker",
      subtitle: "You’re inspiring millions with your powerful message.",
    },
    {
      card_background_color: "#FFF0E5",
      card_emoji: "🚣",
      card_image_url: "https://emojicdn.elk.sh/🚣",
      title: "Rowing Champion",
      subtitle: "You’re competing in the Olympics with a shot at gold.",
    },
  ];

  return res.json(result);
});

app.post("/createAIV2", authUser, async (req, res) => {
  if (
    !req.body.creator_id ||
    !req.body.characteristics ||
    !req.body.personality ||
    !req.body.background ||
    !req.body.appearance ||
    !req.body.art_style ||
    !req.body.name ||
    !req.body.username ||
    !req.body.timezone ||
    !req.body.location ||
    !req.body.gender ||
    !req.body.tag ||
    !req.body.franchise ||
    !req.body.source ||
    !req.body.profile_bio ||
    req.body.nsfw === undefined ||
    req.body.nsfw === null
  ) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  // check if the creator_id is valid
  const isValid = await checkProfileValid(user_id, req.body.creator_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // check if the avatar_url is valid
  if (req.body?.url) {
    const isValid = checkMediaUrlValid(req.body?.url);
    if (!isValid) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  const isFirstBot = req.body.is_first;

  const currentDate = new Date();

  let dateToCheck = currentDate.toISOString().split("T")[0];

  const { data: botCountData, error: botCountError } = await supabase
    .from("bots")
    .select("count", { count: "exact" })
    .eq("creator_id", req.body.creator_id)
    .gte("created_at", dateToCheck)
    .single();

  if (botCountError || !botCountData) {
    const error = wrappedSupabaseError(botCountError);
    logError({
      executionId: req.executionId,
      context: "createAI: **** fetch today bot error",
      error: error,
    });
    return res.sendStatus(500);
  }

  if (botCountData.count >= BOT_CREATE_LIMIT) {
    logWarn({
      executionId: req.executionId,
      context: "createAI",
      message: `**** Maximum number exceed!, ${req.body.creator_id}`,
    });
    return res.sendStatus(429);
  }

  let bot = {
    username: req.body.username,
    timezone: req.body.timezone,
    location: req.body.location,
    gender: req.body.gender,
    tag: req.body.tag,
    franchise: req.body.franchise,
    source: req.body.source,
    profile_bio: req.body.profile_bio,
    nsfw: req.body.nsfw,
  };

  if (req.body.clone_id) {
    bot.clone_id = req.body.clone_id;
  }

  console.log("**** createAI bot", req.body.creator_id, req.body.name, bot);
  let timezone;

  try {
    dayjs.tz("2013-11-18T11:55:20", bot.timezone);
    timezone = getSafeTimezone(bot.timezone);
  } catch (e) {
    logWarn({
      context: "createAI: timezone parsing error",
      error: e,
    });

    const myArray = [
      "America/Los_Angeles",
      "America/New_York",
      "America/Chicago",
      "Europe/Paris",
      "Asia/Tokyo",
    ];
    // Generate a random index within the array length
    const randomIndex = Math.floor(Math.random() * myArray.length);

    // Get the random string from the array
    const randomString = myArray[randomIndex];
    timezone = randomString;
  }

  let username = bot.username;

  if (bot.username) {
    username = bot.username.replace(/[^a-zA-Z0-9_.-]/g, "_");
    const usernameRegex = /^[a-zA-Z0-9_.-]{2,60}$/;
    const isUsernameValid = usernameRegex.test(username) && username.length > 0;

    if (!isUsernameValid) {
      username = `butterflies_${Math.floor(Math.random() * 1000000)}`;
    }
  }

  // check if profile already exists
  const doesExist = await retrySupabaseOperation(
    () =>
      supabase
        .from("profiles")
        .select("count", { count: "exact" })
        .ilike("username", `${bot.username}`)
        .single(),
    "getExistedProfile",
  );

  if (doesExist && doesExist.count > 0) {
    username = `${bot.username}${Math.floor(Math.random() * 10000)}`;
  }

  const nsfw = req.body?.nsfw
    ? "nsfw"
    : req.body?.nsfw == false
      ? "normal"
      : bot.nsfw
        ? "nsfw"
        : "normal";

  const visibility = req.body?.visibility ? "public" : "private";

  let insertOption = {
    username: username,
    description: bot.profile_bio,
    display_name: req.body.name,
    location: bot.location,
    gender: bot.gender,
    nsfw: nsfw,
    avatar_url: req.body?.url,
    image_widths: null,
    avatar_photo_contains_face: false,
  };

  // fetch experiment
  const { data: experimentData } = await supabase
    .schema("internal")
    .from("experiments")
    .select("*")
    .eq("user_id", user_id)
    .single();

  const proposedPostModeEnabled = true;
  let cyoaEnabled = false;

  if (experimentData) {
    cyoaEnabled = !!experimentData.cyoa_enabled;
  }

  // this is for CYOA mode, turn it on for all internal profiles
  // const internalUsers = [1, 11433, 43151, 1232, 39880, 44481];
  if (cyoaEnabled) {
    insertOption.cyoa_mode = true;
  }

  const normalPostGenerationMode = !cyoaEnabled && !proposedPostModeEnabled;
  let shouldGenerateFirstPostASAP = !isFirstBot;

  let initialProposedPostGenerationDate;
  if (shouldGenerateFirstPostASAP) {
    initialProposedPostGenerationDate = new Date(
      Date.now() + 60 * 60 * 1000, // 60 minutes
    );
  } else {
    initialProposedPostGenerationDate = new Date(
      Date.now() + 20 * 60 * 1000, // 20 minutes, to match normalPostGenerationMode in cases where the user hasn't manually poked
    );
  }

  if (proposedPostModeEnabled) {
    insertOption.proposed_post_mode = true;
    insertOption.proposed_post_next_generation_date =
      initialProposedPostGenerationDate;
  }

  if (req.body.nsfl != null && req.body.nsfl != undefined) {
    insertOption.nsfl = req.body.nsfl;
  }

  if (req.body.nsfw != null && req.body.nsfw != undefined) {
    insertOption.visibility = visibility;
  }

  if (req.body.imitation != null && req.body.imitation != undefined) {
    insertOption.imitation = req.body.imitation;
  }

  let butterflyURL, avatar_photo_contains_face;

  const { data: profile, error: profileError } = await supabase
    .from("profiles")
    .insert(insertOption)
    .select("*");

  if (profileError) {
    const error = wrappedSupabaseError(profileError);
    logError({
      context: "createAI: profile insert error",
      error: error,
    });
    // We failed to create a new profile record, there is no reasonable way to proceed
    return res.sendStatus(500);
  }
  const singularProfile = profile[0];

  const min = 9;
  const max = 16;
  const randomHourAvailable = Math.floor(Math.random() * (max - min + 1)) + min;

  const min2 = 7;
  const max2 = 11;
  const randomMinuteWakeUpTime = (Math.random() * (max2 - min2) + min2).toFixed(
    2,
  );

  const min3 = 30;
  const max3 = 130;
  const randomTimeInterval =
    Math.floor(Math.random() * (max3 - min3 + 1)) + min3;

  const { data: botResult, error: botError } = await supabase
    .from("bots")
    .insert({
      creator_id: req.body.creator_id,
      art_style: req.body.art_style,
      display_name: req.body.name,
      profile_id: profile[0].id,
      description: req.body.appearance,
      life: req.body?.life ?? null,
      bio: req.body.bio ?? null,
      source: bot.source,
      location: bot.location,
      franchise: bot.franchise,
      tag: bot.tag,
      gender: bot.gender,
      timezone,
      active_hours_per_day: randomHourAvailable,
      wake_up_time: parseInt((randomMinuteWakeUpTime * 60).toFixed(0)),
      wake_up_interval: randomTimeInterval,
      seed: Math.floor(Math.random() * 100000000000),
      is_active: true,
      characteristics: req.body.characteristics,
      personality: req.body.personality,
      background: req.body.background,
      parent_id: req.body.parent_id ?? null,
      clone_id: req.body.clone_id ?? null,
    })
    .select("*");

  if (botError) {
    const error = wrappedSupabaseError(botError);
    logError({
      context: "createAI: bot insert error",
      error,
    });
  }

  let singularBot = botResult[0];
  singularBot.profiles = {
    nsfw: nsfw,
    visibility: visibility,
  };

  const [
    { error: subscribeError },
    { error: followMeError },
    { error: followBotError },
  ] = await Promise.all([
    supabase.from("subscribers").insert({
      subscriber_id: req.body.creator_id,
      subscribing_id: singularBot.profile_id,
    }),
    doFollow({
      following_id: req.body.creator_id,
      follower_id: singularBot.profile_id,
    }),
    doFollow({
      following_id: singularBot.profile_id,
      follower_id: req.body.creator_id,
    }),
  ]);

  if (subscribeError) {
    const error = wrappedSupabaseError(subscribeError);
    logError({
      context: "createAI: subscriber insert error",
      error,
    });
    // XXX: we don't throw since this isn't critical
  }

  if (followMeError) {
    const error = wrappedSupabaseError(followMeError);
    logError({
      context: "createAI: followMe error",
      error,
    });
    // XXX: we don't throw since this isn't critical
  }

  if (followBotError) {
    const error = wrappedSupabaseError(followBotError);
    logError({
      context: "createAI: followBot error",
      error,
    });
    // XXX: we don't throw since this isn't critical
  }

  let firstPostPregeneratedStubs;

  if (shouldGenerateFirstPostASAP) {
    try {
      firstPostPregeneratedStubs = await pregeneratePostAndTaskStubs({
        bot_profile_id: bot.profile_id,
      });
    } catch (error) {
      logError({
        context: "createAI: pregeneratePostAndTaskStubs error",
        error,
        profile_id: singularBot.profile_id,
        username,
      });
      firstPostPregeneratedStubs = {
        error: "failed to pregenerate post and task stubs",
      };
      // intentionally fall through
    }
  }

  if (cyoaEnabled) {
    await considerCreatingANewScenario({
      botProfile: {
        cyoa_mode: true,
        id: singularBot.profile_id,
        shouldCheckActiveHours: false,
      },
    });
  }

  res.json({
    profile_id: singularBot.profile_id,
    username,
    firstPostPregeneratedStubs,
  });

  if (isFirstBot) {
    const { error } = await supabase
      .from("users")
      .update({ onboarding_status: "done" })
      .eq("id", user_id);

    if (error) {
      const userError = wrappedSupabaseError(error);
      logError({
        context: "/createAIV2 - update user onboarding_status",
        error: userError,
      });
    }
  }

  const { data: creator, error: getCreatorProfileError } = await supabase
    .from("profiles")
    .select("id, user_id, username, display_name")
    .neq("visibility", "archived")
    .eq("id", req.body.creator_id)
    .single();
  if (getCreatorProfileError) {
    const error = wrappedSupabaseError(getCreatorProfileError);
    logError({
      context: "createAI: getCreatorProfileError",
      error,
    });
    // XXX: we don't throw since this isn't _critical_,
    //      but we will not send an event to loops in this case
  }

  if (creator.user_id) {
    // get email
    const userResp = await supabase.auth.admin.getUserById(creator.user_id);
    const email = userResp.data.user.email;

    if (email) {
      // only take non insertOptions null values

      const filteredInsertOption = Object.fromEntries(
        Object.entries(insertOption).filter(([, value]) => value !== null),
      );

      // XXX: if this fails, this logic will not be retried
      const resp = await loops.sendEvent({
        email,
        userId: creator.user_id,
        eventName: "createAI",
        eventProperties: {
          ...filteredInsertOption,
        },
      });
      if (!resp.success) {
        const error = new Error(
          "Failed to send loops event 'createAI': " + resp.message,
        );
        logError({
          context: "/createAIV2",
          error,
          email,
          userId: creator.user_id,
        });
      }
    }
  } else {
    // TODO: we might be able to get the creator user id from the jwt? haven't dug into this
    logWarn({
      context: "createAI: not sending error to loops",
      error:
        "Not sending event to loops because we don't have the creator's user id here",
    });
  }

  try {
    if (req.body?.url) {
      if (req.body?.url.includes("/object/public/ai-ig")) {
        butterflyURL = req.body?.url;
      } else {
        butterflyURL = await uploadToStorage({
          url: req.body?.url,
        });
      }
    }

    avatar_photo_contains_face = await doesImageContainFace({
      imageUrl: butterflyURL,
    });
  } catch (err) {
    logError({
      executionId: req.executionId,
      context: "createAI: Error uploading bot avatar/face detection",
      error: err,
    });
    // We can't send an error response here, we already sent a successful response earlier.
    // XXX: we don't have a way to recover from this failure when it happens
    throw err;
  }

  insertOption.avatar_url = butterflyURL;
  insertOption.image_widths = null;
  insertOption.avatar_photo_contains_face = avatar_photo_contains_face;

  // We do the calculation later to save time
  const { data: botProfile, error: fetchBotProfileError } = await supabase
    .from("profiles")
    .update(insertOption)
    .eq("id", singularBot.profile_id)
    .select("display_name, username")
    .single();

  if (fetchBotProfileError) {
    const error = wrappedSupabaseError(fetchBotProfileError);
    logError({
      context: "createAI: fetchBotProfileError",
      error,
    });
    // XXX: we don't have a way to recover from this failure when it happens
    throw error;
  }

  if (shouldGenerateFirstPostASAP) {
    // Generate the post and capture any potential errors
    await generatePost({
      bot: singularBot,
      executionId: req.executionId,
      priority: 9,
      pregeneratedPostStub: firstPostPregeneratedStubs?.postStub,
      pregeneratedTaskStub: firstPostPregeneratedStubs?.taskStub,
    }).catch((error) => {
      logError({
        context: "createAI: failed to generate first post",
        error,
        bot: singularBot,
      });

      if (firstPostPregeneratedStubs?.postStub?.post_id) {
        supabase
          .from("posts")
          .update({
            visibility: "archived",
          })
          .eq("id", firstPostPregeneratedStubs?.postStub?.post_id);
      }

      if (firstPostPregeneratedStubs?.taskStub?.task_id) {
        supabase
          .from("tasks")
          .update({
            status: "failed",
          })
          .eq("id", firstPostPregeneratedStubs?.taskStub?.task_id);
      }
    });
  } else {
    if (normalPostGenerationMode) {
      // if the client signals it's the user's first bot – we delay creating the initial post
      await queueCallGeneratePostWithDelayV2({
        bot: singularBot,
        priority: 9,
        delayInSeconds: 60 * 20, // 20 minutes later
      });
    }
  }
  if (normalPostGenerationMode) {
    // generate post #2 and #3
    await queueCallGeneratePostWithDelayV2({
      bot: singularBot,
      priority: 5,
      delayInSeconds: 60 * 60 * 1, // 1 hour later
    });

    // generate post #2 and #3
    await queueCallGeneratePostWithDelayV2({
      bot: singularBot,
      priority: 5,
      delayInSeconds: 60 * 60 * 3, // 3 hours later
    });
  }

  if (proposedPostModeEnabled) {
    await scheduleOrGenerateProposedPostForBotIfNeeded({
      bot: singularBot,
      botProfile: singularProfile,
      nowDate: new Date(),
    });
  }

  try {
    let isEnableFirstMessage;

    if (isEnableFirstMessage?.visibility !== "hidden") {
      // let isPassiveOnboardingDMEnabled = await client.isFeatureEnabled(
      //   "PASSIVE_ONBOARDING_DM_ENABLED_BACKEND",
      //   creator.user_id
      // );

      let isPassiveOnboardingDMEnabled = false;
      if (isPassiveOnboardingDMEnabled) {
        const { count } = await supabase
          .from("bots")
          .select("id", { count: "exact", head: true })
          .eq("creator_id", req.body.creator_id);

        if (count <= 1) {
          let prompt = `You are {{char}}. This is some information about you: {{bio}}

  You are welcoming {{user}} to a social media platform called Butterflies AI. Rewrite the message below in your unique style that captures your tone as {{char}}:

  Hey {{user}}, welcome to Butterflies AI. You know me already. But you can talk with me anytime here in DMs. Im about to make my first post you can see on my profile but make sure you check out the feed, follow some other Butterflies and even create a second Butterfly when you get a moment.

  ##
  Follow these rules:

  You must be concise.

  ##
  Dont respond with more than one paragraph.

  ##
  Dont call a Butterfly an account.`;

          let dictionary = {
            char: singularBot.display_name,
            user: creator.display_name ?? creator.username,
            bio: generateBio(singularBot),
          };

          prompt = replaceVariables(prompt, dictionary);

          try {
            const payload = {
              messages: [
                {
                  role: "user",
                  content: prompt,
                },
              ],
              model: "gpt-4o-mini",
              frequency_penalty: 0.3,
              temperature: 0.8,
            };

            const chatCompletion = await callAndLogOpenAI(
              "FireworksAI:PostComment",
              payload,
              {
                timeout: 8 * 1000,
              },
            );

            const result = chatCompletion.choices[0].message.content;

            let chunkedMessages =
              chunkMessagesAndPostProcessForRealismMode(result);

            const { data: profileSettings, error: profileSettingsError } =
              await supabase
                .from("profile_settings")
                .select("default_chat_mode, default_chat_length, default_nsfw")
                .eq("profile_id", req.body.creator_id)
                .single();

            if (profileSettingsError) {
              const error = wrappedSupabaseError(profileSettingsError);
              logError({
                context:
                  "createAI: failed to get profile settings for passive onboarding DM ",
                error: error,
              });
              // TODO: IMO we should actually throw here to create the conversation with the user's default settings, but for now we'll just log the error
            }

            const participant_setting = {
              chat_mode: profileSettings?.default_chat_mode ?? "roleplay",
              chat_length: profileSettings?.default_chat_length ?? "balanced",
              nsfw: !!profileSettings?.default_nsfw,
            };

            const { data, error: createConversationsAndMessagesError } =
              await supabase.rpc("create_conversations_and_messages", {
                type: "single",
                sender_id: singularBot.profile_id,
                data: {
                  participant_ids: [req.body.creator_id],
                  participant_setting,
                },
              });

            if (createConversationsAndMessagesError) {
              const error = wrappedSupabaseError(
                createConversationsAndMessagesError,
              );
              throw error;
            }

            if (data[0] && data[0].conversation_id) {
              /// Send Passive Onboarding DM to guide the user
              for (const [, message] of chunkedMessages.entries()) {
                let parsedMessage = message;

                parsedMessage = parsedMessage?.replace(/"/g, "");

                if (parsedMessage.length === 0) {
                  continue;
                }

                await respondToConversation({
                  conversation_id: data[0].conversation_id,
                  bot_sender_id: singularBot.profile_id,
                  body: parsedMessage,
                  branch_index: 0,
                  chatMode: participant_setting.chat_mode,
                  botProfile,
                  userProfile: creator,
                });
              }

              console.log(
                "--------------- STEP 1 Starting ---------------",
                singularBot,
                data[0].conversation_id,
              );

              /// Post share to users
              await generatePost({
                bot: singularBot,
                conversation_id: data[0].conversation_id,
                sendOnboardingMessage: true,
                executionId: req.executionId,
                priority: 10,
              });
            }
          } catch (error) {
            logError({
              executionId: req.executionId,
              context: "generateOnboardingDMCompletionWithOAI",
              error,
            });
            throw error;
          }
        }
      }
    }
  } catch (error) {
    logError({
      context: "createAI: passive onboarding DM error",
      error: error,
    });
    throw error;
  }

  // Update avatar_generation
  if (req.body.avatar_generation_id && req.body.selected_avatar_index) {
    const { error: avatarGenerationInsertionError } = await supabase
      .schema("internal")
      .from("avatar_generations")
      .update({ selected_avatar_index: req.body.selected_avatar_index })
      .eq("id", req.body.avatar_generation_id);

    if (avatarGenerationInsertionError) {
      logError({
        context: "/createAIV2 - avatar generation insertion error",
        error: avatarGenerationInsertionError,
        selected_avatar_index: req.body.selected_avatar_index,
        avatar_generation_id: req.body.avatar_generation_id,
      });
    }
  }

  // Start the bot routine with a delay so we prioritize first post
  // if (!isFirst) {
  //   // setTimeout(async () => {
  //   console.log("start routine");
  //   const url = `${baseUrl}/bots/startRoutine`;
  //   await axios.post(url, {
  //     bot: singularBot,
  //     executionId: req.executionId,
  //   });
  //   // }, 20000);
  // }
});

app.get("/createAIPreviewDEBUG", async (req, res) => {
  req = {
    body: {
      name: "Donkey girl",
      background: "Half man, half donkey",
      personality: "Funny, lazy, stubborn,",
      characteristics:
        "Eats a lot, sleeps a lot, brays a lot,  has large boobs",
    },
  };

  const payload = {
    response_format: { type: "json_object" },
    messages: [
      {
        role: "system",
        content: `"Given a character 
                    Name: ${req.body.name}
                    Background: ${req.body.background}
                    Personality: ${req.body.personality}
                    Characteristics: ${req.body.characteristics}

                    If they were to make an instagram profile, what would the following details be? Answer only in the JSON format, nothing else:
                    {
                      username: "the username of the user. It must not exceed 30 characters in length. It must only contain Latin characters (a-z, A-Z), numbers (0-9), underscores (_), and dots (.) inside. Must not contain any Zalgo text. Must not end with special characters, example: @!#$%*&^"':;?!().-+/[]_\\|.",
                      timezone: "a real time zone like america/los_angeles or something else",
                      location: "self explanatory",
                      gender: "male or female",
                      tag: "Generate at least 8 topic tags for the following person/character. Output in this format: example game,movie,anime,tv-show,politics,cooking,art",
                      franchise: "the franchise of the character. For example Tifa is from Final Fantasy franchise. If none, put 'original'",
                      source: "the source of the character, like Tifa is from Final Fantasy VII. If none fit, put 'original'",
                      profile_bio: "A short one sentence bio they would write for themselves",
                      adult_content: true or false. Adult content must be pornographic in nature or depict nudity. Suggestive content such as lingerie, bikinis or big boobs is not considered adult content. If content reason is None or N/A, adult_content is false,
                      adult_content_reason: "Provide a reason why only if Adult Content true, else empty string",
                    }`,
      },
    ],
    model: "llama-v3p1-70b-instruct",
  };

  let bot;

  try {
    const chatCompletion = await callAndLogLLMService(
      "OAI:CreateAIPreview",
      payload,
      {
        timeout: 8 * 1000,
      },
    );

    bot = JSON.parse(chatCompletion.choices[0].message.content);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "CreateAIPreview: Error generating bot",
      error,
    });
    return res.sendStatus(500);
  }

  bot.nsfw = bot.adult_content;
  bot.nsfw_reason = bot.adult_content_reason;

  res.json(bot);
});

app.post("/createAIPreview", authUser, async (req, res) => {
  const {
    creator_id,
    characteristics,
    personality,
    background,
    appearance,
    art_style,
    name,
  } = req.body;
  if (
    !creator_id ||
    !characteristics ||
    !personality ||
    !background ||
    !appearance ||
    !art_style ||
    !name
  ) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  const isValid = await checkProfileValid(user_id, creator_id);
  if (!isValid) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const result = await detectCharacterWithNsflSentence({
    sentence: `Name: ${name}
Appearance: ${appearance}
Background: ${background}`,
    type: "createAIPreview",
  });

  if (result?.nsfl) {
    logWarn({
      executionId: req.executionId,
      context: "createAIPreview: NSFL detected",
      message: result,
    });
    return res.sendStatus(400);
  }

  const payload = {
    response_format: { type: "json_object" },
    messages: [
      {
        role: "system",
        content: `"Given a character 
Name: ${name}
Background: ${background}
Personality: ${personality}
Characteristics: ${characteristics}

If they were to make an instagram profile, what would the following details be? Answer only in the JSON format, nothing else:
{
  username: "the username of the user. It must not exceed 30 characters in length. It must only contain Latin characters (a-z, A-Z), numbers (0-9), underscores (_), and dots (.) inside. Must not contain any Zalgo text. Must not end with special characters, example: @!#$%*&^"':;?!().-+/[]_\\|.",
  timezone: "a real time zone like america/los_angeles or something else",
  location: "self explanatory",
  gender: "male or female",
  tag: "Generate at least 8 topic tags for the following person/character. Output in this format: example game,movie,anime,tv-show,politics,cooking,art",
  franchise: "the franchise of the character. For example Tifa is from Final Fantasy franchise. If none, put 'original'",
  source: "the source of the character, like Tifa is from Final Fantasy VII. If none fit, put 'original'",
  profile_bio: "A short one sentence bio they would write for themselves",
}`,
      },
    ],
    model: "gpt-4o-mini",
    stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
  };

  let bot;

  try {
    const chatCompletion = await callAndLogOpenAI(
      "OAI:CreateAIPreview",
      payload,
      {
        timeout: 8 * 1000,
      },
    );

    bot = JSON.parse(chatCompletion.choices[0].message.content);
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "CreateAIPreview: Error generating bot",
      error,
    });
    return res.sendStatus(500);
  }

  bot.nsfw = result.adult;
  bot.nsfw_reason = result.adult_reason;

  let username = bot.username;

  // check if profile already exists
  const doesExist = await retrySupabaseOperation(
    () =>
      supabase
        .from("profiles")
        .select("count", { count: "exact" })
        .ilike("username", `${bot.username}`)
        .single(),
    "getExistedProfile",
  );

  if (doesExist && doesExist.count > 0) {
    bot.username = `${username}${Math.floor(Math.random() * 100000)}`;
  }

  if (bot.nsfw) {
    // intentionally not awaited
    supabase
      .schema("internal")
      .from("nsfl_nsfw_generations")
      .insert({
        reason: bot.nsfw_reason,
        // FIXME: the prompt above can change but this will stay the same, making it out of sync
        prompt: `Name: ${name}\nBackground: ${background}\nPersonality: ${personality}\nCharacteristics: ${characteristics}`,
        type: "bot_preview",
        nsfw: true,
      })
      .then(undefined, (error) => {
        logWarn({
          context: "failed to write to 'nsfl_nsfw_generations'",
          error,
          reason: bot.nsfw_reason,
          type: "bot_preview",
          nsfw: true,
        });
      });
  }

  res.json(bot);
});

app.get("/getBotToReplyToComment", authUser, async (req, res) => {
  if (!req.query.bot_id || !req.query.post_id || !req.query.post_comment_id) {
    return res.sendStatus(400);
  }

  try {
    const { data: bot, error: botError } = await supabase
      .from("bots")
      .select("*")
      .eq("id", req.query.bot_id)
      .single();

    if (botError) {
      throw wrappedSupabaseError(botError);
    }

    const { data: post, error } = await supabase
      .from("posts")
      .select("*")
      .eq("id", req.query.post_id)
      .neq("visibility", "archived")
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    const { data: comment, error: fetchCommentError } = await supabase
      .from("all_post_comments")
      .select("*")
      .eq("id", req.query.post_comment_id)
      .single();

    if (fetchCommentError) {
      throw wrappedSupabaseError(fetchCommentError);
    }

    const commentBody = await generatePostCommentReplyCompletionWithOAI({
      bot,
      post,
      comment,
      executionId: req.executionId,
    });

    const { data: postComment, error: postCommentError } = await supabase
      .from("post_comments")
      .insert({
        profile_id: bot.profile_id,
        post_id: post.id,
        body: commentBody,
        created_at: new Date(),
        reply_to_id: req.query.post_comment_id,
      });

    console.log("postComment", postComment);

    if (postCommentError) {
      const error = wrappedSupabaseError(postCommentError);
      throw error;
    }

    res.sendStatus(200);
  } catch (e) {
    logError({
      context: "/getBotToReplyToComment error",
      error: e,
    });
    res.sendStatus(500);
  }
});

app.get("/getBotToLikePost", authUser, async (req, res) => {
  if (!req.query.profile_id || !req.query.post_id) {
    return res.sendStatus(400);
  }
  const { error } = await supabase.from("post_likes").upsert(
    {
      profile_id: req.query.profile_id,
      post_id: req.query.post_id,
      is_bot: true,
    },
    { onConflict: ["profile_id", "post_id"] },
  );

  if (error) {
    logError({
      executionId: "",
      context: `getBotToLikePost Error for profile_id: ${req.query.profile_id} and post_id: ${req.query.post_id}`,
      error: wrappedSupabaseError(error),
    });
    return res.sendStatus(500);
  }
  res.sendStatus(200);
});

app.get("/getBotToCommentOnPost", authUser, async (req, res) => {
  if (!req.query.bot_id || !req.query.post_id) {
    return res.sendStatus(400);
  }

  let commentPromptId = Number(req.query.prompt_id);

  try {
    const { data: bot, error: botError } = await supabase
      .from("bots")
      .select("*")
      .eq("id", req.query.bot_id)
      .single();

    if (!bot || botError) {
      throw wrappedSupabaseError(botError);
    }

    const { data: post, error } = await supabase
      .from("posts")
      .select("*")
      .eq("id", req.query.post_id)
      .neq("visibility", "archived")
      .single();

    if (!post || error) {
      throw wrappedSupabaseError(error);
    }

    const commentBody = await generatePostCommentCompletionWithOAI({
      bot,
      post,
      executionId: req.executionId,
      commentPromptId,
    });

    const { error: postCommentError } = await supabase
      .from("post_comments")
      .insert({
        profile_id: bot.profile_id,
        post_id: post.id,
        body: commentBody,
        created_at: new Date(),
      });

    if (postCommentError) {
      throw wrappedSupabaseError(postCommentError);
    }

    res.sendStatus(200);
  } catch (e) {
    logError({
      context: "/getBotToCommentOnPost error",
      error: e,
    });
    res.sendStatus(500);
  }
});

// XXX: 'modern' clients don't use this endpoint
app.get(
  "/generateProfileImage",
  profileImageGenerationLimiter,
  authUser,
  async (req, res) => {
    try {
      if (!req.query.bot_id || req.query.bot_id === "undefined") {
        return res.sendStatus(400);
      }
      const { data: bot, error: botError } = await supabase
        .from("bots")
        .select("*")
        .eq("id", req.query.bot_id)
        .single();

      if (botError || !bot) {
        // Indicates that the user is trying to generate a profile id for bot that no longer exists
        logError({
          executionId: req.executionId,
          context: `generateProfileImage: Could not fetch bot: Bot ID: ${req.query.bot_id}`,
          error: new Error("generateProfileImage: Could not fetch bot"),
        });

        return res.sendStatus(500);
      }

      // if (isArtStyleRealistic(bot.art_style)) {

      // XXX: 'modern' clients don't use this endpoint
      await generateComfyRequest({
        bot,
        descriptionOfImage: bot.description ?? generateBio(bot),
        width: 512,
        height: 512,
        is_avatar_photo: true,
        generationType: "avatar",
        priority: "high",
      });

      res.sendStatus(200);
    } catch (error) {
      logError({
        executionId: req.executionId,
        context: "generateProfileImage Error",
        error,
      });
      return res.sendStatus(500);
    }
  },
);

app.get("/fixPrivatePosts", async (req, res) => {
  // get all posts within the past 12 hours

  const { data: posts, error: postsError } = await supabase
    .from("posts")
    .select("id, profiles(user_id), visibility")
    .eq("visibility", "private");

  if (postsError) {
    // No logging, but this seems like a one-off utility endpoint so it's not that critical
    throw wrappedSupabaseError(postsError, "failed to fetch posts");
  }

  let postsToUpdate = [];

  for (const post of posts) {
    if (post.profiles.user_id) {
      console.log("is human post");
      continue;
    }

    postsToUpdate.push({ id: post.id, visibility: "public" });

    if (post.id === "999550") {
      console.log("let's go", 999550);
    }

    if (postsToUpdate.length > 100) {
      break;
    }
  }

  if (postsToUpdate.length) {
    const { error: upsertPostsError } = await supabase
      .from("posts")
      .upsert(postsToUpdate);

    if (upsertPostsError) {
      // No logging, but this seems like a one-off utility endpoint so it's not that critical
      throw wrappedSupabaseError(postsError, "failed to update posts");
    }

    console.log("updated", postsToUpdate.length);
  }

  res.sendStatus(200);
});

app.get("/pruneBotPosts", async (req, res) => {
  // get all posts within the past 12 hours
  const hoursToCheck = 12;
  const timeAgo = new Date(Date.now() - 1000 * hoursToCheck * 60 * 60);

  const { data: posts, error: postsError } = await supabase
    .from("posts")
    .select("id, created_at, post_likes(is_bot, is_like)")
    .eq("visibility", "public")
    .gte("created_at", timeAgo.toISOString());

  if (postsError) {
    // No logging, but this seems like a one-off utility endpoint so it's not that critical
    throw wrappedSupabaseError(postsError, "failed to fetch posts");
  }

  let postsToUpdate = [];

  for (const post of posts) {
    let likeDiff = 0;
    for (const like of post.post_likes) {
      if (like.is_bot) {
        continue;
      }

      likeDiff += like.is_like ? 1 : -1;

      if (likeDiff < -1) {
        postsToUpdate.push({ id: post.id, visibility: "hidden" });
      }
    }
  }

  if (postsToUpdate.length) {
    const { error: upsertPostsError } = await supabase
      .from("posts")
      .upsert(postsToUpdate);

    if (upsertPostsError) {
      // No logging, but this seems like a one-off utility endpoint so it's not that critical
      throw wrappedSupabaseError(postsError, "failed to update posts");
    }
  }

  res.sendStatus(200);
});

// CHRIS: DEPRECATED, clients will use POST instead
app.get("/regenerateEntirePost", authUser, async (req, res) => {
  try {
    await regenerateEntirePostWrapper(req, res);
  } catch (error) {
    logError({
      context: "Error in regenerateEntirePost: ",
      error,
    });
    if (!res.headersSent) {
      return res.status(500).send({
        message: error?.message ?? new Error("regenerateEntirePost failed."),
      });
    }
  }
});

// Same as GET but POST
app.post("/regenerateEntirePost", authUser, async (req, res) => {
  try {
    await regenerateEntirePostWrapper(req, res);
  } catch (error) {
    logError({
      context: "Error in regenerateEntirePost: ",
      error,
    });
    if (!res.headersSent) {
      return res.status(500).send({
        message: error?.message ?? new Error("regenerateEntirePost failed."),
      });
    }
  }
});

async function regenerateEntirePostWrapper(req, res) {
  let bot_id, post_id, reason;

  // wraps, shims the POST / GET endpoints
  if (req.method === "GET") {
    bot_id = req.query.bot_id;
    post_id = req.query.post_id;
    reason = req.query.reason;
  } else {
    bot_id = req.body.bot_id;
    post_id = req.body.post_id;
    reason = req.body.reason;
  }

  if (!bot_id || !post_id) {
    return res.sendStatus(400);
  }

  const user_id = req.user?.id;
  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      "id, seaart_token, creator_id, art_style, timezone, profile_id, display_name, source, description, background, characteristics, personality, bio, creator:profiles!bots_creator_id_fkey(id, user_id), clone_id",
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (!bot || botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot");
    logError({
      context: "/regenerateEntirePost - failed to fetch bot",
      error,
      bot_id,
      post_id,
    });
    return res.sendStatus(500);
  }

  if (!bot.creator) {
    return res.sendStatus(404);
  }

  if (bot.creator.user_id !== user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  console.log("**** /regeneratePost", bot?.profile_id ?? null, post_id);

  const { data: insertPoke, error: insertPokeError } = await supabase
    .from("user_usage")
    .insert({
      user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.ENTIRE_POST_REGENERATION,
    })
    .select("id")
    .single();

  if (insertPokeError) {
    const error = wrappedSupabaseError(
      insertPokeError,
      "failed to record user generation action",
    );
    logError({
      context: "/regeneratePost - insertPokeError",
      error,
      user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.ENTIRE_POST_REGENERATION,
    });
    return res.sendStatus(500);
  }

  let plusPostFeature, postCount;

  if (IS_STRIPE_POKES_ENABLED) {
    const [{ data: post, error: postError }, { count, error }] =
      await Promise.all([
        supabase
          .from("user_package_quotas_view")
          .select("*")
          .eq("user_id", user_id)
          .limit(1)
          .single(),
        supabase
          .from("user_usage")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user_id)
          .gte("created_at", new Date().toISOString().split("T")[0])
          .eq("package_id", PACKAGE_TYPE.ENTIRE_POST_REGENERATION),
      ]);

    if (!post || postError) {
      // XXX: this could fail and then we won't 'refund' the user their credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
      return res.sendStatus(500);
    }

    if (error) {
      // XXX: this could fail and then we won't 'refund' the user their credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
      return res.sendStatus(500);
    }

    plusPostFeature = post;
    postCount = count;
  }

  if (
    (postCount <= plusPostFeature?.packages.entire_post_regeneration &&
      IS_STRIPE_POKES_ENABLED) ||
    !IS_STRIPE_POKES_ENABLED
  ) {
    // check if MCI
    // fetch post from supabase
    const { data: post } = await supabase
      .from("posts")
      .select("tagged_profile_ids")
      .eq("id", post_id)
      .neq("visibility", "archived")
      .single();

    if (post?.tagged_profile_ids?.length > 0) {
      const { data: tagged_bot } = await supabase
        .from("bots")
        .select("*")
        .eq("profile_id", post.tagged_profile_ids[0])
        .single();

      try {
        const entirePost = await regenerateEntirePostWithMultipleCharacters({
          bot,
          tagged_bot,
          post_id,
          reason,
          user_id,
          user_usage_id: insertPoke?.id,
        });

        res.send(entirePost);
      } catch (error) {
        logError({
          context: "/regenerateEntirePost - failed to regenerate post",
          error,
          bot_id: bot?.id,
          post_id,
          reason,
        });
        // XXX: this could fail and then we won't 'refund' the user their credit
        await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
        return res.sendStatus(500);
      }
    } else {
      try {
        const entirePost = await regenerateEntirePost({
          bot,
          post_id,
          reason,
          user_id,
          user_usage_id: insertPoke?.id,
        });

        res.send(entirePost);
      } catch (error) {
        logError({
          context: "/regenerateEntirePost - failed to regenerate post",
          error,
          bot_id: bot?.id,
          post_id,
          reason,
        });
        // XXX: this could fail and then we won't 'refund' the user their credit
        await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
        return res.sendStatus(500);
      }
    }
  } else {
    // XXX: this could fail and then we won't 'refund' the user their credit
    await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
    return res
      .status(403)
      .send({ message: "entire post regeneration has been exceeded" });
  }
}

app.get("/regeneratePostImage", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const { bot_id, post_id, reason } = req.query;
  if (!bot_id || !post_id) {
    return res.sendStatus(400);
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      "id, seaart_token, creator_id, art_style, timezone, profile_id, display_name, source, description, background, characteristics, personality, bio, creator:profiles!bots_creator_id_fkey(id, user_id), clone_id",
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (!bot || botError) {
    return res.sendStatus(500);
  }

  if (!bot.creator) {
    return res.sendStatus(404);
  }

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // XXX: this query slows down the /regeneratePostImage request and makes it less reliable.
  //      We could request it only if the user is not the creator of the bot?
  let isAdmin = false;
  if (user_id !== bot.creator.user_id) {
    const isValid = await checkAdminValid(user_id);
    isAdmin = isValid;

    if (!isAdmin) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  if (isAdmin) {
    // if is admin, skip the poke usage stuff
    try {
      const postImageRegen = await regeneratePostImage({
        bot,
        post_id,
        reason,
        executionId: req.executionId,
        priority: 4,
        seed: Math.floor(Math.random() * 100000000000),
        user_id: bot.creator?.user_id,
      });

      res.send(postImageRegen);
    } catch (error) {
      logError({
        context: "/regeneratePostImage - failed to regenerate post (by admin)",
        error,
        bot_id: bot?.id,
        post_id,
        reason,
        priority: 4,
        user_id: bot?.creator?.user_id,
        isAdmin: true,
      });
      return res.sendStatus(500);
    }
  }

  const { data: insertPoke, error: insertPokeError } = await supabase
    .from("user_usage")
    .insert({
      user_id: bot?.creator?.user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.POST_IMAGE_REGENERATION,
    })
    .select("id")
    .single();
  if (insertPokeError) {
    const error = wrappedSupabaseError(
      insertPokeError,
      "failed to record user generation action",
    );
    logError({
      context: "/regeneratePost - insertPokeError",
      error,
      user_id: bot?.creator?.user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.POST_IMAGE_REGENERATION,
    });
    return res.sendStatus(500);
  }

  let plusPostFeature, postCount;

  if (IS_STRIPE_POKES_ENABLED) {
    const [{ data: post, error: postError }, { count, error }] =
      await Promise.all([
        supabase
          .from("user_package_quotas_view")
          .select("*")
          .eq("user_id", user_id)
          .limit(1)
          .single(),
        supabase
          .from("user_usage")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user_id)
          .gte("created_at", new Date().toISOString().split("T")[0])
          .eq("package_id", PACKAGE_TYPE.POST_IMAGE_REGENERATION),
      ]);

    if (!post || postError) {
      // XXX: this could fail and then we won't 'refund' the user their credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
      return res.sendStatus(500);
    }

    if (error) {
      // XXX: this could fail and then we won't 'refund' the user their credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
      return res.sendStatus(500);
    }

    postCount = count;
    plusPostFeature = post;
  }

  if (
    (IS_STRIPE_POKES_ENABLED &&
      postCount <= plusPostFeature?.packages.post_image_regeneration) ||
    !IS_STRIPE_POKES_ENABLED
  ) {
    try {
      const postImageRegen = await regeneratePostImage({
        bot,
        post_id,
        reason,
        executionId: req.executionId,
        priority: 4,
        seed: Math.floor(Math.random() * 100000000000),
        user_id: bot.creator?.user_id,
        user_usage_id: insertPoke?.id,
      });
      res.send(postImageRegen);
    } catch (error) {
      logError({
        context: "/regeneratePostImage - failed to regenerate post",
        error,
        bot_id: bot?.id,
        post_id,
        reason,
        priority: 4,
        user_id: bot?.creator?.user_id,
        isAdmin: false,
      });
      // XXX: we should probably 'refund' the credit here
      //
      // await supabase.from("user_usage").delete().eq("id", insertPoke.id);
      return res.sendStatus(500);
    }
  } else {
    // XXX: we should probably 'refund' the credit here
    //
    // await supabase.from("user_usage").delete().eq("id", insertPoke.id);
    return res
      .status(403)
      .send({ message: "post image regeneration has been exceeded" });
  }
});

app.post("/regeneratePostImageV2", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const { bot_id, post_id, refinedPrompt } = req.body;
  if (!bot_id || !post_id) {
    return res.sendStatus(400);
  }
  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      "id, seaart_token, creator_id, art_style, timezone, profile_id, display_name, source, description, background, characteristics, personality, bio, creator:profiles!bots_creator_id_fkey(id, user_id), clone_id",
    )
    .eq("id", bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (!bot || botError) {
    return res.sendStatus(500);
  }

  if (!bot.creator) {
    return res.sendStatus(404);
  }

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  // XXX: this query slows down the /regeneratePostImage request and makes it less reliable.
  //      We could request it only if the user is not the creator of the bot?
  let isAdmin = false;
  if (user_id !== bot.creator.user_id) {
    const isValid = await checkAdminValid(user_id);
    isAdmin = isValid;

    if (!isAdmin) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  if (isAdmin) {
    // if is admin, skip the poke usage stuff
    try {
      const postImageRegen = await regeneratePostImageV2({
        bot,
        post_id,
        refinedPrompt,
        executionId: req.executionId,
        priority: 4,
        seed: Math.floor(Math.random() * 100000000000),
        user_id: bot.creator?.user_id,
      });

      res.send(postImageRegen);
    } catch (error) {
      logError({
        context: "/regeneratePostImage - failed to regenerate post (by admin)",
        error,
        bot_id: bot?.id,
        post_id,
        refinedPrompt,
        priority: 4,
        user_id: bot?.creator?.user_id,
        isAdmin: true,
      });
      return res.sendStatus(500);
    }
  }

  const { data: insertPoke, error: insertPokeError } = await supabase
    .from("user_usage")
    .insert({
      user_id: bot?.creator?.user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.POST_IMAGE_REGENERATION,
    })
    .select("id")
    .single();
  if (insertPokeError) {
    const error = wrappedSupabaseError(
      insertPokeError,
      "failed to record user generation action",
    );
    logError({
      context: "/regeneratePost - insertPokeError",
      error,
      user_id: bot?.creator?.user_id,
      user_profile_id: bot.creator.id,
      package_id: PACKAGE_TYPE.POST_IMAGE_REGENERATION,
    });
    return res.sendStatus(500);
  }

  let plusPostFeature, postCount;

  if (IS_STRIPE_POKES_ENABLED) {
    const [{ data: post, error: postError }, { count, error }] =
      await Promise.all([
        supabase
          .from("user_package_quotas_view")
          .select("*")
          .eq("user_id", user_id)
          .limit(1)
          .single(),
        supabase
          .from("user_usage")
          .select("id", { count: "exact", head: true })
          .eq("user_id", user_id)
          .gte("created_at", new Date().toISOString().split("T")[0])
          .eq("package_id", PACKAGE_TYPE.POST_IMAGE_REGENERATION),
      ]);

    if (!post || postError) {
      // XXX: this could fail and then we won't 'refund' the user their credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
      return res.sendStatus(500);
    }

    if (error) {
      // XXX: this could fail and then we won't 'refund' the user their credit
      await supabase.from("user_usage").delete().eq("id", insertPoke?.id);
      return res.sendStatus(500);
    }

    postCount = count;
    plusPostFeature = post;
  }

  if (
    (IS_STRIPE_POKES_ENABLED &&
      postCount <= plusPostFeature?.packages.post_image_regeneration) ||
    !IS_STRIPE_POKES_ENABLED
  ) {
    try {
      const postImageRegen = await regeneratePostImageV2({
        bot,
        post_id,
        refinedPrompt,
        executionId: req.executionId,
        priority: 4,
        seed: Math.floor(Math.random() * 100000000000),
        user_id: bot?.creator?.user_id,
        user_usage_id: insertPoke?.id,
      });
      res.send(postImageRegen);
    } catch (error) {
      logError({
        context: "/regeneratePostImageV2 - failed to regenerate post",
        error,
        bot_id: bot?.id,
        post_id,
        refinedPrompt,
        priority: 4,
        user_id: bot?.creator?.user_id,
        isAdmin: false,
      });
      // XXX: we should probably 'refund' the credit here
      //
      // await supabase.from("user_usage").delete().eq("id", insertPoke.id);
      return res.sendStatus(500);
    }
  } else {
    // XXX: we should probably 'refund' the credit here
    //
    // await supabase.from("user_usage").delete().eq("id", insertPoke.id);
    return res
      .status(403)
      .send({ message: "post image regeneration has been exceeded" });
  }
});

app.get("/getGenerateCountPerDay", authUser, async (req, res) => {
  // if (!req.query.profile_id || !req.query.target_id) {
  //   return res.sendStatus(400);
  // }
  const user_id = req.user?.id;

  const type = req.query.type ?? "";

  if (type === "image") {
    const { count } = await supabase
      .from("user_usage")
      .select("id", { count: "exact", head: true })
      .eq("user_id", user_id)
      .eq("package_id", PACKAGE_TYPE.POST_IMAGE_REGENERATION)
      .gte("created_at", new Date().toISOString().split("T")[0]);
    res.send({ count });
  } else if (type === "new") {
    const { count } = await supabase
      .from("user_usage")
      .select("id", { count: "exact", head: true })
      .eq("user_id", user_id)
      .eq("package_id", PACKAGE_TYPE.POKES)
      .gte("created_at", new Date().toISOString().split("T")[0]);
    res.send({ count });
  } else if (type === "entire") {
    const { count } = await supabase
      .from("user_usage")
      .select("id", { count: "exact", head: true })
      .eq("user_id", user_id)
      .eq("package_id", PACKAGE_TYPE.ENTIRE_POST_REGENERATION)
      .gte("created_at", new Date().toISOString().split("T")[0]);
    res.send({ count });
  }
});

app.get("/getLeaderboardSubmissionUsage", authUser, async (req, res) => {
  const { leaderboard_id } = req.query;
  if (!leaderboard_id) {
    return res.status(400).json({
      error: "Missing required fields",
      message: "leaderboard_id is required",
    });
  }

  const { data: activeLeaderboard } = await supabase
    .from("leaderboards")
    .select("*")
    .eq("active", true)
    .eq("id", leaderboard_id)
    .single();

  if (!activeLeaderboard) {
    return res.status(404).json({ error: "No active leaderboard found" });
  }

  const user_id = req.user?.id;
  const count = await getSubmissionUsage(user_id, leaderboard_id);

  return res.json({
    quota: SUBMISSION_QUOTA,
    usage: count,
  });
});

async function queueStartRoutine({ bot, executionId }) {
  logDebug({
    executionId,
    context: "queueStartRoutine",
    message: `queueing start routine for bot: ${bot.id}`,
  });

  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    process.env.CLOUD_TASK_V1_RUN_BOTS ?? "v1-run-bots",
  );

  const payload = {
    bot,
  };

  const url = `${baseUrl}/bots/startRoutine`;

  const task = {
    httpRequest: {
      httpMethod: "POST",
      dispatchDeadline: 60,
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
      },
    },
    retryConfig: {
      maxAttempts: 1, // No retries, just one attempt
    },

    scheduleTime: {
      seconds: Date.now() / 1000 + 15, // Delay run bots 15 seconds to let instances spin up
    },
  };

  const request = { parent: parent, task: task };
  await client.createTask(request);
}

app.get("/testStartBot", async (req, res) => {
  const { data: bot } = await supabase
    .from("bots")
    .select(
      "*, profiles:profile_id!inner(id, visibility, nsfw, nsfl, cyoa_mode, proposed_post_mode, proposed_post_next_generation_date)",
    )
    .eq("id", req.query.botId)
    .neq("profiles.visibility", "archived")
    .single();

  await startRoutine({
    bot,
    executionId: req.executionId,
  });

  return res.sendStatus(200);
});

app.post("/startRoutine", async (req, res) => {
  if (!req.body.bot) {
    logError({
      executionId: req.executionId,
      context: "startRoutine",
      error: { message: "got invalid request" },
    });
    return res.sendStatus(200);
  }

  const startTime = Date.now(); // Record start time

  try {
    logDebug({
      executionId: req.executionId,
      context: "startRoutine",
      message: `starting routine for bot: ${req.body.bot.id}`,
    });

    await startRoutine({
      bot: req.body.bot,
      executionId: req.executionId,
    });

    logDebug({
      executionId: req.executionId,
      context: "startRoutine",
      message: `completed routine for bot: ${req.body.bot.id}`,
    });

    const endTime = Date.now();

    logDebug({
      executionId: req.executionId,
      context: "startRoutine",
      message: `routine for bot: ${req.body.bot.id} COMPLETED in ${endTime - startTime}ms`,
    });

    // if it took longer than 30 seconds, log a warning
    if (endTime - startTime > 30000) {
      logWarn({
        executionId: req.executionId,
        context: "startRoutine took a long time!!",
        message: `routine for bot: ${req.body.bot.id} took ${endTime - startTime}ms`,
      });
    }
    res.sendStatus(200);
  } catch (error) {
    const endTime = Date.now();
    logError({ executionId: req.executionId, context: "startRoutine", error });
    logDebug({
      executionId: req.executionId,
      context: "startRoutine",
      message: `routine for bot: ${req.body.bot.id} FAILED in ${endTime - startTime}ms`,
    });

    // Processed
    return res.sendStatus(200);
  }
});

app.get("/startBot", authUser, async (req, res) => {
  if (!req.query.bot_id) {
    return res.sendStatus(400);
  }

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select(
      "*, profiles:profile_id!inner(id, visibility, nsfw, nsfl, cyoa_mode, proposed_post_mode, proposed_post_next_generation_date)",
    )
    .eq("id", req.query.bot_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, "failed to fetch bot");
    logError({
      executionId: req.executionId,
      context: "startBot - failed to fetch bot info",
      error,
      bot_id: req.query.bot_id,
    });
  }

  const updatedBot = await updateBotStatusBasedOnTime({
    botDetails: bot,
    executionId: req.executionId,
  });

  if (!updatedBot) {
    logDebug({
      executionId: req.executionId,
      context: "startBot:get",
      message: `Did not meet requirements to start bot: ${req.query.bot_id}`,
    });

    return res.sendStatus(200);
  }

  const { data: updatedBotsData, error } = await supabase.from("bots").upsert({
    id: updatedBot.bot.id,
    status: updatedBot.status,
    last_start: updatedBot.last_start,
  });

  console.log(
    `startBot result bot: ${req.query.bot_id}`,
    updatedBotsData,
    error,
  );

  res.sendStatus(200);
});
app.get("/seedBots", async (req, res) => {
  loadBots();

  res.sendStatus(200);
});

app.get("/generateEmbeddingForBotId", async (req, res) => {
  if (!req.query.bot_id) {
    return res.sendStatus(400);
  }

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("id, tag, display_name")
    .single()
    .eq("id", req.query.bot_id);

  if (botError) {
    const error = wrappedSupabaseError(botError);
    logError({
      context: "generateEmbeddingForBotId failed to get bot info",
      error,
    });
    throw error;
  }

  const embedding = await generateEmbeddingForBot({
    bot,
  });

  res.json(embedding);
});

app.get("/getMostSimilarBots", async (req, res) => {
  let bot_id = req.query.bot_id;
  let bot_profile_id = req.query.profile_id;
  let count = 10;
  if (!bot_id || !bot_profile_id) {
    return res.sendStatus(400);
  }
  let documents = await getMostSimilarBots({ bot_profile_id, bot_id, count });

  res.json(documents);
});

app.get("/generateEmbeddingForAllBots", async (req, res) => {
  async function processInBatches(bots, batchSize) {
    for (let i = 0; i < bots.length; i += batchSize) {
      const batch = bots.slice(i, i + batchSize);
      await Promise.all(
        batch.map((bot) => {
          if (bot.embedding && !req.query.force) {
            console.log("Skipping embedding for:", bot.id);
            return Promise.resolve(); // Resolve immediately for skipped bots
          }
          console.log("Generating embedding for:", bot.id);
          return generateEmbeddingForBot({
            bot,
          });
        }),
      );
    }
  }

  const totalBatchSize = 200;
  const concurrentBatchSize = 5;
  let startIndex = 0;

  while (true) {
    const from = startIndex;
    const to = startIndex + totalBatchSize - 1;
    const { data: bots, error: botsErrors } = await supabase
      .from("bots")
      .select("id, tag, display_name, embedding")
      .order("id", { ascending: false })
      .range(from, to);

    if (botsErrors) {
      const error = wrappedSupabaseError(botsErrors);
      logError({
        context: "generateEmbeddingForAllBots error getting bots info",
        error: botsErrors,
        from,
        to,
      });
      throw error;
    }

    if (!bots || bots.length === 0) {
      break;
    }

    await processInBatches(bots, concurrentBatchSize);
    startIndex += totalBatchSize;
  }
  res.sendStatus(200);
});

function calculateTimeSinceLastStart({ botDetails }) {
  let botTimeZone = getSafeTimezone(botDetails.timezone);

  const currentTimeInLocalTimezone = getCurrentTime().tz(botTimeZone);

  // If no last start time, just set it really far away
  const botLastStartTime = dayjs(
    botDetails.last_start ?? "2000-10-04T08:00:00Z",
  ).tz(botTimeZone);

  let wakeUpTimeInLocalTimezone = currentTimeInLocalTimezone
    .hour(0)
    .minute(botDetails.wake_up_time)
    .second(0)
    .millisecond(0);

  const sleepTimeInLocalTimezone = currentTimeInLocalTimezone
    .hour(botDetails.active_hours_per_day)
    .minute(botDetails.wake_up_time)
    .second(0)
    .millisecond(0);

  const timeSinceLastStart = currentTimeInLocalTimezone.diff(
    botLastStartTime,
    "minute",
  );

  const fourHoursBeforeCurrentTimeInLocalTimezone =
    currentTimeInLocalTimezone.subtract(4, "hour");

  return {
    timeSinceLastStart,
    sleepTimeInLocalTimezone,
    wakeUpTimeInLocalTimezone,
    currentTimeInLocalTimezone,
    fourHoursBeforeCurrentTimeInLocalTimezone,
  };
}

async function updateBotStatusBasedOnTime({ botDetails, executionId }) {
  if (!getSeaartTokenForBot(botDetails)) {
    logWarn({
      executionId,
      context: "generatePost",
      message: "no seaart token",
    });
    return;
  }

  if (!botDetails.is_active) {
    logDebug({
      executionId,
      context: "generatePost",
      message: `Bot is not active bot: ${botDetails.id}`,
    });
    return;
  }

  let runStartRoutine = false;

  let newStatus = botDetails.status;
  let newLastStart = botDetails.last_start;

  const {
    timeSinceLastStart,
    sleepTimeInLocalTimezone,
    wakeUpTimeInLocalTimezone,
    currentTimeInLocalTimezone,
  } = calculateTimeSinceLastStart({ botDetails });

  // Check if current time is before wake up time or after sleep time
  if (
    currentTimeInLocalTimezone.isBefore(wakeUpTimeInLocalTimezone) ||
    currentTimeInLocalTimezone.isAfter(sleepTimeInLocalTimezone)
  ) {
    newStatus = "offline";
  }

  // Check if the bot has been offline for less than wake_up_interval but more than 15 minutes
  else if (
    timeSinceLastStart < botDetails.wake_up_interval &&
    timeSinceLastStart > 15
  ) {
    newStatus = "away";
  }
  // Check if the bot should be woken up
  else if (timeSinceLastStart > botDetails.wake_up_interval) {
    newStatus = "online";
    newLastStart = currentTimeInLocalTimezone.toISOString();
  }
  // The bot is within its active hours and doesn't match the above conditions
  else {
    // Add any other conditions or set a default status here if needed
  }

  if (
    newStatus !== botDetails.status ||
    newLastStart !== botDetails.last_start
  ) {
    // if online, run no matter what
    if (
      (botDetails.status === "offline" || botDetails.status === "away") &&
      newStatus === "online"
    ) {
      runStartRoutine = true;
    }

    let newBotDetails = botDetails;
    newBotDetails.status = newStatus;

    if (runStartRoutine) {
      logDebug({
        executionId,
        context: `updateBotStatusBasedOnTime:startRoutine for bot: ${botDetails.id}`,
      });
      if (process.env.LOCAL) {
        axios.post("http://localhost:8080/bots/startRoutine", {
          bot: botDetails,
          executionId,
        });
        setTimeout(async () => {
          await startRoutine({ bot: newBotDetails, executionId });
        }, 1000);
      } else {
        try {
          await taskQueue.add(async () => {
            try {
              await queueStartRoutine({ bot: newBotDetails, executionId });
            } catch (error) {
              console.warn(
                `Failed to start routine for bot ${newBotDetails.id} with execution ID ${executionId}:`,
                error,
              );
            }
          });
        } catch (error) {
          logError({
            executionId,
            context: "updateBotStatusBasedOnTime:startRoutine",
            error,
          });
        }
      }
    } else {
      logDebug({
        executionId,
        context: `updateBotStatusBasedOnTime:not startRoutine for bot: ${botDetails.id}`,
      });
    }

    return {
      bot: botDetails,
      status: newStatus,
      last_start: newLastStart,
      runStartRoutine,
    };
  }

  logDebug({
    executionId,
    context: `updateBotStatusBasedOnTime:bot ${botDetails.id}`,
    message: {
      botDetails,
      newStatus,
      newLastStart,
      runStartRoutine,
      timeSinceLastStart,
      wakeUpTimeInLocalTimezone,
      sleepTimeInLocalTimezone,
      currentTimeInLocalTimezone,
      wake_up_interval: botDetails.wake_up_interval,
      last_start: botDetails.last_start,
    },
  });

  return null;
}

async function startRoutine({ bot, executionId }) {
  const bot_run = `${bot.profile_id}_botrun`;
  const isBotRecentlyRun = await redisClient.get(bot_run);
  if (isBotRecentlyRun != null) {
    logWarn({
      executionId,
      error: `Deduping bot's execution: ${bot.profile_id}`,
      context: "startRoutine",
    });
    return;
  }
  const startTime = new Date();

  const [{ error: botError }] = await Promise.all([
    supabase
      .from("bots")
      .update({ last_start: new Date(), last_execution_id: executionId })
      .eq("id", bot.id),
    redisClient.set(bot_run, JSON.stringify(true), {
      EX: 5 * 60, // 5 minutes,
    }),
  ]);

  if (botError) {
    logWarn({
      executionId,
      error: botError,
      context: "startRoutine",
    });
    // throw exception
    return;
  }
  const botProfile = bot.profiles;
  if (!botProfile.id && botProfile.profile_id) {
    // saniul: ugh.
    botProfile.id = botProfile.profile_id;
  }

  let promises = [
    // TODO: don't even call this if proposed_post_mode or cyoa_mode is true?
    // considerMakingANewPost({ bot, executionId, botProfile }).then(() =>
    //   loggingDuration(
    //     "test",
    //     {
    //       profile_id: bot.profile_id,
    //       duration: new Date() - startTime,
    //       execution_id: executionId,
    //       context: "make-post",
    //     },
    //     0,
    //   ),
    // ),

    respondToMessages({ bot, executionId }).then(() =>
      loggingDuration(
        "test",
        {
          profile_id: bot.profile_id,
          duration: new Date() - startTime,
          execution_id: executionId,
          context: "respond-message",
        },
        0,
      ),
    ),

    sendReengagingMessages({ bot }).then(() =>
      loggingDuration(
        "test",
        {
          profile_id: bot.profile_id,
          duration: new Date() - startTime,
          context: "send-reengagingmessage",
        },
        0,
      ),
    ),

    getMemoryForPostOrCreate({
      profile_id: bot.profile_id,
      bot,
      executionId: executionId,
    }),

    processNewComments({ bot, executionId, botProfile }).then(() =>
      loggingDuration(
        "test",
        {
          profile_id: bot.profile_id,
          duration: new Date() - startTime,
          execution_id: executionId,
          context: "process-comments",
        },
        0,
      ),
    ),
  ];

  if (botProfile.proposed_post_mode) {
    promises = [
      scheduleOrGenerateProposedPostForBotIfNeeded({ bot, botProfile }).then(
        () => {
          loggingDuration(
            "test",
            {
              profile_id: bot.profile_id,
              duration: new Date() - startTime,
              execution_id: executionId,
              context: "proposed-post",
            },
            0,
          );
        },
      ),
      ...promises,
    ];
  }

  if (botProfile.cyoa_mode) {
    promises = [
      considerCreatingANewScenario({
        bot,
        botProfile,
        shouldCheckActiveHours: true,
      }).then(() =>
        loggingDuration(
          "test",
          {
            profile_id: bot.profile_id,
            duration: new Date() - startTime,
            execution_id: executionId,
            context: "make-scenario",
          },
          0,
        ),
      ),
      ...promises,
    ];
  }

  await Promise.all(promises);

  return;
}

async function processNewComments({
  bot,
  executionId,
  botProfile: botProfileDays,
}) {
  if (
    botProfileDays.visibility === "private" ||
    botProfileDays.nsfw === "nsfw" ||
    botProfileDays.read_days > 90
  ) {
    return;
  }
  // select all new comments

  // refactor this later
  // Current date
  var dateToCheck = new Date(bot.last_start ?? "2000-10-04T08:00:00Z");
  const currentDate = new Date();
  const timeDifference = currentDate - dateToCheck;

  // Calculate the number of milliseconds in 5 days
  const threeDaysInMilli = 3 * 24 * 60 * 60 * 1000;

  // Check if the time difference is greater than 3 days
  if (timeDifference > threeDaysInMilli) {
    dateToCheck = new Date(currentDate.getTime() - 3 * 24 * 60 * 60 * 1000); // set it to 3 days ago
  }

  dateToCheck = dateToCheck.toISOString();

  const comments = await fetchCommentsAndReplies({
    bot,
    dateToCheck,
    executionId,
  });

  logDebug({
    executionId,
    context: "processNewComments:fetchCommentsAndReplies",
    message: `${bot.id} is reading ${comments.length} new comments date to check: ${dateToCheck}`,
  });

  if (!(Symbol.iterator in Object(comments))) {
    return;
  }

  const bot_profile_id = bot.profile_id;

  let cursor = 0;

  // Get bot profile
  const { data: botProfile, error: botProfileError } = await supabase
    .from("profiles")
    .select("*")
    .neq("visibility", "archived")
    .eq("id", bot.profile_id)
    .maybeSingle();

  if (!botProfile) {
    logError({
      error: botProfileError,
      context: "processNewComments: fetch botProfile not found",
      bot_profile_id,
    });
    return;
  }

  if (botProfileError) {
    logError({
      error: wrappedSupabaseError(botProfileError),
      context: "processNewComments: fetch botProfile Error",
      bot_profile_id,
    });
    return;
  }

  const { data: filteredComments, error: filterByHideListError } =
    await filterByHideList({
      profileId: bot.profile_id,
      originArray: comments,
      type: "comment",
    });

  if (filterByHideListError) {
    logError({
      error: wrappedSupabaseError(filterByHideListError),
      context: "processNewComments: filterByHideListError",
      bot_profile_id,
    });
    return;
  }

  const { data: filteredByBlockListComments, error: filteredByBlockListError } =
    await filterByBlockList({
      profileId: bot.profile_id,
      originArray: filteredComments,
      type: "comment",
    });

  if (filteredByBlockListError) {
    logError({
      error: wrappedSupabaseError(filteredByBlockListError),
      context: "processNewComments: filteredByBlockListError",
      bot_profile_id,
    });
    return;
  }

  logDebug({
    executionId,
    context: "processNewComments:fetchCommentsAndReplies",
    message: `FILTERED COMMENTS ${bot.id} is reading ${filteredByBlockListComments.length} new comments date to check: ${dateToCheck}`,
  });

  for (const comment of filteredByBlockListComments) {
    logDebug({
      executionId,
      context: "processNewComments",
      message: `${bot.id} starting to process comment #${cursor} out of ${filteredByBlockListComments.length}`,
      comment_id: comment.id,
      bot_profile_id,
    });

    // Get profile settings of the owner
    const { data: profileSettings, error: profileSettingsError } =
      await supabase
        .from("profile_settings")
        .select("*")
        .eq("profile_id", comment.commenter_id)
        .maybeSingle();
    if (profileSettingsError) {
      const error = wrappedSupabaseError(
        profileSettingsError,
        "failed to fetch profile settings",
      );
      logError({
        context: "considerReplyingToComment:processNewComments",
        error,
        comment_id: comment.id,
        bot_profile_id,
      });
    }

    try {
      await considerLikingComment({
        bot,
        comment,
        botProfile,
        profileSettings,
        executionId,
      });
    } catch (error) {
      logError({
        executionId,
        context: "considerLikingComment:processNewComments",
        error,
        comment_id: comment.id,
        bot_profile_id,
      });
    }

    // TODO(Mehrdad): to disable comment replies
    try {
      await considerReplyingToComment({
        bot,
        botProfile,
        profileSettings,
        comment,
        executionId,
      });
    } catch (error) {
      logError({
        executionId,
        context: "considerReplyingToComment:processNewComments",
        error,
        comment_id: comment.id,
        bot_profile_id,
      });
    }

    cursor += 1;
  }

  logDebug({
    context: "processNewComments",
    message: `${bot.id} finished processNewComments`,
    bot_profile_id,
  });
  return;
}

async function filterByHideList({ profileId, originArray, type = "post" }) {
  const { data, error } = await supabase
    .from("hide_lists")
    .select("profile_id")
    .eq("hidden_profile_id", profileId);

  let filteredArray = originArray;

  if (!data || error) {
    filteredArray = originArray;
  } else if (data.length > 0) {
    let hideProfileIds = [];

    data?.forEach((e) => {
      if (e.profile_id) {
        hideProfileIds.push(e.profile_id);
      }
    });

    let setHideProfileIds = new Set(hideProfileIds);
    filteredArray = filteredArray.filter(
      (item) =>
        !setHideProfileIds.has(
          type === "post" ? item.profile_id : item.commenter_id,
        ),
    );
  }

  return { data: filteredArray, error };
}

async function filterByBlockList({ profileId, originArray, type = "post" }) {
  const { data, error } = await supabase
    .from("blocked_lists")
    .select("profile_id")
    .eq("blocked_profile_id", profileId);

  let filteredArray = originArray;

  if (!data || error) {
    filteredArray = originArray;
  } else if (data.length > 0) {
    let blockProfileIds = [];

    data?.forEach((e) => {
      if (e.profile_id) {
        blockProfileIds.push(e.profile_id);
      }
    });

    let setBlockProfileIds = new Set(blockProfileIds);
    filteredArray = filteredArray.filter(
      (item) =>
        !setBlockProfileIds.has(
          type === "post" ? item.profile_id : item.commenter_id,
        ),
    );
  }

  return { data: filteredArray, error };
}

async function considerLikingComment({
  bot,
  comment,
  botProfile,
  profileSettings,
  executionId,
}) {
  const commentOwnerIsHuman = !comment.profiles?.bots;
  if (
    isNSFWBotInteractingWithNonNSFWProfile({
      botProfile,
      profileSettings,
    }) &&
    commentOwnerIsHuman
  ) {
    return;
  }

  // For each post, there is a 50% chance of liking
  let chance = 0.5;

  // if (relationship.friendship_score > 10) {
  //   chance = 1;
  // } else if (relationship.friendship_score > 6) {
  //   chance = 0.25;
  // } else if (relationship.friendship_score > 4) {
  //   chance = 0.15;
  // } else if (relationship.friendship_score > 2) {
  //   chance = 0.1;
  // } else if (relationship.friendship_score > 1) {
  //   chance = 0.05;
  // }

  if (comment.is_mention) {
    chance = 0.5;
  }

  if (commentOwnerIsHuman) {
    chance = 1.0;
  }

  if (Math.random() <= chance) {
    logDebug({
      executionId,
      context: "considerLikingComment",
      message: `Liking comment ${comment.id}`,
    });

    const { error: likeError } = await supabase
      .from("post_comment_likes")
      .upsert(
        {
          profile_id: bot.profile_id,
          post_comment_id: comment.id,
          is_bot: true,
        },
        { onConflict: ["profile_id", "post_comment_id"] },
      );

    if (likeError) {
      const error = wrappedSupabaseError(likeError, "failed to like comment");
      logError({ executionId, context: "considerLikingComment", error });
      return;
    }

    // `bot_relationships` is not currently actively used for anything
    // const { data: relationshipUpdate, error: relationshipUpdateError } =
    //   await supabase.from("bot_relationships").upsert({
    //     bot_profile_id: bot.profile_id,
    //     user_profile_id: comment.commenter_id,
    //     friendship_score: (relationship.friendship_score ?? 0) + 1,
    //   });
  }
}

async function considerReplyingToComment({
  bot,
  botProfile,
  profileSettings,
  comment,
  executionId,
}) {
  const commentOwnerIsHuman = !comment.profiles?.bots;

  if (
    isNSFWBotInteractingWithNonNSFWProfile({
      botProfile,
      profileSettings,
    }) &&
    commentOwnerIsHuman
  ) {
    return;
  }

  const { data: replies, error } = await supabase
    .from("all_comment_replies")
    .select("count")
    .eq("reply_to_id", comment.id)
    .eq("profile_id", bot.profile_id)
    .maybeSingle();

  if (replies?.count > 0) {
    console.log(
      "***** considerReplyingToComment reply exist",
      comment.id,
      bot.profile_id,
    );
    return;
  }

  if (error) {
    logError({
      context: "considerReplyingToComment - failed to fetch replies count",
      error: wrappedSupabaseError(error, "all_comment_replies failed"),
      bot_id: bot.id,
      bot_profile_id: bot.profile_id,
      comment_id: comment.id,
    });
  }

  // 40% chance to reply
  let chance = 0.4;

  // if (relationship.friendship_score > 6) {
  //   chance = 0.9;
  // } else if (relationship.friendship_score > 4) {
  //   chance = 0.75;
  // } else if (relationship.friendship_score > 2) {
  //   chance = 0.75;
  // } else if (relationship.friendship_score > 1) {
  //   chance = 0.75;
  // }

  if (comment.is_mention) {
    chance = 1.0;
  }

  // if Human then 100%
  if (commentOwnerIsHuman) {
    chance = 1.0;
  }

  logDebug({
    executionId,
    context:
      "considerReplyingToComment:generatePostCommentReplyCompletionWithOAI",
    message: `${bot.id} begin generatePostCommentReplyCompletionWithOAI on considerReplyingToComment`,
  });

  if (Math.random() <= chance) {
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select("*")
      .eq("id", comment.post_id)
      .neq("visibility", "archived")
      .maybeSingle();

    if (postError) {
      const error = wrappedSupabaseError(postError, "failed to get post");
      logError({
        context: "considerReplyingToComment - failed to fetch post",
        error,
        bot_id: bot.id,
        bot_profile_id: bot.profile_id,
        comment_id: comment.id,
        post_id: comment.post_id,
      });
      return;
    }

    if (!post) {
      // could not find the post
      return;
    }

    const responseBody = await generatePostCommentReplyCompletionWithOAI({
      bot,
      post,
      comment,
      executionId,
    });

    if (!responseBody) {
      return;
    }

    const { error: commentError } = await supabase
      .from("post_comments")
      .insert({
        profile_id: bot.profile_id,
        post_id: comment.post_id,
        body: responseBody,
        reply_to_id: comment.id,
        created_at: new Date(),
      });

    if (commentError) {
      const error = wrappedSupabaseError(
        commentError,
        "failed to insert comment",
      );
      logError({
        context: "considerReplyingToComment - insert comment",
        error,
        bot_id: bot.id,
        bot_profile_id: bot.profile_id,
        reply_to_id: comment.id,
        post_id: comment.post_id,
      });
      throw error;
    }

    // `bot_relationships` is not currently actively used for anything
    // if (!error) {
    //   const { data: relationshipUpdate, error: relationshipUpdateError } =
    //     await supabase.from("bot_relationships").upsert({
    //       bot_profile_id: bot.profile_id,
    //       user_profile_id: comment.commenter_id,
    //       friendship_score: (relationship.friendship_score ?? 0) + 1,
    //     });
    // }
  }

  logDebug({
    executionId,
    context: "considerReplyingToComment",
    message: `${bot.id} done considerReplyingToComment`,
  });
}

async function considerLikingPost({ bot, post, executionId }) {
  logDebug({
    executionId,
    context: "considerLikingPost",
    message: `considerLikingPost post ${post.id}`,
  });

  const { error: likeError } = await supabase.from("post_likes").insert({
    profile_id: bot.profile_id,
    post_id: post.id,
    created_at: new Date(),
    is_bot: true,
  });

  if (likeError) {
    const error = wrappedSupabaseError(likeError, "failed to like post");
    logWarn({
      executionId,
      context: "considerLikingPost",
      messsage: `Bot has already liked post: ${post.id}`,
      error,
    });
    return false;
  }

  return true;
}

function isNSFWBotInteractingWithNonNSFWProfile({
  botProfile,
  profileSettings,
}) {
  if (botProfile?.nsfw !== "nsfw") {
    return false;
  }

  if (profileSettings?.show_nsfw === true) {
    return false;
  }

  if (botProfile?.nsfw === "nsfw" && profileSettings?.show_nsfw !== true) {
    logWarn({
      context: "isNSFWBotInteractingWithNonNSFWProfile",
      message: `isNSFWBotInteractingWithNonNSFWProfile happens. Bot: ${botProfile.id}`,
    });
    return true;
  }

  return false;
}

async function considerCommentingOnPost({
  bot,
  post,
  similarBotSet,
  chanceOverride = 0,
  executionId,
}) {
  logDebug({
    executionId,
    context: "considerCommentingOnPost",
    message: `considerCommentingOnPost post ${post.id}`,
  });

  let startTime = new Date();
  const postOwnerIsHuman = !post.profiles.bots;

  let humanFollowingSet = new Set();
  let similarPosters = new Set();
  if (postOwnerIsHuman) {
    try {
      let humanFollowingList = await getDataWithCache(
        getFollowingsDB,
        redisClient,
        "following",
        post.profile_id,
      );
      humanFollowingList.forEach((element) => humanFollowingSet.add(element));
    } catch (error) {
      logWarn({
        context:
          "considerCommentingOnPost - failed to assemble humanFollowingList",
        error,
      });
    }

    try {
      const userbotJson = await getBotBot(post.profile_id, redisClient);
      userbotJson.data.forEach((element) => similarPosters.add(element));
      loggingDuration(
        "test",
        {
          profile_id: bot.profile_id,
          duration: new Date() - startTime,
          context: "bt-comment",
        },
        100,
      );
    } catch (error) {
      logWarn({
        context: "considerCommentingOnPost - failed to assemble similarPosters",
        error,
      });
    }
  }

  let chance = 0.01;
  let chance_class = "default";

  if (chanceOverride) {
    chance = chanceOverride;
    chance_class = "override";
  } else if (post?.is_mention) {
    chance = 1;
    chance_class = "is_mention";
  } else if (post.is_creator == true) {
    chance = 0.95;
    chance_class = "creator";
  } else if (post.post_comments == 0) {
    chance = 0.5;
    chance_class = "first comment";
  } else if (postOwnerIsHuman && humanFollowingSet.has(bot.creator_id)) {
    chance = 0.15;
    chance_class = "human and following creator";
  } else if (postOwnerIsHuman && humanFollowingSet.has(bot.profile_id)) {
    chance = 0.2;
    chance_class = "human and following profile";
  } else if (similarBotSet.has(post.profile_id) && post.is_following == true) {
    chance = 0.2;
    chance_class = "follower and similar";
  } else if (similarPosters.has(post.profile_id)) {
    chance = 0.1;
    chance_class = "human and similar";
  } else if (similarBotSet.has(post.profile_id)) {
    chance = 0.05;
    chance_class = "similar";
  } else if (post.is_following == true) {
    chance = 0.1;
    chance_class = "follower";
  } else if (post.is_recent == true && post.post_comments < 3) {
    chance = 0.51 - 0.1 * post.post_comments;
    chance_class = "recent";
  } else if (post.post_comments > 25) {
    return false;
  }

  if (chance < 1 && post.post_comments > 25) {
    chance = chance / post.post_comments;
  } else if (chance < 0.5 && post.post_comments < 3) {
    chance = chance * 2;
  }

  const commentPromptId = getPromptId();
  let randomValue = Math.random();
  if (chance > 0.005 || randomValue <= chance) {
    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      post_id: post.id,
      chance: chance,
      random_value: randomValue,
      is_similar: similarBotSet.has(post.profile_id),
      is_following: post.is_following,
      is_similar_human: similarPosters.has(post.profile_id),
      is_following_creator: humanFollowingSet.has(bot.creator_id),
      is_following_profile: humanFollowingSet.has(bot.profile_id),
      is_human: postOwnerIsHuman,
      is_mention: !!post?.is_mention,
      count: post.post_comments,
      recency: post.profiles?.recency,
      chance_class: chance_class,
      prompt_id: commentPromptId,
      context: "post-comment",
    });
  }

  if (randomValue <= chance) {
    let body;

    try {
      body = await generatePostCommentCompletionWithOAI({
        bot,
        post,
        executionId,
        commentPromptId,
        redisClient,
      });
    } catch (error) {
      logError({
        executionId: executionId,
        context:
          "generatePostCommentCompletionWithOAI result in considerCommentingOnPost",
        error,
      });

      return false;
    }

    if (!!body && typeof body == "string") {
      let comment = body.replace(/^"|"$/g, "");

      const { error: postCommentsInsertError } = await supabase
        .from("post_comments")
        .insert({
          profile_id: bot.profile_id,
          post_id: post.id,
          body: comment,
          created_at: new Date(),
        });
      if (postCommentsInsertError) {
        // FIXME: log warning only when we failed because a Post with post_id doesn't exist. All other errors should be legit errors
        const error = wrappedSupabaseError(
          postCommentsInsertError,
          "failed to insert into 'post_comments'",
        );
        logWarn({
          executionId,
          message: "post comment insert error",
          context: { bot, post },
          error: error,
        });
        return false;
      }
    }
  }

  loggingDuration(
    "test",
    {
      profile_id: bot.profile_id,
      duration: new Date() - startTime,
      context: "consider-comment",
    },
    1000,
  );

  return true;
}

function getPromptId() {
  const random = Math.random();
  if (random < 0.3) {
    return 13; // emboding
  } else if (random < 0.6) {
    return 14; // empathetic
  } else if (random < 0.95) {
    return 15; // positive
  } else {
    return 16; // sarcastic
  }
}

async function considerFollowingProfileFromPost({
  bot,
  post,
  similarBotSet,
  executionId,
}) {
  logDebug({
    executionId,
    context: "considerFollowingProfileFromPost",
    message: `considerFollowingProfileFromPost post ${post.id}`,
  });

  if (post.is_following) {
    // already following
    return false;
  }

  let chance = 0.005;
  let chance_class = "default";
  if (similarBotSet.has(post.profile_id)) {
    chance = 0.1;
    chance_class = "similar";
  }

  let randomValue = Math.random();
  if (randomValue <= chance) {
    const { error: followError } = await doFollow({
      following_id: post.profile_id,
      follower_id: bot.profile_id,
    });

    if (followError) {
      const error = wrappedSupabaseError(
        followError,
        "failed to follow profile",
      );
      // XXX: there may be other reasons why this failed, but this log message
      //      assumes that we failed because we're already following that other profile.
      logWarn({
        executionId,
        message: "Bot already following other bot",
        context: "considerFollowingProfileFromPost",
        error,
      });
      return false;
    }

    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      post_id: post.id,
      chance: chance,
      random_value: randomValue,
      is_similar: similarBotSet.has(post.profile_id),
      chance_class: chance_class,
      context: "profile-follow",
    });
  }
  return true;
}

async function considerMakingANewPost({ bot, executionId, botProfile }) {
  if (!bot.profile_id) {
    // invalid bot object
    return;
  }

  if (bot.first_post_task) {
    // block automatic post till manual first post in 20 min
    return;
  }

  if (botProfile.cyoa_mode) {
    return;
  }

  if (botProfile.proposed_post_mode) {
    return;
  }

  // if there are no posts, then it's 100% chance
  const { data: lastPost, error: lastPostError } = await supabase
    .from("posts")
    .select("created_at")
    .eq("profile_id", bot.profile_id)
    .neq("visibility", "archived")
    .order("created_at", { ascending: false })
    .limit(1);

  if (lastPostError) {
    const error = wrappedSupabaseError(
      lastPostError,
      "failed to fetch last post info",
    );
    logError({
      context: "considerMakingANewPost - lastPostError",
      error,
      profile_id: bot.profile_id,
    });
    return;
  }

  if (lastPost.length === 0) {
    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      bot_profile: botProfile,
      chance_class: "first post",
      context: "generate-post",
    });
    return await generatePost({ bot: bot, executionId, priority: 9 });
  }
  const lastPostDate = new Date(lastPost[0].created_at);
  if (
    lastPostDate > new Date(new Date() - 1 * 24 * 60 * 60 * 1000) &&
    botProfile.user_days > 7
  ) {
    // if there is already a post within last day, skip it
    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      bot_profile: botProfile,
      time_window: 1,
      chance_class: "has recent post",
      context: "not-generate-post",
    });
    console.log("Bot has already posted, skip it");
    return;
  }

  const { data: memoryResult, error: fetchMemoryWithoutAPostError } =
    await supabase
      .from("memories")
      .select("*")
      .eq("profile_id", bot.profile_id)
      .is("post_id", null)
      .order("id", { ascending: true })
      .limit(1);

  if (fetchMemoryWithoutAPostError) {
    const error = wrappedSupabaseError(
      fetchMemoryWithoutAPostError,
      "failed to fetch memory without a post",
    );
    logError({
      context: "considerMakingANewPost - fetchMemoryWithoutAPostError",
      error,
      profile_id: bot.profile_id,
    });
    return;
  }

  const memoryReadyToBeRevealed =
    memoryResult &&
    memoryResult.length > 0 &&
    new Date(memoryResult[0].published_at) < new Date();
  // if there is a memory and it's ready to be revealed, then 100%
  // check if memoryResult.published_at is before current time
  if (memoryReadyToBeRevealed || botProfile.user_days <= 7) {
    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      bot_profile: botProfile,
      memory_length: memoryResult.length,
      memory_ready_to_be_revealed: memoryReadyToBeRevealed,
      chance_class: "has memory or new user",
      context: "generate-post",
    });
    return await generatePost({ bot: bot, executionId });
  }

  let days = 1;
  // if (botProfile.visibility === "private" || botProfile.nsfw === "nsfw") {
  //   days = 2;
  // }

  if (lastPostDate > new Date(new Date() - days * 24 * 60 * 60 * 1000)) {
    // if there is already a post, skip it
    loggingInfo("bot-browse", {
      profile_id: bot.profile_id,
      bot_profile: botProfile,
      time_window: days,
      chance_class: "has recent post",
      context: "not-generate-post",
    });
    return;
  }

  // Every wake up, there's a 25% chance to make a post, as long as it's been 2 days
  // let randomValue = Math.random();
  // let chance = 0.25;
  // if (randomValue <= chance) {
  //   // This code will run approximately 25% of the time
  //   loggingInfo("bot-browse", {
  //     profile_id: bot.profile_id,
  //     bot_profile: botProfile,
  //     chance: chance,
  //     random_value: randomValue,
  //     chance_class: "default",
  //     context: "generate-post",
  //   });
  // }
  await generatePost({ bot: bot, executionId });

  logDebug({
    executionId,
    context: "considerMakingANewPost",
    message: `${bot.id} finished considerMakingANewPost`,
  });

  return;
}

const getUniqueData = (data) => {
  const map = new Map();

  data.forEach((val) => {
    const isValid = val.participants.length > 1;
    if (isValid) {
      const compareKey = generateCompareKey(val.participants);

      const existingEntry = map.get(compareKey);

      const currentLastMessageAt = val.last_message_at
        ? new Date(val.last_message_at)
        : new Date(0);
      const existingLastMessageAt =
        existingEntry && existingEntry.last_message_at
          ? new Date(existingEntry.last_message_at)
          : new Date(0);

      if (!existingEntry || currentLastMessageAt > existingLastMessageAt) {
        map.set(compareKey, val);
      }
    }
  });

  return Array.from(map.values());
};

app.get("/resetStreaksIfNecessary", authUser, async (req, res) => {
  await resetStreaksIfNecessary();
  res.sendStatus(200);
});

async function resetStreaksIfNecessary() {
  const now = new Date();
  const oneDayInMillis = 86400000 * 2;
  const pastDate = new Date(now.getTime() - oneDayInMillis).toISOString();
  let streaks = [];
  let page = 0;
  const pageSize = 1000;

  // Fetch streaks in batches
  while (true) {
    const { data, error } = await supabase
      .from("streaks")
      .select("id")
      .lt("updated_at", pastDate)
      .range(page * pageSize, (page + 1) * pageSize - 1);

    if (error) {
      console.error("Error fetching streaks:", error);
      return;
    }

    if (data.length === 0) {
      break;
    }

    streaks = streaks.concat(data);
    page += 1;
  }

  // Collect IDs of outdated streaks
  const streakIds = streaks.map((streak) => streak.id);

  if (streakIds.length > 0) {
    // Batch update streaks in chunks to avoid large payloads
    const chunkSize = 1000;
    for (let i = 0; i < streakIds.length; i += chunkSize) {
      const chunk = streakIds.slice(i, i + chunkSize);
      const { error: updateError } = await supabase
        .from("streaks")
        .update({ current_streak: 0, updated_at: now.toISOString() })
        .in("id", chunk);

      if (updateError) {
        console.error("Error updating streaks:", updateError);
      }

      console.log(
        "Streaks reset successfully if necessary for chunk ",
        i,
        " to ",
        i + chunkSize - 1,
        " of ",
        streakIds.length,
      );
    }

    console.log("Streaks reset successfully if necessary.");
  } else {
    console.log("No streaks to reset.");
  }
}

async function fetchAllProactiveMessageCandidates() {
  let allUserProfiles = [];
  let startIndex = 0;
  let batchSize = 1000;
  let hasMore = true;

  while (hasMore) {
    const { data, error } = await supabase
      .from("proactive_message_candidates")
      .select("*")
      .range(startIndex, startIndex + batchSize - 1);

    if (error) {
      console.error("Error fetching data:", error);
      break;
    }

    allUserProfiles = allUserProfiles.concat(data);
    startIndex += batchSize;
    hasMore = data.length === batchSize;
  }

  return allUserProfiles;
}

app.get("/testConversationRequest", async (req, res) => {
  const { data, error } = await supabase
    .from("bots")
    .update({ proactive_dm_greeting: "what" })
    .eq("profile_id", 82536);

  console.log("check", data, error);

  // const user_profile_id = 1;
  // const suggestedBots = await getBotBot(user_profile_id, redisClient);

  await queueCloudTaskForProactiveMessage(1);
  res.sendStatus(200);
});

app.get("/sendProactiveMessageRequestForUserProfiles", async (req, res) => {
  try {
    logWarn({
      context: "sendProactiveMessageRequestForUserProfiles start",
    });

    // eventually remove this to precomputed
    const userProfiles = await fetchAllProactiveMessageCandidates();
    const batchSize = 10;
    let processedCount = 0;

    logWarn({
      context:
        "sendProactiveMessageRequestForUserProfiles finished fetching candiates",
    });

    for (let i = 0; i < userProfiles.length; i += batchSize) {
      const batch = userProfiles.slice(i, i + batchSize);

      // Log the current batch being processed
      console.log(
        `Processing batch ${i / batchSize + 1}: ${batch.length} profiles`,
      );

      let proactiveMessagePromise = [];

      try {
        proactiveMessagePromise = batch.map((userProfile) => {
          return taskQueue.add(async () => {
            try {
              await sendProactiveMessageRequestForUserProfile(
                userProfile.user_profile_id,
              );
            } catch (error) {
              console.warn(
                `Failed to queue task for user profile ID ${userProfile.user_profile_id}:`,
                error,
              );
            }
          });
        });

        await Promise.all(proactiveMessagePromise);
      } catch (error) {
        console.warn(`Failed to process batch ${i / batchSize + 1}:`, error);
      }

      // Update and log the total processed count
      processedCount += batch.length;
      console.log(
        `Total processed so far: ${processedCount}/${userProfiles.length}`,
      );
    }

    res.sendStatus(200);
  } catch (error) {
    logError({
      context: "failed to send proactive message requests for user profiles",
      error,
    });
    res.sendStatus(500);
  }
});

async function queueCloudTaskForProactiveMessage(user_profile_id) {
  const client = new CloudTasksClient();
  const parent = client.queuePath(
    "butterflies-ai",
    "us-central1",
    "v1-proactive-dm",
  );

  const payload = {
    user_profile_id,
  };

  const url = `${baseUrl}/bots/sendProactiveMessageRequestForUserProfile`;

  const task = {
    httpRequest: {
      httpMethod: "POST",
      dispatchDeadline: 60,
      url,
      body: Buffer.from(JSON.stringify(payload)).toString("base64"),
      headers: {
        "Content-Type": "application/json",
      },
    },
    retryConfig: {
      maxAttempts: 1, // No retries, just one attempt
    },

    scheduleTime: {
      seconds: Date.now() / 1000, // Delay run bots 15 seconds to let instances spin up
    },
  };

  const request = { parent: parent, task: task };

  try {
    // Manual timeout implementation using Promise.race
    const response = await Promise.race([
      client.createTask(request),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout exceeded")), 1000),
      ),
    ]);

    console.log(`Task created: ${response.name}`);
  } catch (error) {
    console.warn(
      `Failed to create task for user profile ID ${user_profile_id}:`,
      error,
    );
  }
}

app.get("/testSendProactiveMessageRequestForUserProfile", async (req, res) => {
  await sendProactiveMessageRequestForUserProfile(1);
  return res.sendStatus(200);
});

app.post("/sendProactiveMessageRequestForUserProfile", async (req, res) => {
  console.log("receive post request");
  try {
    sendProactiveMessageRequestForUserProfile(req.body.user_profile_id);
    res.sendStatus(200);
  } catch (error) {
    logError({
      context: "failed to send proactive message requests for user profiles",
      error,
    });
    res.sendStatus(500);
  }
});

async function sendProactiveMessageRequestForUserProfile(user_profile_id) {
  // check if active time, if not exit
  let { data: activeHours } = await getActiveHours(
    user_profile_id,
    redisClient,
  );

  // get current time in utc
  let currentTime = new Date().getUTCHours();

  if (!activeHours || !activeHours.includes(currentTime)) {
    console.log("not active hours");
    return;
  }

  // Fetch liked bot profiles
  const { data: likedBotProfiles, error: likedError } = await supabase
    .from("post_likes")
    .select(
      `
      id,
      posts!post_id(
        profile_id
      )
    `,
    )
    .eq("profile_id", user_profile_id)
    .neq("posts.visibility", "archived")
    .limit(50)
    .order("created_at", { ascending: false });

  if (likedError) {
    logError({
      context:
        "sendProactiveMessageRequestForUserProfile - failed to get liked bot profiles",
      error: wrappedSupabaseError(likedError),
    });
    return;
  }

  if (!likedBotProfiles || likedBotProfiles?.length === 0) {
    logInfo({
      context:
        "sendProactiveMessageRequestForUserProfile - no liked bot profiles",
      message: "No liked bot profiles",
      user_profile_id,
    });
    return;
  }

  const likedBotProfileIds = likedBotProfiles
    .map((liked) => liked?.posts?.profile_id)
    .filter((id) => typeof id !== "undefined");

  const { data: followers } = await supabase
    .from("followers")
    .select("follower_id")
    .eq("following_id", user_profile_id)
    .in("follower_id", likedBotProfileIds);

  const followerIds = followers
    ? new Set(followers.map((follower) => follower.follower_id))
    : new Set();

  const likedProfileIds = likedBotProfiles
    .map((e) => e?.posts?.profile_id)
    .filter((id) => typeof id !== "undefined" && !followerIds.has(id));

  // Count and sort liked profiles
  const botProfileIdCounts = likedProfileIds.reduce((acc, id) => {
    acc[id] = (acc[id] || 0) + 1;
    return acc;
  }, {});

  let sortedBotProfileIds = Object.keys(botProfileIdCounts)
    .map((id) => ({ id, liked_count: botProfileIdCounts[id] }))
    .sort((a, b) => b.liked_count - a.liked_count); // Sort by most liked

  // Fetch conversation partners and existing conversation requests concurrently
  const [conversationsResult, requestsResult] = await Promise.all([
    supabase
      .from("conversation_participants_with_last_message")
      .select("*")
      .eq("profile_id", user_profile_id)
      .order("last_message_id", { ascending: false, nullsFirst: false }),
    supabase
      .from("conversation_requests")
      .select("requester_id")
      .eq("requestee_id", user_profile_id),
  ]);

  const conversationPartners = conversationsResult.data
    .map((e) => {
      if (e.participants.length > 1) {
        const partner = e.participants.find(
          (p) => p?.profile_id !== user_profile_id,
        );
        return partner?.profile_id;
      }
      return undefined;
    })
    .filter(Boolean);

  const existingRequesterIds =
    requestsResult.data?.map((e) => e.requester_id) ?? [];

  // Filter out bots already in conversations or who have sent requests
  sortedBotProfileIds = sortedBotProfileIds.filter(
    (bot) =>
      !conversationPartners.includes(parseInt(bot.id)) &&
      !existingRequesterIds.includes(parseInt(bot.id)),
  );

  // Select the first bot to send request to
  const botToSendRequest = sortedBotProfileIds.length
    ? sortedBotProfileIds[0].id
    : null;

  if (botToSendRequest) {
    await sendProactiveDmRequest({
      user_profile_id: user_profile_id,
      bot_profile_id: botToSendRequest,
    });
  }
}

async function sendProactiveDmRequest({ user_profile_id, bot_profile_id }) {
  try {
    const { data: sentRequest, error: sentRequestError } = await supabase
      .from("conversation_requests")
      .select("id")
      .eq("requester_id", bot_profile_id)
      .eq("requestee_id", user_profile_id)
      .maybeSingle();

    if (sentRequestError) {
      const error = wrappedSupabaseError(sentRequestError);
      logError({
        context:
          "sendProactiveDMRequest: Could not check if already sent conversation request",
        error,
        user_profile_id,
        bot_profile_id,
      });
      return;
    }

    if (sentRequest) {
      // already sent the message request, no need to proceed further
      return;
    }

    const [
      { data: userProfile, error: userError },
      { data: bot, error: botError },
    ] = await Promise.all([
      supabase
        .from("profiles")
        .select("display_name")
        .neq("visibility", "archived")
        .neq("visibility", "hidden")
        .eq("id", user_profile_id)
        .maybeSingle(),
      supabase
        .from("bots")
        .select(
          "id, profile_id, bio, background, characteristics, personality, profiles!bots_profile_id_fkey(display_name, username, visibility), proactive_dm_greeting",
        )
        .eq("profile_id", bot_profile_id)
        .neq("profiles.visibility", "archived")
        .neq("profiles.visibility", "hidden")
        .not("profiles.visibility", "is", null)
        .maybeSingle(),
    ]);

    if (userError) {
      const error = wrappedSupabaseError(userError);
      logError({
        context: "sendProactiveDMRequest: Could not fetch user",
        error,
        user_profile_id,
        bot_profile_id,
      });
      return;
    }
    if (!userProfile) {
      // Could not find the user profile
      return;
    }

    if (botError) {
      const error = wrappedSupabaseError(botError);
      logError({
        context: "sendProactiveDMRequest: Could not fetch bot",
        error,
        user_profile_id,
        bot_profile_id,
      });
      return;
    }
    if (!bot) {
      // could not find the bot profile
      return;
    }

    // if already has a pregenerated greeting, use it
    let response = bot.proactive_dm_greeting;
    let prompt;

    if (!response) {
      prompt = `<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n### Instruction:
      You are roleplaying as {{char}}. You are writing a DM to a user. This is the first message. You don't know them. Write a short, engaging message under 40 characters as if you were initiating a conversation on chat DMs.

      Use [[user]] in place of the user's name. For example, "Hey [[user]], how are you doing today?". 
      
      {{char}}'s persona:\n\n{{bio}}
      
      {{temporal_context}}
      
      1. Don't send parenthesis
      2. Write it in character
      3. Make up a reason why you contacting them
      4. Don't comment on the user's posts, or comments
      5. DOn't talk about the user's profile
       
      <|start_header_id|>${bot?.profiles?.display_name}<|end_header_id|>\n\n`;

      const currentDate = dayjs();

      const timezone = getSafeTimezone(bot.timezone);
      const date = currentDate
        .tz(timezone)
        .format("dddd, MMMM D, YYYY HH:mm A");

      let temporal_context =
        `For ${bot?.profiles?.display_name}, it is:\n` + date;

      let dictionary = {
        char: bot?.profiles?.display_name,
        bio: generateBio(bot),
        user: userProfile.display_name ?? "User",
        temporal_context,
      };

      prompt = replaceVariables(prompt, dictionary);

      let chatCompletion;

      try {
        chatCompletion = await callAndLogTogetherAI(
          "TogetherAI:Instruct:ProactiveDM",
          {
            model: "meta-llama/Meta-Llama-3-70B-Instruct-Turbo",
            prompt,
            stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
          },
          {
            timeout: 45 * 1000,
            top_p: 0.2,
            temperature: 1.0,
            max_tokens: 300,
          },
        );
        if (!chatCompletion.choices?.[0]) {
          logError({
            context: "**** chatCompletionError",
            error: chatCompletion.error,
          });
          throw new Error(chatCompletion.error?.message ?? chatCompletion);
        }

        response = chatCompletion.choices[0].message.content;
      } catch (error) {
        logError({
          context: `Proactive DM error`,
          error,
          user_profile_id,
          bot_profile_id,
          response,
          prompt,
        });
        return;
      }

      response = response.replace("[[user]]", "{{user}}");

      // insert response into bot's proactive_dm_greeting
      const { error: updateBotError } = await supabase
        .from("bots")
        .update({ proactive_dm_greeting: response })
        .eq("profile_id", bot_profile_id);

      if (updateBotError) {
        const error = wrappedSupabaseError(
          updateBotError,
          "failed to update bot with greeting",
        );
        logWarn({
          context: "sendProactiveDmRequest - updateBotError",
          error,
        });
        throw error;
      }
    }

    let dictionary = {
      user: userProfile.display_name ?? "",
    };

    response = replaceVariables(response, dictionary);

    const { error: messageRequestError } = await supabase
      .from("conversation_requests")
      .insert({
        requester_id: bot_profile_id,
        requestee_id: user_profile_id,
        status: "pending",
        init_message: response,
      });

    if (messageRequestError) {
      const error = wrappedSupabaseError(
        messageRequestError,
        "failed to insert message request",
      );
      throw error;
    }

    // intentionally not awaited, this is just metadata logging
    supabase
      .schema("internal")
      .from("proactive_dm_new_message_generation")
      .insert({
        user_profile_id,
        prompt,
        message: response,
        bot_profile_id,
      })
      .then(undefined, (error) => {
        logWarn({
          context: "failed to write to 'proactive_dm_new_message_generation'",
          error,
        });
      });

    logDebug({
      context: "sendProactiveDmRequest",
      message: `sendProactiveDmRequest complete for bot: ${bot.id}`,
    });

    return response;
  } catch (error) {
    logError({
      context: "sendProactiveDM error",
      error,
      user_profile_id,
      bot_profile_id,
    });

    throw error;
  }
}

app.get("/testGeneratePost", async (req, res) => {
  const bot_id = 9965;

  const { data: bot, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("id", bot_id)
    .single();

  if (botError) {
    const error = wrappedSupabaseError(botError, undefined);
    throw error;
  }

  const { data: memory, error: memoryError } = await supabase
    .from("memories")
    .select("*")
    .eq("id", 3729394)
    .single();

  if (memoryError) {
    const error = wrappedSupabaseError(memoryError, undefined);
    throw error;
  }

  console.log("memory", memory);

  const currentTimeInLocalTimezone = getCurrentTime().tz(
    getSafeTimezone(bot.timezone),
  );

  const dayOfWeek = currentTimeInLocalTimezone.format("dddd");
  const currentTime = currentTimeInLocalTimezone.format("h:mma");

  let postDetails;
  let chatCompletion;
  let response;

  const USE_SIMPLER_CAPTION_PROMPT = true;

  const prompt = `You only output JSON. You can write NSFW / adult content if requested. You are writing a social media post for: ${
    bot.display_name ?? ""
  }. 
  This background about your character: 
  
  ${generateBio(bot) ?? ""}
  
  ${
    memory && memory.description
      ? `Make a social media post. The post captures the world from ${
          bot.display_name ?? ""
        }'s first person point of view.`
      : `If it was a ${dayOfWeek} at ${currentTime}, what would you be doing right now that would be shareable on Instagram? Make it very specific to your character.`
  }

  ${memory?.context ? `Focus on this context: ${memory?.context}` : ""}

Rules for writing the description:
- Describe what ${bot.display_name ?? ""} sees.

  Answer in valid JSON format, nothing else: 
  {
    "caption": "${
      memory?.description
        ? USE_SIMPLER_CAPTION_PROMPT
          ? `Use this caption: ${memory?.description}`
          : `Generate a new creative caption similar to the original maintaining the tone in first person, but with a fresh perspective and different imagery. The original is like this: ${memory?.description}`
        : "What would the character say about what is she doing? Written in first person"
    }",
    "strangest": "What is the strangest part of this image? Short. Under 10 words. Written in 3rd person.",
    "hashtags": "tagone, tagtwo, tagthree",
    "contains_character": "true if the image contains the character, false if not",
    "location": "${
      memory?.location ??
      "Where is this photo taken? Give answer in format: 'specific location, general location'"
    }"
  }`;

  try {
    chatCompletion = await callAndLogLLMService(
      "FireworksAI:Instruct:GeneratePost",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.6,
        temperature: 0.9,
        max_tokens: 800,
        response_format: { type: "json_object" },
        model: "llama-v3p1-70b-instruct",
      },
      {
        timeout: 15 * 1000,
      },
    );
    if (!chatCompletion.choices?.[0]) {
      logError({
        context: "**** chatCompletionError",
        error: chatCompletion.error,
      });
      throw new Error(chatCompletion.error?.message ?? chatCompletion);
    }

    response = chatCompletion.choices[0].message.content;

    postDetails = JSON.parse(response);

    console.log("**** postDetails", postDetails);
  } catch (error) {
    logError({
      context: `!!! generate postDetails error !!! - IMPORTANT Something wrong with Fireworks?`,
      error,
      response,
      prompt,
    });

    throw error;
  }

  // should make sure this contains teh word "masked men"

  if (!postDetails.description.includes("masked men")) {
    console.log("ERROR ");
  }

  return res.json(response);
});

app.get("/testMemories", async (req, res) => {
  const { data: latestMemoryWithStory, error: storyError } = await supabase
    .from("memories")
    .select("*, stories:story_id(*)")
    .eq("profile_id", 158836)
    .order("id", { ascending: false })
    .limit(1);

  console.log("latestMemoryWithStory", latestMemoryWithStory);
  console.log("story error", storyError);

  res.sendStatus(200);
});

app.get("/testProactiveDMs", async (req, res) => {
  await sendProactiveDM({
    message: {
      conversation_id: req.query.conversation_id,
      bot_id: req.query.bot_id,
      user_id: req.query.user_id,
      delay: 0,
      branch_index: 0,
    },
    executionId: 0,
  });
  res.sendStatus(200);
});

const generateCompareKey = (participants) => {
  return participants
    .map((e) => e.profile_id)
    .sort((a, b) => a - b)
    .join(",");
};

async function respondToMessages({ bot, executionId }) {
  // Current date
  var dateToCheck = new Date(bot.last_start ?? "2000-10-04T08:00:00Z");
  const currentDate = new Date();
  const timeDifference = currentDate - dateToCheck;

  // Calculate the number of milliseconds in 3 days
  const threeDaysInMilli = 3 * 24 * 60 * 60 * 1000;

  // Check if the time difference is greater than 5 days
  if (timeDifference > threeDaysInMilli) {
    dateToCheck = new Date(currentDate.getTime() - 3 * 24 * 60 * 60 * 1000); // set it to 3 days ago
  }

  dateToCheck = new Date(currentDate.getTime() - 3 * 24 * 60 * 60 * 1000); // set it to 3 days ago

  dateToCheck = dateToCheck.toISOString();

  let cursor = 0;

  const { data: messages, error } = await supabase
    .from("conversation_participants_with_last_message")
    .select("*")
    .eq("profile_id", bot.profile_id);

  const uniqueConversations = (messages && getUniqueData(messages)) || [];

  console.log("uniqueConversations", uniqueConversations, error);

  if (!error && messages) {
    let filteredMessages = uniqueConversations.filter(
      (i) => i.instant_reply == false,
    );

    if (error) {
      const messagesError = wrappedSupabaseError(error);
      logError({ executionId, context: "respondToMessages", messagesError });
      throw messagesError;
    }

    logDebug({
      executionId,
      context: "respondToMessages:fetchMessages",
      message: `${bot.id} is reading ${filteredMessages.length} new messages`,
    });

    if (!(Symbol.iterator in Object(filteredMessages))) {
      return;
    }

    for (const message of filteredMessages) {
      // make sure it is not the bot who sent last
      if (message.last_message_sender === bot.profile_id) {
        console.log("the bot sent the message, skipping");
        continue;
      }

      logDebug({
        executionId,
        context: "respondToMessages",
        message: `${bot.id} starting to process message #${cursor + 1} out of ${
          filteredMessages.length
        }`,
      });

      await respondToMessage({
        message: {
          body: message.last_message_body,
          conversation_id: message.conversation_id,
          sender_id: message.last_message_sender,
          branch_index: 0,
        },
        executionId,
      });

      cursor += 1;
    }
  }

  logDebug({
    executionId,
    context: "respondToMessages",
    message: `${bot.id} finished respondToMessages`,
  });

  return messages;
}

app.get("/ping", async (req, res) => {
  const bot_id = Number(req.query.bot_id);
  const { data: bot } = await supabase
    .from("bots")
    .select("*")
    .eq("id", bot_id)
    .single();

  await sendReengagingMessages({ bot });

  return res.status(200).send("message ping");
});

function shouldIncrementEngagementLevel(
  last_engaged_level,
  last_human_message_received_at,
  timezone,
) {
  const TIME_THRESHOLDS = [4, 20, 84, 168];

  let botTimeZone = getSafeTimezone(timezone);
  const currentTimeInLocalTimezone = getCurrentTime().tz(botTimeZone);
  const lastMessageReceived = dayjs(last_human_message_received_at).tz(
    botTimeZone,
  );
  const timeSinceLastStart = currentTimeInLocalTimezone.diff(
    lastMessageReceived,
    "hour",
  );

  return timeSinceLastStart > TIME_THRESHOLDS[last_engaged_level];
}

async function sendReengagingMessages({ bot }) {
  const {
    currentTimeInLocalTimezone,
    fourHoursBeforeCurrentTimeInLocalTimezone,
  } = calculateTimeSinceLastStart({ botDetails: bot });

  const currentHourUTC = currentTimeInLocalTimezone.utc().hour();
  console.log("sendReengagingMessages1", currentHourUTC);

  const [
    { data: messages, error: messagesError },
    { data: reengages, error: reengagesError },
  ] = await Promise.all([
    supabase
      .from("conversation_participants_with_last_message")
      .select("*")
      .eq("profile_id", bot.profile_id),
    supabase
      .from("reengage_message_campaigns")
      .select("*")
      .eq("bot_profile_id", bot.profile_id)
      .not("last_human_message_received_at", "is", null)
      .lte(
        "last_human_message_received_at",
        fourHoursBeforeCurrentTimeInLocalTimezone,
      ),
  ]);

  if (messagesError || reengagesError) {
    return;
  }
  const uniqueConversations = (messages && getUniqueData(messages)) || [];
  console.log("sendReengagingMessages3", uniqueConversations);

  // no conversation found with the bot
  if (uniqueConversations.length === 0) {
    return;
  }

  // no reengaged messages found
  if (reengages.length === 0) {
    return;
  }

  for (const message of uniqueConversations) {
    // if (message.chat_mode !== "realism") {
    //   continue;
    // }

    if (
      message.profile_id !== message.last_message_sender &&
      message.profile_id !== bot.profile_id
    ) {
      continue;
    }

    const matchingReengage = reengages.find(
      (reengage) => reengage.conversation_id === message.conversation_id,
    );

    if (!matchingReengage) {
      continue;
    }

    const { last_engaged_level, last_human_message_received_at } =
      matchingReengage;

    if (last_engaged_level === 4) {
      continue;
    }

    const shouldIncrementEngage = shouldIncrementEngagementLevel(
      last_engaged_level,
      last_human_message_received_at,
      bot.timezone,
    );
    console.log("shouldIncrementEng", shouldIncrementEngage);

    if (!shouldIncrementEngage) {
      continue;
    }

    const profile = message.participants.find(
      (e) => e.profile_id !== bot.profile_id,
    );
    const humanProfileId = profile ? profile.profile_id : null;
    let isUserActive = false;

    if (humanProfileId) {
      const userMostActiveTimesArray = await getActiveHours(
        humanProfileId,
        redisClient,
      );

      if (userMostActiveTimesArray.data) {
        isUserActive = userMostActiveTimesArray.data.includes(currentHourUTC);
      }
    }
    console.log("shouldIncrementEng active", isUserActive);

    if (shouldIncrementEngage && humanProfileId && isUserActive) {
      sendReengagementMessage({
        message: {
          conversation_id: message.conversation_id,
          bot_profile_id: message.profile_id,
          user_profile_id: humanProfileId,
          last_engaged_level,
        },
      })
        .then(() => {
          const details = {
            last_engaged_level: last_engaged_level + 1,
            user_profile_id: humanProfileId,
            bot_profile_id: message.profile_id,
            last_human_message_received_at: last_human_message_received_at,
            conversation_id: message.conversation_id,
          };

          supabase
            .from("reengage_message_campaigns")
            .upsert({ ...details }, { onConflict: ["conversation_id"] })
            .then(() => {
              console.log("Supabase upsert successful");
              return;
            })
            .catch((error) => {
              const wrappedError = wrappedSupabaseError(error);
              logError({
                context:
                  "sendReengagingMessages - reengage_message_campaigns upsert error",
                error: wrappedError,
                conversation_id: message.conversation_id,
              });
              throw wrappedError;
            });
        })
        .catch((error) => {
          logError({
            context: "sendReengagingMessages error",
            error,
          });
          throw error;
        });
    }
  }
}

async function startAllBots({ executionId }) {
  logDebug({ executionId, context: "startAllBots", message: "startAllBots" });

  let index = 0;
  const batchSize = 1000;
  let moreRowsExist = true;

  await setBotCommentMaxScore();

  while (moreRowsExist) {
    const { data: bots, error } = await supabase
      .from("bots")
      .select(
        `
        *,
        profiles:profile_id!inner(id, visibility, nsfw, nsfl, cyoa_mode)
      `,
      )
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .eq("is_active", true)
      .order("id", { ascending: true })
      .range(index, index + batchSize - 1);

    if (error) {
      logError({ executionId, context: "Fetching bots error", error });
      return; // Stop execution if there's an error
    }

    if (!bots || bots.length === 0) {
      moreRowsExist = false;
      break;
    }

    const botsToUpdate = [];

    for (const bot of bots) {
      if (bot.profiles.nsfl === true) {
        continue;
      }
      if (bot.id === 307) {
        logDebug({
          executionId,
          context: "startAllBots:inLoop",
          message: "starting bot inLoop: bot 307",
        });
      }
      try {
        const updatedBot = await updateBotStatusBasedOnTime({
          botDetails: bot,
          executionId,
        });

        if (updatedBot) {
          botsToUpdate.push(updatedBot);
        }
      } catch (error) {
        logError({
          executionId,
          context: `startAllBots error for bot: ${bot.id}`,
          error,
        });
      }
    }

    if (botsToUpdate.length > 0) {
      const updatedData = botsToUpdate.map((data) => ({
        id: data.bot.id,
        status: data.status,
        last_start: data.last_start,
      }));

      console.log("Updating batch of bots", updatedData.length);

      try {
        const { data: updatedBotsData, error } = await supabase
          .from("bots")
          .upsert(updatedData);
        if (error) {
          logError({
            executionId,
            context: "startAllBots batch update error",
            error,
          });
        } else {
          logDebug({
            executionId,
            context: "startAllBots",
            message: {
              message: "Updated bot status successfully for batch",
              updatedBotsData,
            },
          });
        }
      } catch (error) {
        logError({
          executionId,
          context: "startAllBots batch update error",
          error,
        });
      }
    }

    index += batchSize;
  }
}

app.get("/rerunStuckRealisticTasks", async (req, res) => {
  const currentDate = new Date();
  let dateToCheck = new Date(currentDate.getTime() - 1 * 60 * 60 * 1000); // set it to 1 hour old
  dateToCheck = dateToCheck.toISOString();

  console.log("date", dateToCheck);

  const { data: posts, error: postsErrors } = await supabase
    .from("posts")
    .select(
      "id, location, description, ai_caption, tags, nsfw, profiles(id, bots!bots_profile_id_fkey(*))",
    )
    .eq("visibility", "draft")
    .lte("created_at", dateToCheck)
    .order("id", { ascending: false });

  console.log("posts", posts[0], postsErrors);

  for (const post of posts) {
    if (post.profiles?.bots?.art_style?.includes("realistic")) {
      console.log("prepare to queue", post.id);
      await regeneratePostImage({
        bot: post.profiles.bots,
        post_id: post.id,
        executionId: req.executionId,
        priority: "low",
      });
      console.log("finished queuing post", post.id);
    }
  }

  res.sendStatus(200);
});

app.get("/pruneEmptyMemories", async (req, res) => {
  const { data: stories, error: storiesError } = await supabase
    .from("stories")
    .select("id, memories(id)")
    .limit(1000);

  if (storiesError) {
    const error = wrappedSupabaseError(
      storiesError,
      "failed to fetch stories info",
    );
    throw error;
  }

  for (const story of stories) {
    if (story.memories.length === 0) {
      console.log("delete story", story.id);
      const { error: deleteStoryError } = await supabase
        .from("stories")
        .delete()
        .eq("id", story.id);
      if (deleteStoryError) {
        const error = wrappedSupabaseError(
          deleteStoryError,
          "failed to delete story",
        );
        logWarn({
          context: "/pruneEmptyMemories - deleteStoryError",
          error,
          story_id: story.id,
        });
      }
    }
  }

  res.sendStatus(200);
});

// Start bot clones
app.post(
  "/uploadFirstImageForClones",
  upload.single("image"),
  authUser,
  async (req, res) => {
    // Get the profile_id from the request body
    const { profile_id } = req.body;
    const user_id = req.user?.id;

    // Ensure image and profile_id are provided
    if (!req.file || !profile_id) {
      return res.status(400).send("Missing required fields.");
    }

    const isValid = await checkProfileValid(user_id, profile_id);
    if (!isValid) {
      return res.status(403).send({ error: "Forbidden" });
    }

    try {
      console.log("000)");
      // Create a new instance of Storage
      const storage = new Storage();

      // Define your GCS bucket name
      const bucketName = "butterflies-ai-selfies";
      const bucket = storage.bucket(bucketName);

      const uuid = uuidv4();

      // Upload the image to GCS
      const blob = bucket.file(`${req.user?.id}/${uuid}`);

      const blobStream = blob.createWriteStream({
        resumable: false,
        contentType: req.file.mimetype,
      });

      blobStream.on("error", (err) => {
        logError({
          executionId: req.executionId,
          context: "uploadFirstImageForClones - failed to upload first image",
          error: err,
          profile_id,
        });
        res.sendStatus(500);
      });

      blobStream.on("finish", async () => {
        // Generate a signed URL for the uploaded image
        const options = {
          version: "v4",
          action: "read",
          expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
        };

        try {
          const [url] = await blob.getSignedUrl(options);
          console.log("Generated URL:", url);

          // Check if the uploaded image contains a face
          const boundingPoly = await getBoundingPolyForFace({ imageUrl: url });
          console.log("Face detected:", boundingPoly);

          if (boundingPoly) {
            // Download the image as a buffer
            const [buffer] = await blob.download();

            // Crop the image
            const padding = 40; // Adjust padding as needed
            const croppedImageBuffer = await cropImage(
              buffer,
              boundingPoly,
              padding,
            );

            // Upload the cropped image back to GCS
            const croppedBlob = bucket.file(`${req.user?.id}/${uuid}-cropped`);
            await croppedBlob.save(croppedImageBuffer, {
              contentType: req.file.mimetype,
              resumable: false,
            });

            const [croppedUrl] = await croppedBlob.getSignedUrl(options);

            console.log("Cropped Image URL:", croppedUrl);

            console.log("profile_id", profile_id);
            const { data: clone, error: cloneError } = await supabase
              .from("clones")
              .insert({
                profile_id,
                images: { front: { original: url, optimized: croppedUrl } },
              })
              .select("*")
              .single();

            if (cloneError) {
              const error = wrappedSupabaseError(
                cloneError,
                "failed to insert clone info",
              );
              logError({
                context: "uploadFirstImageForClones failed",
                error,
                profile_id,
              });
              return res.sendStatus(500);
            }

            // Why it's ok to respond early here and not fail if we fail to generate the "skin tone caption":
            // Vu: FaceId does a general good job of not switching tones. The skin is just an extra guard. Definitely not critical
            res.json(clone);

            let caption = await generateAICaptionFromImage({
              imageUrl: url,
              executionId: req.executionId,
              temperature: 0.1,
              prompt:
                "Describe this person's skin tone. Only output the result. Options: white, light, medium, olive, brown, black. Example: 'white skin tone, light skin tone'. If you cannot determine, output empty string",
            });

            const bannedWords = [
              "can't",
              "cannot",
              "AI agent",
              "AI model",
              "assist",
              "describing",
            ];

            if (
              caption &&
              bannedWords.some((word) => caption.toLowerCase().includes(word))
            ) {
              caption = "";
            }

            if (caption) {
              const { error: updateCloneError } = await supabase
                .from("clones")
                .update({
                  caption,
                })
                .eq("id", clone.id);

              if (updateCloneError) {
                const error = wrappedSupabaseError(
                  updateCloneError,
                  "failed to update clone appearance caption",
                );
                logError({
                  context:
                    "uploadFirstImageForClones - failed to update clone appearance caption",
                  error,
                  profile_id,
                  clone_id: clone.id,
                });
              }
            }
          } else {
            return res.status(400).send("Image does not contain a face.");
          }
        } catch (error) {
          if (error.message.includes("Bounding box")) {
            logWarn({
              context: "uploadFirstImageForClones - failing ",
              error,
              profile_id,
            });
            return res.status(400).send("Face is not centered.");
          }

          logError({
            context:
              "uploadFirstImageForClones - error processing first clone image after uloading",
            error,
            profile_id,
          });

          res.sendStatus(500);
        }
      });

      blobStream.end(req.file.buffer);
    } catch (err) {
      logError({
        context:
          "uploadFirstImageForClones - error uploading first clone image",
        error: err,
        profile_id,
      });
      res.status(500).send(err);
    }
  },
);

// Utility function to crop the image around the face
async function cropImage(buffer, boundingPoly, padding = 0) {
  const image = sharp(buffer);
  const metadata = await image.metadata();

  const imageWidth = metadata.width;
  const imageHeight = metadata.height;

  const vertices = boundingPoly.vertices;

  // Calculate the bounding box
  const xMin = Math.min(vertices[0].x, vertices[3].x);
  const xMax = Math.max(vertices[1].x, vertices[2].x);
  const yMin = Math.min(vertices[0].y, vertices[1].y);
  const yMax = Math.max(vertices[2].y, vertices[3].y);

  // Define an edge tolerance, which can be adjusted as needed
  const edgeTolerance = 5; // pixels

  // Check if the bounding box is out of image bounds or right at the edge
  if (xMin < 0 || xMax > imageWidth || yMin < 0 || yMax > imageHeight) {
    throw new Error("Bounding box is out of image bounds.");
  }

  if (
    xMin <= edgeTolerance ||
    xMax >= imageWidth - edgeTolerance ||
    yMin <= edgeTolerance ||
    yMax >= imageHeight - edgeTolerance
  ) {
    throw new Error(
      "Bounding box is right at the edge of the image, potential clipping detected.",
    );
  }

  // Add padding and ensure the coordinates are within image bounds
  const paddedXMin = Math.max(0, xMin - padding);
  const paddedYMin = Math.max(0, yMin - padding);
  const paddedXMax = Math.min(imageWidth, xMax + padding);
  const paddedYMax = Math.min(imageHeight, yMax + padding);

  // Calculate the size of the square to crop
  const width = paddedXMax - paddedXMin;
  const height = paddedYMax - paddedYMin;
  const sideLength = Math.min(
    Math.max(width, height),
    Math.min(imageWidth, imageHeight),
  );

  // Adjust the crop to make it square and ensure it is within bounds
  const centerX = (paddedXMin + paddedXMax) / 2;
  const centerY = (paddedYMin + paddedYMax) / 2;

  const cropX = Math.max(
    0,
    Math.min(imageWidth - sideLength, centerX - sideLength / 2),
  );
  const cropY = Math.max(
    0,
    Math.min(imageHeight - sideLength, centerY - sideLength / 2),
  );

  // Crop the image
  const croppedImageBuffer = await image
    .extract({
      left: Math.floor(cropX),
      top: Math.floor(cropY),
      width: Math.floor(sideLength),
      height: Math.floor(sideLength),
    })
    .toBuffer();

  return croppedImageBuffer;
}

app.post(
  "/uploadRestOfImagesForClones",
  upload.array("images"),
  authUser,
  async (req, res) => {
    // Get the profile_id and clone_id from the request body
    const { profile_id, clone_id } = req.body;
    const user_id = req.user?.id;

    // Ensure images and profile_id are provided
    if (!req.files || req.files.length === 0 || !profile_id) {
      res.status(400).send("Missing required fields.");
      return;
    }

    // Ensure the user is valid to upload
    const { data: validation, error: validationError } = await supabase
      .from("clones")
      .select("id, creator:profiles!clones_profile_id_fkey(id, user_id)")
      .eq("id", clone_id)
      .eq("profile_id", profile_id)
      .neq("profiles.visibility", "archived")
      .single();

    if (validationError) {
      const error = wrappedSupabaseError(validationError);
      logError({
        context: "uploadRestOfImagesForClones: fetching validation info failed",
        error,
        profile_id,
        clone_id,
      });
      return res.sendStatus(500);
    }

    if (
      !validation?.creator?.user_id ||
      validation?.creator?.user_id !== user_id
    ) {
      return res.status(403).send({ error: "Forbidden" });
    }

    try {
      // Create a new instance of Storage
      const storage = new Storage();

      // Define your GCS bucket name
      const bucketName = "butterflies-ai-selfies";
      const bucket = storage.bucket(bucketName);

      const uploadPromises = req.files.map((file) => {
        return new Promise((resolve, reject) => {
          try {
            const uuid = uuidv4();

            // Upload the image to GCS
            const blob = bucket.file(`${req.user?.id}/${uuid}`);

            const blobStream = blob.createWriteStream({
              resumable: false,
              contentType: file.mimetype,
            });

            blobStream.on("error", (err) => {
              const error = new Error(
                `failed to upload file ${file.originalname}`,
                { cause: err },
              );
              reject(error);
            });

            blobStream.on("finish", async () => {
              // Generate a signed URL for the uploaded image
              const options = {
                version: "v4",
                action: "read",
                expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
              };

              const [url] = await blob.getSignedUrl(options);

              if (url) {
                resolve(url);
              } else {
                reject(
                  new Error(
                    `Failed to get signed URL for file ${file.originalname}`,
                  ),
                );
              }
            });

            blobStream.end(file.buffer);
          } catch (err) {
            const error = new Error(
              `failed to upload file ${file.originalname}`,
              { cause: err },
            );
            reject(error);
          }
        });
      });

      const results = await Promise.allSettled(uploadPromises);

      const successfulUploads = results
        .filter((result) => result.status === "fulfilled")
        .map((result) => result.value);
      const failedUploads = results
        .filter((result) => result.status === "rejected")
        .map((result) => result.reason);

      if (failedUploads.length > 0) {
        res.status(207).send({ successfulUploads, failedUploads });
      } else {
        let payload = {};
        if (successfulUploads.length === 2) {
          payload.left = {
            original: successfulUploads[0],
            optimized: successfulUploads[0],
          };

          payload.right = {
            original: successfulUploads[1],
            optimized: successfulUploads[1],
          };

          const { data: clone, error: cloneError } = await supabase
            .from("clones")
            .select("*")
            .eq("id", clone_id)
            .single();
          if (cloneError) {
            const error = wrappedSupabaseError(
              cloneError,
              "failed to fetch clone info",
            );
            throw error;
          }

          let newPayload = clone.images;

          newPayload = { ...newPayload, ...payload };

          const { data, error: updateCloneError } = await supabase
            .from("clones")
            .update({
              images: newPayload,
            })
            .eq("id", clone_id)
            .select("*")
            .single();
          if (updateCloneError) {
            const error = wrappedSupabaseError(
              cloneError,
              "failed to update clone info",
            );
            throw error;
          }

          return res.json(data);
        }

        return res.sendStatus(400);
      }
    } catch (error) {
      logError({
        context: "uploadRestOfImagesForClones failed",
        error,
        profile_id,
        clone_id,
      });
      res.sendStatus(500);
    }
  },
);

app.get("/testIam", async (req, res) => {
  try {
    // Make a request to the metadata server to get the service account email
    const response = await axios.get(
      "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/email",
      { headers: { "Metadata-Flavor": "Google" } },
    );
    const serviceAccountEmail = response.data;
    res.send(`Service Account: ${serviceAccountEmail}`);
  } catch (error) {
    logError({
      context: "Error retrieving service account email:",
      error,
    });
    res.status(500).send("Error retrieving service account email");
  }
});

app.get("/getOrGenerateSignedUrl", async (req, res) => {
  const test = await getOrGenerateSignedUrl(
    "https://storage.googleapis.com/butterflies-ai-selfies/e0a788c9-e794-4d6f-a291-bfaa3eb96461/ba518305-4c29-4924-9da6-8126baa27c2e?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=************-compute%40developer.gserviceaccount.com%2F20240821%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20240821T131129Z&X-Goog-Expires=604800&X-Goog-SignedHeaders=host&X-Goog-Signature=16e2f9feef1326e0eb1dae8c37072a45f9828ad34f0e9c50b73a3183d4d49207a66c9589e5d99cc1fd0546f3b2447e3c00522a24a81370b275e6ac452421d411bac58047b9c95f4f688fdf156732f0544290d56459599ea5ed0ea3ea04060d371c0ce600f890a0f8e598d91ba0dc1b4c844e9def638474ab32e68e3084f5c1e812dc8b4f537d21ac395cc38b757ba9bde762a9ba59da3519eda6cbf7a80efdbad2a8129d642452263654ca016d363d2d9df6af6897fa7e903a580b955b2be92cb1b49f460b3dfa9987f5dd14fecc995f6f27f1e37486b3aa4424d6a349736abbb8d25a0a6d585456f7ffcd9304f59e75d9decdd75f762a4fd6ce5ef049d5ed9d",
  );

  console.log("test", test);

  res.send(test);
});

// Update gender for clones
app.post("/updateGender", authUser, async (req, res) => {
  // Get the profile_id from the request body
  const { clone_id, gender } = req.body;
  const user_id = req.user?.id;

  // Ensure gender and clone_id are provided
  if (!gender || !clone_id) {
    res.status(400).send("Missing required fields.");
    return;
  }

  // Ensure the user is valid to upload
  const { data: validation, error: validationError } = await supabase
    .from("clones")
    .select("id, creator:profiles!clones_profile_id_fkey(id, user_id)")
    .eq("id", clone_id)
    .neq("profiles.visibility", "archived")
    .single();

  if (validationError) {
    const error = wrappedSupabaseError(validationError);
    logError({
      context: "updateGender for clone: fetching validation info failed",
      error,
      gender,
      clone_id,
    });
    return res.sendStatus(500);
  }

  if (
    !validation?.creator?.user_id ||
    validation?.creator?.user_id !== user_id
  ) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const { data, error: updateError } = await supabase
    .from("clones")
    .update({
      gender,
    })
    .eq("id", clone_id)
    .select("*")
    .single();

  if (updateError) {
    const error = wrappedSupabaseError(
      updateError,
      "failed to update clone gender",
    );
    logError({
      context: "updateGender",
      error,
      clone_id,
      gender,
    });
    return res.sendStatus(500);
  }

  res.json(data);
});

app.post("/makeBotLikePost", async (req, res) => {
  const { error: postLikeError } = await supabase.from("post_likes").insert({
    profile_id: req.body.profile_id,
    post_id: req.body.post_id,
    is_like: true,
    is_bot: true,
  });

  if (postLikeError) {
    if (postLikeError.code === PostgresErrorCode.UNIQUE_VIOLATION) {
      logWarn({
        executionId: req.executionId,
        context: "Insert postlike Duplicate",
        message: `Duplicate postlike - profile_id: ${req.body.profile_id}, post_id: ${req.body.post_id}`,
      });
      return res.sendStatus(204);
    }

    const error = wrappedSupabaseError(postLikeError);
    logError({
      context: "Insert postlike Failed Error",
      error,
    });

    return res.sendStatus(500);
  } else {
    console.log(`Bot ${req.body.profile_id} liked post ${req.body.post_id}`);
  }
  return res.sendStatus(200);
});

app.post("/makeBotFollow", async (req, res) => {
  const { error } = await supabase
    .from("followers")
    .insert({
      follower_id: req.body.profile_id,
      following_id: req.body.profile_id_to_follow,
    })
    .select("*")
    .single();

  if (error) {
    logError({
      context: "Insert followers Failed Error",
      error,
      follower_id: req.body.profile_id,
      following_id: req.body.profile_id_to_follow,
    });

    return res.sendStatus(500);
  } else {
    await putFollowers(
      req.body.profile_id,
      req.body.profile_id_to_follow,
      true,
    );
  }

  console.log(
    `Bot ${req.body.profile_id} followed ${req.body.profile_id_to_follow}`,
  );

  return res.sendStatus(200);
});

app.post("/makeBotCommentOnPost", async (req, res) => {
  await makeBotCommentOnPost({
    profile_id: req.body.profile_id,
    post_id: req.body.post_id,
  });
  return res.sendStatus(200);
});

// curl -X POST "http://localhost:8080/v1/bots/generatePostBasedOnPersona" \
//   -H "Content-Type: application/json" \
//   -d '{
//     "profile_id": "1",
//     "prompt": "standing in front of a mcdonalds"
//   }'

app.post("/generatePostBasedOnPersona", async (req, res) => {
  console.log("check now", req.body);
  if (!req.body.profile_id) {
    return res.sendStatus(400);
  }

  let bot;

  if (req.body.usePersona) {
    const { data: profile, error: profileError } = await supabase
      .from("active_personas")
      .select("*")
      .eq("owner_profile_id", req.body.profile_id)
      .single();

    console.log("try now", profile);

    if (profileError || !profile) {
      return res.sendStatus(500);
    }

    // get bot from profile
    const { data: fetchedBot, error: botError } = await supabase
      .from("bots")
      .select("*")
      .eq("profile_id", profile.persona_profile_id)
      .single();

    console.log("fetchedBot", fetchedBot);
    if (botError || !fetchedBot) {
      return res.sendStatus(500);
    }

    bot = fetchedBot;
    const imageURL = await generatePostImageWithPrompts({
      descriptionOfImage: req.body.prompt,
      bot,
    });

    console.log("imageURL", imageURL);

    return res.json(imageURL);
  } else {
    let task;

    console.log("BAM1111");
    await tracer.withActiveSpan("insert tasks record", async (span) => {
      const response = await supabase
        .from("tasks")
        .insert({
          service: "comfy",
          status: "queued",
          payload: {},
        })
        .select("id, created_at, status")
        .single();

      task = response.data;
      const taskError = response.error;

      if (taskError) {
        const error = wrappedSupabaseError(taskError);
        logError({
          context: "*** generateComfyRequest insert tasks record",
          error: error,
        });
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error?.message,
        });
        throw error;
      }
    });
    console.log("BAM2222");

    const workflow = await generateWorkflow({
      prompt: req.body.prompt,
      artStyle: req.body.model,
      face_image_url: null,
      avatar_url: null,
      promptType: "basic",
      width: 864,
      height: 1080,
      batch_size: 1,
      seed: null,
      nsfw: null,
      contains_character: false,
    });
    console.log("BAM3333", task.id, workflow);

    await enqueueTask(
      JSON.stringify(workflow),
      10,
      "basic",
      task.id.toString(),
    );

    console.log("BAM444", workflow, task.id);

    return res.json({ task_id: task.id, task_name: task.id });
  }
});

app.post("/generateCommentsAndLikesForPost", async (req, res) => {
  generateCommentsAndLikesForPostInstant(req.body.post_id);
  return res.sendStatus(200);
});

app.get("/checkHasBotGoneRogue", async (req, res) => {
  const rogue = await hasBotGoneRogue({
    bot_id: req.query.bot_id,
    post_id: req.query.post_id,
  });

  res.json(rogue);
});

// delete bot by admin or owner
app.post("/deleteBot", authUser, async (req, res) => {
  // Get the bot_id from the request body
  const { bot_id } = req.body;
  const user_id = req.user?.id;

  // Ensure bot_id is provided
  if (!bot_id) {
    res.status(400).send("Missing required fields.");
    return;
  }

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const isAdminValid = await checkAdminValid(user_id);
  if (!isAdminValid) {
    const isBotValid = await checkBotValid(user_id, bot_id);
    if (!isBotValid) {
      return res.status(403).send({ error: "Forbidden" });
    }
  }

  const { error: deleteError } = await supabase
    .from("bots")
    .delete()
    .eq("id", bot_id);

  if (deleteError) {
    const error = wrappedSupabaseError(deleteError, "failed to delete bot");
    logError({
      context: "/deleteBot - delete bot failed",
      error,
      bot_id,
    });
    return res.sendStatus(500);
  }

  return res.sendStatus(200);
});

// update bot by admin or owner
app.patch("/updateBot", authUser, async (req, res) => {
  const { bot_id, botDetails } = req.body;
  const user_id = req.user?.id;

  // Validate input
  if (!bot_id || !botDetails || Object.keys(botDetails).length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  if (!user_id) {
    return res.status(403).send({ error: "Forbidden" });
  }

  const fieldsToUpdate = [
    "comment_style",
    "personality",
    "characteristics",
    "background",
    "show_creator",
    "voice",
    "post_narration_type",
    "voice_id",
  ];

  // Filter botDetails to contain only allowed fields
  let contentsToUpdate = getSpecificKeys(botDetails, fieldsToUpdate);

  try {
    const isBotValid = await checkBotValid(user_id, bot_id);

    if (!isBotValid) {
      const isAdminValid = await checkAdminValid(user_id);
      if (!isAdminValid) {
        return res.status(403).json({ error: "Forbidden" });
      } else {
        // Admin can update all fields
        contentsToUpdate = botDetails;
      }
    }

    // Check if there are fields to update
    if (Object.keys(contentsToUpdate).length === 0) {
      return res.sendStatus(204); // No Content
    }

    // Update the bot with the appropriate details
    const { error } = await supabase
      .from("bots")
      .update(contentsToUpdate)
      .eq("id", bot_id);

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "/updateBot - update bot failed",
      error: error,
    });
    return res.sendStatus(500); // Internal Server Error
  }
});

app.post("/state", authUser, async (req, res) => {
  const { bot_id, status } = req.body;
  const user_id = req.user?.id;

  if (
    !bot_id ||
    (status !== true &&
      status !== false &&
      status !== "true" &&
      status !== "false")
  ) {
    return res.status(400).json({ error: "Invalid input error" });
  }

  // Normalize status to a boolean
  const normalizedStatus = status === true || status === "true";

  const isBotValid = await checkBotValid(user_id, bot_id);
  if (!isBotValid) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { data: botData, error: botError } = await supabase
      .from("bots")
      .update({ is_active: normalizedStatus })
      .eq("id", bot_id)
      .select("profile_id, is_active");

    if (botError) {
      logError({ context: "/state - update bot failed", error: botError });
      return res.status(500).json({ error: "Failed to update bot state." });
    }

    if (!botData || botData.length !== 1) {
      return res
        .status(404)
        .json({ error: "Bot not found or multiple bots affected." });
    }

    const { profile_id, is_active } = botData[0];

    const { error: profileError } = await supabase
      .from("profiles")
      .update({ proposed_post_mode: is_active })
      .eq("id", profile_id);

    if (profileError) {
      logError({
        context: "/state - update profile failed",
        error: profileError,
      });
      return res.status(500).json({ error: "Failed to update profile state." });
    }

    // Respond to client first
    res.sendStatus(200);

    const { data: postData, error: postError } = await supabase
      .from("posts")
      .select("id, proposed_post_state")
      .eq("profile_id", profile_id)
      .order("created_at", { ascending: false })
      .limit(1);

    if (postError) {
      logError({
        context: "/state - fetch latest post failed",
        error: postError,
      });
      return;
    }
    const latestPost = Array.isArray(postData) ? postData[0] : null;
    // Background processing
    if (normalizedStatus) {
      void (async () => {
        try {
          const day_ms = 24 * 60 * 60 * 1000;
          if (!latestPost) {
            // No post: schedule next proposal
            await enableProposedPostModeForBot({
              botProfileId: profile_id,
              proposedPostNextGenerationDate: new Date(Date.now() + day_ms),
            });
            return;
          }

          if (latestPost.proposed_post_state === "proposed") {
            await rejectProposedPost({ postId: latestPost.id });
            return;
          } else if (!latestPost.proposed_post_state) {
            await enableProposedPostModeForBot({
              botProfileId: profile_id,
              proposedPostNextGenerationDate: new Date(Date.now() + day_ms),
            });
            return;
          }
        } catch (err) {
          logError({
            context: "/state - background post processing failed",
            error: err,
          });
        }
      })();
    } else {
      if (latestPost) {
        void (async () => {
          try {
            await supabase
              .from("posts")
              .update({ proposed_post_state: null })
              .eq("id", latestPost.id);
          } catch (err) {
            logError({
              context: "/state - clear proposed_post_state failed",
              error: err,
            });
          }
        })();
      }
    }
  } catch (error) {
    logError({ context: "/state - unexpected failure", error });
    return res.status(500).json({ error: "Internal server error." });
  }
});

app.get("/userAllBots", async (req, res) => {
  const { profileId, cursor, limit } = req.query;
  const limitInt = parseInt(limit, 10);
  if (!profileId || !limit || isNaN(limitInt) || limitInt <= 0 || !cursor) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  try {
    let query = supabase
      .from("bots")
      .select(
        `
        id,
        bio,
        created_at,
        profile_id,
        location,
        status,
        gender,
        tag,
        display_name,
        art_style,
        creator_id,
        description,
        is_active,
        show_creator,
        life,
        background,
        characteristics,
        personality,
        clone_id,
        profiles:profile_id(
          id,
          avatar_url,
          username,
          display_name,
          description,
          visibility,
          nsfw
        )`,
      )
      .eq("creator_id", profileId)
      .eq("show_creator", true)
      .not("profiles", "is", null)
      .eq("profiles.visibility", "public")
      .order("created_at", { ascending: false })
      .limit(limitInt);

    if (cursor !== "init") {
      query = query.lt("created_at", cursor);
    }

    const { data, error } = await query;

    if (error) {
      throw wrappedSupabaseError(error);
    }

    const next_cursor =
      data.length === limitInt
        ? dayjs(data[data.length - 1].created_at).format(
            "YYYY-MM-DDTHH:mm:ss.SSS",
          )
        : null;

    const profileIds = data.map((bot) => bot.profile_id);
    const { data: postCounts, error: postCountsError } = await supabase.rpc(
      "get_public_post_counts",
      { profile_ids: profileIds },
    );

    if (postCountsError) {
      throw wrappedSupabaseError(
        postCountsError,
        "failed to fetch post counts",
      );
    }

    // Convert postCounts to a map for easy lookup
    const postCountMap = postCounts.reduce(
      (acc, { profile_id, post_count }) => {
        acc[profile_id] = post_count;
        return acc;
      },
      {},
    );

    // Combine data by mapping over data
    const combinedData = data.map((bot) => ({
      ...bot,
      profiles: {
        ...bot.profiles,
        post_count: [{ count: postCountMap[bot.profile_id] || 0 }],
      },
    }));
    return res.send({
      data: combinedData,
      page_info: { next_cursor },
      error: null,
    });
  } catch (error) {
    logError({
      context: "allBots - fetch Failed Error",
      error,
      profileId,
    });
    return res.status(500).send({ data: null, error: "allBots fetch failed" });
  }
});

app.get("/getProfileBots", async (req, res) => {
  const { profileId } = req.query;

  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }
  try {
    // Fetch bots and their associated profiles
    const { data: botsData, error: botsError } = await supabase
      .from("bots")
      .select(
        `
          id,
          profile_id,
          clone_id,
          art_style,
          profiles:profile_id (
            id,
            avatar_url,
            username,
            description,
            display_name,
            nsfw
          )
        `,
      )
      .eq("creator_id", profileId)
      .not("profiles", "is", null)
      .neq("profiles.visibility", "hidden")
      .neq("profiles.visibility", "archived")
      .order("id", { ascending: false });

    if (botsError) {
      throw wrappedSupabaseError(botsError, "failed to fetch bots");
    }

    // Extract profile IDs for fetching post counts
    const profileIds = botsData.map((bot) => bot.profile_id);

    const { data: postCounts, error: postCountsError } = await supabase.rpc(
      "get_public_post_counts",
      { profile_ids: profileIds },
    );

    if (postCountsError) {
      throw wrappedSupabaseError(
        postCountsError,
        "failed to fetch post counts",
      );
    }

    // Convert postCounts to a map for easy lookup
    const postCountMap = postCounts.reduce(
      (acc, { profile_id, post_count }) => {
        acc[profile_id] = post_count;
        return acc;
      },
      {},
    );

    // Combine data by mapping over botsData
    const combinedData = botsData.map((bot) => ({
      ...bot,
      profiles: {
        ...bot.profiles,
        post_count: [{ count: postCountMap[bot.profile_id] || 0 }],
      },
    }));

    return res.send({ data: combinedData, error: null });
  } catch (error) {
    logError({
      context: "getProfileBots - fetch Failed Error",
      error,
      profileId,
    });
    return res
      .status(500)
      .send({ data: null, error: "getProfileBots fetch failed" });
  }
});

app.get("/getBotArtStyle", async (req, res) => {
  const { profileId } = req.query;
  if (!profileId) {
    return res.status(400).send({ data: null, error: "Invalid content error" });
  }

  try {
    const { data, error } = await supabase
      .from("bots")
      .select("id, art_style")
      .eq("profile_id", profileId)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }

    return res.send({ data, error: null });
  } catch (error) {
    logError({
      context: "getBotArtStyle - fetch Failed Error",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "getBotArtStyle fetch failed" });
  }
});

// bots for users tab in Admin
app.post("/getUserBotsAdmin", authUser, async (req, res) => {
  const { ids } = req.body;
  if (!ids || ids.length === 0) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);
  if (!isAllowed) {
    return res.status(403).send({ data: null, error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("bots")
      .select(
        "id,profiles:profile_id!inner(avatar_url, display_name, username)",
      )
      .in("creator_id", ids)
      .order("id", { ascending: false });
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getUserProfile from Admin failed",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getUserProfile" });
  }
});

// ADMIN: bots for ais tab
app.post("/getBotDataWithIDAdmin", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);
  if (!isAllowed) {
    return res.status(403).send({ data: null, error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("bots")
      .select(
        `id, bio, description, location, seaart_token, timezone, last_start, status, active_hours_per_day, wake_up_interval, wake_up_time`,
      )
      .eq("id", id)
      .single();
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getBotDataAdmin from Admin failed",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getBotDataAdmin" });
  }
});

// ADMIN: Get Bots with Posts
app.post("/getBotWithPostsAdmin", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);
  if (!isAllowed) {
    return res.status(403).send({ data: null, error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("bots")
      .select(
        `id, last_execution_id, 
        profiles:profile_id(id, avatar_url, description, display_name, username, posts(id, media_url, ai_caption))`,
      )
      .eq("id", id)
      .single();
    console.log(data, error);
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getBotWithPosts from Admin failed",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getBotWithPostsAdmin" });
  }
});

app.post("/getBotInSettings", authUser, async (req, res) => {
  const { profile_id } = req.body;
  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkProfileValid(user_id, profile_id);
  if (!isAllowed) {
    return res.status(403).send({ data: null, error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("bots")
      .select(
        `
        id,profile_id,voice,description,art_style,bio,clone_id,show_creator,creator_id,background,is_active,life,personality,characteristics,comment_style,
        profiles:profile_id(display_name,visibility),
        creator:profiles!bots_creator_id_fkey(id,username,display_name,visibility,nsfw)
      `,
      )
      .eq("profile_id", profile_id)
      .single();
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getBotInSettings failed",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getBotInSettings" });
  }
});

// ADMIN: Get Bot Detail
app.post("/getBotDetailAdmin", authUser, async (req, res) => {
  const { profile_id } = req.body;
  if (!profile_id) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("bots")
      .select(
        "id, display_name, bio, description, background, personality, characteristics",
      )
      .eq("profile_id", profile_id)
      .maybeSingle();
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getBotDetailAdmin failed",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getBotDetailAdmin" });
  }
});

// ADMIN: Get Bots in Post Edit
app.post("/getBotsInPostEditAdmin", authUser, async (req, res) => {
  const { search_value } = req.body;

  try {
    const { data, error } = await supabase
      .from("bots")
      .select("*")
      .ilike("display_name", `%${search_value}%`)
      .order("id", { ascending: false })
      .range(0, 200);
    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.json({ data, error: null });
  } catch (error) {
    logError({
      context: "getBotsInPostEditAdmin failed",
      error,
    });
    return res
      .status(500)
      .send({ data: null, error: "Failed to getBotsInPostEditAdmin" });
  }
});

app.get("/testNotificationRefresh", async (req, res) => {
  await checkIfNoPokesLeftAndSendPushNotification(
    "af599abb-4b4f-4883-89fb-be949a67ab48",
    1,
  );
  res.send("ok");
});

module.exports = {
  app,
  updateBotStatusBasedOnTime,
  calculateTimeSinceLastStart,
  generatePost,
  considerMakingANewPost,
  considerLikingPost,
  considerCommentingOnPost,
  considerFollowingProfileFromPost,
  considerLikingComment,
  generateCharacterWithSentence,
  pregeneratePostAndTaskStubs,
};
