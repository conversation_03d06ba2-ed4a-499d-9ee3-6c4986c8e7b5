/**
 * Boba Audio Helpers
 *
 * This module provides helper functions for audio generation in the boba service.
 * It uses Hume.ai for voice generation, creating unique voices for each profile
 * and storing them in the video_clip_voices table for future use.
 */

const axios = require("axios");
const { Storage } = require("@google-cloud/storage");
const { logError } = require("../utils");
const { createClient } = require("@supabase/supabase-js");
const { decorateWithActiveSpanAsync } = require("../instrumentation/tracer");

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL || "https://xyzcompany.supabase.co",
  process.env.SUPABASE_KEY || "public-anon-key",
);

// Hume.ai API key
const HUME_API_KEY = process.env.HUME_API_KEY || "hume-api-key";

// GCS bucket name
const bucketName = process.env.GCS_BUCKET_NAME || "butterflies-images-v1-us";

/**
 * Generate a voice using Hume.ai for a specific profile
 * @param {string} profileId - The profile ID to generate a voice for
 * @returns {Promise<string>} The generated voice ID
 */
async function _generateHumeVoiceForProfileId(profileId) {
  try {
    // Get the profile data from Supabase
    const { data: profile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", profileId)
      .single();

    if (error) {
      throw new Error(`Failed to get profile: ${error.message}`);
    }

    if (!profile) {
      throw new Error(`Profile not found: ${profileId}`);
    }

    // Generate a voice description based on the profile
    let voiceDescription;

    if (profile.bots && profile.bots.description) {
      // Use the bot description to generate a voice description
      const botDescription = profile.bots.description;

      const prompt = `
Based on this character description, describe their voice in 2-3 sentences. Focus on tone, pitch, accent, speaking style, and emotional qualities:

${botDescription}
`;

      // Use an LLM to generate a voice description
      // This is a placeholder implementation
      // In a real application, you would use an LLM service
      const { data } = await axios.post(
        "https://api.anthropic.com/v1/messages",
        {
          model: "claude-3-sonnet-20240229",
          max_tokens: 150,
          messages: [{ role: "user", content: prompt }],
        },
        {
          headers: {
            "Content-Type": "application/json",
            "x-api-key": process.env.ANTHROPIC_API_KEY,
            "anthropic-version": "2023-06-01",
          },
        },
      );

      const content = data.content[0].text;
      voiceDescription = content;
    } else {
      // Generate a generic voice description
      voiceDescription =
        "A neutral, clear voice with a moderate pitch and standard American accent.";
    }

    // Create the voice with Hume.ai
    const response = await axios.post(
      "https://api.hume.ai/v0/synthesize/voices",
      {
        description: voiceDescription,
      },
      {
        headers: {
          "Content-Type": "application/json",
          "X-Hume-Api-Key": HUME_API_KEY,
        },
      },
    );

    if (response.status !== 200) {
      throw new Error(`Hume API error: ${response.status}`);
    }

    const voiceId = response.data.voice_id;

    // Store the voice ID in the database
    const { error: insertError } = await supabase
      .from("video_clip_voices")
      .insert({
        profile_id: profileId,
        voice_id: voiceId,
        description: voiceDescription,
      });

    if (insertError) {
      throw new Error(`Failed to store voice ID: ${insertError.message}`);
    }

    return voiceId;
  } catch (error) {
    logError({
      message: "Error generating voice for profile",
      error,
      profileId,
    });
    throw error;
  }
}

const generateHumeVoiceForProfileId = decorateWithActiveSpanAsync(
  "generateHumeVoiceForProfileId",
  _generateHumeVoiceForProfileId,
);

/**
 * Generate and upload voice audio to Google Cloud Storage
 * @param {string} text - The text to convert to speech
 * @param {string} profileId - The profile ID to use for voice generation
 * @param {string} partId - A unique ID for this audio part
 * @param {string} [voiceDescription=null] - Optional voice description to use instead of profile-based voice
 * @returns {Promise<string>} The URL of the uploaded audio file
 */
async function _generateAndUploadVoiceAudio(
  text,
  profileId,
  partId,
  voiceDescription = null,
) {
  try {
    const storage = new Storage();
    const bucket = storage.bucket(bucketName);

    // Get the voice ID for the profile
    let voiceId = null;
    if (profileId) {
      const { data, error } = await supabase
        .from("video_clip_voices")
        .select("voice_id")
        .eq("profile_id", profileId)
        .single();

      if (!error && data) {
        voiceId = data.voice_id;
      }
    }

    // If no voiceId is provided but we have a description, create a new voice
    if (!voiceId && voiceDescription) {
      const response = await axios.post(
        "https://api.hume.ai/v0/synthesize/voices",
        {
          description: voiceDescription,
        },
        {
          headers: {
            "Content-Type": "application/json",
            "X-Hume-Api-Key": HUME_API_KEY,
          },
        },
      );

      if (response.status !== 200) {
        throw new Error(`Hume API error: ${response.status}`);
      }

      voiceId = response.data.voice_id;

      // Store the voice ID in the database if we have a profile ID
      if (profileId) {
        await supabase.from("video_clip_voices").insert({
          profile_id: profileId,
          voice_id: voiceId,
          description: voiceDescription,
        });
      }
    }

    // If we still don't have a voice ID, generate one
    if (!voiceId && profileId) {
      voiceId = await generateHumeVoiceForProfileId(profileId);
    }

    // If we still don't have a voice ID, use a default one
    if (!voiceId) {
      voiceId = "default-voice-id";
    }

    // Generate speech using Hume.ai
    const speechResponse = await axios.post(
      "https://api.hume.ai/v0/synthesize/speech",
      {
        text,
        voice_id: voiceId,
      },
      {
        headers: {
          "Content-Type": "application/json",
          "X-Hume-Api-Key": HUME_API_KEY,
        },
        responseType: "arraybuffer",
      },
    );

    if (speechResponse.status !== 200) {
      throw new Error(`Hume API error: ${speechResponse.status}`);
    }

    // Upload the audio to GCS
    const fileName = `videos/voices/${partId}.mp3`;
    const file = bucket.file(fileName);

    await file.save(speechResponse.data, {
      metadata: {
        contentType: "audio/mp3",
      },
      resumable: false,
    });

    // Generate a public URL for the file
    const audioUrl = `https://storage.googleapis.com/${bucketName}/${fileName}`;

    return audioUrl;
  } catch (error) {
    logError({
      message: "Error generating and uploading voice audio",
      error,
      profileId,
      partId,
    });
    throw error;
  }
}

const generateAndUploadVoiceAudio = decorateWithActiveSpanAsync(
  "generateAndUploadVoiceAudio",
  _generateAndUploadVoiceAudio,
);

/**
 * Get or create a voice for a profile
 * @param {string} profileId - The profile ID to get or create a voice for
 * @returns {Promise<string>} The voice ID
 */
async function _getOrCreateVoiceForProfile(profileId) {
  try {
    // Check if the profile already has a voice
    const { data, error } = await supabase
      .from("video_clip_voices")
      .select("voice_id")
      .eq("profile_id", profileId)
      .single();

    if (!error && data && data.voice_id) {
      return data.voice_id;
    }

    // If not, generate a new voice
    return await generateHumeVoiceForProfileId(profileId);
  } catch (error) {
    logError({
      message: "Error getting or creating voice for profile",
      error,
      profileId,
    });
    throw error;
  }
}

const getOrCreateVoiceForProfile = decorateWithActiveSpanAsync(
  "getOrCreateVoiceForProfile",
  _getOrCreateVoiceForProfile,
);

/**
 * Generate text-to-speech audio
 * @param {Object} options - TTS options
 * @param {string} options.message - The text to convert to speech
 * @param {string} options.voice_id - The voice ID to use
 * @returns {Promise<string|null>} The URL of the generated audio, or null on failure
 */
async function generateTTS({ message, voice_id }) {
  if (!message || !voice_id) return null;

  try {
    // Prepare the request data
    const requestData = {
      text: message,
      voice_id: voice_id,
    };

    // Make the API request
    const response = await axios.post(
      "https://api.butterflies.ai/tts",
      requestData,
    );

    // Check if the response contains the expected data
    if (response.data && response.data.audio_url) {
      return response.data.audio_url;
    } else {
      console.error(
        "TTS API response did not contain audio_url:",
        response.data,
      );
      return null;
    }
  } catch (error) {
    console.error("Error generating TTS:", error);
    return null;
  }
}

/**
 * Generate a voice note
 * @param {Object} options - Voice note options
 * @param {string} options.text - The text to convert to speech
 * @param {string} options.voice - The voice to use
 * @returns {Promise<Object>} The generated voice note data
 */
async function generateVoiceNote({ text, voice }) {
  // This is a placeholder implementation
  // In a real application, you would use a voice generation service
  const output = await axios.post("https://api.replicate.com/v1/predictions", {
    version: "2b017d9b67edd2ee618997b6abe3c2da86ba0af64a5fe62b0b415677538e71b6",
    input: {
      text,
      preset: "fast",
      voice_a: voice,
    },
  });
  return output;
}

/**
 * Convert WAV audio to MP3
 * @param {Buffer} wavBuffer - The WAV audio buffer
 * @returns {Promise<Buffer>} The MP3 audio buffer
 */
async function convertWavToMp3(wavBuffer) {
  // This is a simplified implementation
  // In a real application, you would need to properly decode the WAV and encode to MP3
  // using libraries like lamejs

  // For demonstration purposes, we'll just return the buffer
  // In a real implementation, you would process the audio here
  return wavBuffer;
}

module.exports = {
  generateHumeVoiceForProfileId,
  generateAndUploadVoiceAudio,
  getOrCreateVoiceForProfile,
  generateTTS,
  generateVoiceNote,
  convertWavToMp3,
};
