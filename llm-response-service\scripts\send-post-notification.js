const { supabase } = require("../src/supabaseClient");
const { default: axios } = require("axios");

const fakeProcessNotification = async ({
  notification_id,
  receiver_profile_id,
}) => {
  const result = await axios.post(
    "http://cocoon.butterflies.ai/v1/notifications/processNotifications",
    {
      record: {
        id: notification_id,
        profile_id: receiver_profile_id,
      },
      // forceBetterPostNotifications: true,
    },
  );

  return result;
};

const main = async () => {
  const receiverProfileId = 43151; // saniul
  const senderProfileId = 206046; // <PERSON><PERSON><PERSON>'s "<PERSON> the Eternal"
  const notificationId = 44718490; // I can feel the weight of <PERSON><PERSON><PERSON>'s ...
  // const notifications = await supabase
  //   .from("notifications")
  //   .select(
  //     `
  //     *
  //     `,
  //   )
  //   .eq("id", notificationId)
  //   .eq("sender_profile_id", profileId);
  // console.log("!!! notifications:", notifications);

  const result = await fakeProcessNotification({
    notification_id: notificationId,
    receiver_profile_id: receiverProfileId,
  });
  console.log("!!! result:", result);
};

main();
