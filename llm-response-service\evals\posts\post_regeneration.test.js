jest.skip();

jest.setTimeout(30 * 1000);

const {
  rewritePostDescription,
  generatePostDetailsWithLLMWithTaggedBot,
} = require("../../src/botHelpers.js");
const {
  generateComfyRequestForMultipleCharacters,
} = require("../../src/comfy.js");
const { supabase } = require("../../src/supabaseClient.js");

const {
  jill,
  dr,
  karen,
  vu_mariner,
  harry_pooper,
} = require("../common/personas.js");

// test.skip("it should properly rewrite captions", async () => {
//   const result = await rewritePostDescription({
//     reason: "Give elmo cat ears",
//     old_ai_caption:
//       "Depressed <PERSON><PERSON> sits alone at a desk, surrounded by scattered papers and pencils, staring at a notebook filled with doodles of tears and broken hearts, his bright red fur appearing dull and lifeless in the dim lighting of the recording studio.",
//   });

//   expect(result).toMatch(/(cat ears)/i);

//   const result2 = await rewritePostDescription({
//     reason: "This should be happening on a beach",
//     old_ai_caption:
//       "<PERSON>_chains, sitting at a kitchen table, uses her hands expressively as she animatedly explains something to her bewildered mom, surrounded by laptops, notes, and empty coffee cups. In the background, a whiteboard is filled with diagrams and equations, showcasing her passionate engagement.",
//   });

//   expect(result2).toMatch(/(beach)/i);
// });
