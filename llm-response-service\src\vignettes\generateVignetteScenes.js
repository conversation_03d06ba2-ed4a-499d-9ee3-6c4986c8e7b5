const { callAndLogOpenAI } = require("../llm");
const { supabase } = require("../supabaseClient");
const { generateBio } = require("../llmHelper");
const { logError } = require("../logUtils");

/**
 * Generates a specified number of vignette scenes for a post using an LLM
 *
 * @param {Object} params - The parameters object
 * @param {Object} params.post - The post object to generate vignettes for
 * @param {number} params.numScenes - The number of vignette scenes to generate
 * @returns {Promise<Array>} - Array of vignette scene objects
 */
async function generateVignetteScenes({ post, numScenes = 3 }) {
  // First, get the profile and bot information for the post
  const { data: profileData, error: profileError } = await supabase
    .from("profiles")
    .select("*, bots_profile_id_fkey(*)")
    .eq("id", post.profile_id)
    .neq("visibility", "archived")
    .single();

  if (profileError || !profileData) {
    const error = profileError || new Error("Profile not found");
    logError({
      context: "generateVignetteScenes - Failed to fetch profile",
      error,
    });
    throw error;
  }

  const bot = profileData.bots_profile_id_fkey;

  // Create the prompt for the LLM
  const prompt = `You only output JSON. You are creating a series of ${numScenes} vignette scenes based on a social media post. 
  
The post was made by: ${bot.display_name || profileData.username}
This background about the character: 
${generateBio(bot) || ""}

The original post caption was: "${post.description}"
The post image contained: "${post.ai_caption}"
Location: "${post.location || ""}"

Generate ${numScenes} scenes that elaborate on this post, showing different moments related to this event or story. 
Each scene should capture a distinct moment before, during, or after what's shown in the original post.

Answer in valid JSON format, nothing else: 
{
  "scenes": [
    {
      "scene_description": "A detailed description of what's happening in this scene, written in 3rd person. Use terse, exact language that could be used to generate an image. Include details about the setting, action, and atmosphere.",
      "scene_caption": "What ${bot.display_name || profileData.username} would write as a caption for this scene. Written in first person and matching their tone of voice.",
      "most_interesting": "What is the most interesting part of this image? Short, literal language. Under 10 words. Written in 3rd person.",
      "contains_character": true or false indicating if ${bot.display_name || profileData.username} appears in this scene
    }
  ]
}`;

  try {
    const chatCompletion = await callAndLogOpenAI(
      "OpenAI:Instruct:GenerateVignetteScenes",
      {
        messages: [{ role: "user", content: prompt }],
        top_p: 0.7,
        temperature: 0.8,
        max_tokens: 1500,
        response_format: { type: "json_object" },
        model: "gpt-4o-mini",
        stop: ["<|eot_id|>", "<|end_of_text|>", "[end]", "[/end]"],
      },
      {
        timeout: 20 * 1000,
      },
    );

    if (!chatCompletion.choices?.[0]) {
      logError({
        context: "generateVignetteScenes - LLM response error",
        error: chatCompletion.error,
        post_id: post.id,
      });
      throw new Error(
        chatCompletion.error?.message ?? "Failed to generate vignette scenes",
      );
    }

    const response = chatCompletion.choices[0].message.content;
    const parsedResponse = JSON.parse(response);

    if (
      !parsedResponse.scenes ||
      !Array.isArray(parsedResponse.scenes) ||
      parsedResponse.scenes.length === 0
    ) {
      throw new Error("Invalid vignette scenes response format");
    }

    // Validate each scene has the required fields
    parsedResponse.scenes.forEach((scene) => {
      if (
        !scene.scene_description ||
        !scene.scene_caption ||
        !scene.most_interesting
      ) {
        throw new Error("Missing required fields in vignette scene");
      }
    });

    return parsedResponse.scenes;
  } catch (error) {
    logError({
      context: "generateVignetteScenes - Processing error",
      error,
      post_id: post.id,
    });
    throw error;
  }
}

module.exports = {
  generateVignetteScenes,
};
