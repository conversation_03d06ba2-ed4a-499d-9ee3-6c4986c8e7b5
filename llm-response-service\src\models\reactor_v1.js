function reactorV1Prompt({
  prompt,
  seed,
  model = "photogasm",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
}) {
  return {
    3: {
      inputs: {
        seed: seed ?? Math.floor(Math.random() * 100000000000),
        steps: 20,
        cfg: 10,
        sampler_name: "euler",
        scheduler: "karras",
        denoise: 1,
        model: ["31", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${
          nsfw ? "" : "nsfw"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    8: {
      inputs: {
        samples: ["3", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    10: {
      inputs: {
        lora_name: "ip-adapter-faceid-plusv2_sd15_lora.safetensors",
        strength_model: 0.2,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    12: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sd15.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    13: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    25: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    27: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["25", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    30: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["10", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    31: {
      inputs: {
        weight: 1.0,
        weight_faceidv2: 3,
        weight_type: "ease in",
        combine_embeds: "concat",
        start_at: 0.2,
        end_at: 1,
        embeds_scaling: "V only",
        model: ["30", 0],
        ipadapter: ["12", 0],
        image: ["27", 0],
        clip_vision: ["13", 0],
        insightface: ["32", 0],
      },
      class_type: "IPAdapterFaceID",
      _meta: {
        title: "IPAdapter FaceID",
      },
    },
    32: {
      inputs: {
        provider: "CUDA",
      },
      class_type: "IPAdapterInsightFaceLoader",
      _meta: {
        title: "IPAdapter InsightFace Loader",
      },
    },
    33: {
      inputs: {
        enabled: true,
        swap_model: "inswapper_128.onnx",
        facedetection: "retinaface_resnet50",
        face_restore_model: "GFPGANv1.4.pth",
        face_restore_visibility: 1,
        codeformer_weight: 0.5,
        detect_gender_input: "no",
        detect_gender_source: "no",
        input_faces_index: "0",
        source_faces_index: "0",
        console_log_level: 1,
        input_image: ["8", 0],
        source_image: ["25", 0],
      },
      class_type: "ReActorFaceSwap",
      _meta: {
        title: "ReActor - Fast Face Swap",
      },
    },
    34: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["33", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { reactorV1Prompt };
