const { generateComfyRequest } = require("../../comfy");
const { supabase, wrappedSupabaseError } = require("../../supabaseClient");
const { logError, logInfo } = require("../../logUtils");

async function respondWithImageMessage(
  ctx,
  /*      ids= */ { conversationId },
  /* profiles= */ { botConfiguration, botProfile, userProfile },
  /* metadata= */ { branchIndex, deviceType },
  /*  message= */ { description, contains_character, nsfw },
) {
  ctx.startOperation("database");
  const { data: imageGeneration, error: imageGenerationError } = await supabase
    .schema("internal")
    .from("dm_image_generations")
    .insert({
      bot_id: botConfiguration.id,
      // TODO I'm assuming this table is used for debugging, so I'm not providing
      // the following info. They might not even make sense if we make changes to
      // image prompt anyways.
      human_response: "",
      message_context: "",
      ai_response: "",
      image_description: description,
    })
    .select("id")
    .single();
  if (imageGenerationError) {
    logError({
      ...ctx.logging,
      msg: "failed to insert into dm_image_generations",
      error: wrappedSupabaseError(
        imageGenerationError,
        "Failed to insert into dm_image_generations",
      ),
    });
  }
  ctx.endOperation("database");

  ctx.startOperation("database");
  const { data: systemMessage, error: systemMessageError } = await supabase
    .from("messages")
    .insert({
      sender_id: botProfile.id,
      body: description,
      conversation_id: conversationId,
      is_bot: true,
      branch_index: branchIndex,
      is_system_message: true,
    })
    .select("id")
    .single();
  if (systemMessageError) {
    const error = wrappedSupabaseError(
      systemMessageError,
      "Failed to insert image system message",
    );
    logError({
      ...ctx.logging,
      msg: "failed to insert image system message",
      error,
    });
    throw error;
  }

  let is_premium = false;
  if (userProfile?.user_id) {
    const { data: currentPlan, error: currentPlanError } = await supabase
      .from("user_current_plans_view")
      .select("current_plan_id, current_plan_is_active")
      .eq("user_id", userProfile.user_id)
      .maybeSingle();
    if (currentPlanError) {
      const error = wrappedSupabaseError(
        currentPlanError,
        "Failed to fetch user current plan",
      );
      logError({
        ...ctx.logging,
        msg: "failed to fetch user current plan",
        error,
      });
    }
    if (
      currentPlan &&
      currentPlan.current_plan_id === 2 &&
      currentPlan.current_plan_is_active
    ) {
      is_premium = true;
    }
  }

  ctx.endOperation("database");

  const metadata = {
    width: 864,
    height: 1024,
    bot_image: true,
    is_premium,
  };

  ctx.startOperation("database");
  const { data: imageMessage, error: imageMesageError } = await supabase
    .from("messages")
    .insert({
      sender_id: botProfile.id,
      body: null,
      conversation_id: conversationId,
      is_bot: true,
      branch_index: branchIndex,
      is_system_message: false,
      type: "image",
      metadata,
    })
    .select("id")
    .single();
  if (imageMesageError) {
    const error = wrappedSupabaseError(
      imageMesageError,
      "Failed to insert image message",
    );
    logError({
      ...ctx.logging,
      msg: "failed to insert image message",
      error,
    });
    throw error;
  }
  ctx.endOperation("database");

  logInfo({
    ...ctx.logging,
    msg: "Image message created",
    data: {
      imageMessageId: imageMessage?.id,
      systemMessageId: systemMessage?.id,
      nsfw,
      is_premium,
    },
    context: "respondWithImageMessage",
  });

  ctx.startOperation("generate_comfy");
  const task = await generateComfyRequest({
    bot: botConfiguration,
    descriptionOfImage: description,
    generatePhotoMessage_id: undefined,
    generate_urls: undefined,
    imageMessage_id: imageMessage.id,
    systemMessage_id: systemMessage.id,
    generationType: "message",
    device_type: deviceType,
    is_avatar_photo: false,
    priority: "high",
    width: 864,
    height: 1024,
    contains_character: contains_character,
    nsfw,
    is_dm_message: imageGeneration ? true : false,
    is_premium,
  });
  ctx.endOperation("generate_comfy");
  ctx.setComfyTask(task);

  logInfo({
    ...ctx.logging,
    msg: "Comfy task created for image message",
    data: {
      taskId: task?.id,
      imageMessageId: imageMessage?.id,
      systemMessageId: systemMessage?.id,
      nsfw,
      is_premium,
    },
  });

  ctx.startOperation("database");
  const { error: updateImageMessageError } = await supabase
    .from("messages")
    .update({
      metadata: {
        ...metadata,
        task_id: task.id,
        task_name: task.task_name,
        position: -1,
        nsfw,
      },
    })
    .eq("id", imageMessage.id)
    .select("*")
    .single();

  if (updateImageMessageError) {
    const error = wrappedSupabaseError(
      updateImageMessageError,
      "Failed to update image message",
    );
    logError({
      ...ctx.logging,
      msg: "failed to update image message",
      error,
    });
    throw error;
  }

  if (imageGeneration) {
    await supabase
      .schema("internal")
      .from("dm_image_generations")
      .update({
        message_id: imageMessage.id,
      })
      .eq("id", imageGeneration.id);
  }
  ctx.endOperation("database");
}

module.exports = {
  respondWithImageMessage,
};
