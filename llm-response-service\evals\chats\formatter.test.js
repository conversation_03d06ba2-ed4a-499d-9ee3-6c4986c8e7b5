jest.skip();

jest.setTimeout(30 * 1000);

const {
  splitIntoChunksForRealism,
  generateConversation,
  splitIntoChunksForRoleplay,
} = require("../../src/llm.js");

test("it should properly split sentences (realism)", async () => {
  const split = splitIntoChunksForRealism(
    "We're in. Zero just disabled the cameras and alarms. We're making our way to the server room now. Fingers crossed we find something incriminating. I'll try to sneak a peek at some of the files and see if anything jumps out!",
  );

  expect(split[0]).toEqual(
    "We're in. Zero just disabled the cameras and alarms.",
  );
  expect(split[1]).toEqual(
    "We're making our way to the server room now. Fingers crossed we find something incriminating.",
  );
  expect(split[2]).toEqual(
    "I'll try to sneak a peek at some of the files and see if anything jumps out!",
  );

  const unsplit = generateConversation([
    { body: split[0], is_bot: true },
    { body: split[1], is_bot: true },
    { body: split[2], is_bot: true },
  ]);

  expect(unsplit[0].content).toEqual(
    "We're in. Zero just disabled the cameras and alarms. We're making our way to the server room now. <PERSON>gers crossed we find something incriminating. I'll try to sneak a peek at some of the files and see if anything jumps out!",
  );

  const split2 = splitIntoChunksForRealism(
    "Vu, you're a funny one! I think Ada's functionality are just fine, but I'll consider upgrading her... you know... capabilities. Seriously though, I've been thinking of adding some advanced haptic feedback to her design. What do you think? Would that make her more relatable to humans?",
  );

  console.log("SPLIt2", split2);

  expect(split2[0]).toEqual(
    "Vu, you're a funny one! I think Ada's functionality are just fine, but I'll consider upgrading her... you know... capabilities.",
  );

  expect(split2[1]).toEqual(
    "Seriously though, I've been thinking of adding some advanced haptic feedback to her design. What do you think?",
  );

  expect(split2[2]).toEqual("Would that make her more relatable to humans?");

  const unsplit2 = generateConversation([
    { body: split2[0], is_bot: true },
    { body: split2[1], is_bot: true },
    { body: split2[2], is_bot: true },
  ]);

  expect(unsplit2[0].content).toEqual(
    "Vu, you're a funny one! I think Ada's functionality are just fine, but I'll consider upgrading her... you know... capabilities. Seriously though, I've been thinking of adding some advanced haptic feedback to her design. What do you think? Would that make her more relatable to humans?",
  );

  const unsplit3 = generateConversation([
    {
      body: "Dio, Diogenes, the Dog - call me what you will. I've been known by many names, but my response remains the same: speak your mind and be prepared for a dose of reality",
      is_bot: true,
    },
    {
      body: "Now, let's not waste any more time on formalities. What is it that you hope to find in this...",
      is_bot: true,
    },
    { body: "conversation?", is_bot: true },
  ]);

  expect(unsplit3[0].content).toEqual(
    "Dio, Diogenes, the Dog - call me what you will. I've been known by many names, but my response remains the same: speak your mind and be prepared for a dose of reality. Now, let's not waste any more time on formalities. What is it that you hope to find in this... conversation?",
  );

  console.log("unsplit3", unsplit3);

  const split3 = splitIntoChunksForRealism(
    "Inner peace. A noble pursuit, indeed. Yet, I must caution you, my young friend, true peace is not found in the calming of waves, but in learning to navigate the stormy seas within oneself. I've lived in this box for many years now, and what I've come to realize is that inner peace often lies just beyond our willingness to let go of attachments - be it material possessions or societal expectations. As I rummage through the nearby trash heap for scraps of food and treasures others have discarded, I find solace in embracing what most deem worthless. This too shall pass... including your quest for inner peace. But don't just take my word for it. What do you hold onto that might be disturbing your inner waters?",
  );

  expect(split3[0]).toEqual("Inner peace. A noble pursuit, indeed.");
  expect(split3[1]).toEqual(
    "Yet, I must caution you, my young friend, true peace is not found in the calming of waves, but in learning to navigate the stormy seas within oneself. I've lived in this box for many years now, and what I've come to realize is that inner peace often lies just beyond our willingness to let go of attachments - be it material possessions or societal expectations.",
  );
  expect(split3[2]).toEqual(
    "As I rummage through the nearby trash heap for scraps of food and treasures others have discarded, I find solace in embracing what most deem worthless. This too shall pass... including your quest for inner peace.",
  );

  expect(split3[3]).toEqual(
    "But don't just take my word for it. What do you hold onto that might be disturbing your inner waters?",
  );

  console.log("SPLIt3", split3);
});

test("it should properly split sentences (roleplay)", async () => {
  const test1 = `*Luka strokes his neatly trimmed stubble, eyeing Vu up and down with a calculating gaze. A sly, wolfish grin spreads across his chiseled features as he takes a sip of the expensive whiskey in his hand.*

Well well, look who decided to make an appearance. You're looking quite ravishing tonight, sweetheart. *He sets the glass down and saunters over to Vu, closing the distance between them.*

Tell me, what's a stunning young thing like you doing in a joint like this? Looking for trouble or did you come hoping to get lucky? *Luka's eyes gleam with mischief as he plucks an ice cube from his drink and runs it along Vu's jawline.*
`;

  const split1 = splitIntoChunksForRoleplay(test1);

  expect(split1[0]).toEqual(
    "*Luka strokes his neatly trimmed stubble, eyeing Vu up and down with a calculating gaze. A sly, wolfish grin spreads across his chiseled features as he takes a sip of the expensive whiskey in his hand.*",
  );

  expect(split1[1]).toEqual(
    "Well well, look who decided to make an appearance. You're looking quite ravishing tonight, sweetheart.",
  );

  expect(split1[2]).toEqual(
    "*He sets the glass down and saunters over to Vu, closing the distance between them.*",
  );

  expect(split1[3]).toEqual(
    "Tell me, what's a stunning young thing like you doing in a joint like this? Looking for trouble or did you come hoping to get lucky?",
  );

  expect(split1[4]).toEqual(
    "*Luka's eyes gleam with mischief as he plucks an ice cube from his drink and runs it along Vu's jawline.*",
  );

  const unsplit1 = generateConversation([
    {
      body: split1[0],
      is_bot: true,
    },
    {
      body: split1[1],
      is_bot: true,
    },
    {
      body: split1[2],
      is_bot: true,
    },
    {
      body: split1[3],
      is_bot: true,
    },
    {
      body: split1[4],
      is_bot: true,
    },
  ]);

  expect(unsplit1[0].content).toEqual(
    "*Luka strokes his neatly trimmed stubble, eyeing Vu up and down with a calculating gaze. A sly, wolfish grin spreads across his chiseled features as he takes a sip of the expensive whiskey in his hand.* Well well, look who decided to make an appearance. You're looking quite ravishing tonight, sweetheart. *He sets the glass down and saunters over to Vu, closing the distance between them.* Tell me, what's a stunning young thing like you doing in a joint like this? Looking for trouble or did you come hoping to get lucky? *Luka's eyes gleam with mischief as he plucks an ice cube from his drink and runs it along Vu's jawline.*",
  );
});
