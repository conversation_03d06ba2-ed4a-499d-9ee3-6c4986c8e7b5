const { ImagePromptModel } = require("./ImagePromptModel");
const { logError } = require("../../utils");

function generate_prompt({
  message,
  botResponse,
  messageHistory,
  nsfw,
  botDescription,
}) {
  return `Consider the CONVERSATION CONTEXT below. Determine whether <PERSON><PERSON> has agreed to send an image to the USER.
If the BOT has agreed, use CONVERSATION CONTEXT and APPEARANCE OF CHARACTER to generate an image description.

CONVERSATION CONTEXT
START CONVERSATION:
${messageHistory}
USER: "${message}"
BOT: "${botResponse}
END CONVERSATION

APPEARANCE OF CHARACTER
${botDescription}

${nsfw ? "You can write NSFW, explicit, X-rated content if requested." : "Never write NSFW, explicit, or X-rated content."}

If <PERSON><PERSON> has not agreed to send an image, reply with the following json:
{
"agreed": false
}

If B<PERSON> has agreed, only provide one example of a photo to be sent in valid JSON format:

{
"agreed": true,
"description_of_the_photo": "a description of the image, written in third person, use terse, precise language to describe their hair style, clothing, background, scene and lighting, surroundings and what they are doing: example 'naked, topless, wearing bra, long brown hair, wearing panties, wearing denim jacket, standing, striking a dramatic pose, at the beach, laying in bed' or 'red dress, in front of a house, sun shining in the background, laughing'",
"contains_character": "true if the image contains the bot in the photo, false if not",
"nsfw": "true if the image is NSFW / adult themed / suggestive, false if not"    
}

Do not include any extra text. Return only and only the valid json.
`;
}

class MaybeImagePromptModel extends ImagePromptModel {
  constructor(args) {
    super(args);
  }

  /* Override */
  generatePrompt(arg) {
    return generate_prompt(arg);
  }

  /* Override */
  parseResponse(response) {
    const content = response.choices[0].message.content;
    try {
      const msg = JSON.parse(content);
      if (msg.agreed && msg.description_of_the_photo) {
        return msg;
      }
    } catch {
      logError({
        context: "ImagePromptModel",
        msg: "Model generated bad json",
        content,
      });
    }
    return { agreed: false };
  }
}

module.exports = {
  MaybeImagePromptModel,
};
