const { logError, logInfo, wrappedSupabaseError } = require("../utils");
const { supabase } = require("../supabaseClient");
const { processVignetteSceneImage } = require("./processVignetteSceneImage");
const { updatePostVignetteStatus } = require("./processVignetteSceneImage");

async function onVignetteImageGenerationCompleted({
  taskId,
  imageUrls,
  blurhashes,
  payload,
}) {
  const imageUrl = imageUrls[0];
  const blurhash = blurhashes[0];
  const { vignette_scene_id, post_vignette_id, post_id } = payload;
  logInfo({
    context: "onVignetteImageGenerationCompleted",
    taskId,
    vignette_scene_id,
    post_vignette_id,
    post_id,
    imageUrl,
  });

  await processVignetteSceneImage({
    sceneId: vignette_scene_id,
    postVignetteId: post_vignette_id,
    postId: post_id,
    imageUrl,
    blurhash,
  });
}

async function onVignetteImageGenerationFailed({ taskId, error, payload }) {
  const { vignette_scene_id, post_vignette_id, post_id } = payload;

  try {
    logError({
      context: "onVignetteImageGenerationFailed",
      error,
      taskId,
      vignette_scene_id,
      post_vignette_id,
      post_id,
    });

    // 1. Update the scene status to failed_generation
    const { error: updateSceneError } = await supabase
      .from("vignette_scenes")
      .update({
        status: "failed_generation",
      })
      .eq("id", vignette_scene_id);

    if (updateSceneError) {
      const error = wrappedSupabaseError(updateSceneError);
      logError({
        context:
          "onVignetteImageGenerationFailed - Failed to update scene status",
        error,
        vignette_scene_id,
      });
    }
    logInfo({
      context: "onVignetteImageGenerationFailed",
      message: `Updated vignette scene ${vignette_scene_id} status to failed_generation`,
      vignette_scene_id,
    });

    // 2. Check if all scenes for this post_vignette have been processed and update the status
    await updatePostVignetteStatus(post_vignette_id);
  } catch (processingError) {
    logError({
      context:
        "onVignetteImageGenerationFailed - Error during failure processing",
      error: processingError,
      original_error: error,
      taskId,
      vignette_scene_id,
      post_vignette_id,
    });
  }
}

module.exports = {
  onVignetteImageGenerationCompleted,
  onVignetteImageGenerationFailed,
};
