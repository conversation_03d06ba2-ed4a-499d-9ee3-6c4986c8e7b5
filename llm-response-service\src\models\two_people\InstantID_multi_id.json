{"last_node_id": 92, "last_link_id": 290, "nodes": [{"id": 15, "type": "PreviewImage", "pos": [2160, -150], "size": {"0": 584.0855712890625, "1": 610.4592895507812}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 19}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 8, "type": "VAEDecode", "pos": [2170, -270], "size": {"0": 210, "1": 46}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 254}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [19], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 81, "type": "Reroute", "pos": [1980, 120], "size": [75, 26], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 253}], "outputs": [{"name": "VAE", "type": "VAE", "links": [254], "slot_index": 0}], "properties": {"showOutputText": true, "horizontal": false}}, {"id": 38, "type": "InstantIDFaceAnalysis", "pos": [-210, -40], "size": [210, 60], "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [198, 239], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis"}, "widgets_values": ["CPU"]}, {"id": 16, "type": "ControlNetLoader", "pos": [-210, 70], "size": [210, 60], "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [199, 240], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["instantid/diffusion_pytorch_model.safetensors"]}, {"id": 79, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [1410, -190], "size": [228.39999389648438, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 247}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 248}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [249], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}}, {"id": 84, "type": "ImageFlip+", "pos": [990, -210], "size": {"0": 315, "1": 58}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 258}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [259], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageFlip+"}, "widgets_values": ["x"]}, {"id": 13, "type": "LoadImage", "pos": [715, 35], "size": [213.36950471073226, 296.38119750842566], "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [214], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["face4.jpg", "image"]}, {"id": 88, "type": "MaskFlip+", "pos": [990, -110], "size": {"0": 315, "1": 58}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 263}], "outputs": [{"name": "MASK", "type": "MASK", "links": [264], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskFlip+"}, "widgets_values": ["x"]}, {"id": 78, "type": "LoadImage", "pos": [714, -512], "size": [210, 314], "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [246], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["joseph-gonzalez-iFgRcqHznqg-unsplash.jpg", "image"]}, {"id": 85, "type": "SolidMask", "pos": [970, 510], "size": [210, 106], "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "MASK", "type": "MASK", "links": [260], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidMask"}, "widgets_values": [0, 1280, 960]}, {"id": 11, "type": "InstantIDModelLoader", "pos": [-210, -150], "size": [210, 60], "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [197, 238], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "InstantIDModelLoader"}, "widgets_values": ["ip-adapter.bin"]}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": [-312, 198], "size": {"0": 315, "1": 98}, "flags": {}, "order": 6, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [206], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [122, 123, 266], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [253], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/AlbedoBaseXL.safetensors"]}, {"id": 87, "type": "MaskComposite", "pos": [1232, 583], "size": [210, 126], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "destination", "type": "MASK", "link": 260}, {"name": "source", "type": "MASK", "link": 261}], "outputs": [{"name": "MASK", "type": "MASK", "links": [262, 263], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskComposite"}, "widgets_values": [0, 0, "add"]}, {"id": 86, "type": "SolidMask", "pos": [970, 660], "size": {"0": 210, "1": 106}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "MASK", "type": "MASK", "links": [261], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "SolidMask"}, "widgets_values": [1, 640, 960]}, {"id": 82, "type": "LoadImage", "pos": [591, 511], "size": [315, 314.0000190734863], "flags": {}, "order": 8, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [257, 258], "shape": 3, "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pose (1).jpg", "image"]}, {"id": 40, "type": "CLIPTextEncode", "pos": [146, 487], "size": {"0": 286.3603515625, "1": 112.35245513916016}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 123}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [204, 278], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["photograph, deformed, glitch, noisy, realistic, stock photo, naked"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "EmptyLatentImage", "pos": [1431, 20], "size": [210, 106], "flags": {}, "order": 9, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1280, 960, 1]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [1730, -180], "size": {"0": 315, "1": 262}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 256}, {"name": "positive", "type": "CONDITIONING", "link": 249}, {"name": "negative", "type": "CONDITIONING", "link": 288}, {"name": "latent_image", "type": "LATENT", "link": 2}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [1631594039, "fixed", 30, 4.5, "ddpm", "normal", 1]}, {"id": 80, "type": "Conditioning<PERSON><PERSON><PERSON>", "pos": [1410, -90], "size": {"0": 228.39999389648438, "1": 46}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "CONDITIONING", "link": 290}, {"name": "conditioning_2", "type": "CONDITIONING", "link": 287}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [288], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Conditioning<PERSON><PERSON><PERSON>"}}, {"id": 77, "type": "ApplyInstantID", "pos": [990, -528], "size": {"0": 315, "1": 266}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 238}, {"name": "insightface", "type": "FACEANALYSIS", "link": 239}, {"name": "control_net", "type": "CONTROL_NET", "link": 240}, {"name": "image", "type": "IMAGE", "link": 246}, {"name": "model", "type": "MODEL", "link": 255}, {"name": "positive", "type": "CONDITIONING", "link": 272}, {"name": "negative", "type": "CONDITIONING", "link": 278}, {"name": "image_kps", "type": "IMAGE", "link": 259}, {"name": "mask", "type": "MASK", "link": 264}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [256], "shape": 3, "slot_index": 0}, {"name": "POSITIVE", "type": "CONDITIONING", "links": [247], "shape": 3, "slot_index": 1}, {"name": "NEGATIVE", "type": "CONDITIONING", "links": [290], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "ApplyInstantID"}, "widgets_values": [0.8, 0, 1]}, {"id": 60, "type": "ApplyInstantID", "pos": [991, 73], "size": {"0": 315, "1": 266}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 197}, {"name": "insightface", "type": "FACEANALYSIS", "link": 198}, {"name": "control_net", "type": "CONTROL_NET", "link": 199}, {"name": "image", "type": "IMAGE", "link": 214}, {"name": "model", "type": "MODEL", "link": 206}, {"name": "positive", "type": "CONDITIONING", "link": 203}, {"name": "negative", "type": "CONDITIONING", "link": 204}, {"name": "image_kps", "type": "IMAGE", "link": 257}, {"name": "mask", "type": "MASK", "link": 262}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [255], "shape": 3, "slot_index": 0}, {"name": "POSITIVE", "type": "CONDITIONING", "links": [248], "shape": 3, "slot_index": 1}, {"name": "NEGATIVE", "type": "CONDITIONING", "links": [287], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "ApplyInstantID"}, "widgets_values": [0.9, 0, 1]}, {"id": 89, "type": "CLIPTextEncode", "pos": [314, -421], "size": {"0": 291.9967346191406, "1": 128.62518310546875}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 266}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [272], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["comic male character. graphic illustration, comic art, graphic novel art, vibrant, highly detailed. New York background"], "color": "#232", "bgcolor": "#353"}, {"id": 39, "type": "CLIPTextEncode", "pos": [309, 171], "size": {"0": 291.9967346191406, "1": 128.62518310546875}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 122}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [203], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["comic female character. graphic illustration, comic art, graphic novel art, vibrant, highly detailed. New York background"], "color": "#232", "bgcolor": "#353"}], "links": [[2, 5, 0, 3, 3, "LATENT"], [7, 3, 0, 8, 0, "LATENT"], [19, 8, 0, 15, 0, "IMAGE"], [122, 4, 1, 39, 0, "CLIP"], [123, 4, 1, 40, 0, "CLIP"], [197, 11, 0, 60, 0, "INSTANTID"], [198, 38, 0, 60, 1, "FACEANALYSIS"], [199, 16, 0, 60, 2, "CONTROL_NET"], [203, 39, 0, 60, 5, "CONDITIONING"], [204, 40, 0, 60, 6, "CONDITIONING"], [206, 4, 0, 60, 4, "MODEL"], [214, 13, 0, 60, 3, "IMAGE"], [238, 11, 0, 77, 0, "INSTANTID"], [239, 38, 0, 77, 1, "FACEANALYSIS"], [240, 16, 0, 77, 2, "CONTROL_NET"], [246, 78, 0, 77, 3, "IMAGE"], [247, 77, 1, 79, 0, "CONDITIONING"], [248, 60, 1, 79, 1, "CONDITIONING"], [249, 79, 0, 3, 1, "CONDITIONING"], [253, 4, 2, 81, 0, "*"], [254, 81, 0, 8, 1, "VAE"], [255, 60, 0, 77, 4, "MODEL"], [256, 77, 0, 3, 0, "MODEL"], [257, 82, 0, 60, 7, "IMAGE"], [258, 82, 0, 84, 0, "IMAGE"], [259, 84, 0, 77, 7, "IMAGE"], [260, 85, 0, 87, 0, "MASK"], [261, 86, 0, 87, 1, "MASK"], [262, 87, 0, 60, 8, "MASK"], [263, 87, 0, 88, 0, "MASK"], [264, 88, 0, 77, 8, "MASK"], [266, 4, 1, 89, 0, "CLIP"], [272, 89, 0, 77, 5, "CONDITIONING"], [278, 40, 0, 77, 6, "CONDITIONING"], [287, 60, 2, 80, 1, "CONDITIONING"], [288, 80, 0, 3, 2, "CONDITIONING"], [290, 77, 2, 80, 0, "CONDITIONING"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}