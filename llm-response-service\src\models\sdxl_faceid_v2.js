function sdxlFaceIdV2Prompt({
  prompt,
  appearancePrompt,
  seed,
  model = "realvisxlV40_v40LightningBakedvae",
  width = 864,
  height = 1024,
  faceImageData,
  batch_size = 1,
  nsfw = false,
  steps = 5,
  cfg = 1.5,
  faceSteps = 3,
  faceCfg = 1.5,
  faceDenoise = 0.4,
  sampler_name = "dpmpp_sde",
  scheduler = "karras",
  badquality = 0,
  blurxl = 0,
  envyzoomslider = 0,
}) {
  seed = seed ?? Math.floor(Math.random() * 100000000000);

  return {
    4: {
      inputs: {
        ckpt_name: `${model}.safetensors`,
      },
      class_type: "CheckpointLoaderSimple",
      _meta: {
        title: "Load Checkpoint",
      },
    },
    5: {
      inputs: {
        width,
        height,
        batch_size,
      },
      class_type: "EmptyLatentImage",
      _meta: {
        title: "Empty Latent Image",
      },
    },
    6: {
      inputs: {
        text: prompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    7: {
      inputs: {
        text: `(worst quality), (low quality), (normal quality), lowres, normal quality, ${
          nsfw ? "" : ", (nudity, nsfw, naked)"
        }`,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    11: {
      inputs: {
        interpolation: "LANCZOS",
        crop_position: "center",
        sharpening: 0,
        image: ["64", 0],
      },
      class_type: "PrepImageForClipVision",
      _meta: {
        title: "Prep Image For ClipVision",
      },
    },
    33: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    39: {
      inputs: {
        ipadapter_file: "ip-adapter-faceid-plusv2_sdxl.bin",
      },
      class_type: "IPAdapterModelLoader",
      _meta: {
        title: "IPAdapter Model Loader",
      },
    },
    40: {
      inputs: {
        provider: "CPU",
        model_name: "buffalo_l",
      },
      class_type: "IPAdapterInsightFaceLoader",
      _meta: {
        title: "IPAdapter InsightFace Loader",
      },
    },
    41: {
      inputs: {
        clip_name: "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors",
      },
      class_type: "CLIPVisionLoader",
      _meta: {
        title: "Load CLIP Vision",
      },
    },
    50: {
      inputs: {
        lora_name: "NSFWFilter.safetensors",
        strength_model: nsfw ? 0 : -1,
        model: ["4", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    60: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["33", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    61: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["60", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    64: {
      inputs: {
        image: "#DATA",
        image_data: faceImageData,
        upload: "image",
      },
      class_type: "LoadImage //Inspire",
      _meta: {
        title: "Load Image (Inspire)",
      },
    },
    66: {
      inputs: {
        lora_name: "badquality.safetensors",
        strength_model: badquality,
        model: ["50", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    67: {
      inputs: {
        lora_name: "blurxl.safetensors",
        strength_model: blurxl,
        model: ["66", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    68: {
      inputs: {
        lora_name: "envyzoomslider.safetensors",
        strength_model: envyzoomslider,
        model: ["67", 0],
      },
      class_type: "LoraLoaderModelOnly",
      _meta: {
        title: "LoraLoaderModelOnly",
      },
    },
    70: {
      inputs: {
        seed,
        steps,
        cfg,
        sampler_name,
        scheduler,
        denoise: 1,
        model: ["68", 0],
        positive: ["6", 0],
        negative: ["7", 0],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    72: {
      inputs: {
        samples: ["70", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    78: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["81", 0],
        image: ["72", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    81: {
      inputs: {
        model_name: "segm/person_yolov8m-seg.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    82: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["78", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    83: {
      inputs: {
        segs: ["82", 0],
      },
      class_type: "SegsToCombinedMask",
      _meta: {
        title: "SEGS to MASK (combined)",
      },
    },
    86: {
      inputs: {
        detection_hint: "center-1",
        dilation: 0,
        threshold: 0.93,
        bbox_expansion: 0,
        mask_hint_threshold: 0.7,
        mask_hint_use_negative: "False",
        sam_model: ["87", 0],
        segs: ["82", 0],
        image: ["72", 0],
      },
      class_type: "SAMDetectorSegmented",
      _meta: {
        title: "SAMDetector (segmented)",
      },
    },
    87: {
      inputs: {
        model_name: "sam_vit_b_01ec64.pth",
        device_mode: "AUTO",
      },
      class_type: "SAMLoader",
      _meta: {
        title: "SAMLoader (Impact)",
      },
    },
    88: {
      inputs: {
        mask: ["86", 0],
      },
      class_type: "MaskToImage",
      _meta: {
        title: "Convert Mask to Image",
      },
    },
    90: {
      inputs: {
        strength: 1,
        set_cond_area: "mask bounds",
        conditioning: ["6", 0],
        mask: ["115", 0],
      },
      class_type: "ConditioningSetMask",
      _meta: {
        title: "Conditioning (Set Mask)",
      },
    },
    92: {
      inputs: {
        strength: 1,
        set_cond_area: "mask bounds",
        conditioning: ["94", 0],
        mask: ["114", 0],
      },
      class_type: "ConditioningSetMask",
      _meta: {
        title: "Conditioning (Set Mask)",
      },
    },
    94: {
      inputs: {
        text: appearancePrompt,
        clip: ["4", 1],
      },
      class_type: "CLIPTextEncode",
      _meta: {
        title: "CLIP Text Encode (Prompt)",
      },
    },
    95: {
      inputs: {
        provider: "CPU",
      },
      class_type: "InstantIDFaceAnalysis",
      _meta: {
        title: "InstantID Face Analysis",
      },
    },
    96: {
      inputs: {
        ip_weight: 0.8,
        cn_strength: 0.8,
        start_at: 0.5,
        end_at: 1,
        noise: 0,
        combine_embeds: "concat",
        instantid: ["97", 0],
        insightface: ["95", 0],
        control_net: ["98", 0],
        image: ["11", 0],
        model: ["68", 0],
        positive: ["107", 0],
        negative: ["107", 1],
        image_kps: ["101", 0],
        mask: ["114", 0],
      },
      class_type: "ApplyInstantIDAdvanced",
      _meta: {
        title: "Apply InstantID Advanced",
      },
    },
    97: {
      inputs: {
        instantid_file: "ip-adapter.bin",
      },
      class_type: "InstantIDModelLoader",
      _meta: {
        title: "Load InstantID Model",
      },
    },
    98: {
      inputs: {
        control_net_name: "diffusion_pytorch_model.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    101: {
      inputs: {
        x: 0,
        y: 0,
        resize_source: false,
        destination: ["72", 0],
        source: ["88", 0],
        mask: ["116", 0],
      },
      class_type: "ImageCompositeMasked",
      _meta: {
        title: "ImageCompositeMasked",
      },
    },
    103: {
      inputs: {
        seed,
        steps: 5,
        cfg: 1.5,
        sampler_name: "dpmpp_sde",
        scheduler: "karras",
        denoise: 1,
        model: ["96", 0],
        positive: ["118", 0],
        negative: ["96", 2],
        latent_image: ["5", 0],
      },
      class_type: "KSampler",
      _meta: {
        title: "KSampler",
      },
    },
    104: {
      inputs: {
        samples: ["103", 0],
        vae: ["4", 2],
      },
      class_type: "VAEDecode",
      _meta: {
        title: "VAE Decode",
      },
    },
    106: {
      inputs: {
        detect_hand: "disable",
        detect_body: "enable",
        detect_face: "enable",
        resolution: 512,
        image: ["72", 0],
      },
      class_type: "OpenposePreprocessor",
      _meta: {
        title: "OpenPose Pose",
      },
    },
    107: {
      inputs: {
        strength: 1,
        start_percent: 0,
        end_percent: 0.5,
        positive: ["92", 0],
        negative: ["7", 0],
        control_net: ["108", 0],
        image: ["106", 0],
        vae: ["4", 2],
      },
      class_type: "ControlNetApplyAdvanced",
      _meta: {
        title: "Apply ControlNet",
      },
    },
    108: {
      inputs: {
        control_net_name: "OpenPoseXL2.safetensors",
      },
      class_type: "ControlNetLoader",
      _meta: {
        title: "Load ControlNet Model",
      },
    },
    113: {
      inputs: {
        expand: 32,
        tapered_corners: true,
        mask: ["86", 0],
      },
      class_type: "GrowMask",
      _meta: {
        title: "GrowMask",
      },
    },
    114: {
      inputs: {
        amount: 16,
        device: "auto",
        mask: ["113", 0],
      },
      class_type: "MaskBlur+",
      _meta: {
        title: "🔧 Mask Blur",
      },
    },
    115: {
      inputs: {
        mask: ["114", 0],
      },
      class_type: "InvertMask",
      _meta: {
        title: "InvertMask",
      },
    },
    116: {
      inputs: {
        mask: ["86", 0],
      },
      class_type: "InvertMask",
      _meta: {
        title: "InvertMask",
      },
    },
    118: {
      inputs: {
        conditioning_1: ["96", 1],
        conditioning_2: ["90", 0],
      },
      class_type: "ConditioningCombine",
      _meta: {
        title: "Conditioning (Combine)",
      },
    },
    133: {
      inputs: {
        target: "area(=w*h)",
        order: true,
        take_start: 0,
        take_count: 1,
        segs: ["137", 0],
      },
      class_type: "ImpactSEGSOrderedFilter",
      _meta: {
        title: "SEGS Filter (ordered)",
      },
    },
    137: {
      inputs: {
        bbox_threshold: 0.5,
        bbox_dilation: 0,
        crop_factor: 3,
        drop_size: 10,
        sub_threshold: 0.5,
        sub_dilation: 0,
        sub_bbox_expansion: 0,
        sam_mask_hint_threshold: 0.7,
        post_dilation: 0,
        bbox_detector: ["138", 0],
        image: ["104", 0],
      },
      class_type: "ImpactSimpleDetectorSEGS",
      _meta: {
        title: "Simple Detector (SEGS)",
      },
    },
    138: {
      inputs: {
        model_name: "bbox/face_yolov8m.pt",
      },
      class_type: "UltralyticsDetectorProvider",
      _meta: {
        title: "UltralyticsDetectorProvider",
      },
    },
    141: {
      inputs: {
        guide_size: 384,
        guide_size_for: true,
        max_size: 1024,
        seed,
        steps: 5,
        cfg: 1.5,
        sampler_name: "dpmpp_sde",
        scheduler: "karras",
        denoise: 0.5,
        feather: 5,
        noise_mask: true,
        force_inpaint: true,
        wildcard: "",
        cycle: 1,
        inpaint_model: false,
        noise_mask_feather: 20,
        image: ["104", 0],
        segs: ["133", 0],
        model: ["96", 0],
        clip: ["4", 1],
        vae: ["4", 2],
        positive: ["118", 0],
        negative: ["96", 2],
      },
      class_type: "DetailerForEach",
      _meta: {
        title: "Detailer (SEGS)",
      },
    },
    144: {
      inputs: {
        filename_prefix: "ComfyUI",
        images: ["141", 0],
      },
      class_type: "SaveImage",
      _meta: {
        title: "Save Image",
      },
    },
  };
}

module.exports = { sdxlFaceIdV2Prompt };
