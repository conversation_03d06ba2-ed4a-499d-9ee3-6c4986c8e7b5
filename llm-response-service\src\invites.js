const express = require("express");
const app = express.Router();
const { supabase } = require("./supabaseClient");
const { logError, wrappedSupabaseError, checkAdminValid } = require("./utils");
const { doFollow } = require("./root");
const { authUser } = require("./middleware");
const {
  REDEMPTION_VALUE_PREMIUM_DAYS,
  REDEMPTION_VALUE_EXTRA_QUOTA,
  REDEMPTION_TYPE,
  REDEMPTION_COUNT_LIMIT,
  PACKAGE_TYPE,
} = require("./constants");
require("dotenv").config();

const { mixpanelTrackReq } = require("./mixpanel");

async function generateUniquePassphraseFromUser(user_id) {
  try {
    // Query the user's profile to get the username
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("username")
      .neq("visibility", "archived")
      .eq("user_id", user_id)
      .limit(1)
      .single();

    if (profileError) {
      throw wrappedSupabaseError(profileError, "Failed to query profile");
    }

    const username = profile?.username;
    if (!username) {
      throw new Error("No username found for the given user_id");
    }

    let code = username;
    let uniqueCode;

    while (true) {
      // Check for collision
      const { data: existingCode, error: inviteCodesError } = await supabase
        .from("invite_codes")
        .select("id")
        .eq("code", code)
        .maybeSingle();

      if (inviteCodesError) {
        throw wrappedSupabaseError(
          inviteCodesError,
          "Failed to query invite codes",
        );
      }

      if (!existingCode) {
        uniqueCode = code;
        break;
      }

      // Append random digits if a collision is found
      code = `${username}${Math.floor(Math.random() * 10000)}`;
    }

    return uniqueCode;
  } catch (error) {
    logError({
      context: "generateUniquePassphraseFromUser",
      error,
      user_id,
    });
    throw error; // Re-throw to handle at a higher level if needed
  }
}

async function generateInviteCodeIfNeeded({ user_id }) {
  try {
    // Check for an existing invite code
    const { data: inviteCode, error: queryInviteCodesError } = await supabase
      .from("invite_codes")
      .select("code")
      .eq("user_id", user_id)
      .maybeSingle();

    if (queryInviteCodesError) {
      throw wrappedSupabaseError(
        queryInviteCodesError,
        "Failed to query invite_codes",
      );
    }

    if (inviteCode?.code) {
      return inviteCode.code;
    }

    // Generate a new unique passphrase
    const code = await generateUniquePassphraseFromUser(user_id);

    // Attempt to insert the new invite code
    const { data, error: insertCodeError } = await supabase
      .from("invite_codes")
      .insert({ code, user_id })
      .select("code")
      .single();

    if (insertCodeError) {
      if (insertCodeError.code === "23505") {
        // Unique violation error code, try again
        return await generateInviteCodeIfNeeded({ user_id });
      }
      throw wrappedSupabaseError(
        insertCodeError,
        "Failed to insert into invite_codes",
      );
    }

    if (!data) {
      throw new Error("Failed to insert code into invite_codes");
    }

    return data.code;
  } catch (error) {
    logError({
      context: "generateInviteCodeIfNeeded",
      error,
      user_id,
    });
    throw error; // Re-throw to handle at a higher level if needed
  }
}

app.get("/ping", async (req, res) => {
  return res.send("invites ping");
});

app.post("/createInvite", authUser, async (req, res) => {
  const user_id = req.user?.id;
  try {
    const code = await generateInviteCodeIfNeeded({ user_id });
    mixpanelTrackReq(user_id, "invite_code_created", { code }, req);
    return res.json({ code });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "/createInvite: failed to generate invite code",
      error,
      user_id: user_id,
    });
    return res.sendStatus(500);
  }
});

// TODO: Refactor to throw most of these errors instead of returning them.
// We are not reading inspecting them at the call-site and for most of them we don't want to
// leak the error details to the client.
async function redeemInviteCode({ code, type, post_id, user_id }) {
  const args = { code, type, post_id, user_id };

  // check if exists
  const { data, error: queryInviteCodesError } = await supabase
    .from("invite_codes")
    .select("id, user_id")
    .eq("code", code)
    .maybeSingle();

  if (queryInviteCodesError) {
    const error = wrappedSupabaseError(queryInviteCodesError);
    logError({
      context: "redeemInviteCode: failed to query invite_codes",
      error: error,
      ...args,
    });
    throw error;
  }

  if (!data) {
    const error = new Error("Invite code invalid");
    logError({
      context: "redeemInviteCode: invalid invite code",
      error,
      ...args,
    });
    return { error: error };
  }

  // check if already redeemed by a user
  const { data: userRedemed, error: userRedemedError } = await supabase
    .from("user_redemptions")
    .select("id")
    .eq("user_id", user_id);

  if (!userRedemedError && userRedemed.length > 0) {
    return { error: new Error("User already redeemed an invite") };
  }

  // check how many redemeptions
  const { data: userRedemption, error: userRedemptionError } = await supabase
    .from("user_redemptions")
    .select("id")
    .eq("user_id", data.user_id)
    .in("type", [REDEMPTION_TYPE.INVITER, REDEMPTION_TYPE.POST_SHARER]);

  if (userRedemptionError) {
    const error = wrappedSupabaseError(userRedemptionError);
    logError({
      context: "**** userRedemptionError",
      error,
      ...args,
    });
    throw error;
  }

  if (userRedemption.length > REDEMPTION_COUNT_LIMIT) {
    // Too many redemptions
    return { error: new Error("Too many redemptions") };
  }

  // add check abuse
  const { data: userData } = await supabase
    .from("users")
    .select("created_at")
    .eq("id", user_id)
    .single();

  //check 10min
  if (
    new Date().valueOf() - 600 * 1000 >
    new Date(userData?.created_at).valueOf()
  ) {
    return { error: new Error("prevent abuse") };
  }

  const { data: inviterProfiles, error: inviterProfileError } = await supabase
    .from("profiles")
    .select("id, username, user_id, avatar_url")
    .eq("user_id", data?.user_id)
    .neq("visibility", "archived")
    .order("id", { ascending: true });

  const { data: inviteeProfiles, error: inviteeProfileError } = await supabase
    .from("profiles")
    .select("id, username, user_id, avatar_url")
    .eq("user_id", user_id)
    .neq("visibility", "archived")
    .order("id", { ascending: true });

  if (inviterProfileError) {
    const error = wrappedSupabaseError(inviterProfileError);
    logError({
      context: "redeemInviteCode: failed to fetch a profile for inviter user",
      error,
      user_id: data?.user_id,
    });
    throw error;
  }

  if (inviteeProfileError) {
    const error = wrappedSupabaseError(inviteeProfileError);
    logError({
      context: "redeemInviteCode: failed to fetch a profile for invitee user",
      error,
      user_id: user_id,
    });
    throw error;
  }

  let redemptionData, redemptionError;
  if (type === REDEMPTION_TYPE.INVITE) {
    const inviter = {
      user_id: data.user_id,
      type: REDEMPTION_TYPE.INVITER,
      description: `You invited a user, ${inviteeProfiles[0].username}. You received ${REDEMPTION_VALUE_PREMIUM_DAYS[type]} days of Plus for free`,
      metadata: {
        invitee: inviteeProfiles[0],
        value: REDEMPTION_VALUE_PREMIUM_DAYS[type],
        invite_code_id: data.id,
      },
    };

    const invitee = {
      user_id: user_id,
      type: REDEMPTION_TYPE.INVITEE,
      description: `You are invited by ${inviterProfiles[0].username}. You received ${REDEMPTION_VALUE_PREMIUM_DAYS[type]} days of Plus for free`,
      metadata: {
        inviter: inviterProfiles[0],
        value: REDEMPTION_VALUE_PREMIUM_DAYS[type],
        invite_code_id: data.id,
      },
    };

    ({ data: redemptionData, error: redemptionError } = await supabase
      .from("user_redemptions")
      .insert([inviter, invitee])
      .select("id, user_id, type"));

    if (!redemptionError) {
      // follow each other
      const inviteeProfileId = inviteeProfiles[0]?.id;
      const inviterProfileId = inviterProfiles[0]?.id;
      const { error: followInviterError } = await doFollow({
        following_id: inviteeProfileId,
        follower_id: inviterProfileId,
      });
      if (followInviterError) {
        const error = wrappedSupabaseError(followInviterError);
        logError({
          context: "redeemInviteCode: failed to doFollow",
          error,
          following_id: inviteeProfileId,
          follower_id: inviterProfileId,
          ...args,
        });
      }

      const { error: followInviteeError } = await doFollow({
        following_id: inviterProfileId,
        follower_id: inviteeProfileId,
        ...args,
      });
      if (followInviteeError) {
        const error = wrappedSupabaseError(followInviteeError);
        logError({
          context: "redeemInviteCode: failed to doFollow",
          error,
          following_id: inviterProfileId,
          follower_id: inviteeProfileId,
          ...args,
        });
      }

      //invite accept notification
      const { error: insertNotificationError } = await supabase
        .from("notifications")
        .insert({
          profile_id: inviterProfiles[0]?.id,
          source_type: "invite_accepted",
          source_id: redemptionData[0]?.id,
          title: "accepted your invite",
          text: "",
          path: `/users/${inviteeProfiles[0]?.username}`,
          sender_profile_id: inviteeProfiles[0]?.id,
        });
      if (insertNotificationError) {
        const error = wrappedSupabaseError(insertNotificationError);
        logError({
          context: "redeemInviteCode: failed to insert notification record",
          error,
          ...args,
        });
      }
    }
  } else if (type === REDEMPTION_TYPE.POST_SHARE) {
    const { data: post, error: postError } = await supabase
      .from("posts")
      .select(
        "id, media_url, slug, profiles(id, username, user_id, avatar_url)",
      )
      .eq("id", post_id)
      .neq("visibility", "archived")
      .single();

    if (postError) {
      const error = wrappedSupabaseError(postError);
      logError({
        context: "redeemInviteCode: failed query post info",
        error,
        ...args,
      });
    }
    const inviter = {
      user_id: data.user_id,
      type: REDEMPTION_TYPE.POST_SHARER,
      description: `You received ${REDEMPTION_VALUE_PREMIUM_DAYS[type]} days of Plus for free by sharing a post to ${inviteeProfiles[0]?.username}`,
      metadata: {
        invitee: inviteeProfiles[0],
        value: REDEMPTION_VALUE_PREMIUM_DAYS[type],
        invite_code_id: data.id,
        post: post,
      },
    };

    // TODO: apply redemption to the invitee too
    // const invitee = {
    //   user_id: user_id,
    //   type: REDEMPTION_TYPE.POST_SHAREE,
    //   description: `You received ${REDEMPTION_VALUE_PREMIUM_DAYS[type]} days of Plus for free by accepting invitation from a post shared by ${inviterProfiles[0].username}`,
    //   metadata: {
    //     inviter: inviterProfiles[0],
    //     value: REDEMPTION_VALUE_PREMIUM_DAYS[type],
    //     invite_code_id: data.id,
    //     post: post,
    //   },
    // };

    ({ data: redemptionData, error: redemptionError } = await supabase
      .from("user_redemptions")
      .insert([inviter])
      .select("id, user_id, type"));
  }

  if (redemptionError) {
    const error = wrappedSupabaseError(redemptionError);
    logError({
      context: "*** redemptionError",
      error,
      ...args,
    });
    return { error: new Error("failed to insert invite redemptions") };
  }

  const { error: extraQuotaError } = await supabase.rpc(
    "func_add_extra_quota",
    {
      records: [
        {
          user_id: data?.user_id, // add extra quota to inviter
          package_id: PACKAGE_TYPE.POKES, // poke
          value: REDEMPTION_VALUE_EXTRA_QUOTA[type] ?? 0,
        },
        {
          user_id: data?.user_id, // add extra quota to inviter
          package_id: PACKAGE_TYPE.IMAGE_IN_CHAT, // image in chat
          value: REDEMPTION_VALUE_EXTRA_QUOTA[type] ?? 0,
        },
      ],
    },
  );

  if (extraQuotaError) {
    const error = wrappedSupabaseError(extraQuotaError);
    logError({
      context: "**** extraQuota error",
      error,
      ...args,
    });
  }

  return { success: true };
}

/// Function to insert user to "Waitlist"
async function insertWaitlist({ user_id, loopCount = 0 }) {
  if (loopCount >= 3) {
    return { data: null, error: "insertion exceeds" };
  }

  const { data, error } = await supabase
    .from("waitlist")
    .insert({
      user_id,
    })
    .select("*")
    .single();

  if (!data || error) {
    await insertWaitlist({ user_id, loopCount: loopCount + 1 });
  }

  return { data, error };
}

/// Function to read user from "Waitlist"
async function readWaitlist({ user_id }) {
  const { data, error } = await supabase
    .from("waitlist")
    .select("*")
    .eq("user_id", user_id);

  return { data, error };
}

/// Function to update user on "Waitlist"
async function updateWaitlist({ rank }) {
  const { data, error } = await supabase
    .from("waitlist")
    .delete()
    .order("id", { ascending: true })
    .limit(rank)
    .select("*");

  if (error) {
    const waitlistError = wrappedSupabaseError(error);
    throw waitlistError;
  }

  data.map(async (i) => {
    /// Send emails to all of the users who are now being let in
    const { data: userResp, error } = await supabase.auth.admin.getUserById(
      i.user_id,
    );

    if (!error && userResp) {
      // FIXME: send email to people who are being released from the waitlist
    }
  });

  return { message: "success", error: null };
}

app.post("/updateWaitlist", authUser, async (req, res) => {
  const { rank } = req.body;

  if (rank < 0 || !rank) {
    return res.status(400).send({ message: "Count is less than 0 or null" });
  }

  const user_id = req.user?.id;
  const isAllowed = await checkAdminValid(user_id);
  if (!isAllowed) {
    return res.status(403).send({ error: "Forbidden" });
  }

  try {
    await updateWaitlist({ rank });
    return res.status(200).send({ message: "Success" });
  } catch (error) {
    logError({
      executionId: req.executionId,
      context: "**** Update Waitlist Error",
      error: error,
    });

    return res.status(500).send({ message: "Internal Server Error" });
  }
});

app.post("/readWaitlist", authUser, async (req, res) => {
  const { user_id } = req.body;

  if (user_id === req.user.id) {
    try {
      const { data, error } = await readWaitlist({ user_id });

      return res.status(200).send({ data: data?.[0] ? true : false, error });
    } catch (error) {
      return res.status(500).send({ error: error });
    }
  } else {
    return res.status(403).send({ error: "Forbidden" });
  }
});

app.post("/redeemInviteCode", authUser, async (req, res) => {
  if (!req.body.code) {
    return res.sendStatus(400);
  }

  mixpanelTrackReq(
    req.user.id,
    "invite_code_redeemed",
    { code: req.body.code },
    req,
  );

  const { error } = await redeemInviteCode({
    code: req.body.code,
    type: req.body?.type || "invite",
    post_id: req.body?.post_id,
    user_id: req.user.id,
  });

  if (error) {
    return res.status(400).send({ message: error.message });
  } else {
    res.sendStatus(200);
  }
});

app.post("/fetchProfileWithInviteCode", async (req, res) => {
  const { inviteCode } = req.body;

  if (!inviteCode) {
    return res.status(400).send({ data: null, error: "Invalid input error" });
  }

  try {
    const { data, error } = await supabase
      .from("invite_codes")
      .select("user_id")
      .eq("code", inviteCode)
      .single();

    if (error) {
      const wrappedError = wrappedSupabaseError(error);
      throw wrappedError;
    }
    const user_id = data?.user_id;
    if (!user_id) {
      return res.status(404).send({ data: null, error: "User not found" });
    }

    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("display_name, username, avatar_url")
      .eq("user_id", user_id)
      .order("id", { ascending: true });

    if (profileError) {
      const wrappedError = wrappedSupabaseError(profileError);
      throw wrappedError;
    }

    return res.status(200).json({
      data: profile[0],
      error: null,
    });
  } catch (error) {
    logError({
      context: "fetchProfileWithInviteCode failed",
      error,
    });
    return res.status(500).json({ error: "Failed to fetch profile" });
  }
});

module.exports = {
  app,
  redeemInviteCode,
  insertWaitlist,
};
