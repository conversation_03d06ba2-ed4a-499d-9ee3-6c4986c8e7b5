const { PACKAGE_TYPE } = require("../constants");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");
const { logError } = require("../utils");
const {
  cloneImageRejector,
  cloneSensitiveRejector,
  childSensitiveRejector,
  outOfImageQuotaRejector,
} = require("./message/rejectors");
const { realismGenerator } = require("./message/realismGenerator");
const { roleplayGenerator } = require("./message/roleplayGenerator");
const { ImageRequestDetector } = require("./model/ImageRequestDetector");
const { SensitiveTopicDetector } = require("./model/SensitiveTopicDetector");

async function* generateBotResponseStreaming(
  ctx,
  /*  configs= */ { conversationConfiguration, botConfiguration },
  /* profiles= */ { userProfile, botProfile },
  /*  request= */ { body, history, deviceType },
) {
  let imageRequested;

  if (body.startsWith("/imagine")) {
    imageRequested = true;
  } else {
    ctx.startOperation("image_request_detection");
    imageRequested = await new ImageRequestDetector({ message: body }).run(ctx);
    ctx.endOperation("image_request_detection");
  }

  ctx.setImageRequested(imageRequested);

  const messageGeneratorArgs = {
    imageRequested,
    message: body,
    messages: history,
    botProfile,
    userProfile,
    botConfiguration,
    conversationConfiguration,
    forceSfwImage: deviceType === "android",
  };

  const isClone = !!botConfiguration.clone_id;

  ctx.startOperation("generator_selection");
  if (imageRequested && isClone) {
    ctx.endOperation("generator_selection");
    ctx.setGenerator("cloneImageRejector");

    yield* cloneImageRejector(ctx, messageGeneratorArgs);
    return;
  }

  // TODO: It's easy to work around this. You can say things in multiple messages
  //       or you can get the bot to say things which will be taken into the message
  //       history later.
  const isSensitiveTopic = await new SensitiveTopicDetector({
    message: body,
  }).run();

  if (isSensitiveTopic && isClone) {
    ctx.endOperation("generator_selection");
    ctx.setGenerator("cloneSensitiveRejector");
    yield* cloneSensitiveRejector(ctx, messageGeneratorArgs);
    return;
  }

  if (isSensitiveTopic && botProfile.age === "child") {
    ctx.endOperation("generator_selection");
    ctx.setGenerator("childSensitiveRejector");
    yield* childSensitiveRejector(ctx, messageGeneratorArgs);
    return;
  }

  if (!ctx.unLimitedQuotaExperiment) {
    if (imageRequested && (await isUserOutOfImageQuota(userProfile))) {
      ctx.endOperation("generator_selection");
      ctx.setGenerator("outOfImageQuotaRejector");
      yield* outOfImageQuotaRejector(ctx, messageGeneratorArgs);
      return;
    }
  }

  if ((conversationConfiguration.chat_mode ?? "realism") === "realism") {
    ctx.endOperation("generator_selection");
    ctx.setGenerator("realismGenerator");
    yield* realismGenerator(ctx, messageGeneratorArgs);
    return;
  } else {
    ctx.endOperation("generator_selection");
    ctx.setGenerator("roleplayGenerator");
    yield* roleplayGenerator(ctx, messageGeneratorArgs);
    return;
  }
}

async function isUserOutOfImageQuota(userProfile) {
  let { data: userQuota, error: userQuotaError } = await supabase
    .from("user_package_quotas_view")
    .select("*")
    .eq("profile_id", userProfile.id)
    .single();
  if (userQuotaError) {
    const error = wrappedSupabaseError(
      userQuotaError,
      "Failed to fetch user image quota",
    );
    logError({
      context: "/sendImage - Failed to fetch user image quota",
      user_profile_id: userProfile.id,
      error,
    });
    throw error;
  }

  const { count: userUsage } = await supabase
    .from("user_usage")
    .select("id", { count: "exact", head: true })
    .eq("user_id", userProfile.user_id)
    .gte("created_at", new Date().toISOString().split("T")[0])
    .eq("package_id", PACKAGE_TYPE.IMAGE_IN_CHAT);

  if (userUsage >= userQuota.packages.image_in_chat) {
    return true;
  }

  const { error: usageUpdateError } = await supabase
    .from("user_usage")
    .insert({
      user_id: userProfile.user_id,
      user_profile_id: userProfile.id,
      package_id: PACKAGE_TYPE.IMAGE_IN_CHAT,
    })
    .select("*")
    .single();

  if (usageUpdateError) {
    const error = wrappedSupabaseError(
      usageUpdateError,
      "Failed to update user usage",
    );
    logError({
      context: "/sendImage - Failed to update user usage",
      user_profile_id: userProfile.id,
      error,
    });
    throw error;
  }

  return false;
}

module.exports = {
  generateBotResponseStreaming,
};
