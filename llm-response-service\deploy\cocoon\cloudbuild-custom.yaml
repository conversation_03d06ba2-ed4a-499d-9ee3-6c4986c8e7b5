steps:
  - name: docker
    script: |
      set -ex

      cd llm-response-service

      TAG=${BRANCH_NAME}-${SHORT_SHA}

      docker build -t us-central1-docker.pkg.dev/$PROJECT_ID/images/cocoon:${TAG} .

      docker push us-central1-docker.pkg.dev/$PROJECT_ID/images/cocoon:${TAG}

  - name: gcr.io/cloud-builders/gcloud
    script: |
      set -ex

      cd llm-response-service/deploy/cocoon

      # Set up GKE access
      PROJECT=$(gcloud config get-value core/project)
      gke-gcloud-auth-plugin --version
      export USE_GKE_GCLOUD_AUTH_PLUGIN=True
      gcloud container clusters get-credentials "image-service" --project "$PROJECT_ID"  --zone "us-central1"  

      TAG=${BRANCH_NAME}-${SHORT_SHA}

      # Find out the latest image hash.
      kubectl set image -n cocoon deployment/cocoon-stage-deploy node=us-central1-docker.pkg.dev/butterflies-ai/images/cocoon:${TAG}
      kubectl annotate deployment -n cocoon cocoon-stage-deploy kubernetes.io/change-cause="custom ${TAG}" --overwrite=true

options:
  automapSubstitutions: true
