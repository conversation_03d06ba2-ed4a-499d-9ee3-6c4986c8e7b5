const { logError } = require("../logUtils");

const authAdminDebugOnly = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const access_token = authHeader && authHeader.split(" ")[1];

    if (
      !access_token ||
      access_token === "undefined" || // some clients are currently sending this, see https://github.com/butterflies-ai/ai-ig/pull/2954
      access_token === "null"
    ) {
      return res.status(401).json({ error: "Access token is missing" });
    }

    if (access_token === process.env.BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN) {
      req.isAdmin = true;
      next();
      return;
    } else {
      return res.status(403).json({ error: "Unauthorized" });
    }
  } catch (error) {
    logError({
      context: "authAdminDebugOnly: error authing",
      error,
    });
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = { authAdminDebugOnly };
