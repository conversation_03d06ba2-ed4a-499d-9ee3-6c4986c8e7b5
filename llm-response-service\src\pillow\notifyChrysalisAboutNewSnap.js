const { logInfo, logError } = require("../logUtils");
const { default: axios } = require("axios");
const { CHRYSALIS_BASE_URL } = require("./config");

async function notifyChrysalisAboutNewSnap({
  sender_profile_id,
  receiver_profile_id,
  message,
  image_url,
  image_description,
  is_sender_bot,
}) {
  const params = {
    sender_profile_id,
    receiver_profile_id,
    message,
    image_url,
    image_description,
    is_sender_bot,
  };

  logInfo({
    context: "notifying chrysalis about new message...",
    params,
  });
  return axios
    .post(`${CHRYSALIS_BASE_URL}/new_message_from_snap`, params)
    .catch((error) => {
      logError({
        context: "notifyChrysalisAboutNewSnap failed",
        error,
        params,
      });
    })
    .then((result) => {
      logInfo({
        message: "chrysalis notified about new message",
        params,
        result: result.data,
      });
    });
}

module.exports = {
  notifyChrysalisAboutNewSnap,
};
