# Build stage
FROM node:18-buster AS BUILD_IMAGE

WORKDIR /usr/src/app

COPY package.json yarn.lock AuthKey_MGAJ4V4JWR.p8 google.json ./

RUN git config --global url."https://".insteadOf ssh://
RUN yarn install --ignore-engines --frozen-lockfile --production

COPY src ./src/

# TODO: Why even install them in the first place? Can we just comment this out?
# # Remove development dependencies
# RUN npm prune --production

# Final stage
FROM node:18-slim
WORKDIR /usr/src/app

# TODO: build speed up - use our own base image that's just node:18-slim + ffmpeg
# Install ffmpeg
RUN apt-get update && apt-get install -y ffmpeg

# Copy only necessary files from build stage
COPY --from=BUILD_IMAGE /usr/src/app/AuthKey_MGAJ4V4JWR.p8 /usr/src/app/AuthKey_MGAJ4V4JWR.p8
COPY --from=BUILD_IMAGE /usr/src/app/src ./src
COPY --from=BUILD_IMAGE /usr/src/app/node_modules ./node_modules
COPY --from=BUILD_IMAGE /usr/src/app/google.json ./google.json

EXPOSE 8080
CMD [ "node", "--require", "./src/instrumentation/instrumentation.js", "src/index.js" ]
