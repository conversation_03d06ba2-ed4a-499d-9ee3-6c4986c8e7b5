const express = require("express");
require("dotenv").config();

const { authUser } = require("./middleware");
const { supabase } = require("./supabaseClient");
const app = express.Router();
const { logError, checkAdminValid, wrappedSupabaseError } = require("./utils");

// ADMIN : Get Post Prompt with ID
app.post("/getPostPromptWithID", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("post_prompts")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get post prompt with id",
      error,
    });
    return res.status(500).json({ error: "Failed to get post prompt with id" });
  }
});

// ADMIN : Update Post Prompt
app.post("/updatePostPrompt", authUser, async (req, res) => {
  const { id, content } = req.body;
  if (!id || !content) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const { name, post_prompt, memory_prompt } = content;

  try {
    const { error } = await supabase
      .from("post_prompts")
      .update({
        name: name,
        post_prompt: post_prompt,
        memory_prompt: memory_prompt,
      })
      .eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to update post prompt",
      error,
    });
    return res.status(500).json({ error: "Failed to get update post prompt" });
  }
});

// ADMIN : Get Count of Post Prompts
app.post("/getPostPromptsCount", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { count, error } = await supabase
      .from("post_prompts")
      .select("*", { count: "exact", head: false });

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ count });
  } catch (error) {
    logError({
      context: "Error to get count of post prompts",
      error,
    });
    return res.status(500).json({
      error: "Failed to get count of post prompts",
    });
  }
});

// ADMIN : Delete Post Prompts with ID
app.post("/deletePostPrompt", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { error } = await supabase.from("post_prompts").delete().eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to delete post prompt",
      error,
    });
    return res.status(500).json({
      error: "Failed to delete post prompt",
    });
  }
});

// ADMIN : Get Post Prompts
app.post("/getPostPrompts", authUser, async (req, res) => {
  const { page_size, page_number = 1, search_value } = req.body;
  if (!page_size || !page_number) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const offset = (page_number - 1) * page_size;
    const { data, error } = await supabase
      .from("post_prompts")
      .select(`*`)
      .ilike("name", `%${search_value}%`)
      .order("id", { ascending: false })
      .range(offset, offset + page_size - 1);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get post prompts",
      error,
    });
    return res.status(500).json({ error: "Failed to get post prompts" });
  }
});

// ADMIN : Get Conversation Prompt with ID
app.post("/getConversationPromptWithID", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("conversation_prompts")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get conversation prompt with id",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get conversation prompt with id" });
  }
});

// ADMIN : Get Conversation Prompts
app.post("/getConversationPrompts", authUser, async (req, res) => {
  const { page_size, page_number = 1 } = req.body;
  if (!page_size || !page_number) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const offset = (page_number - 1) * page_size;
    const { data, error } = await supabase
      .from("conversation_prompts")
      .select(`*`)
      .order("id", { ascending: false })
      .range(offset, offset + page_size - 1);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get conversation prompts",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get conversation prompts" });
  }
});

// ADMIN : Get Count of Conversation Prompts
app.post("/getConversationPromptsCount", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { count, error } = await supabase
      .from("conversation_prompts")
      .select("*", { count: "exact", head: false });

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ count });
  } catch (error) {
    logError({
      context: "Error to get count of conversation prompts",
      error,
    });
    return res.status(500).json({
      error: "Failed to get count of conversation prompts",
    });
  }
});

// ADMIN : Get Message Request Prompt with ID
app.post("/getMessageRequestPromptWithID", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("message_request_prompts")
      .select("*")
      .eq("id", id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get message request prompt with id",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get message request prompt with id" });
  }
});

// ADMIN : Update Message Request Prompt
app.post("/updateMessageRequestPrompt", authUser, async (req, res) => {
  const { id, content } = req.body;
  if (!id || !content) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const {
    name,
    following_prompt,
    post_like_prompt,
    post_comment_prompt,
    post_comment_reply_prompt,
    model,
    temperature,
    frequency_penalty,
  } = content;

  try {
    const { error } = await supabase
      .from("message_request_prompts")
      .update({
        name: name,
        following_prompt: following_prompt,
        post_like_prompt: post_like_prompt,
        post_comment_prompt: post_comment_prompt,
        post_comment_reply_prompt: post_comment_reply_prompt,
        model: model,
        temperature: temperature,
        frequency_penalty: frequency_penalty,
      })
      .eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to update message request prompt",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get update message request prompt" });
  }
});

// ADMIN : Get Count of Message Request Prompts
app.post("/getMessageRequestPromptsCount", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { count, error } = await supabase
      .from("message_request_prompts")
      .select("*", { count: "exact", head: false });

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ count });
  } catch (error) {
    logError({
      context: "Error to get count of message request prompts",
      error,
    });
    return res.status(500).json({
      error: "Failed to get count of message request prompts",
    });
  }
});

// ADMIN : Delete Message Request Prompt with ID
app.post("/deleteMessageRequestPrompt", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { error } = await supabase
      .from("message_request_prompts")
      .delete()
      .eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to delete message request prompt",
      error,
    });
    return res.status(500).json({
      error: "Failed to delete message request prompt",
    });
  }
});

// ADMIN : Get Message Request Prompts
app.post("/getMessageRequestPrompts", authUser, async (req, res) => {
  const { page_size, page_number = 1, search_value } = req.body;
  if (!page_size || !page_number) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const offset = (page_number - 1) * page_size;
    const { data, error } = await supabase
      .from("message_request_prompts")
      .select(`*`)
      .ilike("name", `%${search_value}%`)
      .order("id", { ascending: false })
      .range(offset, offset + page_size - 1);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get message request prompts",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get message request prompts" });
  }
});

// ADMIN : Get Comment Prompt with ID
app.post("/getCommentPromptWithID", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const { data, error } = await supabase
      .from("comment_prompts")
      .select("name, prompt, temperature, frequency_penalty")
      .eq("id", id)
      .single();

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get Comment prompt with id",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get Comment prompt with id" });
  }
});

// ADMIN : Update Comment Prompt
app.post("/updateCommentPrompt", authUser, async (req, res) => {
  const { id, content } = req.body;
  if (!id || !content) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const { name, prompt, temperature, frequency_penalty } = content;

  try {
    const { error } = await supabase
      .from("comment_prompts")
      .update({
        name: name,
        prompt: prompt,
        temperature: temperature,
        frequency_penalty: frequency_penalty,
      })
      .eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to update Comment prompt",
      error,
    });
    return res
      .status(500)
      .json({ error: "Failed to get update Comment prompt" });
  }
});

// ADMIN : Get Count of Comment Prompts
app.post("/getCommentPromptsCount", authUser, async (req, res) => {
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { count, error } = await supabase
      .from("comment_prompts")
      .select("*", { count: "exact", head: false });

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ count });
  } catch (error) {
    logError({
      context: "Error to get count of Comment prompts",
      error,
    });
    return res.status(500).json({
      error: "Failed to get count of Comment prompts",
    });
  }
});

// ADMIN : Delete Comment Prompt with ID
app.post("/deleteCommentPrompt", authUser, async (req, res) => {
  const { id } = req.body;
  if (!id) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }
  try {
    const { error } = await supabase
      .from("comment_prompts")
      .delete()
      .eq("id", id);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.sendStatus(200);
  } catch (error) {
    logError({
      context: "Error to delete Comment prompt",
      error,
    });
    return res.status(500).json({
      error: "Failed to delete Comment prompt",
    });
  }
});

// ADMIN : Get Comment Prompts
app.post("/getCommentPrompts", authUser, async (req, res) => {
  const { page_size, page_number = 1, search_value } = req.body;
  if (!page_size || !page_number) {
    return res.status(400).json({ error: "Invalid content error" });
  }
  const user_id = req.user?.id;
  const isAdmin = await checkAdminValid(user_id);
  if (!isAdmin) {
    return res.status(403).json({ error: "Forbidden" });
  }

  try {
    const offset = (page_number - 1) * page_size;
    const { data, error } = await supabase
      .from("comment_prompts")
      .select(`*`)
      .ilike("name", `%${search_value}%`)
      .order("id", { ascending: false })
      .range(offset, offset + page_size - 1);

    if (error) {
      throw wrappedSupabaseError(error);
    }
    return res.status(200).json({ data });
  } catch (error) {
    logError({
      context: "Error to get Comment prompts",
      error,
    });
    return res.status(500).json({ error: "Failed to get Comment prompts" });
  }
});

module.exports = {
  app,
};
