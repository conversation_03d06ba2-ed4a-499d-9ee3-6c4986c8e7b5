{"4": {"inputs": {"ckpt_name": "realvisxlV40_v40LightningBakedvae.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 864, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": "a man, skydiving off a plane", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(worst quality), (low quality), (normal quality), lowres, normal quality, , (nudity, nsfw, naked)", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "11": {"inputs": {"interpolation": "LANCZOS", "crop_position": "center", "sharpening": 0, "image": ["64", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "33": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "39": {"inputs": {"ipadapter_file": "ip-adapter-faceid-plusv2_sdxl.bin"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "IPAdapter Model Loader"}}, "40": {"inputs": {"provider": "CPU", "model_name": "buffalo_l"}, "class_type": "IPAdapterInsightFaceLoader", "_meta": {"title": "IPAdapter InsightFace Loader"}}, "41": {"inputs": {"clip_name": "CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "50": {"inputs": {"lora_name": "NSFWFilter.safetensors", "strength_model": -1, "model": ["4", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "60": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7000000000000001, "post_dilation": 0, "bbox_detector": ["33", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "61": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["60", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "64": {"inputs": {"image": "#DATA", "image_data": "data:image/webp;base64,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***************************+mKT7ZZQ2Kj1KPh25GbVA1dMsylW6faR1ckuAjV30FPXP/i89on/oGbp7/4OedP6GFJxsOFtBiYzETIkIS/h0R6ma7W5bf0Ge9eB3JoaR6HgLhd0evyE4urD9Wyn2yHFxRt/Yz/6RBkW7hiPYfzD3oXLc1QOQwYwGR/zhHw1GxTsrY9KpRVYnEvaxqZUO7FjFBVwNu35bu8ALWjJ3JD2utkCnMI5bt95DtBiO9x4DlkxUhaNTIcFoNqPvR7cw05mVgR85fZt6R7UAhHOuuQHMuxwXjTP5NjXsMKEdVUm9xH+D9f0+z4mSEgnnOxPNb6w3ewtOczoLXctYXXfVgUZ3W9bdHCl505r3b6CTtsWbb5pvXIBA+6hklrXWFjF4CiRfwLTpYC9eN+NOkLU1EHN6CjodMimRjbv/zSr4pG/JV6Ak905q3v6qLthet9KZa/6MZ19f4jQl18fcHFgmT7sMP8mJ0TfdfOfA8hkwQTNH755VHVJxsxSvpXzXwjJcdaGm7iz+Pj25XaRDows8HUyrf+5vJk0uOD0AuaiDKZ5fTTpBx3PfT8HxrnOenZA0GLJfqu81Sc8xfkkQty9n3VRlCd4B/BQ6AW1yFKyivOSlrAbxFl4opzeWpQ8YDtwGFfeGN+jWneqY3Su6ptp5ygXCXUcgUSjguW1I2r8dyYmRZSHCu6nePEUHHDByS1VmUCvHDdVF/ww4EKmt8BtyEp910GRQnu30FeJsR+CaCfcVTkRHEV7e6BX3pmJS91YmGIHH+K4YdQ5NFSq91e0eUzbjbJ3MsjuSiticBANZbYkXnL6E8Y4V/+c2LK6Cwxt/kzf8/O1ekj8g1HVV6NJ6CgfDQWRVZTv8UsfJE0G759nyjNDbD20SMLBDh2KLGxAMzj7am8wWYIEEnHgp3I2FhnkUwPkrJgJgwq/jBeCCiFGzAYaDDaRGOu19vFGQ6aYLhxTEXNGNuoyNLKIi4kEzf/qaL6wZ+8/aXdGXZXXJ0kqMGFJcfN0eRmRzfv56EaTrL//G+xYUlKdlZRnnrw4GoUO4d58QAm27mxVqbGt+6xZ20i6IPburP4NFrx0D6ubQiX4IUGgPJQDTfna+/H//+0eXOAPSpoEg8U9JBBGvWnKjqh8JjY/EgjQ7LUWA/K8vuFHKKBX3qaFXSXs4a9U0yG+83Ov1ZAbDRFMDiw4iMAXXWkaAGSlwrdpgJVzaA5IwmodIzEQ5QTjqw1OxVTGScGvUTCnVMDWzSkfnVEAYSp6gSlpKaoKigE5xRWLD4G6MSNfWxbMQdejyCQfB6iy8HziUeVc1tbnSWwupav77dPsm/gqTde2KQKNFr2mMwumc7MbKOKTiwip22ezmbYlOZAv++hplP3wYAGsxBoquBasgzWfvOmzvl/reiTf77BvMNGkSnm1ltjFogKbURxLa5AwX6Gn5cGCLbh3HLd/ph7G8drakou/MnNIix7B2lJ50k80EikgULGFNC0VYa2gm337V4dapWSO2GqpLZtt+9ny8A/pe9LgoQYfa12jL/sCcPJIAXslDCQm3eVVXuZ1QU7qbNAktx0vu/lv/c7JSHevJWXHQoigS7eUYKAVPAJLYqansig3efHtrtgqguVn9aGgDMtti1w1nWmVddfQKg/GGvofH3CXLoAmrT+1OOhodDXJBFOOYpG+IxZ7zqIfvTqr/L4pOR2239+WdDimDojfVXBimCzQg6/8J/XaTq2QEsRt2p6MpDCzf7lk9rk8FMCO/NxoBdk/+sXW5/WgUxyuev5CTjsKt5UW/Mf8EAbdHCJYf+WwNfV5VBVv0qLd1bbA+r6Px3L/7eN3lkef2XNkt69fmV62vNOLL0DHoyZhsq380S7Mf5yb0JckGedjcjCQIudFwBmDk2SFMK6ko+VpYt4iKtdgYeG+Lj4O5sbJCADbRR7+DjKJIQP+xI/prhgrHpIbs3C/+S8SlDG50NCaXj9R6v0bz4oVZY7qbT1sNEqoG8YWjHlNE+OnQrOSjE/9wsMb7PByYKlHBFTt0N8E95qHCmtGVwFvJSdJQcLn915p3VYU2uwCFZcS8rRezmHqJdzViV82UTVPMaQGq5gr2gF7JUFMhw6i6zHQk/BpnIOJd77I6r0AYJMI17TDgB5EX4VfzMk4JGhm88Ylaj347EK//FAKUEtQjiy7L+ujP4xyf6xDXP5YOqJjNlPP/hchUEtJN5e7nbM2C4yvZmHUBDO3isIwZ3HzhY17+GCSzS9MVdvCEtF21UBBNmSUZD3CkiscJHy+zDeo6K0XfiAV8nCn7p0eJOvVA3859JDrK0CFpjuGMWC/16mEg1sttakFAL6uKgKtSe52HWoZnIZPj43vd82z4QSktEG1ghdsSLwPNuwOjDxSGlSMTrU/Eyq6FT/EXfUYI0j8LyWfW+d7TExLT4fFaEQLWNfxHpasDJ2nJwwSG7KSKgvzaGcG+V7etRQRaw4q9Bl8fhVfSrOGdJ6fWkhFn1ajLeCJZc+Vjtc9lIOW9kMFTstOhNJmvg99z0OknTgfbsLPLS0tht8b/1fDUXt2gVcbWRfw85mrd95Y9b2eLpiZtZ1lAJlDqn5+835fda+s+JXYBID/8PslRUPbD31qyNprhCKanm0EdC03MRYzvhJfDxsRuZgbGrc99WvJoGJJGBv2R2vdiy1SLLFOxnNuGlYqNGKzs3c3opH36Io5SzxZwGYVdGvq/88v79mIgJXQJJX95ulLgYJyxx1qBgsDkIvDDEy7huEWhodCdI0UtXvsEzSf3UODCakToCTQThMLBGM7vhwvZYK6xAn6OcZjw9u58MZu10SUj2++8HsWBC4r4eoFRViMZhaCHpE+OHRrYrzUc09Rs46R63kPsVC/1IAg3ykSeuUEt6OeSS2U1XMB2KGoQTn4taTqPtEqGpv6i7/zS5dYhuqI84iSuX1FVYKR/tEcScuDYjF0f+lbOgUXuEFbl3XdTdyyTXvuvzM87kyq/zY9WBoLk8ohSL2a0Yl2I+wOrSJ/2QkiHpkPsec76oDz68AszDzL3ZoBuiAzL+40ZV/Jl7nPxgdetsvJN0JVd6pqshcOXSaMoiE0juttnED2Lx5qXn32FuaNFaDYY5e3quaaXfMAf4sl4b9cD9yvCDT2ybPU32mTcee2PbyqSPbbDNJug+03E8aq00bfmSKrtKTa/wE1WciflcN9e2NYdTdyyoEQgL1i0pvDEBybllq7plvbPX3HutRJU2loXjF6oXtskux976rUEqNn849EIO5jZFDJ31Ya8eFW757XGRrdoRhSF6MH/OVVdxRAkCrP11yOUUnmSCXzNMb27ICbqK1qCH+R6Ja2b6qZ+6uCk7t4pTGd64JG1eBwLufTWmJ4pc35gj8BD2l1rQ+KzWimSV3XokcvuEDlaHyzSxe8gfM1D0Dm1OHQr05oC89iAy++aKOTt8l9PwUkE03DwBgfio3Nbz2R2FIrZLjJtB0n8uQQI2W4ymdPMZjE+srjEUOHA5O5mmzTlncFwL1KqTdxhljMpapmNTt+Vel3JIr7Y48+5YK3pXLNGKujHtYt0OlbMUL/ULgCaie+5CJbMHiGm5B3teXZMNc/uo3f+Nzcpb96T2eRyylt4HraesN905fJIX6Egptns2FS/CzRdJnl+J/KvBqQrrtikQbqL8Iqy8gSoLcLkDMvUyPqta+JbdF4mEhla0ldvGSpei26mGWO8435uW0xst1BnROXGfjS+fBZ3Pw1Dg8WsaDGS8yIMUgURrMTBDfi01qgQ217XioC3bh6TwTR89U6fEhGJ7I0iSEQEMLPAfuLrOVRqvhHpfD2WLczi8UFa/O6lHZPlC+v698JavYivxh+5bNo9yr2xWH793Kx4RAoB+Koy0zhTiHJk6EHcN4moSU0ndqyf7okXQj6BK3pw+0ZCWgjT0O6+N5XmfhZ9UiDIDxiX5CuC0BkgGdR3GbRGe3HAtRrjl54Ox7543aBDa4VmnH0EkJmOXm717dzbR1WMkCHFv3OY3hWAcZu+0XF5cK95bnKT2d1mqzuHTaOUGrAxUceAV2u1Bqs/hjwrrAFB++I/3P4ZeV3mON9tMZmuLHW6QcsspD+KX2xq0x7uYGhuG/t95oQp07b3TTy9/wu71Ty3h9bSAKVhE9oQXiPZoyEEjhNiEvRaUvSWNczBNH+3qzucKPd/Fd3no4vS4qLcsKnilYrc7zjP8Pi2g1z5PzkznsHiY+rXUtuJNFlZmKxcAnzlrTgE3Ivffd9j+msCQcAJfVxcwpGeIvXwxk8C8BESFUJOniFXL/nriWLzZC871s+cvFEmoHaywVKWFOwgKfX1k7yD9Pw+QFvuwm6EsYubcUW4eouchEBFPd9o08DcD8iap5otFeJhZQYcWnTPGQbpoq90XeD8MTAXXc8LaRCW/jq3zhifVIQa8Y0FG8zSBaSVxFm0mxydtev8WYYkFpUPjFvejfJ0Rbt9d6OgAocTJazXpxA5Ktlpt9YNlcW/E8wLzyuRkp8NZURrmK5+4bG+W4KQrw0XwGOSP+LvKvMonBU75FYV3JYXNplUyVZ5+tb/81zS7SUOtS6QOvGh+LVcS+2JsEdLSIZyJheGXneT/aJTrZsLkFgtz0NFnsRxa5h4GT5WRZhd0GOFZpCXCj0fUCc9FLJCLCXX36JAjVgwhL6evYfnbP7xqq+kuupYjCSYC8+fmqNdvfK6D6vraJii1KKRHgCXlS/3KQVTSzWr5mW11TnM1BdGYKfI+Tke7Eul/5h0KfDAk2P6mlEPYM42WbFUVmwpQ6RlWQzz460Mo2S0H5WTkuXK/7Jh4Xuq2t6WE4T/iFP6YVcdD2mdkGbzgC/AsofFarKJLksXfiN9qClXgXcyTX/HNfybufH9AA7EzMpIzFxKp5U19hW6kzvRc0UZSd2WngXLh0VD8GUp6JxkM4/lvDPGZ+UqBmt4H9LoOwu6F47VZeYJaFSqzgEIMTDr+U4ToEyw9XEMN/Sm+ADXJAZjIPlaoHLl8FapHpK0TTH/2IG+ewTRCaZSPIOYDCiG5co02kWxCRbna9MazRSAVLgX9xNMvLkIZGtgaL04jtXQf4BK2Lz6NY/sITfHg0MbRdYuWemDc1TsvkFwaSaPAqq6z0USIECCTnafDn/K5Yek8YnWny9eTT61iq7wSYGuA4EVZGnpfH9CIHA8RU9PEeu7uDe6lr4R2x93nnoSCokBJ6EqHhhujy/7hOS+929tqXZFwht9ihFMxuU5t/U0A7mZe94FpOKEoVvfx5zLHlBPbHNQLbv5Xp33WAWHksrp4BI4GoYiv2D1/eT3Kqs68Wkd/8kXP+ErnJkPr2F8fI8k0UtrwzwQSJxpfGAiyjYGAKJE1kycAx91SxQgmEft5zj4/EIF1PK5k8XQdtnKtBXJKuCsoWLwUTe5meWMbhw32opK72NryMFJfc/xYwPGvC2KuIqg1KE0yrHkT+a/abJ8hBvrkcy1NhBI72sya/umLnR7YzPhjOfEYsgLvZ4GlWmsdefxWUM4BWdwFjbV6aldqrCm2f1vhFalK0Pt/PXDDuJoFCEsCSTp/jt+ls7ha+FFUdTWOThfq08BjWtq89IWa6Vx3M9Q1LyYxp+tM7diklqKRVlDJz+ETSfSuZE2+ruEc4up8cLX4Sp3lq19RL0S7kk/+qX2R1qPY5Ug4Mzw93RhyRvKrQ6EMxDVmTpvzJ1CQ3RqN48wQxVYy/fPOU5eVAiVtbnamdWxY3TfdckL9SeOxnuRbG5F2CJYDAugdCb4miNu5CsibRzWCq0oxRRVNAtfLZRSZrXowShI/dVp9J/vWT314HHVmMYrkpZwExLwqGAD+/Ix5zRs8WwyVQ1yQtC8JUC8IP+VUCNrDINYhwcN+eZUyiqNWEDGj9eBXDoF0LRmzdQ4RvUkq/s/ZyBb8QuWZWDqVoUWc/sHuftdsKljBvJJg9y4h8f2Jo1C/7kB4VlOC5IAoOILezxv3zs+1b60qnLQVqIJ+v9ALjhsR5Xib6cehWDQkySuJHEEVZrbJWqP6aM7DP3/bg8Y2zi7vE6d1pg2gDEzxBauT/4/dp+6QzSwAgjFHwafYrE1xGmCM95iW2t/FF3h6vA9nmGlikqGrTNuNcQGq7z4ZEksc5x6JrqKSshBsfYJVtrw5jKDOXeDJ0L88LwhZJmR1VczI5mq64g1HA0Iz+UhLT0VkC05XzUbO2pAzZIJemkIkJe/99/Wsbr01VEDTutOWXdqPPmZZD2gg+a6PY87c1jnKIiMqfRW41KCkySs3bPD/5ny/F3StxJ8SfeiCD2uuP0c3EDJtWp1QLC8oKi8Rpo4GwhYCCgwbi5cRlW0nsYZGz4k18hfWMubyIax/tQ1ZN5hoiHOjEHdQqZmrjk2liKhI3+/dNsKco1rbdD1MeOpgkgdPklyHa0ewWIcImUuOCZER/QCoAIkn6rQiRpZugkoTRhMgY/wPbHs0USGi37vkH9w+vxkx1sNiW8FoZKCphNLK4OH3yV9JVOUY4BNgoMCwd4Bo8qiIKwYtbWAxhzW2BnbQRo/2iO1YN8t16rQ6g4Z/4R7HcS80iYWcqMk834U2vdq2lkRMunDvA9hg/5HULL9RWu870IhgXBSHV3LEo9N8YWuoHKnEJ7STov10UH4DO7cCNW/I1qfo7SW6noOYOn4ace6/0Ml0KDM0sNTrfcMoBw/pVqLdFpKDZJVSQXcGlXjrFJmvRaRbnxoinjTKtG/YvuqmHGD4I6QplvTvt+1EEKpsbRVjLbp7KGwBB2iKWU3UBfZkf5W56FLx/DfOvRcZQFxcVVPsihdIJk64OJ8EK2ii5BgZZ5O5oHbFf/dPIqJbpTAVoSNW4riCBdXlO4rEW4XI6EDLjy+rJCUzfm7fFjrEcZxjM7Aw99tF7F7OUC+qgEz6zaGWTjTNA2icU6lWytiKAAFQXGhyj0YD1DyYH2HQXccCIuNv4MFFiFXwtm8JiAqXd37cXd1MUdVo1akdT1fVy0CAluzIrZIso3GZLn9PQcihn76rwhnfEeSNf4ThgEZygVauBgWvRCvE9JaQsSrupearp3Xu5a1Y8lIo4c6eaOB/ifAuClBk6Z9pDE763C+PcinNeAP4uNnxDcfqcwTxp0L/N5ScQUdkf1K2nqOXG1wuvMDkjvEUrhebd+XuGjj8mMwAXPeACZdUUAbIJ2W9APnAJB6Q/8KcD0HkIfGIDnL20ODJUK5lBTgH7OXnU3ZTHe96eBSUmlwaJZyqFC2uWfq/oDmyG5aHrIDSn2bNKaz/Fp0Og+Rvp3e6QmYoFuYfQ18qtQPyK1N+pCsDybRLHcD9+159bpsEGByZQrL+rbcxFAWIGvr7ccw6DBTL1+8YZOpiOo3wToJuR4mi6TXegKs6Q1JoO5192pSdjFUKkxKT2s/X6RakGmYvx2QWMQAGRteuW+7Akr86i5DBwdqP7DqMtRrPb9RCsn8R/9VuqxhXXkqfQgHMCucB3WiE4629x6ntMYdqZZFkJaEvSrB9Y1geZCi31gR0jNwBuBGMjNZaFsOHQtZMSTeqGdFpYzR1quzKslGM951ZhhiNwgghWSoYXtyEvx42RwqrlEv7OPkKhkgBsbfRXdhL0Yg5EPzQmsN+L7CEJq1zOFjUBHNobxds32Hqq+Q4ASOu9M7Gx6DNYf9SWXpHW52/W+Zyd6+uokCowgX0Fq4mdOh6foqCgiSV49nrebC0WLBumLbnBUnSWlCoZBDgTux8hx+7r15gDB7QZyDOWmtvmYquCHqQkev7Cx0NqJZ8hYiBJLmCt7HFm0v/oaMzj0Hix9p/Y7Rp4ktou6w+KCl4S5chRTQGgZriz5k/MABdqE/27L2epzDGXJgqgOJIsZHDVTQXgUZRUTK080ZziM724LgGMNMChw0zCTepsfRm8BgZtJzqRCQFKTDbwnUbkGiqv5bouSK71iaW1Zuw2ry/XuZ0yiUuLi18qtBddPzDWwVpVs6pzB/73UoTvIKHeAMhl2agUc84My75lz5rYm6qc98SSACLW+BhZhbuXHM3+Q/HtwUnPws3nt1OCO/Y2/GivcoLgnlOWHlgKzM6oSMKGAHuIkAk+l4SN0iDFVudbN971VPT3+LiRleFZY7mObjCE93w/lcsFqZurMf7RJq0RMwea7rf8oj106kI/wJLYmQF7oBDvGb7Z0IUP05mkFre2MNxLuWcKGf5gMiBQYyqH2SqoNfA5RSeuZJRz7yPLPvuTHYCi2aAZwhJVa23l4RRHky0cCuty547mnp+ySvX/wYytlN1kA+zIpMf+dpZ7tC8LpRnh/TTGRx8p8XIkXFoH+noXaQbGHqmU44GXG43hsOU9J2A5fobL3VCOWMX6diAKsmXK186YntM0nVOmalMKeCcZ3uHvxf8G2IBWNaXTGIIO4g7xX332EznQ7uS7c1jAF6bj1Pvg+pjND5UGHeHoLGEaNkdwaRAnzDEeeJhIjJt0nkYJpsxwbECbvLjVT8b8+0O5hTBzeAGZeuj3h5fQna1+BHaXPnt7BnCQLxeSNPCdK0ho7IAOaAOfBuK4wIdctKecq+f18wpDgymjubiAufF2qj+rJfALm4GkZg7QCLN8GYA2ZSH0AIx/0FYceU0imUtwH0vSuKCuX+3Wt/4QsoLnUnke6Cg/UqKkW4PMA3yalGRNOyDpgUcR6ds+RRsAlvDa994s5UgMGQhJB8J7GluEOsOEF1YFGMiJW01ZZ/Gzi4ISvjEoOokBpmbYlaQLlv0jzsaHjmTZq/t1Aa6BRhOshAMLBDN2mjlnW99Hw4MZjJ1Bkz6Hyrw7wCATV+aK50VXJlNP63oHWCNEXHFsbV0LeCIQaZ4NQyrXD/JXDC8eYcO7RM6eX85XsYTlGrNlNNhmtgt6bqdY8TMBuJleDM07YxVPZCneHYQjx85xb9S3Sz1AzpWEQqzrccekE1WRHA+wVmyQJ5XpkE+Ff5K4dH5DzAgqMa8XbJ0CZ14EkYJYMZ1X7md5aMAuF0DO01U/J+24csnBHGIzO7TLxH+GAu+7mO3OurDngXEZC5ekLK2JApquaUVWUhM+4031MmLltshdgbO0/c5+qbMsJzmfWSL99cCphxf9FUtwyBW81qBv56ZiIUgVvW2gHYEYVBoRXh4lj5lpoFt4jZIXzqm5koNvFOfLoRslbH9Iht5tcicHQ6u0pbl3wiu7i+xVo6yMtEcc5JygHsWI0iv2ff2NA9RCUMJLhYIqegXg8DWkbmT2Dr7KHoUE66LbxUbj+kifdDushKEVnhGipiQlViGpzNJylc6BjZ+qKmZJ9v0RwEDOlqDuUrb3RuovGCbrgNH40L6cZ5OlHLIypja1OeZwhcHsWYTVB0C6AzbuszjdVm5uCTdEoKWxuxCUAlpdS6PB5PpjnrQ5JAouNBQjTIOLSQz7fWShW7/i/jTAF6DPP+wZD0kZf7Tg0Fgh6S3k2Yvb6hS0EMN2bUNYaSO57ECBty2EDqG47WJyD/rEfHO3RemspDYl9ttRJXySqHHPX0TrPMJwBPvz7dT7yDHCRcF9+4XGz4XMZwd+p64FiXb7EiK7UlquwWVkX0lmnVjbaJ0mSs0OAUgkWRcDiU6xdXc2UkL3Cc70jBgc6F/W0PiCCjbG/tryisALxOzBBJmtfMT1Wg76mISXV0EGrSIzyuJT6KU+78g/3+QTOAY6RhVfCcc8fMqA9a8F6s8vhhjfZ9bK8117dReBQbQx+qpWB5b6gVHoEco72cdo/I8YwtBKAxeaEc4j9SpJah/u0YotGT/oXBZHBZpz5XdVuoE1EBLmsG5vTwYv2euFkxPMBmM1EcxS89RmhuweCqh9BF2Vxe87+XAGyLZveoELvhDajLWxY1TFPqBGx81PtDc1DPY0dV/1GbfRfo5S+1PEm568h5l5rhiIyV7l/SW2EtOtdt2Vh+/I5H8VLI+/TWk3ZfpVob4a0k+DQxwETUdoYbo1S4ioL9vK/NhIvljNPkWocWKRhRksVudCCxPkRZtZX6lPSr+dx4WTqFKnlht3/HX5Bycr0jW4kC7u/XPBuB1Lhp2Wh6BfJbe7JQP8u3NQvRwdin5ja9iBtf71VAnIo9HmWf6ofe/OXs4nG7uA88KSKH5vdcADDwigO14EDifVljDmo40RghbMm5AFZwLP5HEE1QbMLDlN4/cYSI741fQh4Ylhegez9PlmA0UI3LE2BlSuxJ+/JsdhOfUknNUvWkZhPRZ4u/bZJlVRtHbZdQxX0gxTYGVcMFQJxT7CyibfF+KolgvnBVemVDxaPeFAnkUfihF4MDFutNpJUe8SxEIGHxC1bNnpELDsPp1fWLDXKPtOmPnv71h1lsveWeNejVohxKUv6645HRNxSwofKTNCvfmgeD1cqie5MZ320s81qU1IDQDgRDVZgm/SEs8nXSQzgJugy/d4WcdKB/KYIPjnb8IC+AEFE+htCEJ30uqPTbuBIuWW73LXtXXU/pykl2B99d79ZZEvyrx8ver75iQM4RWEtY+W/FOrKa02ekBtaGIh41MoGy8NhUgFn+Pt1CywUR1ANVMqX2G60m2md1yEpmKNPIVQm6gOs5A7JGY3gcSrQfPC7tZjYp3eEABaKFzHule1LyG/t9/lpyrWzxjgJQDIj/ly2G9JhdhMOmNF85FZmZmpP4TtGrMrHnDn53HQfrkzUnsXwGTNtz6ub4V21peKHJYTVZNYQbLNimQxwluXUysuoDdix9ZfOmED0ifOPmWWGWp0MtV3SlOaujaT3rjp5q+HJfcXj4mvRP2hOkmBh0U0fjYICyl1HFcJ02q127oSn4SYovWNnLx0/obLnnGuHkN6ibX4CI/G8bReEGT/vF7/3SnVotTSZeiFFwiAtW6fm8kIRftyRGkc5MUJS5kmEIEwONqMQjXNH8BvQVfPpn6IkISfBSqANshs4FMG4hqw2UC/n/TlpUXYGYRlKndGvn3hV0Sk7YZCj01RMfiOOBGbpjMtL5NGgeS9mIIJ9AmKyrBXLTWPdJs1PnUDpuLerfz6CPgXrVODntAaaS4uJyOj8KoJBXN1bDLPsVkmCjJp8tc7o7SXJO1RZVTN4ihWA2ThvddFJ6aV92PWwZ8J8WLAHnPxRKeVOc9n+dYjylEfLoxCtGTSjKT3I820ZHhwRWlgmQ8tb3rhZLfLojZQWslcVrnDtAhhNKlVHRImrZsPLQ9FR4eqQSdJl26Sj0j6b/vvYgPzeUv88pxanA0ZYHnnURtk4NTS+GlMAna9qnrAM2+RiEABXgIJIK7EPg3mm3nb7uyeC2JOmUwd+2wpG5Sbg3YHcIoQOr8uIhaom7hTB6BNnlRDvwW9DVgR1aa4fv5KmeksN12WBdlGGGwlKaNkrw4QCaTKDoG7NNm0n5FUF7navNAYVUeODtpkY9HdFFU4UoT29z8tHT9Y34JAYkE6o2jjS3HxyWn50xD7keIeoO1TnzojNNAkCwSj95iz0feT7eay1acjKgCgTWS4mPq8pREzY1hDEoN4VdqBK+duWb6QbMvPMxK/lEaYKqFTJ3l3t/QyFClV4Mzoo/tot1nFY1T4yJNpgQTHKGaFvtJpTA1XCDzu8rfozj/nsH7SPivbpK9nGI/WngQhrnIvC+2jv4217WponuT82bPTE+8AsO5n42+kNVv1Q5uMrywJ3yP8hbcuM1QZ9UlDvzL+86d/dnaJ5fMasTDqExOsT8STbKWG1M26ji+IZNPZs9HRh8mMpLnDI/fs62YwWUPLunzF5aCHSxewBHkc647ePmQEAOC4Y0kIu/VIoXvtN0m22Ni3GC5wwFCkXj+LSA7KYgxz5+VbyCalNbcAuIzbV29tr0bCCbaN/92vIXR+blKb/yuvWPVNz7hlnvNTn9Tfvimx7xdgy+yHgpYmnZWYdEwz4zoIVFTqe48Gd4mWifu4zJJRBPGhGNjTQSco5VYuJ2hmKgtk6A8liE+K2A9bh52LcDcTr6tDxNjCv4BLoRbcrcFDzBf1Ewai2a1iEsLSS4x4gGG7mdxQ+YrTWKfDWEf2z6OiUbt8Bj6pKWQDZ3nxBUzkUU9Fpf3ZMoor7Mn9HgSjtmMQ2R9J2uNJqvk++TrP0otXjGP/kg8pkIo52qdeJWNyRkXvUp8C4g5CW3mo+7A8O8V8xf6GOkdSkSZc1U5UD3suLLowZuOrnQKqo8qUxLWDR7vlNCU17jil/oayR7j76vXtZvbnoAqPdjKqoIbbi5wlKpncrXXDF0eDhuvlXhdPdMMqqMMgSE0b050rDKKmiDIZhXpYWVAlspuaGGmJkNUOIDaQ8e1hBnoLAlRgWbkxY1QqMQKTU28IuuIzXncGyUSLQFeBeTbKrUvR9dJsj/9ztd4voa27HZ+UyVLOUAVdzH1sclvrlPIpvSXeOi1Lc3juojmqGYrVPdbkbzBO4o8GFy+WST58b6YJ4Q7eSuU4LHJcAH5udVhPxyHFOWYKddCUh1yRZvFFCrx1jDoNAFiyo/wdKqnWGvB3qZAggHvXpryPXcV6Gg0ljVK0Gk1/tak7h5pefi2wZF0rLlGwB1OZus+sZTkSfbWwxPmrnLL1CubqJLf03jNUkKco9tIgfThF2poPf/D3/u+iWnSaYHFmu1LN9WvIAUS3teUYoloGhCT0U8zSvw+VA2szFksHPrQAsx+tQydacfA3OB5Hw7X1JIpW1SVK4tfiKsqxFG317/JsnpY6ifuIAh0skUTuD7h8FzynzVpa+mOijoc9wRXFHypFS41J+bobb9VA9R6TSPXsiNwwnsZhB1FYzguPEFbQL6YhXu+bzX1JC4W6aBCSnasYPUQRLKOO3v8dJ2PHSNgKvUVCqXXjpSgilhyoqWXiBH8tACF3tg8QP1Yks3RS7GFDcl8AEcWoDD9v7cZ0ZycEGcQufOmGhkuKhwf43yToqJaIk9i6JF+wNgyNhiyyMjWPWgEzl27WJpQC4Zobg1r01AtkWB4YiVH7coqwftVMOcFSIIHq9D8KoistNpvhl4EjbGboNRHjA6hjRrNUtSM7Sm1z/s2X6bpZwwKkYXxSShXPW/3DBlMjOiN8nCzX1j/V538xjq0z6aov3kvqIqObVl1N8veSKlrxS2NHivyr7IkLLXIHUAyyWIfdsTLtSkfIx1Ns4ltxt+7yOpIY1x+rELgWXxQzIjR3n4MHdanEgzgifwuhOhR9EOvactOqRJZwfu7D2sxJlZVECApEtCxRj6d62lYSurXVsrJMZzU5gOMGNcUJFxzEgz0MlBvMXhZNQqu//9jnEEb8HzJLedmQOWDAs5xnfxnKF1zcDg7dhS5WC8Vqkn95pXUYh6+rPQWlR3HNaZp1+uI00O97FqFB67EtjiRXO7GMgP+v4nEyjabNu7zPcC2X5oh+KPPhdESlpyI3bB/BslixCeQF30jkx5yLlN3Nqk2m7rvyb+vVMLZMPRdodjjHfEUCJx39003l0/6mlPwc5snOc80gCB6kGSktYGX187lNlYMqWAfajj4sn0GZ74ve7Rf650GpZYJ9t/Qi7OY5rB14SwstRJ/FK5VlpstHWcfP9Du0bhGKVMTF8xdGXTq72Q7EZj99gseAPPvXycuX1bJovx4kkcrlJ1l84/q4q7dbE7fkdSonWZeaM9AovxTXDEIKu5SJ58f7Fvw0ck8nYAyZ4Bh3Vp1oOVKL70b97So5EDB+VFJ4jt7JRnzEx5IVp+2yOkuA+KZ1cnLq2u0SCWrl3+ZYU+hPGJrAAl4LHoannVo80pHvluL8cgyewnvN1/fUPYr6NOZqUe1sHViz9CsLy4rGnQGuHOKb0vzWKk3h1FJnad0FulI/4G4FCAd2ISzemNHTeLSQFOZCd2wBnt4dXoP4nOCkrMJv7z5PFbSJdDVvWg3V+hgCDChSeBgQTXxrLOwD6WdC5wLbJNhCW33Z91fNyxvyO/NOMflcDa0rYoj1+b3WhxVdjKSf6/tFR6MUqLL8re77dy72pU/jM0Ge7co8eGcs9YeES1YC9FdHpjhHhzJXIbGpK+nyNPoFv6u60yxcJ3WRrRolRDalSpSlU5qmAK25RkpJMTl+JmRzkCxYqTI/LU6iBBUPVADgHAZ62sPSnVQtz16xr9RTCXmbmax2eair94l3TrbRMvFyg+TzxLSeU4LHgsWR2V76UX37byV670lcZf+f7d2EiOGQChafEE8UFE65UeQF5izZubL2tdVJq6WsoDjXCkHGMj/62OWm32KX3kmpWAWr3LQWix3PqwUByozEAT/rn2CRdmEppqiXir5XF1RzoKQFnJlLHzs8ZAufzghDopWtGEttZkfCW96jZcRBmJzAMJNmmDc5bg7l2k76z1KhE6BRClbabtYFGMvrxFC3oahBLOm3jInYrdR9LulkRSQ/PSocA+PURDlFMjB4U5idHaC8AwWJsSd1irFWRjTuc2jP2SvDLN6byYZ82II/mAwzEFhj9STDTCHuJUSUyogWDhgwAVjg6QSnGnsUUXZrWnndoPlqovS3nohHQ8K47cYeETmo8/turzSADr44dQsG2jc1ofZIkMEjanqYvCFhDSVU4mDq+1+bsOuOMXbIxMnEu54FKVp6M/YycH34/tfrWFZE39trkXDtQXw38qe9sGBCdMxNNddVPwXD/9QtCkhAfqJyJp7TSvO6rvyZaD2S23eQEzWq3S3mC7HhA0RyNwr2ufOV3vnsZDTR3GZhYEbzU/9+24g7Rx9l01YUFFULe0nT3p1p0Zzjt8b/GSZ6ROEzcC2/3mp2PsoLA9tfYjdx5uPtlaN0/kHvoBOCiSiNjVmqLlJuuOVqbbS+RC0qkR+ezGL4Vqt+RvPZ1AneNah1cmP5ryMSfLn9PrlvCkMy8b+6+CFxFURHyMspSCzMDI4iGV5F0+U96RfDqPbh7qHxQzHqS7vQjWt6zbd8DdA1r5znQMoZ5N9fgG5Ov0ROs3PeuH81nGKxSS1oAu6Xb2oB/LamKk0v/gGZUKck8k3lFEIDAubAumsaErnzlmYec7GA5MZDASGkRqHziPzpPAMmyPu8rUlOzBQbWg6QwP/d5h2COeT+tKmuemGyYckRmh79CJSpuAHK7LImAYYi3UWJOwqnAucGRegz/svKQilYZWWyA+9rIv65xgPorhvM/ChLYGUueq6UH5Ur3++Xy7uaOwLABzDBcAI3te39SVhVLE2dOX6xtyjDKrJCh6pnAR35nn+VQUuHzgBcvOqDS7B4f+TrYLiu+yfJPZgnqEmgTZPv7WRui5WVq2Ey8tnwpeEanc9ikr8fOpfsWZj+hZoe7xXOKlsfZv+MhNgG5tzY+v8tfHmTBqGxKWaeeexIbcbYIR7YImwB1abTRs/z6R8D7dViWDC1HOeZxkiDdi+V/dVenYsJ/VZg+FaCfVmcih3rxrZW4mqLBS2tSbDZyjca9cUQKXN5ljkHN95horRoyAMfl+TvNs6ARQPNM3wvDtm6oNgkAejuLA4gqaVxrMFKRU7BaxC6kp4cnYTmMXHY3F5jZzZqKaN7Wm7lH4bcNqB4sLFvxJIAhA/nCnj4tRNqsUW/LNnQunAPzp/Sg2J9DGYmLPLmb1nolagUWP+CpHVS1OuNdGvpAUC4kEP7K/btyIfqEnOz5yPIJZcBjEGsHfO7lzlWh97Rg6tl2PW167x+DrorlDdwZG3Kk7jl1neOU9t2eUH9Lv785cpopNMVAduXroctHpiMf5gS6TlsPXWkM9npRhJ06tik4WM0AIvM4cFRRFGr1OYuJMz20RShSSGjGKOVU3u/cJOnxZHT4O+tmh4RetDWTeQA3c9Zet1R9pPBIYtVvv2lzJOLEhlxFKktTsKn4RoHcl4STglaJWDtl1Ca6iHA5v8t53URTI5d7ikPM2bv72RHJcIRHnmVClO56KWCnhyKpMaSPfGmoS/GE6ir5P5lMwXDijZeWkWgRtTlJw72TDLbivWY6RaXEroihCfNQmNX0/WoQvW5R5RJe38WEDpmJ6Z/3kHBSPGBYFNpI0m8ERp6SR7RMeqK4/SuQdBpsA6zLNSBhgYdbICR91mOuK6VRysZNGLzkvbPL4y61o4ZNJ/S3SgPHQDSqaxlKZJcgb3qHlw1cCcQWBv5lHWP3o+a9bALVN1Z/9wVAz6OkWZCgIcLg9fO6hWu7iHbw7lALNE8BAhBUvfNU8Qpgyq3DvQpuu9SJOM9SfLI2L6bzlLMBxE7L0Lao+rOp5Oxe3QZqhsV/8g99eGXbOgouPSEdsJ4RDuvbmSeIhWk4PofeOkdM3PCYPeZPAGKf0+o/BNvL1jUy/jZ3ujqZi2lwM3bSg+Jqz2DROqhdCqxx3NKc6MC1XjyIrtsUGxiZVL3tTsmS9AV/lG8WuHh8gRuD8HUKYXx1tLvkV3WhHxnkP/8Z8ohx2Qpzjq4ZYvPcwe+wlK1FwTXux8bNgCNhjk6LX99Q0bmoHg5vCA0siZ8MHdggIW27IeVk+cUmm6nHxXVBJD9fY1Lhi6k6vHMSqadjFEpuhardVYqDYqpyghobfBlNAerUReQCeKO9LSDeiMkCAJKfUKaHFqrMjmOzh3e2VZP5APJS2Xtz64mnGk/j/ePNdDD0iUEXAmTDueoIxWQIoHYwBYWA6d5uq8w+mzwJB6MFHHY9KJZl0j7LjfIK10E84LBAnJIyDypENBEnNCwbJOd5IHmGJvuYt7hOx42Q8x/4EnH26z53HKaTyYAhz6knM8u5YiDI+uqvS+U3FmRNMnpKWlTWwAoBmG4ZgzGVk2k+kHdv3Q1mC2WhFMI6ntMwpw31A87oKYKxzwdAB2yy+xeL85tuOE4cZToNt2jCclpIA2LdziybZgEmlr8F4E6KFSTJXoSdlPVk0jB0ytodlOSfStlP7zX6PtkU9sYBx9U1bTaSQRTNwV8brsq++wS7ZRTM/hA/JSnAA7McsQAtAKA5+q2cEJRCC0S/L+Hbv6StmpJyIYxV/90PEYyOV2VrsggbhAN1Tu4tJ5XKHJBHNKEDLMoY42z3KyWt8QC0I6mFjYy/Lv3rTpeOZIDsGnbdYGArNzq3D/M7Ky1ucibQRxJreJFDP0PaEWQLF9Q/RAbvJ2hOPlIge0cNuEzO91IBFupccy/ncfb8iCNFbRpoBQk3jEcXYz1ghULdrauZmlgrZkArMqlaYBuJaW3YKUCE4+kELajCtxvsL6uX+NmU7pbfVdmRnaqP0nXE5KTHNhe5MmEpnPfqHu4aEN0wYmbdg8P/ldsS8MgBR3LYc8YC0M3V+jtOdYJBnV8eTqE1KFZ+JB0XCaWtFo72HeYb4U4ow3iRawpMBUNfNEXp1TY/DM4Xqcs6cHFome73pOFaAPXABUzVNJ2RoJRQoCyfQYt3wi+g9t9lTXwYdrrvpac6/lgWEdkaUOx9sc1l7WJEboC9jcU8bS7kkGUiXdrESxl10fvY52jPLXhvwuJpVX/VEkC2boiH5xwW0PU6Or5sRCHnEUuixFAT6TE/4BC579PX67DA6bfNNRjkl4GncT1LEZ0GOiZYTszW0dBjyjx8iSAJJPaTP50Tdt+nTMR9FApgLPM4wFBx0LpblJ2Feunj29gOOdEgiHZHpADpDORP3jH+USYjecgtyrsAQ+Bd6jFmsGmiIQxQpOvtiBC4vo06qpleVlRhCu3THsf2Fs1BkL6PL6U8IHSbphL6CoaEfHHvYiGTt3kyhZWfY40ECppJmXb7y57MjyflOSxg3Y0ygdsxVvUH9SzN1T4q1IOg77j4ocXQ3H+6OyrFAaqRx6CDhhDXJvo54XVSke+RckrqDra8uwxVXQJbeY5W/s+BJ+pouYM3svqCp9w37o+rBIgQhSpRpvf/IVeJZfIAI8y7upMefX2Ovxn3LYAWpEZnAho8U/s9ImB9hLUarI4qcBImyaH3QdCvSCfx92lNf0M0LBeeP5jrP6tKh+5j63gqrYDMNCMjakY83hFYTxShx3B+FgQNvBcMIRsdiax2eVwlAXb309qOJcecVdx7nhp1atVmqDMtknyIDMMfUzxNKDG3KXArzqLmZxg58co44w79C/qOgYXONlZNvar1LTszyClxQRbsRSVxyMdnEbETH225TqpFz3NeBE0a6OBViW0VU8fkE99BKLDsnXm4mkn4wOquaYAx3TDjri69co0SBg0LAK531u0RE9M08Fv4VGb0tQYmmzlxuymog5JHj/UR9h3d7/DqS1XaizkWtpZVTrj1WrbFtT74uK2nUuKkmd5wTdg9U5458jn+4S/YrSQqPN6jC3d6mtytd8KvUjyCmSZwQRyFqJiLiQ8WpK24QD1sp3UJzwJTTLn33fyOgg7C/Vtvs410fyQ/B/XxfpCQoVBYhLz50+p+5Vi0IAt738AkKKskOhKjvSVhuuTJ0fTivQZ3nVnzvmNLVvhNGg8h8qjJm6RQB1skFI5LO0XpE+e/jmbt6YcU0c/PB7rHvteun+3fsMXMQPJPI/kT1dvQ8BX8KiF/o4Pt/eF6T6xD5+NjuNRhoseCOz6wulDwPOnrZluXcBPHKsa0Hy+oBsILaxUS70GX+SgQjxyukep6ZYQzK3uhBWn/tiU2oL77VDGnekkIUJ7K0YEBEal0iPVGg2ykvwva73ppHSZgrgoLSgpeDwlUbD9MmRas//rNe4v+boHZhWFeIOGmJsacj94IjuRF/XG9xMvSFHvzAISCZoAK6Kh+Mv0uQwq9G7t25pZy7g2NXM9ypDaXSU/cuTwIw5/hSUZ8ab5TnZBuVNuKR/jSVRXkvDM/UasRVr6JosKJRikeeHocbcziXC6WKEj8sp9N9A9x4juMtUefqOOpHAnJfbOpRvaI96EscyL7aHrUhaiMXZtGEaxVd+g+gGGLKVV6b0Z9/OmNU7p3zDCXLZmRq7+kIieLP7lpkwdGXUJRb5Gwl0evo8v3fDT7KHOWxqaybcEBd3GmKeYfrdKKZaZYexMPmdmW7QjK6yEZPs+XnkPoTXgvxP6LnySujrI+PUHg997FsLmOC+nXjXVEKlo9Ns43wsbAHl7nVkCYW5VeNeZwXdV6egraaauWVr7RYbEHGk73XwNJGZ1RvFCNVnh0/4t3pBxV5JRI5GcUyXD3tQvIeV86HrhJAo0me2hNKXgJmmApUCJ5BdIjGyNtcPiniECNaGHm5n/EWokw4nU3bUspl4Vx8FyzNjxfxWhR4W7liQdys9nu7EDmwAVK2qAzqgpqGwnUAWqoZqglwDUQ65aQ2zoK9EUXpJENy/+1B5R38vUHaj/KHDwLhhdpYDs8xDmKroe623RatsaQpGUplO1CXe1RCvrYXTzAqmgVIffsWasF+gxErBQscEPu4iWz/ZeVyF2D58BjDL5p1trqBAv0WW57nzCBV662zJAJm9amT3QTQYDG8oStOYyW8c50A2imP0vjWutSbYdIeS/83qqs03yntduZFhz6Ic+JCIJDT0BUBdNUENXoeC3AdJIyjADYvUlj7WJTtBDpZqNRbmlcQl7J8jPHlJduAKDyJY6vp4mCujImWZotuzL9lG3EAAhM5oKUUEwVOZBR20mA4VQb8SioNZ3I20jaSX/69qZYCXGKlVt3GG3w2BEc1KTLlk1sd+F7Aup/BVJd22wmMAeiErdsRSO/hpMXB1tPpFBh92qRO5d5FGxb2QnWw9XI8JGRGmjiAiJel8+RBqH9Y7ROb1WW6TDerFaicy9XGGYCk40q9AWRRm0/iUC+PiAya2IhxSU+lpCelKKlPbSyPxE4Kl7r0R+47bb2I99ReTbuf+AfJyJ+gk/+AjRb7TFpjzTQYDBjlDtjRXjuGmbvokco6+F1W/+/EH7bTi8eJOs5HUnYiM5kwQ9htOYScnRXn0qIk98nq6Qad9TqrTOzeOoqXxbSPck+Ab6j9rfbZQVwF413zqDzcX8fsxXNMzPiVvq+EkR6Ac+9hhiLVDjtIM6/BaiO3DYnnSj6yP53eG+t7O5qLevUVMwGjoEylr8F8BmuCx8cuqrGM3rxCeRXrKgRSw5Z0YfizNZfsyHKf+JG0KlxhkME1fy86UrZde8yDK1wJFIbCtPtKKZzRXGmbFUieQvMvVChZ+0c+b/pYAQPIQLn+4dDTwSYOhRFj92jEA67cKUKbmBUb28elEL7aARzZFL6aRIdZvDwKZknsFfL7a3oPWTmJ3Z23skJpc4vV6uip3jMRMMGj/U0CPl0385Ju0n4CpG74C0NE6oWjGdgiP6OLR15VudFBs7VkUwqHsLSRLCF9zEokGwifd/lgJZwLfUOMtoP663x9ttqewSZtC7svGF3beAlpNsn1Hni9eVLiONghULUXMFvUlrPq8ayIMrIOxO7dlgPWVfrR6D9Ing9PrRxLNAsfIgSpo47FWp/fn2xO1NVB7WjHu3IqUtxxhyM7eeZJDUWsE7+3ZecfczcrA7yQoPzPes4VMp2lmeahkY2M0HOkYfV6EiQZVtSQdlp4FKhm75Icgoe5D/1Umk/kYpkCJpg+jW+aPqVvu3GPh3AOTtWdhb8dDKwpEuNs7TIczaGYEooYntCOfzxiL2gqTQYM75/jTVAM4IGgFMUyiavVCL5T2ABchyPv0/Wg8Ys0W3yg2kCL1BYoLnSBoofy3DbMzDPSGnxb5/ecjd5iREVipZb6htSMvvCff8yiOjtW05c0+T5PMu4I8nZkg22YF7zmoUrOXhfYsCsm93jAIky4vOu9i3gz+23o7G2LbZGhPYyC60hZGhafd9DOnL+ra7HSlxAtV8oTFCpkFXARVFOS1J5nVw7vsT3NgXmpeTWtiTF38WhKm6E94di0FTHY0AhIPcNgLFjOx2jmsLR6BkBrTcWBe1dkQOBsAzlPAIsOCyh90ftUFxabH0KTyZPNERtfh2hZqLUBBrwBTqRgTnBdaeVLxqjGjX1eWXYfOiyhSzUNY4psSP3QsIyK3XjPbbaDQXXUvzoNBGc7eML2BS+Ag8KHFDvncOO9GaXdamV6Z9fN2QSmQUikFO6SMPclTa2lT+CrmnvkZGtnpOKwfUv+z42wg9DHw2GbKBwF4szE12FHOAMDOmhdWSvVL17kn3zI1niToOnhVuZsanSdAkKqkQ9K5JEgLZ5MhOjbQNdVYsrwT09FybtLcEnimD/cXNGTELCS9UspfmMM1KV45mzYZrIO4cGrcPVCuxeEptnwyexHbFMjVTwxLpqNixxfoqPOdDYq5ttxY3RsGaYrEqhf6xDCv7qKa/ZzcwIIcMDNwboY1pAIbcAfvW2lbElZ4xrpWFSVB+lfoY8ZPSxUpf4kU0AwHAh7oaUnkELEi3Rcv7zW6G/pK9wpkbUIlwaSgdRReKr/3LDup48KtSkM40wcqDn5c7A9zRl81kFqEUUWJHcD06gQlxFrzxMX8zCpQD7sttQNBh/5S0FjVkG62yZ70oTM8+OranaSXc7INzAQmiOhO/xKhbHKHGMKFE8ArKLOzB1L6Q441UC6cIqp52RL2j/IcE7NREReq3d3P4IN7cCeF9hiGWfyqOiOwV7ArWc192l7KwOL/+1VlnsG/KtMnJEMYVo7SMDzm6tIKh77Pu/f1fE4V+sz/+b09ceyzag7rXGgVnn6Qh8vGWZ+chDPKLqDKSubwlQ01WksHCpAQt8C6UxvuBCnzst/khcW+wEcP6RPTSdXdCAKK1Wqxbnpq26mieS6O0fC2SBK0AGTOrlKVP1iDo3S67uXSsqHFO8wyEM9HfiacjKMLaLnMOT8rE3wmdFz5124mP7vxygU4F3NHhsKyidJ7uPN5O2cE64pQSn5vMIzoIkHBJ4/pR/ovbUPJQSpBS7OFh8MvEDv4dhrL5Kaeb4mU/ZDLL9pXgglzAG53Nj80TMQAZlkq6waD/WeBVJNrYktBERvsyv4NlqVO8yg+lcz8wpPeXoTfTqrqJP5UPyos6pVRROVp/QM710j69+97OlAjxLGLPQTWBUozQRI8PQQtW7AFwPOSrHeKHZKr3OxVS5yvIrr3KIOjWlMRiCH0eaAWcnICeOYQ9HcggWq5FeZ+2huhhODt4PFKV1i7LLPFasErTHSKqVRFpYy0rXM7CZf5eA2Zx4AtwbvbhOnXTUU7DKrUiSYTVBLXF7btBo7WaENvE1IiQxy0sjQPghS0AwSMYaZwiYCVhiQ8VVWbxcfRdatLGlOhdqFb9qKpRQRxqhMbxoBw2l19XABap8A9Egxe4kFlMKbSV+DEHudBs0dWfRqxLuI374QjtQwSNLG0qcBSYFswrYCSBv2SyWTHP+BM2GrJFwPxRaUhMPKtDI79XJ9aJdFssxEPlbPmQoe+xI4RKtE9EBiBkKcKpNbFG8cth2luQha2eVi+2kk2O2naKWklvqupq1aZqZ9rEWbz0+49exImFp5u5Cw49ljlbdeN7+3tkV+E1p5JzfY9/C6YuorlLul3VLJeIhgvfvQMTysOjZmp5d+c6TRaU6Sr3vKvt4xaAg6F0fLUFQgP2pkGtYHxdFEVCd1Ur6Ac5erobIXwTmzaq1MmJyn2FJ1DRhUqJu9s3jd5CwxQcvnJCEIFZmvYAvBG667PNiaRwb0sLx4loWH4UWSuXasWm2YPYlFguCiZXCNQV8F5AEQzeb5urt8CPZM0hHAi6lUPyx1OLN/kyP1GTwjKwXkaSdnHwXYXoL6GVIjEKAJZebPT6N3MQGtV/Vxw3D45EY/afPumclnR3Rn+T8w+NQ0fdpOkpxgiQ6NQVsX0i60FAhCORg9hWhvnVxthH/izE9r6NVvoBm1pzoyOgPkoEgmAVwm60iOGOOWpMSXfJYv6rV36gY3Ao0gLbQcoy5lRST2NVFNN/fp02xoFPKRqxcVBMEP0kNTyeTmb3lA9riVlf+UNS3uYU8MUTzTSBXg+CgHnNRyR2ekbi5VezFmJ01aGY0G9j6Q19zqaq84qvskaja2O/KeP0I7pAJL3EEdQQm/oKRYkHA/JjRJOlRfNHrrYfhNZ69Dx8Dc+2yuHoq3QGp//LdMd7oTrd2vRHhzsgGCfXPDs7MWs9m4JroBShc3BvxjUlurZTq4cz972azpkP15yHIGDaV5USj94D1L9bzF9eSo542sUUr9SBvM/78pE8XPLsZ57rzrsjZjTSA7Z4KADzEYP+oNCBkIEuXGOvJGNpIC/5R/WmL6jCxQSIVdBRvStFuCLlLGIQmA8P3ddG6MyOmeniPF4gT+t7C9DQLBFUGZgvS7NcHEiM+UTPTRgMVmF0vHkVD+NDOMKhHf04zK08mIwN6y3En+VXkZ579NbYpPwT6rn8m1ONXDXwzpmlBliK0XuIjnqPjdTkNTsjxD1LhqQgi7wWPpGVDLl2kqCNJLIh8Yhzdqd73PF2PK5C1xoOP6tGuSMcoxjKh+KWIoh1j44C/rWu/7sDaYXkKfuQ8WV/IQ+js/5IdnbVI9xC2m3vPNN02gXaoHCCWsFlKPymRIRy28AS3z4FvvJwJ6ePKlVcRCuOh4w6lKCFD1uVTceDMX7BWsGSBRQ1zjfdZQvJ2HGpU2wngLSg5XUEBNGYrsETCpT1IXntON84cQgV3oiluB3JSaW0PjuEWG6lApEbmlUOVxijB9ZZyfkOZJklQttMHU8vJQd8q0vVQ1n8MFU9tqRcN0RdkNRHdMhZfiaDe+AJdxXDstr0d2ReueT+GkSQXUiBWEiRMWp7IpuccZ+C5limXfJ1r1Vy7D7lLBRcGO4rf3LdYU1uBdPCvi2x899rpN6uGDsVHXa2AhCxePuDmWgRq5cHCl5W0a995USQhQ6c+b6zaSw4gnG4z+EQ4IvwmxBskxn0XyQRQjK3CFWQ5QxDW8eCmLJVKrXGbLyAmfejzVB9QcGqiLy+QjxZupACxIhXjZyZSlfCLrNttpcTnhIVCkiBhag9U+ybWfxJtQIQ4sqZ6ftnoB6SdE6cdrjYWzLwXlHhQfCnamXDPeMKnrrk0k5aMRkusZhBzbY5yoA8p4bza12ZWlxh4qM/1I//49dom6glUbBzlI0Os6oKAxkfSZXrqR7Z0KW32yGgUDZs5DXHOYrbQe54393SWaEi2k0lQI/b6oRmai/+eYbMZpv94M+6y3Tb0UgQVGYUmdp0tIdzBBzntFJcXUseOWBBno7DrnrnhRNcpE7rFTVQkHQ+rRtpc0FTgMLD8a15krGm/wIfR6yGwu5vyp9wZn2ZOTo0MkVYnKrqlFp+YHX6lXIFgaLjfLIGm1/aYl009RokdDOqOQVqBLYOTttcNdN7WBh0+LaFt6j1YJ71L/P4iDoktIEDk/O1k9UUbgQ9oO6Mx/V/Ial4p/scLYD/wzpO9y8UlKCBbVMTVso3E0MwiDegGAfsUoOrE3EMwQxFXeZY9y6sPkE2wvF8Tt5oizV9+H8mUbAanX+wDsjwkjvJa69y4Mx2hLlV8hC3umdxDje+vXzb2NPmYD4PGYcWqBG1ySUnS7fnVfr6Kg1iN9gOQ8grapJEObQGBamgQUPuyqPcdjoqACSFZbN7M7CLsvf+55mQ/eURQ9E69XfudWsWvMAG1T5hmF4aTlhgT6/otM0qbBNHtbfA2a+6k4O3PE/uNbMOgcnvrkE0l1pwlq1mqtzIDdHxsxEEa8YyF59uI9Y8FyGGLabMT9CS/fpiC5kt4M6OQbsFDnEpDUSkAJdE3ZdRGbSTrTq2A9RnitOdUy1pH9O10BU+RcxvkKiDvhXRy5ILSuDP26eYTuIe2b9BSQnctA4Nsji7f09eTKud7CbRxVNg4QUolod9vQJOb/bCeGKGOoTBHPR/F2GAsBPeKhNY5dhlqikWwCbPfDAIGPJyIrWddNhUnSYoh1II/YZgop+ykBZVeBiGnBG2/bjXT9WXeba0J58fT9KpGNz2sflK4/GyB3b9DS2cravQTx1BRkMRcdhmmcXdDQHE8Y6lEmz2NVERRAcsCQfNuRBzRcRmt8vwH4e/El6xL8ooQMtnyiMPdobiAD7iaGAgsNR65pSR/vHi2vC6r/KbtnnT/IklGXPK0ZBHNXTzIfXQpUizThYpd32WMQ17h5qPTP9qZceuViA/5HybuTs4jnwbohFArN9sInkq6rQ79Ng1UFa0rt8LMZPiMHP7kaHUHLXfY1KqxC77xwrxc0ymUu78g3xKJ08tvQ4t25iJku60sF3I46BFy7dUsxZ9u4zaHKqVZ8hyHGSD/eQ5mRCzO1YF5eW4wH2vITISBWbAHyaRVslCMO9XBF7mVApjXHpVegEpfSVS/NnBV0utyu3+don46ivKr+yR5dsVc/JhqtunHecr6prWGNdbOedKLpjobSnsNRA70CtqAYdgXmQlyzR4DlxsvXXn0jH/3ma7IjbrdWLrS2YMBaLCKttH6pHmtjmrpJmvVO+vqOqCBTyhPqeViV907s1tZgIliFm1oZn95xKoyiMElJ6KJIR0ic/gV+8NNkOxwV2KqqkYVoPOCeD3J2dVeKEar2PR3qLajKAySTyeWcrsR0q6l0M2xWvap85lIepo1z9CNBNG9ANoWCIFP+VHo2RcrW3s7oITnpPI5tY1DqzNNxG+lxGz7ROG7KhfafMG8bLMNtWDgUf0Sz1fyL084y21f/CteDpgsbuzWe+7NLrr0vCW+2moqfuy91abLdInX4w+S9DmtUxrIL6RUk1BKtzlV3IohzdwVnDv6IQCSW4cjgQ0/H6UlBMaP1UiJXBJ3BVGJehMXsRWz/gQAppXzUs49thLMbiHowJaPlag+EVsbps2R+ToM08Si5OkJbrV2TkooDnYgnIGn/hroz7F2AVaQYlOTRY60THOAlmXEST2EAr+4yv9/wQDmxT146ro7/maYvrlvAqnlhAT58UZhVw2DMN3gYxp4OmXX10svKa1UtkK2bXT9F5K367mutB9LmD4OQO3hG5LVQoTLFrnCTdacpL0sLW3VthvnQAyJh28YfQbxpWtF6717JgdriRKCO4thEeveu+lt71Dh4la7dNgGCCqUO/03921BBiHLKLFxHrP6DsaUcHlEwLF4MfmMRiA7cPbkoDWjS+Ynnym3MNb80wrK5pzZ344exSKjSixC72gWlDR/Z7rp67MXJu+UW9ex5KkgUmyRLG1hFvkuwlLRsWa0U9caKvufYW6OX50TYG5tG+kzalX6WRc5s0ReswYneWUyN4p4rQFU7d7mcPU/S6S4danpJThoNPSCHqwlQ32pCDdY5IeOr7mcUrnEhxxSD3m+0uNMF/ZbhFrWcpfu6Pml6CzAQVezjNHnJQyAZLyJWIzBJ9aZwSStwyit77JTPu0QPVy3CJCgVx53QEbCQ2nksPMpgEms+6ivnm5GZIMoyE09ik5X0TmT4G9lnaOkezxTSvapUkMlFwpZ7fBlqni9qYLdOFPA+z0LHWT8jD1qQOcJvVUxLZTxxvq+ayZsvKFVceTCgvKNUdnQuirfBqCLgGeCAVRCN0XFEG3KjaPwx/xa08dYCElxkrj1vL/n8/gBoWeYUBU6TEpw4NFMS6S/TBP96IQJF7rAxD3J7ztOeBP6AUcQswlAlaajgCNoY2KnfcUw/NzNJjFmCZR9KFJDR0Hlsqku+QwVoE+TEItBtfJLWALMwVrLpc1oOXmjSM+Z6FP/eGkACit1W8U7Nou1ylbW0RoIvfd4ZanDkj4wvLN6p0F01PmnEwEK/SYEmKw5+nJZlcSJ2KwHP4R0Fi8OixCmBEwJRMEJLY8/kg2vn5vOA7frh6LdZEsvaP9rYqsSCrKKBeVS8QVENpCFTWwcqRBZu8YuBerDemy5LQlxTtRpEbB9CQRKZBPiNFE+u80uFsm2RUsYPcNbJrnRzT/BgYF17xBHDYGIssiGM9WAKhCGjO4Eyh7qubNhopSKngnxJ9Rnw64ho3Lzi4V/rdhQcoQWpSVVKVxkD5+k6Fjz/CiY+KrxQugBRjLajjM+GbMjhnyCP59qMwpZga7fPw3xG+8Hg+Kffne1P+t5XyuTMebYflVYWEv7e8ftZxqHDJauMqXLb7CFlsLb8dITKbAMO1ge1MFPZRH+1nWBV+Q5dJftygpL0HGL2jVQl27TEa/xv9IXeHnDhMTQf8x1BkP57+G1klkydmWbfz21MBzbZjO1aVg7jwm99aQM2JdFRmQAVgWYAP6B9gYFEMU7sW5UHyKZG+JSXiJBj77N+qCAeTs8BKVIWMg4H3KXhsn0hI2oY8se+b13REdHxBhxeylkEki5U72v2aROG/Vn4ZTKsTNG7hye0F2WaECr3BMQFPtNyfR0UdO4zawxanV88wkR+39JD35JVEqbHEa5f2zz/BcLKXIM7tha556UKT2LvpvBMZvaH0r+PI42MQPL17o8aJJ69FxowcTvOuxGtUVTK4YxMUGzrrokBvkbRTsCgl1MYReqlgIK6ma8Zt+Zir3lYIyEZFsQpt1+laPKghih3cCEpVZbBfOa0ypKTbBpCS3aKBAVyKjwogTu0zUpKBVLI1d2FcVZmGbt4qxjuxtwkerLcgzd4QJC27zMCrBpoYxLE0b4tUFCHXEXUcXG+6bqDBps3ClPAesIl/xsEA5ILWjCCe7VPsMHwQjWOLAZkLhFJXWyjQ9+FIst0CQ8sPXP9aLFhcJVrK09hhYQ8Zo01YeLUOBNBMV1G0346NWWu9dKJ8tNCq8FtW0SR7Kh+xlZxpf1kN8S5+waNozH8qclhKHuMF2n0htuA3S9eD1SDMJlPhtORRgYSvkNRHLJ+ARw/7+SuDmgvIbKSqwIbpbPnFpLgxndGge6OGnvhm7ULhc6Q64i3ldrqbom0n6ZZy+lw8gvwmiCBO8TcBK9QBEV/mOoP309FJ236HN5LOsSyVkePhNAnR2bTLiJJgqVE2KcRjHnN2VS3B7lbWuCrGn2mYEQIvMWP+eYy8bavpcRvL2poUEnYY3AyTx+GFSeK+lmMwfM5l+H6sS975npGTJKqekuWWG7TexUHW6qBKgF4uBFlQYf5J5TV4tlDdL8yqqWKlXn9CCm36fzAaJ2LlWA5zqe/mcnm59tz2dsDXGWGzNQ1378nirJ2FZPmxZBjprH0llC5+QhkJxFCpKuDYPS1QCj57KsdFEy9nfe1d8K1rJg75hpXBobhvgTnTsIt6ZkNCbMsxzjM+H/nfeJdfxzEYBlDlGJNOHGC8CmW51YtcOZKFmtZbWKEXeRKTYc1VYczK1vNKGnfu3BIgtXHntjfXIP9BGr8Z8ScOI+Q3A4QDrFmi0hWTSWS1m/a95KFSC+97eyUVR4Ms1psxEFekHvE2mLHtR61B1iOrGLhuCuXBt+zw1jpjQdub6GKo5Jw++POcfKMmX/uUV3Qj5ztrCDrpbgPSBnbysuhqUcC9WEkSdnYvBTXoumsCiYXEI4Y27Y0hYCOzvV8uLz+To9Oe1Wb4np/hKCxFfSKdSLzqO9xGIN+SJlO/JlPLmL8JPuC8iqk7EfrpGVXIRrCCrq6dMRmpj37Kiej83fBhAAo9X4LU/Ctktx9yziyTugtgp+sC/8qSI5yqJYWbCbyfB1Kf7B3HfanvwuHmXJSDXPSzLO1EpcYV3XykLo1ptweyj0byxL2so7ym1ALWLaXUyGnYM19ocDD5Y8h6kf0Vx+wmr9eM1UnIkMTQ7LJZOgbTopp/T+OMElGkhWKGOGX+mB3ccDmAnGOY/17mO68aj8vxoxQw8EcWsbDi6C35mUIi4L3SBcGsVjMTSiy14DcG6cL3sovKSEnIldu/UjK2Jsex6m02DcNhTgLoljMd/PZWpLI6gumAsRu4QKKnETQFrpSdJ3Mp7Iodz0btwHFuhrXoqg8TjACh6XTXKhjhkA6/eSrgbpkSi2fYCwHTT6N428XE3G7NftXgXLGp4/JFWotMZbeSmw5ZgRhFUEM0yLVhPekQ4zzXf/b8n5f1sGvWKJ2PXLL6gmNg1AnqHkCXVyQYIyc4Y4waWhq0D9ykkxI4sYiwHqOuCWeX466/QRyerklzZ6PbBzROOUaLIP5AKzcutPuJcLkb/bMMBDO+wtm6ji9Qv7r/wHlVubK37sjMQS69ij3Jh9/oG+Z3vvl5c/+vGaWqWmYTGtxg7EtJGYGXdrCGMtajyWX9MyI8GBeqnLpvLKMyQnfJQ7QihvjR2N0vAlYCVsythcJEC+cbG/ptIr7MCusl292h54O/g+L4rt3sMMZ3qQJGfAHZQnvwvzpkS/x+WStsD5X5UGgxJ0kKcLlecCAL82PBPw8wpJwcDW6LTYPZrjt/8kC+udvnbV+6ZI7mxpgMVo5glhUyO0tQ6fy6Bv+zvgaJg204KmRWKcoZcNZHGibd/KOuG4aYKo6PGKqWviH3es0/SzsnRATQAlrv9m00xtxv/8y4Q4jL3FJIwljsvNIzmHNpc8b4n7KuZfSYJvr+d9srpfS5IuR1j9dLZrvcYsJ20nOJBX8+ut+JbuqUprC0Tzabv5wqaJodQ1N/iNG3nPs6hf5nz04aECXTM7jaF7xI/qog4boaGshzwEwPFI10Mb5MsY2GPDa3bc7thoTIcI0zodcVnXr/KKvVaDrVqLUTADffaG1dV8b357nAFxGN9lZLyrUI1m6SKAtqUZl/IidSDyDfZZ+GUdS9ZBFD/+ECGAY20ebqqPSKoKZOt5bwo8Y1g4jJ2DobQk81wrXI/7/4frebvEn6E0cwKrK4gdc2Zn9PAHYzNC384iMyWGUKbMu6KiZ+DIbeGiUgl4tUF8On1b15axbWuakfHxe6akz0OlixTX1rtt6RSMLsDGnE9lOjc0Mc1P8VWfo5iyV4I/zvDu3j2U6C7t9K6DNeIeKort2OaTXMPFG4WGjbhnPWKUAHFMLCZjMwbVBwUGqqVwqPrlv6Gz5IhIItg8fzi0F88bkqmQZjybX8yN7acKT5cbDpQLQQqpOVJnsBSZP6RDGaydw3VxTk422WQ7XY43nYVfTgAFKx/MRQ+0xoSRMrJ1stfuvsMaiW2+gGPad2wWr1pvB+2psXeWjUxHfMrZPEA2g5XlrBDptS6HnxAb3Ovfiy//lREQUUAKb4/IjUlPopE0Zxg96VDpEbGJDMH9YIJs/EgrbfOYrKeb0k/g4oA0Fh/I5OdBGVWwe6Xzag7wInmCgh5FI9c90/hOZJ1cCxDwu64CEXw/2UG3CZvDUSSzL8KQgqZ4D4JLSqKJDczlsHM26y4g7yPgqXjD2pCZYG/Rn2vM6kZklX4mzblInhz0yLoHGv9deO/eRl+XGLk198bkhDIRsntNNMJh7yRTF/GBGG1UXYvD+p2SUYANmFRP89IMREBZu/puy7e5i/TTmsZ6bHHIgNNpwzDZ3Ti4doqr/Fb2qM4INmSOtF/broBLPNbeO5l5LUHw9bm5754Smu/m5N8wm+YsVtmEBDksEX6GlUMKCXOC1CjcPnMbDbUVQIQcThIOSrNDRp0fiaYGq2MgDHM1qmB/zPr62D+DlS4/loFjAD80a0x5ZtbZtfIcHvSvPqDVdCLeXmNnRjIYnRHkAuWUrlMGkovLYpV1fyZdi6N20ppQkjCLFXOI56Cattd19iNSl1IpGgqxMSCmhZUAWUCisZbr50kEG92hFqj2zkqLcTCxBR5nZfd62kNRksZnS5B/ldwyZc0kVUc4P+E5IggVu2e7Xjfa0er5eqDjZsKMuJ2c7vRy+lL+L+yg7y0f1oe3k+eWtiH/izj8rqknA5dHeEgzVdnDaK59MmLFuuC4GsJCLkMatr8iv5JU2twGzFRzw37mfSA5fHrAySUA2fN2NhEtzcUJZ7NsUm0aZgDXuVSWTTJkGmh1JQ/zyJxiDG6FvbEX1jFtNRVHCYpW+s2dNHXUtzBJwauR61qYEEmLLjFojuNby16jrTV/GkW+Wu8ZGdlEtSmajHq/cxF5Ts/jrrTf5kCYZnBp5NbcJjKFLF0HGNBMxzq8IJxMlO4rNN4lx/RZAaS6tC2RagMylIcYu/Krt1dQZSldAmDtkHDLjpBFJKwsPEiFjqQA0UOlePXSG21iigPTVhXJVJnjXeMZPwq5euRZ/k8ZYp3kGn4kUWl1+eLfD9wtFrMzhsI0uuKSUs6KVgqNsTrH0HN4W/gcCFqNKyxW5EMQ2BCjpM8e6ibR1Jaeo/RqfLOHyNUGc5upnOni0rHV1tMtU0kX4dlyKchyX99yi2eIMWsHlBiOXlw5qAz26GXc7Ks6yXRtU794J3bYwaB389L/AwZVnO/Cq0C+P8Bi4CpE7tvP2IQ4xloXYbPz3PtqsrE3+OoQ73rh6hfguPAYrrUVvPIdNTLcnmrqWK8T2XE9t7sQf0J1CxGB1BR7Fu9uS0C3vvDbuvqNaQnnaNToVZuI+qV0StZsXf9E9kATrhHMw/sQfLYKZtkJ0o3vQ8x7xkv0PGJQi9vM73oQJ1dz6SNLCven2a2P166e9pSBU/1OIc6f4rVLlmjEWyeBQOk1f8/dFqy+IYiRcPO3WrYwvM/p1A1olRMaeivNHTLOJ4mTq0qVtUBSyUHPaEaMs1iQIXeGUYDO71mcdiUMFRe7pkGAsCB624prTLrKtlYrFJFHNfdWa09WoGvRbphlyxjQAyDwuh+S95xSrmNSeZVBEcgzNpfLbsUrDustA/Wty9qRAjcmHZF0WAuAf9qZiuCv9+JlAMFAyQCwG/YxxYrVZLD1xSXgTmjb4/sWe1hs9OUeT3lxKdmOMPgRCVs2HKRy0cQkToOpKSMUtj6iJ1MH+a4WdtHBFOwtVSfy0XXffLAVY6ruY635uUiZuhm/DJhaPr6GrGRPVGgC2kQxszSINMnYL2bar0Y44bX2+gejdjz7ldZwedUgombtGXVHyCs914IqcBuG6oRy9q/3My+YExbGq/H3r9eAFtofDbRA35ZanwxUJZo5CTlo54eHT4wnDOECOtSCgZVaMpatXpV0t4oUO9pnTq9Vg0s09EdilEAuwMpiG41UYd0r3xbOYe8EmCaqu38U6gGZELoJrGq2gyIgFb1fvaAvqFozo4Q+YlWwBcwXQKWR/cTiOM12svudKj+GPWLAikbiPl/y+GOgPFYuHS15HZsPm697Y0Jav9rybogJgJp7aTGkbFzdocIsUmmdLPySMjNhl28sq7gelPGlAILz5YVn+kPcphHcH/kbCw+O25XcKlBrFviN1EIPKMv4SN0zthTVLWagkyvywxL2/6UDZDmYtG/ITlh0JLDNnCbUL60SngkjcItStndmqPrmrvjoMyqXoYm97RcgtYVIsXppKOWFTjr6bES7pNMISJj1m4sSBTjF3ZSE9bTzhvDoz5Qcb+yajoOV8eNTyRYRJwupqXYnlFKHSOIKxbzuTCHf4m7EY/XuH6o56yoki7ffEU9rneCvUSi4MC8mcRRjgCCMjFuK5EHcLHB6u9am1bepFYXWsdJxU3dlm3+qCUabL2zN6VqqlCPNPEV9SA2vQmKxDLf7RtGDXh+o+X1aJOfIVyx3yLk0+mU9vyjyeUx6uWG/SeIBksVoRq24V7OEgAZFki6f/SEUT5DkFhKHn3rS1vbpfxfwIVne3HamEFl++HrqQAefPQyOWQkOCVC/dRXhIRQazLztG7G+Qoh6hoZNHF7fKdGQ4X/0vdm5eWc6AwDew2zSN5eKOSk/9nJwGcyO/q3dr4oKIBOKo3x4SwtEpLKHrOMd2cwkXz6T6uhP3ToN0CPpmfd3kh5qGNkBt+ZWiJT35ZA+GOshIjDPaCQM4l9Z/i8rSXQkLIKfaftziOO6cQ1rL+zKClo6U89CUWO951L7GCsmhJmR7rmRsQC30YsOT233RhAu9jxA5KOLmNMah1KsGAWP5O3vNoR3c/qGFJ+b0NTOij+51e3AQxO5pghlq3KP6WXcYWqrIiAqAelG+ND47jwVAOeKXoCFCKZAuZJPpW++dQ+XzUR/7EnUiGFGKhFEyYq0ko6BJVqzEEYpTBL6cNrp1WMaBS7fm5LeFyrBo3l3R+4lz/tdU8lfVtg9cdLMqtBEDghsdOKFDUvaWTk31GywlFronybbix3Vn3Fj/nJ5JULOyvUEUvXUY1Ye6BaU6Yxs6VbSUOo++il41DzdRW9zVchFtUYt9/moelC5lQupqKcBjn0iF2GVfgtCiD3wcT7THpQEMqEnBtGaWTyZBhrzVT1ezr+XFR605hWv4lKcQAsACKlNKb/RvLf3g2+HDlaXmAOO/u/Gd6Jv02fqC9dhAl3LQ/4XOX7EgeB5U6+Hd9awm5jtbm4PbCq6UVyf9QOVkYwySDX4Y7F//DkujT0SBeOHeStfJ82uDgnXbaE4MHleJotN8n49RcYA9CKy/E6874DlYNmvpKRKlRZMz/tj+2ba1G+0KnuNj4yF0v/WrqDyuN/GjbuvpmiZq0UeUR7b9zByQ+IhRiMmwIAynC8Ot5Ncrvjzf6M97VIab2N2AWzKECZ/UFnkJgxNx3fB4NPZ/Dj9ebsf0gHpKDtPq3VOVRxhl5C4P+9i39LNjwsIBqyUqcQDexRN51dr5sx1adMtLA7KpC5aNA6M+FAjClI12ye7UDJ0u6GIVr2Db07WRpKURNM1rrlp7LXbmXPRoEyBSKJVeDAiOrX+g1MNDYBGMNhnIXRo0iQN0RXtwsiyNHJKPnxUSsE7moPFxRRk6gH68Gmi1e6tEpqiiYVaypNptlEW4ObKluNrcPDfjhb/ESnrALEpexjzJo4gYiwsr8ZmwzpZF9PEeUjObm1cwSgrl+xt8D+Ujc/YKAy2XoqDABR5wdMKQJC0jLlOm7Ks3pEv1XbUzt5IXzVxXfzmSunFyUHrACTjIr06CkkwElj6KdSj+3X+jFiZk5jGxpYFwvHPdHPdwHRibNRytxgM2txUN4LoV4m3VZlf7fO9NYR/t36oiO+jISsZhMZwy1Rd6DUyeIPCzuIp6uGyysiMOg35NSp/5o7kFdUScPGdTzM6Vfq/BxliECmNo0ydPZdQwrZHuMUF8XiEx6QCBF+5XkpjD3eZ9TmULCSXO8cOhRP48csDTNUbMBR9YPDLfOa8dxAuUEpfLkfV6sCLgqZzg0WeOZFR+om6P6rJ3V1bSeY5c2OXF09mg3mlda+SXU+oJHTcasdIK9VMjW7zdKon32A9odk+UsN9eFFSNEZqTU/PYN/hgJ0sCf0L2AdnI35fm7xKhXyEVmlHpSaoIVPR8jY/BhV4HPkunr6q0wgft7vdzoOVd2873T4E5Lh58KtmSTmdrKhBMqMXDM9eQr59tKRNnlsoAUjUDDIANa7b3cWIk4S/kMg8PJT/ni5MBRsht2n3g7ShIUsWO8M7DJX8qypjk9GEPfD4VMQHNU40DbBXPAyZsoirC2IZZovl2P/UXGwU0PPWckXPFza2BB9leOoBdTNT94MGJTZ5C3Z5OuRC2FTfNJkfWjAFZwTSRki4NqvobREkq0tStQEtMJaEFx05KoHGkcEhmbKxu2RTrlZMyxhXe/wKZlnOY3cDEAinysEcMvbvHqSbastQdbn8JAklNaQO0ut7FPHaPcFFzpi6tUkAs5yZYkRQGyIzcVhvj9AET5SwEYJ6m0s/Tw4WHxmWcz/hXCiLZnPhvCNfbqyTDBXV/xXMcCfWl4CkQ4b4IKCngs3xB+Ittur3rbgiIrs9nx1a6qtQOSSN6BWBsjsytl7j9O6RfUiRTju6E0C8YODHPZEgsfoYOgVdIG2nLgGsCzDwMAvQbf2dbcfTmvHg4tiUqpAck675H6+lRv+m6nxVj0JR/Lr1XB8tXVTcor7H0vgrhed5X76g/JpgCw8Eg+s53ogAQilh8i6/m12rPzPGtqvOqqf1afurGC347QdRr/WE9X0bTOe8L6A3gaOfaoaoF++qXGDpB33YSnAwAiPGU8TV8OM5lOR4wUreLqknxjsEfr5E73Sq1sN/If4vBWIaobOlW4B9NFySSIgxillCS2S3EFlzFAiq3iUi6g/jcFFpbJsBf2nlmuugj5RjEpsuXb6zoI0c0WdCX4VzbAekBzOpW48E1HBG12ubTWJerLPqGfyHLPUM3ikJiwvcrtdjrnTmvROvX1yHhPA7bri2KL4V7d/UvZHHL/HB37O3ydEBTPN67/Xi9TAc16+vqCJGDa0ZfwR147jLoGfjIjuR8G54cyFhRPMr3WqgBoakO6EiFSJPH0EppCXuaw8f+jW5+Av5CLIYyMhe7Ttu8PccXRx59ozLfQUy0Xub7T469rLQvKKWqiAqfMZBM+bdlWhEBsp7WNBTsAWVpLohDqC6CDw9b0ZtWHMVUHKsV7zBAeTZG7BKgMxAJvQvbPYMZ2jIgwLijYXYnvdZQ5jiF8EAD+NKMSEMgYtaryCQRob88Zvy1tjfeWk2dP4LJiQyBMcPGnmNdlEErcdBmnI8aw4DCcnm4FWNAIOP+HpXXd/s5RAEYMOgN1MzwslFRg6YRkcTTJa46DBzZd+n4wp3x7DNjNVqxh1x1K9Sifr8KWBHXXMwNpDXqa/SIEDpF5ET3gwauc0icaDhaPEwrgLucfEfaiySMdOHFPD7ibX8B7FhdoABkWru+0OkAiRKR9YnsYpdrLGa3mDvjE0Qj/KSyqYtV2/jzLolwIvFyQxqgKU1C3NnKf1lu0z2iL88LFN6q3C2owWj4yE0G2FSSXwIPuGzgmWFzCL+CuqoUxM609o0gr3ZL9yI/uzbH949c+CUOY1vGvNds/J1tEI4kt0FB++Gp6+y1CTuyVCpwNRhsO+bg4Z1D3kMuNJjic2OAFCi+JX94HjenAK7VS8WK9FdjMHAvpJS0DofMMTP4o40LlbHJJyUley+Pp90fF0Atn0YZsOy6D9w+KevWXcNM3n5vyh3oNABh4fkxSoz37UztjIdRS1MLsu6212vJ9ZIOQIrLiY52OL7awE+7ult8buwr/W/r8eNm3844J1/HfQvmLoAF8ZbkxMwidJ/Tg3bfQ/b5GtCVq8uz4Sj0ZD2FwM45Gu9svh6R8f5Xw9PJlcptc8wqY9iEd7GkHjvEGrSCleg+BQyoYqUTEq57ehB5AFx04KXg4MG+SKokPs/ZE3a5YuMRXuV3Xl+gh/qSB6LV2/mopPwDRFvmdrJJ6UiZrucPEwWki8gobALm6UpNvBCnpsuSfP9kZNDvm7VzU4jBvl/MEoybRwZMo9LfDM59OBE6V/6jJzHU2R2IpMHbBvLV31WdDjaDlSF1AriBZBVOGLzTeBfukJC27oZ/ilyADIa7S/hwCWgR21NpDoPGZOfvzHHDNCsxQO5SSIJuFymrJS8grxzZEb7ZQC6Igf0KOfSK2FZJmk9ykcC6UvPG6Fol5VE2nl0JZmNxSV2i12Un/EwEE2SBo7gGf0Ht/ph0OcjKe1vfiDnqZzfBrHRp1D6VeWLIvkAEaYbmdVkgZDboHkeEs1KAKEBYMIr4/c4VmTYSn4lLrk42qtXj+X3OqoN4U04sw+aleXOH9dTHG5SflI2pEMeaTHUAnr/apR8J75lRg35cuvT4EGcfd5YsmL7Yx9LuPcSjV3M6tv5lilHoz8hiJpyMondrY7DcHw2290YhChKEZW3KZhfOMCaNl7n3Pz8nVsacyoLoh9jeM+3sHIXsfBeIA7R/+TdX7WAA7vQh3ZHXANfxNwL13ASOeaeF0OlVOcHKdE9Xn2nv5dOIOHkBfygMRRTBJSHr7TTMj5/K8o08pAjFdDE0uRTTGKS5UJTLJLmFow972FtLm0wXmcICOU5F6iuvbDo8wvwmwQczVpH/OTus504OMmeNtl5fT+CZXtANWPJKwGsYKimQqtaiuUYHFuT/D0diMc8iuebwmSysToRaHLUKMCDKiICAfP5AR5TJS7/VssoRp2XNtujcujTtKRQ5pdYTANLt2+XWu6XmvBx45Eoia5/S8sAlkSkCP1ieQplTXFA82sFUJ+J53OP23a+xwoD0BNt6RnS5nnzet42XlEQMQxpSU4K1zTMPWZjAq2zZaZlBK4ZB6o/6u/E1voiJmxN9vmO3SfbuopJyiiqtStwrKwbtfH0OWHGtRph8txxjXfNgWIOBRKTOKEVmCVIm/M5w1eBVmrJcG1LqzHsuPu95GG6S+bY9fHS/gE6HOsClUH9jXUhkSkkRgym/bzafwTLnQv2qr/I9UsvkMkSgCTgNc0WotMp8qvfv0dpGOl1hcPojY3H8auKh46JhuRr54Xyj9CeIyFGyX8BInb45nx3CIGt11pl1CZ1QkaX29drEynng6/fZjyLOdn1ttJ5eWyKgAfn0rx2/N6iZllRB6+13/U/cSkIqhkzPW2hBoKy8FBmuBy4RQOKLaLcHe4NO84VZG6Gpn6Ac7FSFseVYmbV2iuBglsXleEy3FHSgJ+yPP1fEs7IQmVAAGLDRanrzr1DPxm715G2Ocys8mHHAYNiRGVDjfYEOpxoSYdl0It0i+MsYkD+ydATID7dxgVWlFBV0xJGzXiLblT5xpTUjSZ8dSFu3SrKfZo2HNESII9zjm/4e3u6qlDQZEsHy2G20YcNpBY3A4slcggVLJcGk7jP3dEOK93x+S1UevAP/UPBAk5L/T+kGBMq2mrSIyCT2P+qn1hUgDppFdDMBqqE+gLO1WsBxmYPLMb+UrZk/QEoq8TbiMDNDIbgfO5C4ancXfxvG0kfoijYUgX+y6tu2/+XXEt8AJiwTIFuTyq/+Slf+W1sDxXKsT49ixCkU1lSm5USvNRa6aD1nKPV1a/mWJSjDxxS1z2RFseUL4/7MQwDNUqsU/SwCFcImgviI7xmTphDSFcIF8CbRZ3cRieqw/b74JbxMfcNu2lyIZvB6PDMcZHGjw3GDNBiJUBiQHOK8Ucmv+pEkpB4xFmNvM8Q//Pjzl6qqcPzVs/39VsUtQfMLjnHBBIxzGXoBr8cgwmRIkB4zNc6vQ6Gy2jFggPzIIexIN4uVa3G98hZAIszUgGcfpzAu4Rl8/O3hQ5suGtSeeYTZxk+VI+aph0P+lsByoubHCMaHKuZ5WqC3YKnH/zUxicEJaLOWbR2aeCan2+wAJclctKgdO0dmedtw3I71db51PCbVXI+7nRtNCGGh+FxYo5QuS9TRhLmKXTdIarJdjaQ1+4OLnavM1HAMAcm93W+8R1oY/+0PeSyoLJNhaW4enr3JGUwDIJDInW1j2gu4j+ML+lfjgEBqdcEk0HkkJ6RGH/V2RRyMfzl6QuXGUh6U3c7CUNGcgaZEsu+w+CoLF+iLTzDRQFcE9e7uLRHC88Rh8MYHX8ZpssPotq+btXc3sp7M8NfowCBI67Ci9jY2LGNkkrE2b+VHfNhpYwJLefifTHSgw5ZLfzNA+GlnIQHX/GRbFZPRxz5J7mq/HD6v80xKCrr2K4unXxkuovmyrYn8M4id1uSqnSfphPLSAfe/ALJij6/Q3n1UmO9+AkFc5MmLs3WawvwOy+08TvCgK+elQKblpEiIH2dkhgnmm5PuZJdJB8ICWgg9RW4eK8NILpMzw+Plg+r6S1lK3laplAuPipaRzn8TDEY+7wLYpxDf4z83eJQMUsYtEmhBoucpT9uPTySB2m6o+KZs7AGJ/Ug3mYreqs+TMDaKiFC3bMmLEc8kEgsg+qx37uFZ6PQGmpRQjvKZcIw265XQR6qC53TG47YCOI8BwgBJqulYsw5ZmujRLob34Pjq2i/BLW40rGbMujPDI4ru9CV0XpC/zwKMrKR3cE73Goy/TdkSyC0e8sVhkKyFFWScLVBtR2cv86HM2wUWUO2FXaERN4PhI3GiHsENYBqvvoD5SJvCdWMcnYH7wnkLPmDL6shbp+wzBGr00pe8+F533QPovMuNyP2m9iM5jMuIub6j2S2oigg7B1lHVVtXJkh0uL0keAnfz/IvjiZEHFEOPXIKbKeXxtDZ8r3coc0hIh8refhND8MTKeP7GWMRc3Zyan1v5V726PxGQPr9UvxC0nfkau8ZpHeAOqgtTsX2KzNyxXLyDKILRxNyXDjj6BathQKcaQBJ5W5cbG2tFlQAXJJDlVvFxtkVah+GCKOJtnO+izvetMBmXya7NqjimuOUTh1Za691WyKAPVwAbjldf5zC2NRPtpoGueY5W7LKWdrieYUxc7WAlKt/mSU0hjKXHsbF7womBGS0DIIEdSCVtwZD7n9+wgFGASsD1vPO91123pQza0CqVm4vsINsoDMRxQ8FqYK7P/oF60Lvoh5mzPPi5BwkDatPtjgi3OsQJS1Tic5YVd9RyTC4TqAs6slkfBYUUY6CxvbFcoJIjJDHL+LmjH83j1dWvx3IJbgiWKxdC4Vaj2dj+EG8HLcRbQ5e7rAz7/pxY3TbfR6rjihjSXqMn5iQLhxFEXCgvNQp170yr3195ZPNdimBS3YgvSOHm9RfDbTaNAtGn1A2COwT4qfd+S7LIeRgAFvC583NaTXsn2xjhRYWEvZaPfJ31uSRt6AFl4UjJDzACfUallzwJBI00j9WZTZcv5zHy8rzASniqM5sVUfK1xZ/0LzszPLk+xst5URg9VRK0tiAlu4nxM0LXCTVb7NK7jqAkIHZl/vrhZos5wncsS4LbAtCWBcWTUTBeIhOTATVbRFqd0tHHHiNqMS3kO6wwXRzs9pdjPwA0GCxQhx2K/V5HHZ2MmoRB7VGDqxTSAtnaTpgFVDwhauP0USGz7nym3EWOrsBoOZLBTsaUju3Y13fQsjjdRx8fV7UJprr4vpzQyXAxdyz0bC9yi8KsROeLeJ+g/XKeaDvFih+lUzpe/agicOov9fVcor1TLMHTvBjfK4bu0X1IcrNWVh4PlocSv/3nuXV/rF5zmb/3cGsZ0GcVyKrNt4DkIq9AzGKkqJS41xm8/9TXuN9EL/30CrC5L0h5NkbdxKeak4yPWN5VxUil5PE3uWtRFPHgrp9/KKT2ctMbnKeiW6OAR/beJeWolMokeyxgPZ50nxUtGGw9SZLpCQlVwqxcniXZhMClT/+4XUhKmzX/ij42F1UHBPAmCsQx6AD61Y09L3/nGmt3QmT9DSay10I6z95CAJEVGCqjnGbO63L0IVEtSIumKtXjjlKYXHv0NLkFjMvQOeqIFKz/0UHJCTTWl4iDX4dZPX1GZtalssJZ/ZLwMhGID44xvZe5NBSXdCfAe2VhHeiBjTnLPRCrCA2K6bV5ArUz9n6koiTJrVE9fm240EY/YHitTmta2hJU4cjW+93wHBaqf6I7Mbzwh6nnRDw1qSo8jCWyzNn5B0sNQFjwd/nN7/MOno2CR7hiK2SA5n/cFggTeJIbvlB4fEilHcS3+1TkrEWhm2eb0m5/CezOitpO3YdKU625q7ZB5NWGjAa5WFjh47Brd39cqk6/SXoVkrN0oGHrCQvtet+CAalXIakPBIe5QAizQEBu9v5oc6tURUraO8GFFF1dBzGKT1JWoUAmzgKZcjtuHCERTTOawmdYcZDobczR4S2ub7tKhGggTjbuqbjhAf3QL4I9Z0LMiVDfMMH4y5LhVFjAnNfEMRyzY2EuJ4ZhdD2vwLB6m8o/BfNRTvRtem7B8PyPcJpc49CrBPwQkwhx9RUTrbGKBQHLFLXAxCdxC+ZZ887ZQGrvjqkxVayKaMqEHrH6nARAHkPg13mbMYK2DaNXPYlNRX5F+YWjvzAMm6xZzje75OKcHnfdYCUNfyI9lv+5cJ5UeFpNNhNzHj5gOyoNF5Wck3dLn9MscMmpJdjNHLboBhoVEnURPdBlzGBOhCwwHeB8OrwKqhlSNSkwI5mvonSNRfSPTM2gJ4Kyg7nSenBkg4j446cYX7kq8ltn+Ht8Iy/r1Yk/0Ec5vlNXRWdFYYbYtSbWhWFr4ANBmE/nVlgp9IIuUoEo0KPT+fEhe40GqB8KjfWK6/oi52ShbiyhcuwiZLL1fv8E/i04mt2mCDx7oBE4y6do0VSi8i7NkDVX4Y69V8z2MrZq8/h2FuGw34HpfzVTkb1b74BbomE+FMSvSuUWsHi2hrLn9W404bR/LtPpE2dNsH7ksj9B6byLTfxQgZI8OV0WhssDluqVahURx010i5SuzXB6Ava2tcarFpJj2DtBnZj2eAx7rysb9xqfaYcTo9+9VFQeIcKoeH9RzV1CXza8Zmdhj2EHrXmPp1iB4zyprwi6tPF53vk3zS49TgXIL2UwWrwj5D6IGfkbqXqC2Igz8nqMTnUuXp+P/8AsWKERxi+kpphuN0KmEwdfkKjzjkg3HX1f468lPc+amaZLv//L5u2R18xd/lM3xsr5uvADxZCWHCdNmz7rlkY7dt0j12Bp+Xq0f5/vUe4XKFeDx48eOCwEPwh2Fz/X92uVIu5ezA1vwqA/ryRSCpnjQV7/PlrChQsLsqEegjQEj/rzfnqigRP/7wL9PI+jNXIIirvHcdhrVaXbSqSUgZBLpaUYkNN3HEeZgI4+yYoKM6DHEb2vQMn1lM1xMmK+Ebdzwk5+LSlJA2MbvrBGrKrQUTfl1KvpJktdM5bNAF3BbPvMht7LxsifPbK85+wfO4wHJ5R2EhpkxKr7+i4PXwW0SlYUKpkbddMHKRd8iI3whlBCrZlkmE9HLg1m0akWRz5RREQwAkHATGUCsZlPpRIDfeg75zyUEop3dsPHg5UWokGB08BH4Zx3Ft0wthBEuqcvaYlpMp5qaxNIEvmWNypiCARA2/Sul92DbZna1kludUaozmEJZf4qa+m5FNSnIdvDJ+Ex7PbCt+Dss8dVBC8raLHWm9ReMwoA4Qzm6gI4SxkY6KL2wh33emIqRTil14z0u5rAfO7CRcAHsV9lp0x99BSic9oVBo5k3dxhp/qrzXe+9ATlVZsCwmRP9YacXzAj9gx4+mEo5H2GEl+aPqa5SCNV7cLohlNej3h19zv2L1Yg59Qy2RT0CmQGbtFr/DumtpaMMh7Mhz30f73xPWKWv2mxBtFtuvtPM4savzFZSRsKmNJ5c5Gxh4AipHXgAmGEwpSKQUm5OjJBoxhEXTa2U9yyegZK+/Z6DHbENwxwgf/Dd43EFZ8mrceeockTomWZrlNo7FrpRFCjFF/EG11j7YjsQDaYcDZtpM+OxYtqci3C+Ax9dwViWlAkaC3zYP/HHjYzyFkacJhUvlqQs/4PD8E9sKskgsEhfAt+7jsWquSpEgs32m9jXoXMoJloEW8eJZ7CpcsxlGxFJedzYnancwJAjsGesX1W02tsjfAb8arediGMS2O11+8poliBYwuezxjhIZqfckOCcRvtgq5LjQipGQyfDMgBOOlYCHeIvfwWqJeFUC7MerjXMPmoZvxa9keoJaY2i85Es73/etGK39pjbJ5sC+xwc2l16GcLbl+lxcTKr0o62U+XU2KYxlWHURPMXjTgZdAW2/aLwE3hILi/jo0Uch1X9sLRfO5UqZXhEy2WZcDHUQgSKe7GbK2+19ex0wsovJ+i51N5ZOA3XRGNHpPaJJbr9mLrSX48AHE6YhxdbsrpHfkT+vGvH/6DWRNPhUqhLggVibMHt/bzw5lJPLFEnfpMocHKLzKhC/PtRZoYQT1EI9/zX0nHTV/TbIwzE2jz52QoVmLasqen2LyA/QKbGA1UXFlWrr1C0iJa6TDqhHEDI28k+x3f38NdI7MFznvfSfqoYdtK2P6+oGiWW461XJ/2vH/wtCbDcPrXlZO2v42u1Ho1lVSzxgBTkEETUyO6NXauNAt3LlAqH5ncIYTlz2QM9w73SgmRw6fzvnZIHJ76fJ7s/J+0kpD7PwZKsl1PvW7vY4V8HO9tRJROe1xYEmQOGnqIyViQ1qFC3nJRiTuOHwrZccNeW9Vh8+55VLTwmOYkjTrJkMY8yW6nAUrECVIPzlHkiIUtmpgQaY4hXd/z9yF9mGL0HCkDlbbox3Rt8fJa63hq9xD35D0UrsWFK4QLxifV4z1q2Y54eIpfZG1pN7boB1qlvFke8+4xAxdMGQf+tg6pynT+YqOYA7CtZN6bx8ZkQx4v668ykMfbjenvS/z/FNM2QQmmCRMBOhSp0t74d+X1oZetZj9dv2zv88Yw2KlKgEgw17swV+1o0kmyhpIppn8MeHRl7uiEGi76eLagdD1uh8gWpmMol87hL4tqiGSH9fFPLINJLaMAd8MeANw/7KI5tXzmSHtXz+eOC6Y7tMvkVD3TxAFtA0E1mUFmOU0J/X/v4ktPPPkI+PX/uf7euiM+BMEj5TiC/tq7l9waf1ov6RJvUVi76zxAgjekJcsmd5NLp5JFnfS6mqiz1BAsv1WCwwQPdoPYUEOW/wj0VaxH5WreDBo6hJiOixa3fRO95QU2mJ4V7uq9nOdzvvK+js4hGldqdDPkDoCTE2qcDrr8i8sf/ebSDDw6o/Liq7CAbiNDx33QCTjy0AepdXDMv4YxdBxwjbyLkfIGaalBbdSr7jee+jLAfNclDrmrlB3hj8IA93m8HPPzVDH80cSaM8Dcb5TYujXsJzQV/VQusYMEfhcNu5XnfpEIYeOGhDM0Jhrh6oUS2IqG/yITfyxOrTak0jyFvWhIPr238HLtK8N3uoutq6woYSV8TQQlf5zo0HI55ltjCGDB6gIVBuCRGpFxFFj6cU+//BR6UMAstI1M5WxgHtEQ4WyxLON4dJC+I6z9wGURCFeexFcpy3ob2xxoiljmDuSNYPmkylzb7aTJNu52y+V+VQb4g+nOdwX1+iVjfsC3r2+JjF1piyWagsdRqzq95k7JkaBGDs0aYUCHmyklcWq6qA3838qozGWJ5zwVIGBayrWnvS3G9EyJsbxtQQ3ztUT6yo1CzNHQTUdHdm+pQkCNIFywT01QC/2sWz0QXzjSIJYEnRWyhwmm/BvQVEX9dJNUYoQEGeG64m5/IIT0k9g96+kD4DAULwS2emVc44x1Af39qkRrGChlalpqg4Qcju9VlMfVDspYObNp+bcFABWkiuUjxW5IcvT1mn+GVF15df/m/QWvxtlwC2OJvH5BW2flP0Vn9wm6nlkaPwuHc9KUPieUEaBHK/oPVLZTEKubbdLGKek704ObR3WMTUwXnS1o11QTAB6ef1unEmfyN/bkBsQSUvgwPXXbE/BfJg/HJr5lhzE6dQxsYQpg7qdX7ZF3h1ojhecY+qTymG3+pzJWRhNPBO7AVVZefENr7VR0COWG8xGYR8SQFPZSURndYNZ/fj//OfSlgGtA5G8vC8pYd7nAmPU2qgiB/texMCAS/cEIM7eEJNUUfxkOWqkdjLohbWklCxU6O3u9mF55aJNVIby6oqFfIwqObpJfYdCv9jerohnxbDKJ+bVc54Jexbj3hNPCe0a/A3HVV1bQrINMJG92BX92Zy7hI73wKhdEvO9ufNukP51Sr2Ne4FUyuBZ9Df9QCVdXtCnh35zSgLqHcaO1mLtJPmQfvJhGWSRC2ShAtjMHmQKi3vI2qEQeiDN5ku4rwtu/3A1zIKf60Ya95s5Y2It9LxfpmTg8iLE1pJAYQpsASmQuL9fv7aggHEFkRj701frcpN7w92bYGCME1uLHW+WXQ+bsmfBgP11U8BfGQNDXZIxFp+kQG+YQ4TGUmbf0x8AZ8dGAf5DDQRPEg/nxpWh5KZD9UR4G8ns2NIVfZhD2aKHv504B9yFSMuufICMEn+9PCVqq4wEX+OQWjgHWhdRZJ+fzQdSl84LPZ7NhV7tp/ZaQNrTHUoghlA9xEuauKlM1fpcilCRMcT5YRe1KGYlT/dyOA8GXAMVtiz7s6IvOm0zGXxCu82t7toX9PmmKtr0TBe+xoaOL7gFQvmVkOGtby9MCAx/Vkzr7A6NN7F1ZPYB5RPS9oC0H80y8TkIMelx0vOuTNGPsD9zAD9Rs0F6/mB2nAHJERHke08FaUZv+7VLlVjtufMX9PyqbEl2awcPBc6xXmslNG2PK5PdEBc7s+J0urKor7sFlnI8fXQ4YxejsF25kDlqgONPDK0mbC/eaxkpWO3aoTJX2gz545R4ZtoAyyffZ8iKoSPz8KYlF2VOS6b7qwO21RTiPrXjVQnuT35gC+4YaheGgp0cdRBbtbgISbFnpYyEOvKLReL1ux1eyPIiihb0s4+6nWKenkKRk6cLOdh+NJp+WG/nwOH5f+HbOTGzSNdbe+6mAtXVXDifbGku4eMCh2zHxG313fv50mydkzJlmeGShqdH+dIinS9AhCy08btIre9tCQTLRIdt6qtibi8MjBOhm1C+a2Kn9iTIbhy6VEmAw9l8bUXV1J830Z4yhP/ChBMRvtXckZyrBZ82/y4V5/9rKSbdfCynccMbP+qHYO0HgGeL9Cw+xyMpzL+sxtcX3NCFOH/DFHE8u8vvRLE+L3HwJiY6P/1bRSDJppamG0OSRKIw9qC5qg9+dln5R0pc9yeyKGhJoAvEBTQOlxGfJc6miZliT601b7M+ACtZYRwGQbBKYTxleF7q7owmoJ8H5hViSIZKkJQtIyXvKWRuNrTsdBSoaFgN8K7bGvKzR47p/uF9SfaVCg9WQ3pe4p+dnDiC57hiPTE4D3UtL5qnPZJNgSLjS+dyIIGf2UiBE4AggyYfhbZ3gE6gN8jxHNtzzvu1sJnnuI0Md47N/mrBowZwdRhsHuHRRzTy89LBFxC+Mebg8MwsgI4yn0GYkKIzSF34ikquJ4pb5/jcwhdoJMjWrAS3MU7ccCpv1cb4DTyBTZwfZSjrZGqkbzgr3nPGLo0CoRhoBJ4JNf689PhLgD3iHi3vQUrwOp/vVNxtvZV157uWuG/MPC9SaQLKr+kS7f1Giq9F0wIO3gVyIF+Qjg056gHlXYkbO6GfsY2Ur9sUpQ1mlUrI25voh2w7Nv1/5enZUXYOM8oQerWgVRY9KKFk/JMB3kl5PBZBfwwd9fiGiQ2chnbMoLXlKrOqccKxhBGJ9Cpbhv1kX4i2b0DuTsNPivJI5wrwuTF+2OAqRq2RJeuQAy+wsvxFAXFTwcczo2tUcwwtrVxuwYRxoiZBEMKUV9DcSeXE3Fsi49hOlp1fDE2XyKVcYRs0iukYG/7gnAG187Gd4xVWdFiyVi56BwMoSfAP0k04rSw70sHv4um0x31TbaJSytUdJfUERh3WUi6EwgKwTycO18Xi2wYPVe2OqZGFBHGPUYQOWVastSyAjM+YXGA2wtdZee1yuWKWwK6oxfbLLkcpx4sgsWcpb9Pr/LNkF3WTm41emhzJ+hEjBH0MffRwY/x7z7/TJ21y975Uvay8V9H8o7SHemY5jL2QDNNqjTkGuzyFMSFNeSgLo/FDyRUYvvEbGyArYJyeRVq2TuXByg4qya+W81xT4mRCeedNaIbbWeRIjGG7GXG23hqZOlev+flmAhivYZitAEbosPp/5wW4NwZTI1lXkQWrzW5hA17phD9LdAZf3ysnLW/BiklpOkwvhk4y0EB1x7d/tEUxTQWssr8SNDo7D9jSGNwbaK9HdjzTqm4wbR0Y/w90ba0p/TxtE3TwZK1adte2fBvXELNi0eALRwyawQ+IQYQS2bUFzmmlBrNgo2f8nvSWliQ7rfaBZEwlI1Bc76Ju8/BeOWxKzmuDGTg2qiL5GV5TAdrCl+nNO+fGLskDadvlNKOv/FJQH1I46Q1Z5q6Xc0hlgxnmHyEJxfPH1ITgAiOJXzKDkNd/n758fwhQ1zy4RmiVIeacOzpeGPWFpuI2rD82p09BUBI+/szI8hFhf3MXBD3Wrq3BZjgDj0mcCI+vgWWCJlAy5W0WMA6cZ3qdxY3R4gewcWhsYbnCJzSIi/SEXtb767wJYDqxKCJCBvbbKFfEjiF+6986ypoe528KPXUQQjwxyBd0VJYwGbEJCQMcqAsuEtiS7Zp6VuBkiYZCWhce7cj+yjt1VZIA++wcjCFsrHCv8H9/57ouzfCIL1/0qgccgHhPDfiQy2U5lVb+P8SoXMM6giiUMDMtbW/YHcc6f4py8zIh6+F7zDhDp6pxzRwP5nfwSXBvqYmANOePzsBrsUwaluLkIBQRjaNCftybvbNnbTknsD9zyiceHCuLayht/fQOvSaCFa6anom5dV2QPETwCd1t6LpuRm2yzkVOC4zK09oxaS7LiaQ1rXKyNez6ivh40uui2STS7v7hayyYduphaiALh41T1w+Q3w3JNexFKM7yY6VZdhqFKfQZS/w/jyj5SQqklcoojpx71wmBXqYyAbD7foQJSfuQIA1wumPr2bQUu/Ufcz4vN63FtnewjmFyPCaQh1rr6Tq2jWAKnx08Y5z5N5iBuywvayGgS55SnM0tmgzUbNG9MSBWyMiJUUqUDjDv28pXJNO5FDSSdkbDDKRO2QX0vVh+moaL7BF+iVuCt1O0sn5L07EnTmTk3CuVYc0HdDnQcR3Jn41/w5uLxyChwMhw5VvA4cAcrl0anVqWmIPhhULpM9wSSLynPzNptN2rDYYsGpply1Wny5q6LRYNt2+NqDvKdqoKKh1CMnD2TEPfBAX66El6ScGdCsU6bBctgYpD7bkreYDMMfXCfMmq5t4ygcOt2tWcCM2QD5lG5L8avFl52NTG1OMNuX1VBOQuPKK5mYl11GYRjgRstaz3veDa6ps+BzdAnO3zQzXY9v5Xm8W1lc5UGc9a8VKKVHGbMS1I8d3vIZNkC2gIZgpk2SCiS7RelqNw8Vk9nk239mhSVyBzBNZobI87nZStdqDQlK/SkekUadz2B1NbSe7vWNx6QNNW3pEk42EWXHVH2dKdwEmS6eZXHemApL6x2E/0yMf8RhuMi4Wiv4RKMnIW4DGVeKEohbbRyuSWRgeEvWeiW8s5HU301Ov1Gad7ktEAcrUyN47yG/epxmPISgHEOhWD6LTNK4X9RGTmTMd6unsIkAkmfoswTHxyeZwIw5tGwb9YhpGg5IvC+5W2UmTSeSTya+SqC+z6fIe8iWWTos3chuBUNAbE6XXFkBKViiRC4NAdw18UJlUsC25TI0gvknR1yjL8tMH4fsocGAR9y17XFbVSE/DG+rTpYCqa/zOdoTPKM/4I4LCObk8enOda/YjZU4sILer6/a8kwG7wjaJyIktqEzM+AMikFiKtjWJabNXv5mAaiHzpra1sL+ezlgCfBhevWW/afDGPCYS142uR3mrgL83b1c4MMYzxVNrUL6+o/ucKTeBTw9Q1G8ldOEDOWYeS/mS0Dtm9AfFQY1eTPkmx2DJUJ3wofkcwSXTbLZYayujjBgjrO8pYY1izRK8HzmQHtygkAJwrbpS87iJnQBgR0Ti6J+Sr7vKzGhx8vcRx9HyFHynSns1LiU0bdHSk+33es+iZVU3tVua9qsZkwCzWYR9jN34ixCE31ztoEvxM02nFudGItVyyQ+aryh//qFDYtAt7DKQBw/xa42ucPbJsYuBHYyqHlQkkihQbDTrsuZb+xsry831X9+SM58kLukEjpcTPFp3+MjOz9dwIGLve6LvDhmJmSBRp4mxZp0td1Fe1JtSY//MeOz/fGXLyUwD82cuGIK6tjE1mtm9MRAk9DtFb8jw3CI+oGonE5HXDgKgadpvIS5NtSojn109Q62r4Ij27SUb+95OlA30t75JzQ12Ln5ZTMWcBA/jxqs9JhosGrkpJmJs/yV0UbfkbK9122wdokMMJL+WrPanfp99HSivFobzhx4XLSs711qeVQwFlr+jHDDnJaxlc7rngN1Ivgpd1tEJjbfC2Ka/IXhAJr9YZyN/QgkhwurSFWJLuUL9gsPh4nlnoZqiB1dszhmKsFVYH/GErjUAN1SQ+N7iXjRpR5ipahqf9Vbr1icqaar9NGhwMJMC+Z5GnxGmOk0nJmkFmVhPIESQzGPncXgJ7qUYSKOyF002mspVtkfwVdDpM90lzqOomc5HxF8rvnVAeSecK93WK8PPDBe3BgmRVt7IzpDihifiWEqqPQoxEO5V0r4NnSSmi/2oHPbZvAks+TLAApnCQNcbi9mdCenHZZqslOkEmfhrezsMuYfBG8WqvMSOPrS/0mFI/y00DTXNneUBwVpymCX9LDxe2pM06HvwRy8gU9NqwuR4d9nD9L7GfaV97ZbvF9MzTcossUIUj9bD/nfIBTRIweDSqIU94RfT3DqVTJNQ5BcJ0AdBr4/BYflnufEyuSH4LjKqa4VHRfDRmJvV0nf5YMc7ti6UBxQxjqCdN7qf1LoeGXa9Z0l/emSS81/k11TM4RrNb238I3n1LpOUhudIGTX8GYOGpu9fZnsttOdirIPZWeci3Jrf7PMwAbubAbDo9OdRaEE9K1LnI4WgeGp6VUDGbtH7JuYlkWl+qBqiglmAqjUg0red3xvgA7drN+ytX48QRqCeB7km84W39CCf/sG6RV3sAf9T+Dki0xfV0/h00UExGkVGEuXKZVIbtk6sxIEQD6fOW1W6KTpiPgyGVdxrH3EiPZlEue+G6Fysm99z8n5UFww6iIuHul8vT3c27W+22MhWultjJVp/KIB3/VClwGVLcMVz23UA72butpztN6SmQeJJVwxajs2hDftu9pEJMikpt7RD7J3Elly12emZroFUc99JyS1kCaoejZDVorPP7h/EITn6L4ccESLdfyxOaeKI3Edf2amLlNhF1TjiZ7/rwC6re2CVAF/i2mHnB2IHoXPEgfzAsxTaSRMlfE6Qi64rQA7Xy2/IG6agu7PZmCQPnGWl2+CY6skbiRSvxH75fV5cY7N52emIoprvk/fqfuRSLyQkdoiwTy94Pb/pxX6Qcw7xc0hMO/qWZO0kZwHQBla9KULucmxX/KOb05G7fvGNLo3TdPt6wdGLClmsPV3tLl3TmHxRsRQHTbaw9On5TqxA4eHjMa/46LXQu89oMKu7oGoU5TWoUy/ig8IYqSt55GWEzCjcXukWuAPe65RtDesHj9/ECdzpM/DMPBp9tDrOdRM9ZhBqkp5nR0a6UJQ2euZWC/UK1pBLpWMM14w11qZqpn9K/9ZVcsKuLP/yeFL2iF6n6b+FPqiCE0QdUT/8U8XDmjgFodGrGYPFjwZXArW+67ihboZXmdWyXYs0jommH30uEOzum9C/25790qKSjCPPqutJ9xMOZN2x6chs73PdGtkyHw5fr448+wsDv7w5SnDyvrCgBM21piGDk+QaH20l3h8vtprnRkppLIi4mIXuozO+cWlJ0pOJADVaFW0+2YOxI6suj/15DwvUwnapIoB2MRx5nXn87tApsd+PFrI1BTEbj46pt9I2iSB/EK3L18tnsBSdsQw215mjdsDQmjdudiUSLthg5L9AjM4pokRV8whOkmwAvt3yGyU8eLy84DIXz/QThhuEBme3PU8//iovOWnCDKKuw5EFLkiJt4peLwWz5JfB5K7RpT8mFk6Agsqbo7kS+OMFFjHdx1GzRv499oIHbyxBKpVz1RmkJqU50zjQJXFkIg1s18QCG6+JdbgezgRb4rLNgJBekXnxjkI0MDcP3lzDPoeTEpqc7xmTRG14dQd4VFrt1KM96lAoTECRSvqxDIuQCsk5trB4wrH2p6Nvj17XJAKk1oV6Zk0DNjjlPAEqu8rWg2OsKYwpRvC8OVMUZo48FnWr4Wo5iX7SPiMv0QtbSaqawhrrmVRxSO/HIWlUcxVTYtYKbow32OavzREVUkJggePpvExuSjgfkSSNCdhhmG6S62/3y5ur7I+Dm7TOCUCSGAKuFDJnTY0sVUP+jN4ewIm3CPPf/te6URUqPaNzzmti3T0TDmnkz0e8uWvCL1DTrQZSNeqqh1oybUMmpVZAvbzB5xScWSJKyIbPktF7N3QCUrMuXhBuGToPcznLWiEeVTUqsxUx27TOGZ35zKg7BEF2GCNtpiYQcr+CBoL3VIPzmlkNazXedV2MHOv1Wz/Wp69FwllIpEmKe5ULgnyixPpMx61xR8HxxRGmLFXPVXTnMUDjYnSw9MwHp3jWONyoOww9C+zK/Vo8EUVTly9O1AFUqFWv8wxEP2prtjYLcVgtreoerA8SneBNiyuRth/Mx4KV3daZiLvj+**************************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", "upload": "image"}, "class_type": "LoadImage //Inspire", "_meta": {"title": "Load Image (Inspire)"}}, "66": {"inputs": {"lora_name": "badquality.safetensors", "strength_model": 0, "model": ["50", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "67": {"inputs": {"lora_name": "blurxl.safetensors", "strength_model": 0, "model": ["66", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "68": {"inputs": {"lora_name": "envyzoomslider.safetensors", "strength_model": 0, "model": ["67", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "70": {"inputs": {"seed": 599117685811701, "steps": 5, "cfg": 1.5, "sampler_name": "dpmpp_sde", "scheduler": "karras", "denoise": 1, "model": ["68", 0], "positive": ["6", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "72": {"inputs": {"samples": ["70", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "78": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["81", 0], "image": ["72", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "81": {"inputs": {"model_name": "segm/person_yolov8m-seg.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "82": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["78", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "83": {"inputs": {"segs": ["82", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "86": {"inputs": {"detection_hint": "center-1", "dilation": 0, "threshold": 0.93, "bbox_expansion": 0, "mask_hint_threshold": 0.7, "mask_hint_use_negative": "False", "sam_model": ["87", 0], "segs": ["82", 0], "image": ["72", 0]}, "class_type": "SAMDetectorSegmented", "_meta": {"title": "SAMDetector (segmented)"}}, "87": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "88": {"inputs": {"mask": ["86", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "90": {"inputs": {"strength": 1, "set_cond_area": "mask bounds", "conditioning": ["6", 0], "mask": ["115", 0]}, "class_type": "ConditioningSetMask", "_meta": {"title": "Conditioning (Set Mask)"}}, "92": {"inputs": {"strength": 1, "set_cond_area": "mask bounds", "conditioning": ["94", 0], "mask": ["114", 0]}, "class_type": "ConditioningSetMask", "_meta": {"title": "Conditioning (Set Mask)"}}, "94": {"inputs": {"text": "a man, skydiving off a plane, (<PERSON><PERSON>),<PERSON><PERSON>, (<PERSON><PERSON>), (A long blonde haired guy. With blue eyes. He is the guitarist of a metal band called metalizer. He dresses like a rocker. ) ,Tall, long blonde hair, blue eyes, 25 years old, man, fair skin, black leather jacket, band t-shirt, silver chain necklace,high quality, award winning, highres, 8k", "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "95": {"inputs": {"provider": "CPU"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "96": {"inputs": {"ip_weight": 0.8, "cn_strength": 0.8, "start_at": 0.5, "end_at": 1, "noise": 0, "combine_embeds": "concat", "instantid": ["97", 0], "insightface": ["95", 0], "control_net": ["98", 0], "image": ["11", 0], "model": ["68", 0], "positive": ["107", 0], "negative": ["107", 1], "image_kps": ["101", 0], "mask": ["114", 0]}, "class_type": "ApplyInstantIDAdvanced", "_meta": {"title": "Apply InstantID Advanced"}}, "97": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "98": {"inputs": {"control_net_name": "diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "101": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["72", 0], "source": ["88", 0], "mask": ["116", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "103": {"inputs": {"seed": 599117685811701, "steps": 5, "cfg": 1.5, "sampler_name": "dpmpp_sde", "scheduler": "karras", "denoise": 1, "model": ["96", 0], "positive": ["118", 0], "negative": ["96", 2], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "104": {"inputs": {"samples": ["103", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "106": {"inputs": {"detect_hand": "disable", "detect_body": "enable", "detect_face": "enable", "resolution": 512, "image": ["101", 0]}, "class_type": "OpenposePreprocessor", "_meta": {"title": "OpenPose Pose"}}, "107": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["92", 0], "negative": ["7", 0], "control_net": ["108", 0], "image": ["106", 0], "vae": ["4", 2]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "108": {"inputs": {"control_net_name": "OpenPoseXL2.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "113": {"inputs": {"expand": 32, "tapered_corners": true, "mask": ["86", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "114": {"inputs": {"amount": 16, "device": "auto", "mask": ["113", 0]}, "class_type": "MaskBlur+", "_meta": {"title": "🔧 Mask Blur"}}, "115": {"inputs": {"mask": ["114", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "116": {"inputs": {"mask": ["86", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "118": {"inputs": {"conditioning_1": ["96", 1], "conditioning_2": ["90", 0]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "133": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["137", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "137": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["138", 0], "image": ["104", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "138": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "141": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 1024, "seed": 741729000140363, "steps": 5, "cfg": 1.5, "sampler_name": "dpmpp_sde", "scheduler": "karras", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "image": ["104", 0], "segs": ["133", 0], "model": ["96", 0], "clip": ["4", 1], "vae": ["4", 2], "positive": ["118", 0], "negative": ["96", 2]}, "class_type": "Detailer<PERSON>or<PERSON>ach", "_meta": {"title": "<PERSON>ail<PERSON> (SEGS)"}}, "144": {"inputs": {"filename_prefix": "ComfyUI", "images": ["141", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}