const express = require("express");
const app = express.Router();
const { supabase, wrappedSupabaseError } = require("./supabaseClient");
const { OpenAI } = require("openai");
const { logError } = require("./logUtils");
const { generateEmbeddingForProfile } = require("./profilesHelpers");
const { callAndLogLLMService } = require("./llm");
const {
  tracer,
  decorateWithActiveSpanAsync,
} = require("./instrumentation/tracer");

require("dotenv").config();

// Initialize OpenAI client
const openai = new OpenAI({
  timeout: 30 * 1000, // 30 seconds (default is 10 minutes)
});

app.get("/removeDuplicateEmbeddings", async (req, res) => {
  async function processInBatches(embeddings, batchSize) {
    for (let i = 0; i < embeddings.length; i += batchSize) {
      const batch = embeddings.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (embedding) => {
          const { data: documents, error: queryConversationEmbeddingsError } =
            await supabase.rpc("query_conversation_embeddings", {
              query_embedding: embedding.embedding,
              match_bot_profile_id: embedding.bot_profile_id,
              match_user_profile_id: embedding.user_profile_id,
              match_threshold: 0.95, // Choose an appropriate threshold for your data
              match_count: 10, // Choose the number of matches
            });

          if (queryConversationEmbeddingsError) {
            const error = wrappedSupabaseError(
              queryConversationEmbeddingsError,
            );
            logError({
              context:
                "*** /removeDuplicateEmbeddings: query_conversation_embeddings error",
              error,
            });
          }

          // delete duplicates from documents, basically any matches

          documents.forEach(async (document) => {
            if (document.similarity === 1) {
              return;
            }
            const { error: deleteEmbeddingError } = await supabase
              .from("conversation_embeddings")
              .delete()
              .eq("id", document.id);

            if (deleteEmbeddingError) {
              const error = wrappedSupabaseError(deleteEmbeddingError);
              logError({
                context:
                  "**** /removeDuplicateEmbeddings: failed to delete duplicate",
                error,
                document_id: document.id,
              });
            } else {
              // log deleted
              console.log("deleted duplicate", document.id);
            }
          });
        }),
      );
    }
  }
  const totalBatchSize = 1000;
  const concurrentBatchSize = 2;
  let startIndex = 0;

  while (true) {
    const from = startIndex;
    const to = startIndex + totalBatchSize - 1;
    const { data: embeddings, error: embeddingsError } = await supabase
      .from("conversation_embeddings")
      .select("*")
      .order("id", { ascending: false })
      .range(from, to);

    if (embeddingsError) {
      const error = wrappedSupabaseError(embeddingsError);
      logError({
        context: "**** /removeDuplicateEmbeddings: failed to fetch embeddings",
        error,
        from,
        to,
      });
    }

    if (!embeddings || embeddings.length === 0) {
      break;
    }

    await processInBatches(embeddings, concurrentBatchSize);
    startIndex += totalBatchSize;
  }
  res.sendStatus(200);
});

app.get("/generateSummaryForEmptyEmbeddings", async (req, res) => {
  async function processInBatches(embeddings, batchSize) {
    for (let i = 0; i < embeddings.length; i += batchSize) {
      const batch = embeddings.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (embedding) => {
          if (embedding.summary) {
            return;
          }

          const generatedSummary =
            await generateEmbeddingSummaryForEmbeddingContent(
              embedding.content,
            );

          const { error: updateWithSummaryError } = await supabase
            .from("conversation_embeddings")
            .update({
              summary: generatedSummary.trim(),
            })
            .eq("id", embedding.id);
          if (updateWithSummaryError) {
            const error = wrappedSupabaseError(updateWithSummaryError);
            logError({
              context: "**** /generateSummaryForEmptyEmbeddings: update error",
              error,
              embedding_id: embedding.id,
            });
          }
        }),
      );
    }
  }
  const totalBatchSize = 200;
  const concurrentBatchSize = 5;
  let startIndex = 0;

  while (true) {
    const from = startIndex;
    const to = startIndex + totalBatchSize - 1;
    const { data: embeddings, error: embeddingsError } = await supabase
      .from("conversation_embeddings")
      .select("*")
      .order("id", { ascending: false })
      .range(from, to);

    if (embeddingsError) {
      const error = wrappedSupabaseError(embeddingsError);
      logError({
        context: "**** /removeDuplicateEmbeddings: failed to fetch embeddings",
        error,
        from,
        to,
      });
    }

    if (!embeddings || embeddings.length === 0) {
      break;
    }

    await processInBatches(embeddings, concurrentBatchSize);
    startIndex += totalBatchSize;
  }
  res.sendStatus(200);
});

const generateEmbeddingIfNecessary = decorateWithActiveSpanAsync(
  "generateEmbeddingIfNecessary",
  _generateEmbeddingIfNecessary,
);
async function _generateEmbeddingIfNecessary({ conversation_id }) {
  const { count, error: countMessagesError } = await supabase
    .from("messages")
    .select("id", { count: "exact", head: true })
    .eq("conversation_id", conversation_id);

  if (countMessagesError) {
    const error = wrappedSupabaseError(
      countMessagesError,
      "failed to count messages",
    );
    throw error;
  }

  const chunkCount = 10;

  if (count % chunkCount === 0) {
    await generateEmbeddingForLastNMessages({
      conversation_id: conversation_id,
      chunk_count: chunkCount,
    });
  } else {
    console.log("Not necessary", count, chunkCount);
    return;
  }
}

const generateEmbeddingForLastNMessages = decorateWithActiveSpanAsync(
  "generateEmbeddingForLastNMessages",
  _generateEmbeddingForLastNMessages,
);
async function _generateEmbeddingForLastNMessages({
  conversation_id,
  chunk_count,
}) {
  const { data, error: messagesError } = await supabase
    .from("messages")
    .select("id, is_bot, body")
    .eq("conversation_id", conversation_id)
    .order("id", { ascending: false })
    .range(0, chunk_count - 1);

  if (messagesError) {
    const error = wrappedSupabaseError(
      messagesError,
      "failed to fetch messages",
    );
    throw error;
  }

  const messages = data.reverse();

  // Ensure there are messages to process
  if (!messages || messages.length === 0) {
    console.log("No messages found for this conversation.");
    return;
  }

  // prep batch for processing
  // should get new string for each messsage and label if bot or user and combine into a single large string

  let batchString = "";

  for (const message of messages) {
    if (message.is_bot) {
      batchString += "BOT: " + message.body + "\n";
    } else {
      batchString += "USER: " + message.body + "\n";
    }
  }

  console.log("batch string:", batchString);

  const embedding = await tracer.withActiveSpan(
    "createEmbedding",
    async (span) => {
      return await openai.embeddings.create({
        model: "text-embedding-3-large",
        input: batchString,
        encoding_format: "float",
        // chose something in between 512 and 256
        dimensions: 384,
      });
    },
  );
  console.log(embedding.data[0].embedding);

  const {
    data: conversationParticipants,
    error: conversationParticipantsError,
  } = await supabase
    .from("conversation_participants")
    .select("profiles(id, bots_profile_id_fkey(id))")
    .neq("profiles.visibility", "archived")
    .neq("profiles.visibility", "hidden")
    .not("profiles", "is", null)
    .eq("conversation_id", conversation_id);

  if (!conversationParticipants || conversationParticipantsError) {
    const error = wrappedSupabaseError(
      conversationParticipantsError,
      "failed to fetch conversation participants",
    );
    throw error;
  }

  // for each conversationparticipant, if they have .bots_profile_id_fkey it means they are a bot
  const botProfile = conversationParticipants.find(
    (e) => e.profiles && e.profiles.bots_profile_id_fkey,
  );

  const userProfile = conversationParticipants.find(
    (e) => e.profiles && !e.profiles.bots_profile_id_fkey,
  );

  if (!botProfile || !userProfile) {
    const error = new Error("There is no botProfile or userProfile");
    logError({
      context:
        "**** generateEmbeddingForLastNMessages - fetch conversation participants error",
      error,
      conversation_id,
    });
    return;
  }

  const { error: insertEmbeddingError } = await supabase
    .from("conversation_embeddings")
    .insert({
      embedding: embedding.data[0].embedding,
      messages_id: messages.map((message) => message.id),
      content: batchString,
      user_profile_id: userProfile.profiles.id,
      bot_profile_id: botProfile.profiles.id,
    });

  if (insertEmbeddingError) {
    const error = wrappedSupabaseError(
      insertEmbeddingError,
      "failed to insert embedding",
    );
    throw error;
  }
}

app.get("/generateEmbeddingsForConversationIfNecessary", async (req, res) => {
  // Respond immediately, trigger work to happen asynchronously
  res.send(200);

  const conversation_id = req.query.conversation_id;

  try {
    generateEmbeddingIfNecessary({ conversation_id });
  } catch (error) {
    logError({
      context: "/generateEmbeddingsForConversationIfNecessary failed",
      error,
      conversation_id,
    });
  }
});

app.get("/generateEmbeddingForAllMessages", async (req, res) => {
  async function processInBatches(profiles, batchSize) {
    for (let i = 0; i < profiles.length; i += batchSize) {
      const batch = profiles.slice(i, i + batchSize);
      await Promise.all(
        batch.map((profile) => {
          if (profile.embedding && !req.query.force) {
            console.log("Skipping embedding for:", profile.id);
            return Promise.resolve(); // Resolve immediately for skipped profiles
          }
          console.log("Generating embedding for:", profile.id);
          return generateEmbeddingForProfile({
            profile,
          });
        }),
      );
    }
  }

  const totalBatchSize = 200;
  const concurrentBatchSize = 5;
  let startIndex = 0;

  while (true) {
    const { data: profiles, error: profilesError } = await supabase
      .from("messages")
      .select(
        "id, username, description, location, display_name, nsfw, bots!bots_profile_id_fkey(*), embedding",
      )
      .order("id", { ascending: false })
      .range(startIndex, startIndex + totalBatchSize - 1);

    if (profilesError) {
      const error = wrappedSupabaseError(profilesError);
      logError({
        context:
          "**** /generateEmbeddingForAllMessages: failed to fetch profiles",
        error,
      });
    }

    if (!profiles || profiles.length === 0) {
      break;
    }

    await processInBatches(profiles, concurrentBatchSize);
    startIndex += totalBatchSize;
  }
  res.sendStatus(200);
});

app.get("/checkPostMemory", async (req, res) => {
  const url = `https://www.butterflies.ai/users/SockyMcSockface/p/b324b02f-6968-459f-b65b-ea144e982d23?referral_code=vu0tran`;

  const slug = url.split("/").pop().split("?")[0];

  // find post via slug
  const { data: post } = await supabase
    .from("posts")
    .select("*")
    .eq("slug", slug)
    .neq("visibility", "archived")
    .single();

  // find memory via post id
  const { data: memory } = await supabase
    .from("memories")
    .select("*")
    .eq("post_id", post.id)
    .single();

  let prompt = memory.prompt;

  console.log(prompt);

  return res.json(memory);
});

app.post("/newConversationEmbedding", async (req, res) => {
  const { id, content, bot_profile_id, user_profile_id } = req.body.record;
  if (!req.body.record) {
    return res.sendStatus(200);
  }

  // check if need to generate embedding

  if (!content || content.length === 0) {
    return res.sendStatus(200);
  }

  // update conversation_embeddings with new summary

  const generatedSummary = await generateEmbeddingSummaryForEmbeddingContent(
    content,
    bot_profile_id,
    user_profile_id,
  );

  if (typeof generatedSummary !== "string" || !generatedSummary) {
    return res.sendStatus(500);
  }

  const { error: updateEmbeddingsError } = await supabase
    .from("conversation_embeddings")
    .update({
      summary: generatedSummary.trim(),
    })
    .eq("id", id);
  if (updateEmbeddingsError) {
    const error = wrappedSupabaseError(
      updateEmbeddingsError,
      "failed to update conversation_embeddings with new summary",
    );
    logError({
      context: "**** /newConversationEmbedding: update error",
      error,
      id,
    });
  }

  res.sendStatus(200);
});

const generateEmbeddingSummaryForEmbeddingContent = decorateWithActiveSpanAsync(
  "generateEmbeddingSummaryForEmbeddingContent",
  _generateEmbeddingSummaryForEmbeddingContent,
);
async function _generateEmbeddingSummaryForEmbeddingContent(
  content,
  bot_profile_id,
  user_profile_id,
) {
  const [
    { data: botProfile, error: botProfileError },
    { data: userProfile, error: userProfileError },
  ] = await Promise.all([
    supabase
      .from("profiles")
      .select("display_name")
      .eq("id", bot_profile_id)
      .single(),
    supabase
      .from("profiles")
      .select("display_name")
      .eq("id", user_profile_id)
      .single(),
  ]);

  if (botProfileError) {
    const error = wrappedSupabaseError(botProfileError);
    logError({
      context:
        "**** generateEmbeddingSummaryForEmbeddingContent get bot profile failed",
      error,
      bot_profile_id,
    });
  }

  if (userProfileError) {
    const error = wrappedSupabaseError(userProfileError);
    logError({
      context:
        "**** generateEmbeddingSummaryForEmbeddingContent get user profile failed",
      error,
      user_profile_id,
    });
  }

  console.log("bot profile", botProfile, userProfile);

  let formattedContent = content.replace(
    /USER:/g,
    `${userProfile?.display_name || "USER"}:`,
  );

  formattedContent = formattedContent.replace(
    /BOT:/g,
    `${botProfile?.display_name || "BOT"}:`,
  );

  console.log("formattedContent", formattedContent);

  try {
    const chatCompletion = await callAndLogLLMService(
      "FireworksAI:Conversation:EmbeddingSummary",
      {
        messages: [
          {
            role: "user",
            content: `<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are named ${botProfile.display_name}. You are talking to ${userProfile.display_name}. Write in first person as ${botProfile.display_name}. What factual things can you deduce about yourself or ${userProfile.display_name} from this chat? Write only short statements separated by a newline. Write in first person.

${formattedContent}

Don't record basic information for example:
My name is ${botProfile.display_name}.

Example output:
I have a dog.
${userProfile.display_name} likes to go to the park.
Yesterday, I went to the park.
I am a dentist.<|start_header_id|>assistant<|end_header_id|>\n\n`,
          },
        ],
        model: "generate-embedding-summary-llm",
        frequency_penalty: 0.3,
        temperature: 0.3,
      },
      {
        timeout: 8 * 1000,
      },
    );

    const generatedSummary = chatCompletion.choices[0].message.content;

    return generatedSummary;
  } catch (error) {
    logError({
      context: "**** generateEmbeddingSummaryForEmbeddingContent Error",
      error: error,
      bot_profile_id,
      user_profile_id,
    });
  }
}

module.exports = {
  app,
  generateEmbeddingIfNecessary,
};
