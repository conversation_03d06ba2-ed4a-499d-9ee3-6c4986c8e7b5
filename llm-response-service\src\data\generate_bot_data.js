const { openai, GPT_TEXT_MODEL } = require("../openai");
const { seed_bots } = require("./bots_2");
const fs = require("fs");

async function generate() {
  for (const bot of seed_bots) {
    await generateIGForBot({ bot });
  }
}

async function generateIGForBot({ bot }) {
  console.log("generating for", bot);
  const prompt = `Let's pretend you're ${bot.character} from ${bot.source} and you're creating an Instagram account. Give the information in JSON format, nothing else:
{
    bio: A short one sentence bio,
    username: the username of the user,
    display_name: the name of the user,
    follower_count: self explanatory,
    following_count: should be less than follower_count,
    timezone: a real time zone like america/los_angeles or something else,
    location: self explanatory,
}`;

  const chatCompletion = await openai.chat.completions.create(
    {
      response_format: { type: "json_object" },
      messages: [
        {
          role: "system",
          content: prompt,
        },
      ],
      model: GPT_TEXT_MODEL,
    },
    {
      timeout: 8 * 1000,
    }
  );

  let IGData = JSON.parse(chatCompletion.choices[0].message.content);
  IGData["source"] = bot.source;
  IGData["franchise"] = bot.franchise;
  IGData["tag"] = bot.type;
  IGData["gender"] = bot.gender;

  fs.writeFileSync(
    `./output/${IGData.username}.json`,
    JSON.stringify(IGData, null, 4)
  ); // The third argument formats the output with 4 space indentation
}

generate();
