const { logError } = require("../logUtils");
const { supabase, wrappedSupabaseError } = require("../supabaseClient");

async function readProfiles(ctx, { conversationId, senderId }) {
  // Retrieve the participants.
  const { data: participants, error: participantsError } = await supabase
    .from("conversation_participants")
    .select("*, profiles(*)")
    .neq("profiles.visibility", "archived")
    .neq("profiles.visibility", "hidden")
    .not("profiles", "is", null)
    .eq("conversation_id", conversationId);

  if (participantsError) {
    const error = wrappedSupabaseError(
      participantsError,
      "Failed to fetch participants",
    );
    logError({
      ...ctx.logging,
      msg: "failed to fetch conversation participants",
      error,
    });
    throw error;
  }

  const userParticipant =
    participants.find((e) => e.profile_id === senderId) || {};
  const botParticipant =
    participants.find((e) => e.profile_id !== senderId) || {};
  const userProfile = userParticipant.profiles;
  const botProfile = botParticipant.profiles;

  const { data: botConfiguration, error: botError } = await supabase
    .from("bots")
    .select("*")
    .eq("profile_id", botParticipant.profile_id)
    .single();

  return {
    userParticipant,
    botParticipant,
    userProfile,
    botProfile,
    botConfiguration: botError ? null : botConfiguration,
  };
}

module.exports = {
  readProfiles,
};
