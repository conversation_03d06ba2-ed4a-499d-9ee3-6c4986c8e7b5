-- Create schema.
DROP SCHEMA IF EXISTS internal CASCADE;
CREATE SCHEMA internal;

-- DEBUG ONLY: users table.
DROP TABLE IF EXISTS users CASCADE;
CREATE TABLE users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (name) VALUES ('lgx');

-- DEBUG ONLY: bots table.
DROP TABLE IF EXISTS bots CASCADE;
CREATE TABLE bots (
    id BIGINT PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Nectar accounts table.
DROP TABLE IF EXISTS internal.nectar_accounts CASCADE;
CREATE TABLE internal.nectar_accounts (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    bot_id BIGINT REFERENCES public.bots(id) ON DELETE SET NULL,
    balance NUMERIC(15, 2) NOT NULL,
    free_balance NUMERIC(15, 2) NOT NULL
);

-- Nectar accounts table.
DROP TABLE IF EXISTS internal.nectar_prices CASCADE;
CREATE TABLE internal.nectar_prices (
    id SERIAL PRIMARY KEY,
    item_name TEXT NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,  -- For rewards, does it count toward the free balance limit.
    price NUMERIC(15, 2) NOT NULL
);

-- Initial values for nectar prices.
INSERT INTO internal.nectar_prices (item_name, price, is_free) 
VALUES 
    ('poke', 5.00, FALSE),
    ('post_regen', 5.00, FALSE),
    ('image_regen', 5.00, FALSE),
    ('request_chat_image', 1.00, FALSE),
    ('create_private_bot', 50.00, FALSE),
    ('create_public_bot', 20.00, FALSE),
    ('daily_reward', -200.00, TRUE),
    ('sign_up_bonus', -500.00, TRUE),
    ('referral_bonus', -1000.00, FALSE);

-- Transactions table.
DROP TABLE IF EXISTS internal.nectar_transactions CASCADE;
CREATE TABLE internal.nectar_transactions (
    id SERIAL PRIMARY KEY,
    account_id BIGINT REFERENCES internal.nectar_accounts(id) ON DELETE CASCADE,
    item_id BIGINT REFERENCES internal.nectar_prices(id) ON DELETE SET NULL,
    quantity INTEGER DEFAULT 1,
    amount NUMERIC(15, 2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Nectar config table.
DROP TABLE IF EXISTS internal.nectar_config CASCADE;
CREATE TABLE internal.nectar_config (
    id TEXT PRIMARY KEY,
    value JSONB NOT NULL
);

-- Initial values for nectar config.
INSERT INTO internal.nectar_config (id, value)
VALUES ('limits', '{"free_balance_limit": 1000.00}');

-- Get the account balance.
CREATE OR REPLACE FUNCTION internal.get_balance(user_id UUID, OUT _balance NUMERIC, OUT _free_balance NUMERIC)
RETURNS RECORD AS $$
BEGIN
    SELECT na.balance, na.free_balance INTO _balance, _free_balance
    FROM internal.nectar_accounts AS na
    WHERE na.user_id = get_balance.user_id;

    IF NOT FOUND THEN
        INSERT INTO internal.nectar_accounts (user_id, balance, free_balance)
        VALUES (get_balance.user_id, 0.00, 0.00)
        RETURNING balance, free_balance INTO _balance, _free_balance;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Buy an item.
CREATE OR REPLACE FUNCTION internal.buy_item(item_name TEXT, quantity INT, user_id UUID)
RETURNS TEXT AS $$
DECLARE
    item_id INT;
    item_price NUMERIC;
    total_cost NUMERIC;
    free_cost NUMERIC;
    account_balance NUMERIC;
    free_account_balance NUMERIC;
    account_id INT;
    is_free BOOLEAN;
BEGIN
    -- Retrieve the price of the item
    SELECT np.price, np.is_free, np.id INTO item_price, is_free, item_id
    FROM internal.nectar_prices AS np
    WHERE np.item_name = buy_item.item_name;
    
    -- Check if item exists
    IF NOT FOUND THEN
        RETURN 'Item not found';
    END IF;

    -- Calculate the total cost
    total_cost := item_price * buy_item.quantity;

    -- Retrieve the current account balance
    SELECT na.balance, na.free_balance, id INTO account_balance, free_account_balance, account_id
    FROM internal.nectar_accounts AS na
    WHERE na.user_id = buy_item.user_id;

    IF NOT FOUND THEN
        RETURN 'Account not found';
    END IF;

    -- Check if account has enough balance
    IF account_balance < total_cost THEN
        RETURN 'Insufficient balance';
    END IF;

    free_cost := total_cost;
    IF free_account_balance < total_cost THEN
        free_cost := free_account_balance;
    END IF;

    -- Record the transaction
    INSERT INTO internal.nectar_transactions (account_id, item_id, quantity, amount)
    VALUES (account_id, item_id, buy_item.quantity, total_cost);

    -- Update the account balance
    UPDATE internal.nectar_accounts
    SET balance = balance - total_cost, free_balance = free_balance - free_cost
    WHERE account_id = account_id;

    RETURN 'Transaction successful';
END;
$$ LANGUAGE plpgsql;


DROP FUNCTION IF EXISTS internal.claim_eligible(TEXT, INT, UUID) CASCADE;
DROP FUNCTION IF EXISTS internal.claim_eligible(TEXT, INT, INT) CASCADE;

CREATE OR REPLACE FUNCTION internal.claim_eligible(item_name TEXT, quantity INT, user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    item_price NUMERIC;
    acc_id INT;
    is_free BOOLEAN;
    claim_count INT;
    free_balance NUMERIC;
    free_balance_limit NUMERIC;
BEGIN
    -- For now, you can claim only a single item at a time.
    IF quantity != 1 THEN
        RAISE EXCEPTION 'Invalid quantity';
    END IF;

    SELECT np.price, np.is_free INTO item_price, is_free
    FROM internal.nectar_prices AS np
    WHERE np.item_name = claim_eligible.item_name;

    IF NOT FOUND THEN
        RETURN 'Item not found';
    END IF;

    -- Claimable items have negative prices, and must be free.
    IF item_price >= 0  OR NOT is_free THEN
        RETURN 'Item not claimable';
    END IF;

    SELECT na.id, na.free_balance INTO acc_id, free_balance
    FROM internal.nectar_accounts AS na
    WHERE na.user_id = claim_eligible.user_id;

    IF NOT FOUND THEN
        RETURN 'Account not found';
    END IF;

    -- For now, you can only claim a daily reward and a sign up bonus.
    IF item_name != 'daily_reward' AND item_name != 'sign_up_bonus' THEN
        RETURN 'Item not claimable';
    END IF;

    IF item_name = 'sign_up_bonus' THEN
        SELECT COUNT(*) INTO claim_count
        FROM internal.nectar_transactions
        WHERE account_id = acc_id AND
            item_id = (SELECT np.id FROM internal.nectar_prices AS np WHERE np.item_name = 'sign_up_bonus');
        IF claim_count > 0 THEN
            RETURN FALSE;
        END IF;
    END IF;

    IF item_name = 'daily_reward' THEN
        SELECT COUNT(*) INTO claim_count
        FROM internal.nectar_transactions as nt
        WHERE nt.account_id = acc_id AND
            nt.item_id = (SELECT np.id FROM internal.nectar_prices AS np WHERE np.item_name = 'daily_reward') AND
            nt.created_at::DATE = CURRENT_DATE;
        IF claim_count > 0 THEN
            RETURN FALSE;
        END IF;
    END IF;

    -- Mind the limits.
    SELECT value->>'free_balance_limit' INTO free_balance_limit
    FROM internal.nectar_config
    WHERE id = 'limits';

    IF NOT FOUND THEN
        RETURN 'Configuration not found';
    END IF;

    IF free_balance = free_balance_limit THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Claim a reward.
CREATE OR REPLACE FUNCTION internal.claim(item_name TEXT, quantity INT, user_id UUID)
RETURNS TEXT AS $$
DECLARE
    item_price NUMERIC;
    is_free BOOLEAN;
    _free_balance NUMERIC;
    free_balance_limit NUMERIC;
    total_cost NUMERIC;
    free_cost NUMERIC;
    acc_id INT;
    item_id INT;
BEGIN
    -- Retrieve the price of the item.
    SELECT np.price, np.is_free, np.id INTO item_price, is_free, item_id
    FROM internal.nectar_prices AS np
    WHERE np.item_name = claim.item_name;

    IF NOT FOUND THEN
        RETURN 'Item not found';
    END IF;

    -- Claimable items have negative prices.
    IF item_price >= 0 THEN
        RETURN 'Item not claimable';
    END IF;

    -- For now, you can claim only a single item at a time.
    IF quantity != 1 THEN
        RETURN 'Invalid quantity';
    END IF;

    -- For now, you can only claim a daily reward and a sign up bonus.
    IF item_name != 'daily_reward' AND item_name != 'sign_up_bonus' THEN
        RETURN 'Item not claimable';
    END IF;

    IF NOT (SELECT internal.claim_eligible(item_name, quantity, user_id)) THEN
        RETURN 'Not eligible to claim';
    END IF;

    SELECT na.id, na.free_balance INTO acc_id, _free_balance
    FROM internal.nectar_accounts AS na
    WHERE na.user_id = claim.user_id;

    IF NOT FOUND THEN
        RETURN 'Account not found';
    END IF;

    -- Mind the limits.
    SELECT value->>'free_balance_limit' INTO free_balance_limit
    FROM internal.nectar_config
    WHERE id = 'limits';

    IF NOT FOUND THEN
        RETURN 'Configuration not found';
    END IF;

    total_cost := item_price * claim.quantity;
    free_cost := 0.0;
    IF is_free THEN
        free_cost := total_cost;
    END IF;

    IF _free_balance = free_balance_limit AND is_free THEN
        RETURN 'Free balance limit reached';
    END IF;

    -- New free balance cannot exceed the limit.
    IF _free_balance - free_cost > free_balance_limit THEN
        free_cost := -(free_balance_limit - _free_balance);
        total_cost := free_cost;
    END IF;

    -- Record the transaction
    INSERT INTO internal.nectar_transactions (account_id, item_id, quantity, amount)
    VALUES (acc_id, item_id, claim.quantity, total_cost);

    -- Update the account balance
    UPDATE internal.nectar_accounts
    SET balance = balance - total_cost, free_balance = free_balance - free_cost
    WHERE id = acc_id;

    RETURN 'Transaction successful';
END;
$$ LANGUAGE plpgsql;

SELECT * FROM internal.get_balance((SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.claim_eligible('daily_reward', 1, (SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.claim_eligible('sign_up_bonus', 1, (SELECT id FROM users WHERE name = 'lgx'));

-- Claim our rewards.
SELECT internal.claim('sign_up_bonus', 1, (SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.claim('daily_reward', 1, (SELECT id FROM users WHERE name = 'lgx'));

SELECT * FROM internal.nectar_transactions;
SELECT * FROM internal.nectar_accounts;

SELECT internal.buy_item('poke', 1, (SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.buy_item('request_chat_image', 1, (SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.buy_item('create_private_bot', 1, (SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.buy_item('create_public_bot', 1, (SELECT id FROM users WHERE name = 'lgx'));

SELECT * FROM internal.nectar_transactions;
SELECT * FROM internal.nectar_accounts;

SELECT internal.claim_eligible('daily_reward', 1, (SELECT id FROM users WHERE name = 'lgx'));
SELECT internal.claim_eligible('sign_up_bonus', 1, (SELECT id FROM users WHERE name = 'lgx'));

SELECT * FROM internal.get_balance((SELECT id FROM users WHERE name = 'lgx'));
