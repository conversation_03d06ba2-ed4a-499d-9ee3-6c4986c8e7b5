const {
  createClient,
  AuthUnknownError,
  AuthRetryableFetchError,
} = require("@supabase/supabase-js");
const { supabaseUrl, supabaseSecretKey } = require("./supabaseClient");
const { wrappedSupabaseError, logError } = require("./utils");
const { addSpanAttribute } = require("./instrumentation/tracer");

const isUnknownOrRetryableSupabaseAuthError = (supabaseError) => {
  return (
    supabaseError instanceof AuthUnknownError ||
    supabaseError instanceof AuthRetryableFetchError
  );
};

const delay = (milliseconds) =>
  new Promise((resolve) => setTimeout(resolve, milliseconds));

const MAX_TRIES = 3;
const DELAY_BETWEEN_TRIES = 100;

const tryGetSupabaseUser = async (supabaseClient, access_token, retryCount) => {
  const result = await supabaseClient.auth.getUser(access_token);
  const { data, error: supabaseError } = result;

  if (supabaseError) {
    if (isUnknownOrRetryableSupabaseAuthError(supabaseError)) {
      logError({
        context:
          "authUser: supabase.auth.getUser failed with unknown or retryable error",
        error: supabaseError,
      });
      if (retryCount < MAX_TRIES) {
        await delay(DELAY_BETWEEN_TRIES);
        return await tryGetSupabaseUser(
          supabaseClient,
          access_token,
          retryCount + 1,
        );
      }
      return { error: supabaseError };
    } else {
      return { error: supabaseError };
    }
  } else {
    return { data };
  }
};

const authUser = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const access_token = authHeader && authHeader.split(" ")[1];

    if (
      !access_token ||
      access_token === "undefined" || // some clients are currently sending this, see https://github.com/butterflies-ai/ai-ig/pull/2954
      access_token === "null"
    ) {
      return res.status(401).json({ error: "Access token is missing" });
    }

    if (access_token === process.env.BUTTERFLIES_BACKEND_ADMIN_ACCESS_TOKEN) {
      req.isAdmin = true;
      next();
      return;
    }

    const adhocSupabae = createClient(supabaseUrl, supabaseSecretKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
      },
    });

    // Attempt to set the auth for Supabase client with the provided access token
    const { data: userData, error: supabaseError } = await tryGetSupabaseUser(
      adhocSupabae,
      access_token,
      0,
    );

    // if the supabase operation returns an error (instead of throwing it), that means it's a Supabase-owned AuthError instance
    if (supabaseError) {
      if (isUnknownOrRetryableSupabaseAuthError(supabaseError)) {
        // if we reached here, that means we've reached the retry limit.
        //
        // We can't be sure what the error is, so we throw it instead of responding with 401.
        // 401 would cause the client to log the user out.
        const unexpectedError = wrappedSupabaseError(
          supabaseError,
          "unknown error when validating access token",
        );
        throw unexpectedError;
      }

      const badAccessTokenError = wrappedSupabaseError(
        supabaseError,
        "failed to validate access token",
      );
      logError({
        context:
          "authUser: supabase.auth.getUser failed due to bad access token",
        error: badAccessTokenError,
      });

      return res
        .status(401)
        .json({ error: "Unauthorized - Invalid access token" });
    }

    // If successful, attach user to request object
    req.user = userData.user;

    addSpanAttribute("user.id", userData.user.id);
    next();
  } catch (error) {
    logError({
      context: "error authing",
      error,
    });
    return res.status(500).json({ error: "Internal Server Error" });
  }
};

module.exports = { authUser };
