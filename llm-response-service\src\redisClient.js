const { createClient: createRedisClient } = require("redis");
const { logError } = require("./utils");

const redisClient = createRedisClient({
  password: "Fs9aU42apD1Fx21l1NhCWUZpdCfaL49y",
  socket: {
    host: "redis-10752.c228.us-central1-1.gce.cloud.redislabs.com",
    port: 10752,
  },
});

redisClient.on("error", (err) => {
  logError({
    context: "Redis Client Error",
    error: err,
  });
});

redisClient.connect();

module.exports = redisClient;
