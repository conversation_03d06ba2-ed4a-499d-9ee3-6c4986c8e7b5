const { autoModerate } = require("../moderation");
const { supabase } = require("../supabaseClient");
const {
  logError,
  logInfo,
  wrappedSupabaseError,
  logWarn,
} = require("../utils");
const { updatePostWithVignetteInfo } = require("./updatePostWithVignetteInfo");

/**
 * Process a completed vignette scene image generation
 *
 * @param {Object} params - The parameters object
 * @param {number} params.sceneId - The vignette_scene ID
 * @param {number} params.postVignetteId - The post_vignette ID
 * @param {number} params.postId - The post ID
 * @param {string} params.imageUrl - The URL of the generated image
 * @param {string} params.blurhash - The blurhash of the generated image
 * @returns {Promise<Object>} - The updated vignette scene
 */
async function processVignetteSceneImage({
  sceneId,
  postVignetteId,
  postId,
  imageUrl,
  blurhash,
}) {
  logInfo({
    context: "processVignetteSceneImage",
    sceneId,
    postVignetteId,
    postId,
    imageUrl,
  });

  try {
    // Get the scene and its related post_vignette
    const { data: scene, error: sceneError } = await supabase
      .from("vignette_scenes")
      .select("*, post_vignettes(*)")
      .eq("id", sceneId)
      .single();

    if (sceneError) {
      const error = wrappedSupabaseError(sceneError);
      logError({
        context: "processVignetteSceneImage - Failed to fetch scene",
        error,
        sceneId,
      });
      throw error;
    }

    if (!scene) {
      throw new Error(`No vignette scene found with ID: ${sceneId}`);
    }

    // Perform image moderation
    const moderationResult = await autoModerate({
      imageUrl,
      id: scene.post_vignettes.profile_id,
      context: "vignette-scene-image",
    });

    // Determine the new status based on moderation
    const newStatus = moderationResult.nsfl ? "failed_moderation" : "completed";

    // Update the scene with the image URL and moderation results
    const { data: updatedScene, error: updateError } = await supabase
      .from("vignette_scenes")
      .update({
        image_url: imageUrl,
        blurhash,
        status: newStatus,
        nsfw: moderationResult.nsfw,
        nsfw_score: moderationResult.nsfw_score,
        nsfl: moderationResult.nsfl || false,
        safe_search_detection: moderationResult,
      })
      .eq("id", sceneId)
      .select()
      .single();

    if (updateError) {
      const error = wrappedSupabaseError(updateError);
      logError({
        context: "processVignetteSceneImage - Failed to update scene",
        error,
        sceneId,
      });
      throw error;
    }

    logInfo({
      context: "processVignetteSceneImage",
      message: `Processed image for vignette scene ${sceneId}, status: ${newStatus}`,
      sceneId,
      nsfw: moderationResult.nsfw,
      nsfl: moderationResult.nsfl,
    });

    // Check status of all scenes for this post_vignette
    await updatePostVignetteStatus(scene.post_vignette_id);

    return updatedScene;
  } catch (error) {
    logError({
      context: "processVignetteSceneImage",
      error,
      sceneId,
    });
    throw error;
  }
}

/**
 * Update the post_vignette status based on the status of all its scenes
 *
 * @param {string} postVignetteId - The post_vignette ID to update
 */
async function updatePostVignetteStatus(postVignetteId) {
  try {
    // Get all scenes for this post_vignette
    const { data: scenes, error: scenesError } = await supabase
      .from("vignette_scenes")
      .select("id, status")
      .eq("post_vignette_id", postVignetteId);

    if (scenesError) {
      const error = wrappedSupabaseError(scenesError);
      logError({
        context: "updatePostVignetteStatus - Failed to fetch scenes",
        error,
        post_vignette_id: postVignetteId,
      });
      throw error;
    }

    // Check if any scene has a pending status
    const pendingScenes = scenes.filter((s) => s.status === "pending");
    const nonPendingScenes = scenes.filter((s) => s.status !== "pending");
    const pendingExists = pendingScenes.length > 0;
    if (pendingExists) {
      // Not all scenes are processed yet
      logWarn({
        context: "updatePostVignetteStatus",
        message: `Not all scenes are processed yet for post_vignette ${postVignetteId}`,
        post_vignette_id: postVignetteId,
        pendingScenes,
        nonPendingScenes,
      });
      return;
    }

    // Check if any scene failed (either generation or moderation)
    const failedExists = scenes.some(
      (s) =>
        s.status === "failed_generation" || s.status === "failed_moderation",
    );

    // Determine new status for post_vignette
    const newStatus = failedExists ? "failed" : "completed";

    // Update post_vignette status
    const { error: updateError } = await supabase
      .from("post_vignettes")
      .update({ status: newStatus })
      .eq("id", postVignetteId);

    if (updateError) {
      logError({
        context:
          "updatePostVignetteStatus - Failed to update post_vignette status",
        error: wrappedSupabaseError(updateError),
        post_vignette_id: postVignetteId,
      });
      return;
    }

    logInfo({
      context: "updatePostVignetteStatus",
      message: `Updated post_vignette ${postVignetteId} status to ${newStatus}`,
      post_vignette_id: postVignetteId,
    });

    // If completed, update the post with vignette info
    if (newStatus === "completed") {
      try {
        await updatePostWithVignetteInfo({ postVignetteId });
      } catch (error) {
        logError({
          context:
            "updatePostVignetteStatus - Failed to update post with vignettes",
          error,
          post_vignette_id: postVignetteId,
        });
      }
    }
  } catch (error) {
    logError({
      context: "updatePostVignetteStatus",
      error,
      post_vignette_id: postVignetteId,
    });
  }
}

module.exports = {
  processVignetteSceneImage,
  updatePostVignetteStatus,
};
